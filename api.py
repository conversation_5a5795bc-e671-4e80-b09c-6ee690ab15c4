import os
import sys
import uvicorn
from typing import Dict, Any, Optional, List
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends, status, Request, Query, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, JSONResponse, StreamingResponse, RedirectResponse
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from pydantic import BaseModel, Field, EmailStr
import logging
import sys
import asyncio
import jwt
from datetime import datetime, timedelta
from typing import Optional
import json
from db.user_tracking import track_user_booking, get_user_bookings, get_booking_users
from db.input_text_storage import (
    store_input_text,
    retrieve_input_text,
    delete_stored_input_text,
    list_stored_inputs
)
from contextlib import asynccontextmanager
import time
import threading
from collections import defaultdict
from db_service import get_db_connection, init_db_pool, db_connection, close_db_pool
from services.media_service import media_service
import io
from sse_starlette.sse import EventSourceResponse


# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

from services.extraction_service import ExtractionService
from services.booking_service import BookingService
from services.ui_logger_service import UILoggerService
from Core.ui_logger import set_current_user_context
from db.utils import start_booking_session
from db.session_manager import SessionManager
from db.studio_provider import get_studio_results
from db.ncl_provider import get_ncl_results
from db.cruising_power_provider import get_cruising_power_results
from db.onesource_provider import get_onesource_results
from db.screenshot_manager import get_screenshots_from_db
from db.video_manager import get_videos_by_session_id
from db.user_auth import (
    authenticate_user,
    create_user,
    get_user,
    update_user,
    delete_user,
    list_users,
    update_user_role,
    get_provider_list,
    get_user_accessible_providers,
    get_providers_by_agencies,
    add_provider,
    get_agency_list,
    update_provider,
    update_provider_status,
    delete_provider
)

from dotenv import load_dotenv

load_dotenv()

ENVIRONMENT =  os.getenv('ENVIRONMENT')

if ENVIRONMENT == 'production':
    URL = os.getenv('PROD_URL')
else:
    URL = os.getenv('TEST_URL')

# Initialize UI Logger Service - this will receive UI logs and broadcast them
ui_logger_service = UILoggerService.get_instance()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("api")

# JWT Configuration
SECRET_KEY = "oceanmind@update3.0.0-ultrabuggy--database"  # In production, use a secure key and store in env vars
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60

# Database connection monitoring
class DBConnectionMonitor:
    """Monitor database connection usage for production optimization"""
    
    def __init__(self):
        self.connection_stats = defaultdict(int)
        self.slow_queries = []
        self.lock = threading.Lock()
    
    def log_connection_usage(self, endpoint: str, duration: float):
        """Log connection usage statistics"""
        with self.lock:
            self.connection_stats[endpoint] += 1
            if duration > 1.0:  # Log slow queries (>1 second)
                self.slow_queries.append({
                    'endpoint': endpoint,
                    'duration': duration,
                    'timestamp': datetime.now().isoformat()
                })
                # Keep only last 100 slow queries
                if len(self.slow_queries) > 100:
                    self.slow_queries.pop(0)
    
    def get_stats(self):
        """Get connection usage statistics"""
        with self.lock:
            return {
                'connection_counts': dict(self.connection_stats),
                'slow_queries': self.slow_queries[-10:],  # Last 10 slow queries
                'total_connections': sum(self.connection_stats.values())
            }

# Initialize connection monitor
db_monitor = DBConnectionMonitor()

CHUNK_SIZE = 32 * 1024  # size for streaming from MinIO

# Helper function for cache invalidation
async def invalidate_tracking_cache_after_data_change(operation: str, username: str, details: str = ""):
    """
    Helper function to invalidate tracking cache after any data change
    
    Args:
        operation: Type of operation (e.g., "booking", "validation", "update")
        username: Username who performed the operation  
        details: Additional details about the operation
    """
    try:
        from services.user_tracking_cache_service import UserTrackingCacheService
        await UserTrackingCacheService.invalidate_tracking_cache()  # Clear ALL cache
        logger.info(f"🗑️ Invalidated ALL tracking cache after {operation} by {username} {details}")
    except ImportError:
        logger.debug("Redis not available, skipping cache invalidation")
    except Exception as e:
        logger.warning(f"Failed to invalidate cache after {operation}: {e}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize database pool
    logger.info("Initializing database connection pool...")
    await init_db_pool()
    logger.info("Database connection pool initialized successfully")
    
    # Initialize Redis connection
    logger.info("Initializing Redis connection...")
    try:
        from services.redis_service import redis_service
        await redis_service.initialize()
        logger.info("Redis connection initialized successfully")
    except ImportError as e:
        logger.warning(f"Redis library not available: {e}. Continuing without Redis caching.")
    except Exception as e:
        logger.warning(f"Redis initialization failed: {e}. Continuing without Redis caching.")
    
    # Initialize MinIO buckets
    from config.minio_config import minio_config
    logger.info("Initializing MinIO buckets and client pool...")
    await minio_config.initialize_buckets()
    logger.info("MinIO buckets and client pool initialized successfully")
    
    # Setup UI logger Service on startup
    from Core.ui_logger import set_ui_logger_service
    set_ui_logger_service(ui_logger_service)
    ui_logger_service.start_background_tasks(app)
    logger.info("UI Logger Service initialized and connected to UI logger")
    # Initialize shared browsers for all modules
    from Core.browser_setup import browser_manager
    await browser_manager.start_browsers()
    logger.info("Shared browsers initialized for all modules")
    yield
    # Cleanup on shutdown: stop shared browsers
    from Core.browser_setup import browser_manager
    await browser_manager.stop_browsers()
    logger.info("All shared browsers stopped")
    
    # Close Redis connections
    from services.redis_service import redis_service
    logger.info("Closing Redis connections...")
    await redis_service.close()
    logger.info("Redis connections closed successfully")
    
    # Close MinIO client pool
    from config.minio_config import minio_config
    logger.info("Closing MinIO client pool...")
    await minio_config.close_pool()
    logger.info("MinIO client pool closed successfully")
    
    # Close database connection pool
    await close_db_pool()
    logger.info("Database connection pool shutdown complete")

# Initialize FastAPI app with lifespan
app = FastAPI(
    title="OceanMind API",
    description="API for OceanMind cruise booking and extraction services",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[URL],  # Specific origins for credentials
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# OAuth2 password bearer for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Auth-related Pydantic models
class Token(BaseModel):
    access_token: str
    token_type: str
    user: Dict[str, Any]

class TokenData(BaseModel):
    username: Optional[str] = None

class User(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    role: str
    agency: str
    portal_access: List[str]
    provider_access: List[str] = []
    status: str = "pending"  # New field: pending, approved, rejected

class UserCreate(User):
    password: str

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    role: Optional[str] = None
    agency: Optional[str] = None
    portal_access: Optional[List[str]] = None
    provider_access: Optional[List[str]] = None
    password: Optional[str] = None
    status: Optional[str] = None

class Provider(BaseModel):
    name: str
    agency: str
    description: str
    status: str = "active"

class ProviderResponse(Provider):
    provider_id: int

# SSE Stream endpoint for UI logs
@app.get("/ui-logs/stream")
async def stream_ui_logs(request: Request, token: Optional[str] = None):
    """
    Server-Sent Events endpoint that streams UI logs to the frontend.
    User logs are filtered by session_id to ensure privacy.
    """
    # Default values for user info
    user_id = None
    session_id = None
    is_admin = False

    # Attempt to get user info from token if provided
    if token:
        try:
            # Decode JWT token
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            user_id = payload.get("user_id")
            
            # Get user details if user_id exists
            if user_id:
                user = await get_user(user_id)
                if user:
                    # Check if user is admin (for admin monitoring capabilities)
                    is_admin = user.get("role") == "admin"
                    
                    # Get the user's active session if available
                    if request.query_params.get("session_id"):
                        session_id = request.query_params.get("session_id")
                        logger.info(f"User {user.get('username')} connected to UI logs with session {session_id}")
        except Exception as e:
            # If token validation fails, just continue without user context
            logger.warning(f"Failed to authenticate UI logs client: {e}")
    
    # Register the client with the UI logger service
    client_queue = await ui_logger_service.add_client(
        request, 
        user_id=user_id, 
        session_id=session_id,
        is_admin=is_admin
    )
    
    # SSE streaming response function using EventSourceResponse
    async def event_generator():
        try:
            # Initial connection message so frontend knows we are live
            yield {
                "data": json.dumps({
                    "connected": True,
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                })
            }

            # Stream logs as they arrive
            while True:
                message = await client_queue.get()

                # Forward the log dictionary but convert to JSON string ourselves so that
                # the browser always receives a valid, double-quoted JSON payload.
                yield {"data": json.dumps(message)}

                client_queue.task_done()

        except asyncio.CancelledError:
            # Handle client disconnection
            ui_logger_service.remove_client(client_queue)
            logger.info("Client disconnected from SSE stream")

    # Use EventSourceResponse with automatic heart-beats every 10 seconds to keep proxies alive
    return EventSourceResponse(event_generator(), ping=10)

# ------------------------------------------------------------------
# ENDPOINT: attach session id to existing SSE connection (added after get_current_user)
# ------------------------------------------------------------------
from pydantic import BaseModel

class AttachSessionRequest(BaseModel):
    session_id: str

# (removed duplicate attach_ui_session; single definition appears later)

# Pydantic models for request validation
class ExtractionRequest(BaseModel):
    provider: str = Field(..., description="Provider name ('NCL', 'Cruising Power', or 'Studio')")
    text_input: str = Field(..., description="Raw cruise details text")
    url_input: Optional[str] = Field("", description="Booking URL (for Studio)")
    request_id: Optional[str] = Field(None, description="Request ID")

class BookingRequest(BaseModel):
    provider: str = Field(..., description="Provider name ('NCL', 'Cruising Power', or 'Studio')")
    cruise_details: Any = Field(..., description="Cruise details extracted from text: dict for NCL/CP or list for Studio")
    config: Dict[str, Any] = Field(..., description="Configuration options for the booking process")
    request_id: str = Field(..., description="Unique identifier for the booking request")

# Function to create JWT token
def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# Function to get current user from token
async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except jwt.PyJWTError:
        raise credentials_exception
        
    # get_user is asynchronous (asyncpg), so we must await it
    user = await get_user(payload.get("user_id"))
    if user is None:
        raise credentials_exception
        
    # Check user status
    if user.get("status") == "suspended":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User account is suspended"
        )
    elif user.get("status") != "approved":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User account is not approved yet"
        )
        
    # Store user_id in contextvar so that ui_log() can include it automatically
    try:
        set_current_user_context(user.get("user_id") or user.get("id") or user.get("_id"))
    except Exception:
        # Failsafe: never break auth flow if contextvar cannot be set
        pass
    return user

# Function to check if user is admin
async def get_current_admin(current_user: Dict = Depends(get_current_user)):
    logger.debug(f"Checking admin permissions for user: {current_user.get('username')}, role: {current_user.get('role')}")
    if current_user.get("role") != "admin":
        logger.warning(f"User {current_user.get('username')} attempted to access admin endpoint but has role {current_user.get('role')}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Not enough permissions"
        )
    return current_user

# Function to check if user is at least sub_admin
async def get_current_admin_or_subadmin(current_user: Dict = Depends(get_current_user)):
    logger.debug(f"Checking admin/subadmin permissions for user: {current_user.get('username')}, role: {current_user.get('role')}")
    if current_user.get("role") not in ["admin", "sub_admin"]:
        logger.warning(f"User {current_user.get('username')} attempted to access admin/subadmin endpoint but has role {current_user.get('role')}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Not enough permissions"
        )
    return current_user

# ------------------------------------------------------------------
# Attach session id to existing SSE connection (now correctly positioned)
# ------------------------------------------------------------------
class AttachSessionRequest(BaseModel):
    session_id: str

@app.post("/ui-logs/attach-session")
@app.post("/api/ui-logs/attach-session", include_in_schema=False)
async def attach_ui_session(data: AttachSessionRequest, current_user: Dict = Depends(get_current_user)):
    logger.info(f"AttachSessionRequest received for user {current_user.get('username')} session {data.session_id}")
    user_id = current_user.get("user_id") or current_user.get("id") or current_user.get("_id")
    if not user_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unable to determine user id")
    await ui_logger_service.debug_log(f"attach_session_to_user called for user_id={user_id} session_id={data.session_id}")
    await ui_logger_service.attach_session_to_user(user_id, data.session_id)
    return {"attached": True, "session_id": data.session_id}

# ------------------------------------------------------------------

# Authentication and User Management Endpoints
@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = await authenticate_user(form_data.username, form_data.password)
    if not user:
        logger.warning(f"Failed login attempt for username: {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    # Check if user is approved
    logger.info(f"User {form_data.username} authenticated, status: {user.get('status', 'not set')}")
    
    if user.get("status") == "suspended":
        logger.warning(f"Suspended user {form_data.username} attempted to log in")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Your account has been suspended. Please contact an administrator."
        )
    elif user.get("status") != "approved":
        logger.warning(f"User {form_data.username} tried to login but status is {user.get('status', 'not set')}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Your account is pending approval by an administrator"
        )
        
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"], "user_id": user["user_id"]}, 
        expires_delta=access_token_expires
    )
    logger.info(f"User {form_data.username} logged in successfully")
    return {"access_token": access_token, "token_type": "bearer", "user": user}

@app.post("/register", response_model=Dict[str, Any])
async def register_user(user_data: UserCreate, current_user: Dict = Depends(get_current_admin)):
    """Register a new user (admin only)."""
    try:
        result = await create_user(
            username=user_data.username,
            password=user_data.password,
            email=user_data.email,
            full_name=user_data.full_name,
            role=user_data.role,
            agency=user_data.agency,
            portal_access=user_data.portal_access
        )
        if result:
            return {"success": True, "message": "User created successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to create user")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/users/me", response_model=Dict[str, Any])
async def read_users_me(current_user: Dict = Depends(get_current_user)):
    """Get current user info."""
    return current_user

@app.get("/users", response_model=List[Dict[str, Any]])
async def read_users(
    role: Optional[str] = None, 
    agency: Optional[str] = None,
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """List all users (admin or sub_admin only)."""
    return await list_users(role=role, agency=agency)

@app.get("/users/pending", response_model=List[Dict[str, Any]])
async def get_pending_users(current_user: Dict = Depends(get_current_admin_or_subadmin)):
    """List all pending users (admin or sub_admin only)."""
    try:
        async with db_connection() as conn:
            users = await conn.fetch("""
            SELECT user_id, username, email, full_name, role, agency, portal_access, provider_access, created_at, last_login, status
            FROM users
            WHERE status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1000000
            """)
            result = []
            for user in users:
                user_dict = dict(user)
                user_dict['portal_access'] = json.loads(user_dict['portal_access'])
                if user_dict.get('provider_access'):
                    user_dict['provider_access'] = json.loads(user_dict['provider_access'])
                else:
                    user_dict['provider_access'] = []
                result.append(user_dict)
            return result
    except Exception as e:
        logger.error(f"Error listing pending users: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/users/{user_id}/approve", response_model=Dict[str, Any])
async def approve_user(
    user_id: str, 
    user_data: UserUpdate,
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """Approve a user and set their role, agency, and portal access (admin or sub_admin only)."""
    try:
        user = await get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        update_data = user_data.dict(exclude_none=True)
        update_data["status"] = "approved"
        result = await update_user(user_id, update_data)
        if result:
            return {"success": True, "message": "User approved successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to approve user")
    except Exception as e:
        logger.error(f"Error approving user: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/users/{user_id}/reject", response_model=Dict[str, Any])
async def reject_user(
    user_id: str,
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """Reject a user registration (admin or sub_admin only)."""
    try:
        user = await get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        result = await update_user(user_id, {"status": "rejected"})
        if result:
            return {"success": True, "message": "User rejected successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to reject user")
    except Exception as e:
        logger.error(f"Error rejecting user: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/users/{user_id}", response_model=Dict[str, Any])
async def read_user(
    user_id: str, 
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """Get a specific user by ID (admin or sub_admin only)."""
    user = await get_user(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@app.put("/users/{user_id}", response_model=Dict[str, Any])
async def update_user_info(
    user_id: str, 
    user_data: UserUpdate, 
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """Update user information (admin only)."""
    result = await update_user(user_id, user_data.dict(exclude_none=True))
    if result:
        return {"success": True, "message": "User updated successfully"}
    else:
        raise HTTPException(status_code=404, detail="User not found or update failed")

@app.delete("/users/{user_id}", response_model=Dict[str, Any])
async def delete_user_account(
    user_id: str, 
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """Delete a user (admin only)."""
    # Prevent deleting yourself
    if user_id == current_user["user_id"]:
        raise HTTPException(status_code=400, detail="Cannot delete your own account")
    
    # Get user to check role
    user = await get_user(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
        
    # Prevent deleting any admin user
    if user.get("role") == "admin":
        raise HTTPException(status_code=400, detail="Admin accounts cannot be deleted")
        
    result = await delete_user(user_id)
    if result:
        return {"success": True, "message": "User deleted successfully"}
    else:
        raise HTTPException(status_code=404, detail="User not found or delete failed")

@app.put("/users/{user_id}/role", response_model=Dict[str, Any])
async def update_role(
    user_id: str, 
    role: str, 
    current_user: Dict = Depends(get_current_admin)
):
    """Update a user's role (admin only)."""
    if role not in ["admin", "sub_admin", "user"]:
        raise HTTPException(status_code=400, detail="Invalid role")
        
    result = await update_user_role(user_id, role)
    if result:
        return {"success": True, "message": "Role updated successfully"}
    else:
        raise HTTPException(status_code=404, detail="User not found or update failed")

# Pricing Portal Endpoints
@app.get("/providers", response_model=List[ProviderResponse])
async def list_providers(
    agency: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """List all providers, optionally filtered by agency."""
    providers = await get_provider_list(agency=agency)
    # Rename key in each dict
    return [{
        "provider_id": p["provider_id"],
        "name": p["name"],
        "agency": p["agency"],
        "description": p.get("description"),
        "status": p.get("status", "active"),
    } for p in providers]

@app.post("/providers", response_model=ProviderResponse)
async def create_provider(
    provider: Provider,
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """Create a new provider (admin only)."""
    try:
        logger.info(f"Creating provider: {provider.dict()}")
        logger.info(f"User creating provider: {current_user.get('username', 'unknown')}")
        
        # Ensure database tables exist
        try:
            from db.postgres_schema import initialize_database
            await initialize_database()
            logger.info("Database initialization check completed")
        except Exception as init_error:
            logger.warning(f"Database initialization warning: {init_error}")
        
        provider_id = await add_provider(
            name=provider.name,
            agency=provider.agency,
            description=provider.description,
            status=provider.status
        )
        
        logger.info(f"add_provider returned: {provider_id}")
        
        if not provider_id:
            logger.error("add_provider returned None or empty string")
            raise HTTPException(status_code=400, detail="Failed to create provider - database operation failed")
            
        response_data = {"provider_id": provider_id, **provider.dict()}
        logger.info(f"Returning response: {response_data}")
        return response_data
        
    except HTTPException:
        # Re-raise HTTPExceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error in create_provider endpoint: {e}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.put("/providers/{provider_id}", response_model=Dict[str, Any])
async def update_provider_info(
    provider_id: int,
    provider: Provider,
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """Update a provider."""
    try:
        logger.info(f"Attempting to update provider {provider_id} with data: {provider.dict()}")
        success = await update_provider(provider_id, provider.dict())
        logger.info(f"Update provider result: {success}")
        if not success:
            raise HTTPException(status_code=404, detail="Provider not found or update failed")
        return {"success": True, "message": "Provider updated successfully"}
    except Exception as e:
        logger.error(f"Error updating provider {provider_id}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/providers/{provider_id}", response_model=Dict[str, Any])
async def delete_provider_endpoint(
    provider_id: int,
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """Delete a provider."""
    try:
        logger.info(f"Attempting to delete provider {provider_id}")
        success = await delete_provider(provider_id)
        logger.info(f"Delete provider result: {success}")
        if not success:
            raise HTTPException(status_code=404, detail="Provider not found or delete failed")
        return {"success": True, "message": "Provider deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting provider {provider_id}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

# Booking Routes (now with authentication)
# Routes
@app.post("/extraction", response_model=Dict[str, Any])
async def extract_cruise_details(
    request: ExtractionRequest,
):
    """Extract cruise details from text input based on provider."""
    try:
        # Generate a request_id if one wasn't provided
        if not request.request_id:
            request.request_id = f"{request.provider.lower()}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Store the input text for future reference
        try:
            store_success = await store_input_text(
                request_id=request.request_id,
                text_input=request.text_input,
                url_input=request.url_input
            )
            if store_success:
                logger.info(f"Stored input text for request_id: {request.request_id}")
            else:
                logger.warning(f"Failed to store input text for request_id: {request.request_id}")
        except Exception as e:
            logger.error(f"Error storing input text: {e}")
            # Don't fail the extraction if storing fails
        
        details = ExtractionService.extract_details(
            request.provider,
            request.text_input,
            request.url_input,
            request.request_id
        )
        
        return {
            "success": True,
            "details": details,
            "request_id": request.request_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ncl-booking", response_model=Dict[str, Any])
async def ncl_booking(
    request: BookingRequest, 
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Process NCL booking with the provided cruise details."""
    try:
        # Generate session ID
        session_id = await start_booking_session(request.request_id, request.provider)
        
        async def _run_ncl():
            await BookingService.ncl_booking(
                request.cruise_details,
                request.config,
                request.request_id,
                session_id
            )

        # Create and register the task
        task = asyncio.create_task(_run_ncl())
        from services.task_registry import task_registry
        task_registry.register_task(session_id, task, provider="NCL")
        
        # Track which user initiated this booking
        try:
            await track_user_booking(
                username=current_user["username"],
                provider="ncl",
                request_id=request.request_id,
                session_id=session_id
            )
            logger.info(f"Tracking user {current_user['username']} for NCL booking {request.request_id}")
        except Exception as e:
            logger.error(f"Error tracking user for booking: {e}")
            # Continue with the booking process even if tracking fails
        
        # Invalidate tracking cache after successful booking
        await invalidate_tracking_cache_after_data_change("NCL booking", current_user["username"])
        
        return {
            "success": True,
            "message": "Booking process started",
            "session_id": session_id,
            "request_id": request.request_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/cruising-power-booking", response_model=Dict[str, Any])
async def cruising_power_booking(
    request: BookingRequest, 
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Process Cruising Power booking with the provided cruise details."""
    try:
        # Generate session ID
        session_id = await start_booking_session(request.request_id, request.provider)

        async def _run_cp():
            await BookingService.cruising_power_booking(
                request.cruise_details,
                request.config,
                request.request_id,
                session_id
            )

        # Create and register the task
        task = asyncio.create_task(_run_cp())
        from services.task_registry import task_registry
        task_registry.register_task(session_id, task, provider="Cruising Power")
        
        # Track which user initiated this booking
        try:
            await track_user_booking(
                username=current_user["username"],
                provider="cruising_power",
                request_id=request.request_id,
                session_id=session_id
            )
            logger.info(f"Tracking user {current_user['username']} for Cruising Power booking {request.request_id}")
        except Exception as e:
            logger.error(f"Error tracking user for booking: {e}")
            # Continue with the booking process even if tracking fails

        # Invalidate tracking cache after successful booking
        await invalidate_tracking_cache_after_data_change("Cruising Power booking", current_user["username"])
        
        return {
            "success": True,
            "message": "Booking process started",
            "session_id": session_id,
            "request_id": request.request_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/onesource-booking", response_model=Dict[str, Any])
async def onesource_booking(
    request: BookingRequest, 
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Process OneSource booking with the provided cruise details."""
    try:
        # Generate session ID
        session_id = await start_booking_session(request.request_id, request.provider)

        async def _run_onesource():
            await BookingService.onesource_booking(
                request.cruise_details,
                request.config,
                request.request_id,
                session_id
            )

        # Create and register the task
        task = asyncio.create_task(_run_onesource())
        from services.task_registry import task_registry
        task_registry.register_task(session_id, task, provider="OneSource")

        # Track which user initiated this booking
        try:
            await track_user_booking(
                username=current_user["username"],
                provider="onesource",
                request_id=request.request_id,
                session_id=session_id
            )
            logger.info(f"Tracking user {current_user['username']} for OneSource booking {request.request_id}")
        except Exception as e:
            logger.error(f"Error tracking user for booking: {e}")
            # Continue with the booking process even if tracking fails

        # Invalidate tracking cache after successful booking
        await invalidate_tracking_cache_after_data_change("OneSource booking", current_user["username"])

        return {
            "success": True,
            "message": "Booking process started",
            "session_id": session_id,
            "request_id": request.request_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/studio-booking", response_model=Dict[str, Any])
async def studio_booking(
    request: BookingRequest, 
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Process Studio booking with the provided cruise details."""
    try:
        # Generate session ID
        session_id = await start_booking_session(request.request_id, request.provider)

        async def _run_studio():
            await BookingService.studio_booking(
                request.cruise_details,
                request.config,
                request.request_id,
                session_id
            )

        # Create and register the task
        task = asyncio.create_task(_run_studio())
        from services.task_registry import task_registry
        task_registry.register_task(session_id, task, provider="Studio")

        # Track which user initiated this booking
        try:
            await track_user_booking(
                username=current_user["username"],
                provider="studio",
                request_id=request.request_id,
                session_id=session_id
            )
            logger.info(f"Tracking user {current_user['username']} for Studio booking {request.request_id}")
        except Exception as e:
            logger.error(f"Error tracking user for booking: {e}")
            # Continue with the booking process even if tracking fails

        # Invalidate tracking cache after successful booking
        await invalidate_tracking_cache_after_data_change("Studio booking", current_user["username"])

        return {
            "success": True,
            "message": "Booking process started",
            "session_id": session_id,
            "request_id": request.request_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/booking-status/{session_id}")
async def get_booking_status(
    session_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Get the status of a booking process."""
    try:
        session_manager = SessionManager.get_instance()
        status = await session_manager.get_session_status(session_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return {
            "success": True,
            "session_id": session_id,
            "status": status
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/cancel-booking/{session_id}")
async def cancel_booking(
    session_id: str,
    reset: bool = True,
    current_user: Dict = Depends(get_current_user)
):
    """Cancel an active booking process."""
    try:
        from services.cancellation_service import CancellationService
        
        logger.info(f"User {current_user['username']} requesting cancellation of session {session_id}")
        
        # Cancel the booking
        result = await CancellationService.cancel_booking(
            session_id=session_id,
            user_id=current_user.get('username')
        )
        
        if result['success']:
            logger.info(f"Successfully cancelled booking for session {session_id}")
            
            # Update validation status to 'reset' only when reset=True (for Reset Booking button)
            if reset:
                try:
                    from db_service import db_connection
                    async with db_connection() as conn:
                        await conn.execute("""
                            UPDATE user_booking_tracking
                            SET val_status = 'reset', timestamp = $1
                            WHERE session_id = $2
                        """, datetime.utcnow().isoformat(), session_id)
                    logger.info(f"Updated validation status to 'reset' for cancelled session {session_id}")
                except Exception as e:
                    logger.warning(f"Failed to update validation status for cancelled session {session_id}: {e}")
                    # Don't fail the cancellation if this update fails
            else:
                logger.info(f"Cancelled session {session_id} without updating validation status (reprocess flow)")
            
            return {
                "success": True,
                "message": result['message'],
                "session_id": session_id,
                "details": {
                    "task_cancelled": result['task_cancelled'],
                    "contexts_closed": result['contexts_closed'],
                    "session_updated": result['session_updated']
                }
            }
        else:
            logger.error(f"Failed to cancel booking for session {session_id}: {result['message']}")
            raise HTTPException(status_code=500, detail=result['message'])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling booking for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/onesource-results/{request_id}")
async def get_onesource_booking_results(
    request_id: str,
    session_id: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """Get OneSource booking results for a given request ID and optional session ID."""
    try:
        logger.info(f"OneSource results requested for request_id: {request_id}, session_id: {session_id}")
        
        # First check if we have a session_id and if it's still processing
        if session_id:
            try:
                from db.session_manager import SessionManager
                session_manager = SessionManager.get_instance()
                session_status = await session_manager.get_session_status(session_id)
                
                if session_status and session_status.get('status') == 'processing':
                    return {
                        "success": False,
                        "error": f"Session {session_id} is still processing. Please try again in a few moments.",
                        "status": "processing"
                    }
            except Exception as e:
                logger.warning(f"Error checking session status: {e}")
                # Continue with normal flow if session status check fails
                pass
        
        # Try to get results from the database
        try:
            from db.onesource_provider import get_onesource_results
            booking_info, results = await get_onesource_results(request_id, session_id)
            logger.info(f"Retrieved OneSource data: booking_info={booking_info is not None}, results_count={len(results) if results else 0}")
        except ImportError:
            # Fallback if OneSource provider doesn't exist yet
            logger.warning("OneSource provider not found, returning placeholder results")
            booking_info = None
            results = None
        except Exception as e:
            logger.error(f"Error fetching OneSource results: {e}")
            booking_info = None
            results = None
        
        if not booking_info and not results:
            error_msg = f"No OneSource results found for request ID: {request_id}"
            if session_id:
                error_msg += f" and session ID: {session_id}"
            
            logger.warning(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
            
        logger.info(f"Returning OneSource results: {len(results) if results else 0} cabins")
        return {
            "success": True,
            "bookingInfo": booking_info,
            "results": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/studio-results/{request_id}")
async def get_studio_booking_results(
    request_id: str,
    session_id: str = Query(..., description="Session ID for Studio booking"),
    current_user: Dict = Depends(get_current_user)
):
    """Get Studio booking results for a given request ID and optional session ID."""
    try:
        # Session_id is required; check if it's still processing
        try:
            from db.session_manager import SessionManager
            session_manager = SessionManager.get_instance()
            session_status = await session_manager.get_session_status(session_id)
            
            if session_status and session_status.get('status') == 'processing':
                return {
                    "success": False,
                    "error": f"Session {session_id} is still processing. Please try again in a few moments.",
                    "status": "processing"
                }
        except Exception as e:
            logger.warning(f"Error checking session status: {e}")
            # Continue with normal flow if session status check fails
            pass
        # Retrieve results from the database
        booking_info, results = await get_studio_results(request_id, session_id)
        if not booking_info and not results:
            return {
                "success": False,
                "error": f"No Studio results found for request ID: {request_id} and session ID: {session_id}"
            }
        return {
            "success": True,
            "bookingInfo": booking_info,
            "results": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/discount-data/{request_id}")
async def get_discount_data(
    request_id: str,
    session_id: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """Get discount data for Studio booking results by request ID."""
    try:
        # Get studio booking results to extract discount data
        booking_info, results = await get_studio_results(request_id, session_id)
        
        if not booking_info and not results:
            return {
                "success": False,
                "error": f"No Studio booking found for request ID: {request_id}"
            }
        
        # Extract discount data from cabin data with cabin numbers
        cabin_discounts = []
        if results:
            for cabin in results:
                if isinstance(cabin, dict) and 'cabin_number' in cabin:
                    discount_text = cabin.get('discount_text', 'NA')
                    cabin_number = cabin.get('cabin_number')
                    
                    # Only include cabins with discount text
                    if discount_text and discount_text != 'NA':
                        cabin_discounts.append({
                            "cabin_number": cabin_number,
                            "discount_text": discount_text
                        })
        
        return {
            "success": True,
            "request_id": request_id,
            "session_id": session_id,
            "cabin_discounts": cabin_discounts
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve discount data: {str(e)}")

@app.get("/ncl-results/{request_id}")
async def get_ncl_booking_results(
    request_id: str,
    session_id: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """Get NCL booking results for a given request ID and optional session ID."""
    try:
        # First check if we have a session_id and if it's still processing
        if session_id:
            try:
                from db.session_manager import SessionManager
                session_manager = SessionManager.get_instance()
                session_status = await session_manager.get_session_status(session_id)
                
                if session_status and session_status.get('status') == 'processing':
                    return {
                        "success": False,
                        "error": f"Session {session_id} is still processing. Please try again in a few moments.",
                        "status": "processing"
                    }
            except Exception as e:
                logger.warning(f"Error checking session status: {e}")
                # Continue with normal flow if session status check fails
                pass
        
        # Now try to get results from the database
        booking_info, results = await get_ncl_results(request_id, session_id)
        
        if not booking_info and not results:
            error_msg = f"No NCL results found for request ID: {request_id}"
            if session_id:
                error_msg += f" and session ID: {session_id}"
            
            return {
                "success": False,
                "error": error_msg
            }
            
        return {
            "success": True,
            "bookingInfo": booking_info,
            "results": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/cruising-power-results/{request_id}")
async def get_cruising_power_booking_results(
    request_id: str,
    session_id: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """Get Cruising Power booking results for a given request ID and optional session ID."""
    try:
        # First check if we have a session_id and if it's still processing
        if session_id:
            try:
                from db.session_manager import SessionManager
                session_manager = SessionManager.get_instance()
                session_status = await session_manager.get_session_status(session_id)
                
                if session_status and session_status.get('status') == 'processing':
                    return {
                        "success": False,
                        "error": f"Session {session_id} is still processing. Please try again in a few moments.",
                        "status": "processing"
                    }
            except Exception as e:
                logger.warning(f"Error checking session status: {e}")
                # Continue with normal flow if session status check fails
                pass
        
        # Now try to get results from the database
        booking_info, results = await get_cruising_power_results(request_id, session_id)
        
        if not booking_info and not results:
            error_msg = f"No Cruising Power results found for request ID: {request_id}"
            if session_id:
                error_msg += f" and session ID: {session_id}"
            
            return {
                "success": False,
                "error": error_msg
            }
            
        return {
            "success": True,
            "bookingInfo": booking_info,
            "results": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.get("/results-view/{request_id}", response_model=Dict[str, Any])
async def get_results_view(
    request_id: str,
    provider: str = Query(..., description="Provider name ('NCL', 'Cruising Power', 'OneSource', or 'Studio')"),
    session_id: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """
    Consolidated endpoint returning screenshots, videos, booking results and original quote input.
    This is used by the Validation UI 'View Result' modal.
    """
    try:
        # Get quote input (may be None)
        quote_input_record = await retrieve_input_text(request_id)
        
        # Get provider-specific booking results
        provider_key = provider.lower().replace(' ', '_')
        if provider_key == 'ncl':
            booking_info, provider_results = await get_ncl_results(request_id, session_id)
        elif provider_key in {'cruising_power', 'cruisingpower'}:
            booking_info, provider_results = await get_cruising_power_results(request_id, session_id)
        elif provider_key == 'onesource':
            booking_info, provider_results = await get_onesource_results(request_id, session_id)
        elif provider_key.startswith('studio'):
            booking_info, provider_results = await get_studio_results(request_id, session_id)
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported provider: {provider}")
        
        # Normalize provider for DB lookups
        provider_lookup = provider.lower()
        # Screenshots & Videos
        screenshots = await get_screenshots_from_db(request_id, provider_lookup, session_id=session_id)
        if (not screenshots) and session_id:
            screenshots = await get_screenshots_from_db(request_id, None, session_id=session_id)
        videos = await get_videos_by_session_id(session_id) 
        
        return {
            "success": True,
            "provider": provider,
            "request_id": request_id,
            "session_id": session_id,
            "quote_input": quote_input_record,
            "bookingInfo": booking_info,
            "results": provider_results,
            "screenshots": screenshots,
            "videos": videos
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in /results-view: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    

@app.get("/screenshots/{session_id}", response_model=Dict[str, Any])
async def get_screenshots(session_id: str, 
                         limit: int = Query(500, description="Maximum number of screenshots to return"),
                         offset: int = Query(0, description="Number of screenshots to skip")):
    """Get screenshots saved for a given session ID with pagination."""
    try:
        async with db_connection() as conn:
            rows = await conn.fetch(
                """SELECT id, cabin_id, screenshot_type, timestamp, file_name, 
                          minio_url, minio_bucket, minio_object_key, file_size_kb,
                          CASE 
                            WHEN minio_url IS NOT NULL THEN 'minio'
                            WHEN image_data IS NOT NULL THEN 'database'
                            ELSE 'missing'
                          END as storage_type
                   FROM centralized_screenshots 
                   WHERE session_id = $1
                   ORDER BY timestamp DESC
                   LIMIT $2 OFFSET $3""",
                session_id, limit, offset
            )
            screenshots = [dict(row) for row in rows]
            return {"success": True, "session_id": session_id, "screenshots": screenshots, "count": len(screenshots)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.get("/screenshot-image/{screenshot_id}")
async def get_screenshot_image(screenshot_id: int, range: Optional[str] = Header(None)):
    """Get the image data for a screenshot by ID with range request support"""
    try:
        async with db_connection() as conn:
            result = await conn.fetchrow(
                """SELECT minio_bucket, minio_object_key FROM centralized_screenshots WHERE id = $1""",
                screenshot_id
            )

        if not result:
            logger.warning(f"Screenshot not found: {screenshot_id}")
            raise HTTPException(status_code=404, detail="Screenshot not found")

        bucket = result['minio_bucket']
        key = result['minio_object_key']
        if not bucket or not key:
            logger.error(f"Screenshot {screenshot_id} missing MinIO metadata")
            raise HTTPException(status_code=404, detail="Screenshot data not found")

        # Stream from MinIO using async method with range support
        data, metadata = await media_service.download_screenshot(bucket, key, range)
        
        if not data:
            raise HTTPException(status_code=500, detail="Failed to retrieve screenshot data")
        
        # Add cache control to the headers
        metadata['headers']["Cache-Control"] = "public, max-age=3600"
            
        return StreamingResponse(
            io.BytesIO(data),
            status_code=metadata['status_code'],
            media_type=metadata['content_type'],
            headers=metadata['headers']
        )

    except HTTPException:
        raise
    except ConnectionResetError:
        # Client disconnected, which is normal when user navigates away while image is loading
        logger.debug(f"Client disconnected while streaming screenshot {screenshot_id}")
        return Response(status_code=499)  # Client Closed Request
    except Exception as e:
        logger.error(f"Error streaming screenshot data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Public registration endpoint (no authentication required)
@app.post("/public/register", response_model=Dict[str, Any])
async def public_register_user(user_data: UserCreate):
    """Register a new user (public endpoint)."""
    try:
        # Always set role to 'user' and status to 'pending' for public registrations
        result = await create_user(
            username=user_data.username,
            password=user_data.password,
            email=user_data.email,
            full_name=user_data.full_name,
            role="user",  # Default role
            agency="STUDIO",  # Default agency
            portal_access=[],  # No portal access by default
            status="pending"  # Always set to pending
        )
        if result:
            # Here you would normally send an email notification to admins
            # about the new registration request
            return {"success": True, "message": "Registration request submitted successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to create user")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Define a model for user tracking requests
class UserTrackingRequest(BaseModel):
    username: str
    provider: str
    request_id: str
    session_id: str

@app.post('/api/user-tracking/track', response_model=Dict[str, Any])
async def api_track_user_booking(data: UserTrackingRequest, current_user: Dict = Depends(get_current_user)):
    """API endpoint to track which user processed a booking"""
    try:
        # Track the user booking
        success = await track_user_booking(
            username=data.username,
            provider=data.provider,
            request_id=data.request_id,
            session_id=data.session_id
        )
        
        if success:
            return {
                'success': True,
                'message': f'Successfully tracked booking {data.request_id} for user {data.username}'
            }
        else:
            raise HTTPException(
                status_code=500,
                detail='Failed to track user booking'
            )
    except Exception as e:
        logger.error(f"Error in user tracking API: {e}")
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@app.get('/api/user-tracking/bookings', response_model=Dict[str, Any])
async def api_get_user_bookings(
    username: str, 
    provider: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """API endpoint to get bookings processed by a specific user"""
    try:
        # Get user bookings
        bookings = await get_user_bookings(username, provider)
        
        return {
            'success': True,
            'bookings': bookings
        }
    except Exception as e:
        logger.error(f"Error in get user bookings API: {e}")
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@app.get('/api/user-tracking/users', response_model=Dict[str, Any])
async def api_get_booking_users(
    request_id: str, 
    provider: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """API endpoint to get users who processed a specific booking"""
    try:
        # Get booking users
        users = await get_booking_users(request_id, provider)
        
        return {
            'success': True,
            'users': users
        }
    except Exception as e:
        logger.error(f"Error in get booking users API: {e}")
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

# Add a new endpoint for user booking statistics
@app.get('/api/user-tracking/statistics', response_model=Dict[str, Any])
async def get_user_booking_statistics(
    days: Optional[int] = 30,  # Default to last 30 days
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """
    Get statistics about user booking activities
    Only accessible to admins and sub-admins
    """
    try:
        async with db_connection() as conn:
            # Calculate the date for filtering
            date_threshold = (datetime.utcnow() - timedelta(days=days)).isoformat()
            
            # Get booking counts by user and provider
            results = await conn.fetch("""
                SELECT 
                    username, 
                    provider, 
                    COUNT(*) as booking_count
                FROM user_booking_tracking
                WHERE timestamp > $1
                GROUP BY username, provider
                ORDER BY username, provider
            """, date_threshold)
            
            # Get total unique users who processed bookings
            total_users_row = await conn.fetchrow("""
                SELECT COUNT(DISTINCT username) as total_users
                FROM user_booking_tracking
                WHERE timestamp > $1
            """, date_threshold)
            
            total_users = total_users_row['total_users']
            
            # Get total bookings
            total_bookings_row = await conn.fetchrow("""
                SELECT COUNT(*) as total_bookings
                FROM user_booking_tracking
                WHERE timestamp > $1
            """, date_threshold)
            
            total_bookings = total_bookings_row['total_bookings']
            
            # Get top 5 users by booking count
            top_users_rows = await conn.fetch("""
                SELECT 
                    username, 
                    COUNT(*) as booking_count
                FROM user_booking_tracking
                WHERE timestamp > $1
                GROUP BY username
                ORDER BY booking_count DESC
                LIMIT 5
            """, date_threshold)
            
            top_users = [dict(row) for row in top_users_rows]
            
            # Format the user-provider counts
            user_provider_counts = []
            for row in results:
                user_provider_counts.append({
                    'username': row['username'],
                    'provider': row['provider'],
                    'booking_count': row['booking_count']
                })
        return {
            'success': True,
            'statistics': {
                'total_users': total_users,
                'total_bookings': total_bookings,
                'top_users': top_users,
                'user_provider_counts': user_provider_counts,
                'period_days': days
            }
        }
    except Exception as e:
        logger.error(f"Error getting user booking statistics: {e}")
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

# Endpoint to submit validation data for a booking
class IssueCodeEntry(BaseModel):
    category: str
    code: str

class ValidationRequest(BaseModel):
    session_id: str
    val_status: str  # Changed from bool to str to support 'validated', 'reprocessed', 'reset'
    val_mark: str
    as_per_feature: str
    val_comment: Optional[str] = None
    issue_codes: List[IssueCodeEntry] = []

@app.put('/api/user-tracking/validate', response_model=Dict[str, Any])
async def api_validate_user_booking(
    data: ValidationRequest,
    current_user: Dict = Depends(get_current_user)
):
    """API endpoint to save validation data for a booking"""
    try:
        # Validate val_status values
        ALLOWED_VAL_STATUS = {'validated', 'reprocessed', 'reset'}
        if data.val_status not in ALLOWED_VAL_STATUS:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid val_status. Must be one of: {', '.join(ALLOWED_VAL_STATUS)}"
            )
        
        async with db_connection() as conn:
            await conn.execute("""
                UPDATE user_booking_tracking
                SET val_status = $1, val_mark = $2, as_per_feature = $3, val_comment = $4, issue_codes = $5::jsonb, timestamp = $6
                WHERE session_id = $7
            """,
                str(data.val_status).lower(),
                data.val_mark,
                data.as_per_feature,
                data.val_comment,
                json.dumps([e.dict() for e in data.issue_codes]),
                datetime.utcnow().isoformat(),
                data.session_id
            )

        # Invalidate tracking cache after validation update
        await invalidate_tracking_cache_after_data_change("validation update", current_user["username"], f"(session: {data.session_id})")

        return {'success': True, 'message': 'Validation submitted successfully'}
    except Exception as e:
        logger.error(f"Error submitting validation data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Input Text Storage Endpoints
@app.get("/input-text/{request_id}", response_model=Dict[str, Any])
async def get_input_text(
    request_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Retrieve stored input text by request ID."""
    try:
        text_data = await retrieve_input_text(request_id)
        if not text_data:
            raise HTTPException(status_code=404, detail="Input text not found")
        
        # Remove sensitive info and return only necessary data
        return {
            "success": True,
            "data": {
                "request_id": text_data["request_id"],
                "text_input": text_data["text_input"],
                "url_input": text_data.get("url_input", ""),
                "created_at": text_data["created_at"]
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving input text: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/input-text", response_model=Dict[str, Any])
async def list_input_texts(
    limit: int = 5000,
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """List all stored input texts (admin only)."""
    try:
        stored_inputs = await list_stored_inputs(limit)
        return {
            "success": True,
            "data": stored_inputs,
            "count": len(stored_inputs)
        }
    except Exception as e:
        logger.error(f"Error listing stored inputs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/input-text/{request_id}", response_model=Dict[str, Any])
async def delete_input_text(
    request_id: str,
    current_user: Dict = Depends(get_current_admin)
):
    """Delete stored input text by request ID (admin only)."""
    try:
        success = await delete_stored_input_text(request_id)
        if not success:
            raise HTTPException(status_code=404, detail="Input text not found")
        
        return {
            "success": True,
            "message": "Input text deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting input text: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get('/api/user-tracking/data', response_model=Dict[str, Any])
async def get_user_tracking_data(
    provider: Optional[str] = None,
    username: Optional[str] = None,
    limit: int = Query(100000, description="Maximum number of records to return"),

    offset: int = Query(0, description="Number of records to skip"),
    current_user: Dict = Depends(get_current_user)
):
    """Get user tracking data with Redis caching and optimized N+1 query resolution"""
    try:
        # Import here to avoid circular imports and handle Redis unavailability gracefully
        try:
            from services.user_tracking_cache_service import UserTrackingCacheService
            redis_available = True
        except ImportError:
            logger.warning("Redis service not available, continuing without caching")
            redis_available = False
        
        start_time = datetime.now()
        
        # STEP 1: Try to get from Redis cache first (if available)
        cached_data = None
        if redis_available:
            cached_data = await UserTrackingCacheService.get_cached_tracking_data(
                provider=provider, username=username, limit=limit, offset=offset
            )
            
            if cached_data:
                # Cache hit - return immediately
                cache_time = (datetime.now() - start_time).total_seconds() * 1000
                logger.info(f"🚀 REDIS CACHE HIT: Served in {cache_time:.1f}ms (vs ~300ms from database)")
                
                # Add detailed timing to response
                cached_data["performance_metrics"] = {
                    "source": "redis_cache",
                    "response_time_ms": round(cache_time, 1),
                    "database_queries": 0,
                    "cache_efficiency": "EXCELLENT"
                }
                return cached_data
        
        # STEP 2: Cache miss - fetch from database with optimized queries
        logger.info(f"🔄 Cache miss - fetching user tracking data from database")
        db_start_time = datetime.now()
        
        async with db_connection() as conn:
            # Base query (same as before)
            query = """
                SELECT 
                    t.session_id, t.username, t.provider, t.request_id, t.timestamp, 
                    t.val_status, t.val_mark, t.as_per_feature, t.val_comment,
                    t.issue_codes,
                    COUNT(s.id) as screenshot_count
                FROM user_booking_tracking t
                LEFT JOIN centralized_screenshots s ON t.session_id = s.session_id
            """
            
            conditions = []
            params = []
            param_index = 1
            
            if provider:
                conditions.append(f"t.provider = ${param_index}")
                params.append(provider.lower())
                param_index += 1
                
            if username:
                conditions.append(f"t.username = ${param_index}")
                params.append(username)
                param_index += 1
                
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
                
            query += """
                GROUP BY t.session_id, t.username, t.provider, t.request_id, t.timestamp, t.val_status, t.val_mark, t.as_per_feature, t.val_comment, t.issue_codes
                ORDER BY t.timestamp DESC
                LIMIT $%d OFFSET $%d
            """ % (param_index, param_index + 1)
            
            params.extend([limit, offset])
            results = await conn.fetch(query, *params)
            
            # STEP 3: Optimize N+1 problem - group by provider and fetch booking data in batches
            provider_sessions = {}
            tracking_data = []
            
            # Group sessions by provider
            for row in results:
                data = dict(row)
                provider_name = data['provider']
                session_id = data['session_id']
                
                if provider_name not in provider_sessions:
                    provider_sessions[provider_name] = []
                provider_sessions[provider_name].append(session_id)
                tracking_data.append(data)
            
            # STEP 4: Fetch booking data for each provider in single queries (not N+1!)
            for provider_name, session_ids in provider_sessions.items():
                if session_ids:
                    # Use cache service to fetch/cache provider booking data
                    booking_data = await UserTrackingCacheService.fetch_and_cache_provider_bookings(
                        provider_name, session_ids
                    )
                    
                    # Apply booking data to tracking records
                    for data in tracking_data:
                        if data['provider'] == provider_name and data['session_id'] in booking_data:
                            booking_info = booking_data[data['session_id']]
                            if booking_info:
                                data.update(booking_info)
            
            db_time = (datetime.now() - db_start_time).total_seconds() * 1000
            total_time = (datetime.now() - start_time).total_seconds() * 1000
            
            result = {
                'success': True,
                'data': tracking_data,
                'count': len(tracking_data),
                'from_cache': False,
                'cache_hit': False,
                'db_query_time_ms': round(db_time, 1),
                'total_time_ms': round(total_time, 1),
                'providers_queried': len(provider_sessions),
                'optimization_note': f"Reduced from {len(tracking_data)} queries to {len(provider_sessions) + 1} queries"
            }
            
            # STEP 5: Cache the result for future requests (if Redis is available)
            if redis_available:
                await UserTrackingCacheService.cache_tracking_data(
                    result, provider=provider, username=username, limit=limit, offset=offset
                )
            
            # Enhanced logging with detailed breakdown
            logger.info(f"💾 DATABASE + CACHE: Total {total_time:.1f}ms (DB: {db_time:.1f}ms, Cache: {total_time-db_time:.1f}ms)")
            logger.info(f"🔧 OPTIMIZATION: Reduced from ~{len(tracking_data)}+ queries to {len(provider_sessions) + 1} queries")
            
            # Add detailed performance metrics to response
            result["performance_metrics"] = {
                "source": "database_with_caching",
                "total_response_time_ms": round(total_time, 1),
                "database_time_ms": round(db_time, 1),
                "redis_cache_time_ms": round(total_time - db_time, 1),
                "database_queries": len(provider_sessions) + 1,
                "query_optimization": f"Reduced from {len(tracking_data)}+ to {len(provider_sessions) + 1} queries",
                "cache_efficiency": "FIRST_LOAD"
            }
            
            return result
            
    except Exception as e:
        logger.error(f"Error getting user tracking data: {e}")
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@app.get("/videos/{session_id}")
async def get_videos(session_id: str, 
                    limit: int = Query(50, description="Maximum number of videos to return"),
                    offset: int = Query(0, description="Number of videos to skip"),
                    current_user: Dict = Depends(get_current_user)):
    """Get videos saved for a given session ID with pagination."""
    try:
        async with db_connection() as conn:
            rows = await conn.fetch(
                """SELECT id, cabin_id, video_type, timestamp, file_name, duration, format, 
                          file_size_kb, minio_url, minio_bucket, minio_object_key,
                          CASE 
                            WHEN minio_url IS NOT NULL THEN 'minio'
                            WHEN video_data IS NOT NULL THEN 'database'
                            ELSE 'missing'
                          END as storage_type
                   FROM centralized_videos 
                   WHERE session_id = $1
                   ORDER BY timestamp DESC
                   LIMIT $2 OFFSET $3""",
                session_id, limit, offset
            )
            videos = [dict(row) for row in rows]
            return {"success": True, "session_id": session_id, "videos": videos, "count": len(videos)}
    except Exception as e:
        logger.error(f"Error getting videos: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.get("/video/{video_id}")
async def get_video(video_id: int, range: Optional[str] = Header(None)):
    """Get the video data for a specific video with range request support (streamed from MinIO)."""
    try:
        async with db_connection() as conn:
            result = await conn.fetchrow(
                """SELECT minio_bucket, minio_object_key, format, file_name FROM centralized_videos WHERE id = $1""",
                video_id
            )

        if not result:
            logger.warning(f"Video not found: {video_id}")
            raise HTTPException(status_code=404, detail="Video not found")

        bucket = result['minio_bucket']
        key = result['minio_object_key']
        if not bucket or not key:
            logger.error(f"Video {video_id} missing MinIO metadata")
            raise HTTPException(status_code=404, detail="Video data not found")

        # Stream from MinIO using async generator with range support
        stream_generator, metadata = await media_service.stream_video(bucket, key, range)
        
        if 'error' in metadata:
            raise HTTPException(status_code=500, detail="Failed to retrieve video data")
        
        # Add additional headers
        metadata['headers']["Content-Disposition"] = f"inline; filename=\"{result['file_name']}\""
        metadata['headers']["Cache-Control"] = "public, max-age=3600"
        
        # Ensure media type is correct based on the video format
        media_type = "video/webm" if result['format'] != 'mp4' else "video/mp4"
        
        return StreamingResponse(
            stream_generator, 
            status_code=metadata['status_code'],
            media_type=media_type,
            headers=metadata['headers']
        )

    except HTTPException:
        raise
    except ConnectionResetError:
        # Client disconnected, which is normal when user closes video or navigates away
        logger.debug(f"Client disconnected while streaming video {video_id}")
        return Response(status_code=499)  # Client Closed Request
    except Exception as e:
        logger.error(f"Error streaming video data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/video/{video_id}")
async def delete_video(
    video_id: int,
    current_user: Dict = Depends(get_current_admin)
):
    """Delete a video from the database and MinIO (admin only)."""
    try:
        async with db_connection() as conn:
            # Get video info before deletion for MinIO cleanup
            video_info = await conn.fetchrow(
                "SELECT minio_bucket, minio_object_key FROM centralized_videos WHERE id = $1",
                video_id
            )
            
            if not video_info:
                raise HTTPException(status_code=404, detail="Video not found")
            
            # Delete from database
            await conn.execute(
                "DELETE FROM centralized_videos WHERE id = $1",
                video_id
            )
        
        # Delete from MinIO if it exists there
        if video_info['minio_bucket'] and video_info['minio_object_key']:
            try:
                await media_service.delete_video(video_info['minio_bucket'], video_info['minio_object_key'])
            except Exception as minio_error:
                logger.warning(f"Failed to delete video from MinIO: {minio_error}")
                # Continue anyway since database deletion succeeded
        
        return {"success": True, "message": "Video deleted successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting video: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/videos/cabin/{cabin_id}")
async def get_videos_by_cabin(cabin_id: int, provider: Optional[str] = None, session_id: Optional[str] = None, current_user: Dict = Depends(get_current_user)):
    """Get all videos saved for a specific cabin ID."""
    try:
        from db.video_manager import get_videos_by_cabin_id
        
        # Retrieve videos from database
        videos = await get_videos_by_cabin_id(cabin_id, provider, session_id)
        
        if not videos:
            raise HTTPException(
                status_code=404, 
                detail=f"No videos found for cabin ID: {cabin_id}"
            )
            
        return {"success": True, "cabin_id": cabin_id, "videos": videos}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting videos by cabin ID: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Add a new public endpoint for videos
@app.get("/public/video/{video_id}")
async def get_public_video(video_id: int, range: Optional[str] = Header(None)):
    """Get the video data for a specific video without authentication with range request support (streamed from MinIO)."""
    try:
        async with db_connection() as conn:
            result = await conn.fetchrow(
                """SELECT minio_bucket, minio_object_key, format, file_name FROM centralized_videos WHERE id = $1""",
                video_id
            )

        if not result:
            logger.warning(f"Public video not found: {video_id}")
            raise HTTPException(status_code=404, detail="Video not found")

        bucket = result['minio_bucket']
        key = result['minio_object_key']
        if not bucket or not key:
            logger.error(f"Public video {video_id} missing MinIO metadata")
            raise HTTPException(status_code=404, detail="Video data not found")

        # Stream from MinIO using async generator with range support
        stream_generator, metadata = await media_service.stream_video(bucket, key, range)
        
        if 'error' in metadata:
            raise HTTPException(status_code=500, detail="Failed to retrieve video data")
        
        # Add additional headers
        metadata['headers']["Content-Disposition"] = f"inline; filename=\"{result['file_name']}\""
        metadata['headers']["Cache-Control"] = "public, max-age=3600"
        metadata['headers']["Access-Control-Allow-Origin"] = "*"
        
        # Ensure media type is correct based on the video format
        media_type = "video/webm" if result['format'] != 'mp4' else "video/mp4"
        
        return StreamingResponse(
            stream_generator, 
            status_code=metadata['status_code'],
            media_type=media_type,
            headers=metadata['headers']
        )

    except HTTPException:
        raise
    except ConnectionResetError:
        # Client disconnected, which is normal when user closes video or navigates away
        logger.debug(f"Client disconnected while streaming public video {video_id}")
        return Response(status_code=499)  # Client Closed Request
    except Exception as e:
        logger.error(f"Error streaming public video data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Add monitoring endpoint for admins
@app.get("/admin/db-stats", response_model=Dict[str, Any])
async def get_database_statistics(current_user: Dict = Depends(get_current_admin)):
    """Get database connection usage statistics (admin only)."""
    try:
        stats = db_monitor.get_stats()
        
        # Add current pool status
        from db_service import _pool
        pool_info = {
            'min_connections': _pool.minconn,
            'max_connections': _pool.maxconn,
            'current_connections': len(_pool._pool),
            'available_connections': len([c for c in _pool._pool if not c.closed])
        }
        
        return {
            'success': True,
            'connection_stats': stats,
            'pool_info': pool_info
        }
    except Exception as e:
        logger.error(f"Error getting database statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/users/{user_id}/suspend", response_model=Dict[str, Any])
async def suspend_user(
    user_id: str,
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """Suspend a user account (admin only)."""
    try:
        user = await get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Prevent suspending yourself
        if user_id == current_user["user_id"]:
            raise HTTPException(status_code=400, detail="Cannot suspend your own account")
        
        # Prevent suspending any admin user
        if user.get("role") == "admin":
            raise HTTPException(status_code=400, detail="Admin accounts cannot be suspended")
            
        result = await update_user(user_id, {"status": "suspended"})
        if result:
            return {"success": True, "message": "User suspended successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to suspend user")
    except Exception as e:
        logger.error(f"Error suspending user: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/users/{user_id}/unsuspend", response_model=Dict[str, Any])
async def unsuspend_user(
    user_id: str,
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """Re-activate a suspended user account (admin only)."""
    try:
        user = await get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        result = await update_user(user_id, {"status": "approved"})
        if result:
            return {"success": True, "message": "User unsuspended successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to unsuspend user")
    except Exception as e:
        logger.error(f"Error unsuspending user: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get('/api/providers')
async def get_providers(current_user: Dict = Depends(get_current_user)):
    """
    Get providers that the current user has access to.
    This endpoint filters providers based on user's provider_access permissions.
    
    Returns:
        List of providers the user is authorized to access
    """
    try:
        user_id = current_user.get('user_id')
        user_role = current_user.get('role')
        
        logger.info(f"Fetching providers for user {user_id} with role {user_role}")
        
        # Get providers based on user's access permissions
        providers = await get_user_accessible_providers(user_id)
        
        logger.info(f"Returning {len(providers)} providers for user {user_id}")
        
        return {
            "success": True,
            "providers": providers,
            "user_info": {
                "user_id": user_id,
                "role": user_role,
                "provider_count": len(providers)
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching providers for user {current_user.get('user_id', 'unknown')}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "success": False,
            "message": "Failed to fetch providers",
            "error": str(e)
        }

@app.get('/api/providers/all')
async def get_all_providers(current_user: Dict = Depends(get_current_admin_or_subadmin)):
    """
    Get all providers from the database (admin/sub_admin only).
    This endpoint returns all providers regardless of user access permissions.
    Used for admin management interfaces.
    """
    try:
        providers = await get_provider_list()
        
        return {
            "success": True,
            "providers": providers,
            "total_count": len(providers)
        }
        
    except Exception as e:
        logger.error(f"Error fetching all providers: {e}")
        return {
            "success": False,
            "message": "Failed to fetch providers"
        }

@app.get('/api/agencies')
async def get_agencies():
    """
    Get all agencies from the database
    Returns a list of all available agencies
    """
    try:
        agencies = await get_agency_list()
        
        return {
            "success": True,
            "agencies": agencies
        }
        
    except Exception as e:
        logger.error(f"Error fetching agencies: {e}")
        return {
            "success": False,
            "message": "Failed to fetch agencies"
        }

@app.get('/api/templates')
async def get_templates(
    provider: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """
    Get templates from the database, optionally filtered by provider.
    
    Args:
        provider: Optional provider filter (e.g., 'Studio', 'NCL', 'OneSource', 'cruisingpower')
        
    Returns:
        List of templates for the specified provider or all templates if no provider specified
    """
    try:
        from db.template_manager import get_templates_by_provider, get_all_templates
        
        if provider:
            # Map provider names to database provider names
            provider_mapping = {
                'Studio.Sales.CabinCloseOut': 'Studio',
                'Studio.Reservation.CabinCloseOut': 'Studio',
                'Studio': 'Studio',
                'NCL': 'NCL',
                'OneSource': 'OneSource',
                'Cruising Power': 'cruisingpower'
            }
            
            db_provider = provider_mapping.get(provider, provider)
            
            logger.info(f"Fetching templates for provider: {provider} (mapped to: {db_provider})")
            
            # Fetch templates for specific provider
            templates = await get_templates_by_provider(db_provider)
        else:
            # Fetch all templates
            logger.info("Fetching all templates")
            templates = await get_all_templates()
        
        logger.info(f"Returning {len(templates)} templates")
        
        return {
            "success": True,
            "data": templates,
            "count": len(templates),
            "provider_filter": provider
        }
        
    except Exception as e:
        logger.error(f"Error fetching templates: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "success": False,
            "message": "Failed to fetch templates",
            "error": str(e)
        }

@app.get('/api/test-db')
async def test_database_connection():
    """
    Test database connection and providers table
    """
    try:
        from db_service import get_db_connection, init_db_pool, db_connection
        
        # Make sure the pool is initialized
        await init_db_pool()
        async with db_connection() as conn:
        
            # Test basic connection
            result = await conn.fetchval("SELECT 1")
            logger.info(f"Database connection test: {result}")

            # Test providers table exists
            table_exists = await conn.fetchrow("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name = 'providers'
            """)
            logger.info(f"Providers table exists: {table_exists}")

            # Test providers table structure
            columns = await conn.fetch("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'providers'
            ORDER BY ordinal_position
            """)
            logger.info(f"Providers table columns: {columns}")

            # Test select from providers
            count = await conn.fetchval("SELECT COUNT(*) FROM providers")
            logger.info(f"Current providers count: {count}")

            
            return {
                "success": True,
                "connection": "OK",
                "table_exists": bool(table_exists),
                "columns": [{"name": col[0], "type": col[1]} for col in columns] if columns else [],
                "providers_count": count
            }

    except Exception as e:
        logger.error(f"Database test error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "success": False,
            "error": str(e)
        }

@app.get('/api/test-provider-functions')
async def test_provider_functions():
    """
    Test endpoint to verify provider functions are imported correctly
    """
    try:
        # Test that functions are available
        import inspect
        
        # Check if update_provider function exists and get its signature
        update_func_exists = 'update_provider' in globals()
        delete_func_exists = 'delete_provider' in globals()
        
        # Get function signatures if they exist
        update_sig = str(inspect.signature(update_provider)) if update_func_exists else "Not found"
        delete_sig = str(inspect.signature(delete_provider)) if delete_func_exists else "Not found"
        
        return {
            "success": True,
            "functions": {
                "update_provider": {
                    "exists": update_func_exists,
                    "signature": update_sig
                },
                "delete_provider": {
                    "exists": delete_func_exists,
                    "signature": delete_sig
                }
            },
            "globals_keys": [k for k in globals().keys() if 'provider' in k.lower()]
        }
        
    except Exception as e:
        logger.error(f"Test provider functions error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "success": False,
            "error": str(e)
        }

@app.put('/api/providers/{provider_id}/status')
async def update_provider_status_endpoint(
    provider_id: int,
    status_data: Dict[str, str],
    current_user: Dict = Depends(get_current_admin_or_subadmin)
):
    """
    Admin/sub_admin only endpoint to update a provider's status.
    """
    try:
        logger.info(f"Updating provider {provider_id} status. Request data: {status_data}")
        
        status = status_data.get('status')
        if not status:
            logger.error("Missing status field in request")
            raise HTTPException(status_code=400, detail="Missing status field")
        
        # Validate status value
        valid_statuses = ['active', 'coming_soon', 'maintenance']
        if status not in valid_statuses:
            logger.error(f"Invalid status value: {status}")
            raise HTTPException(status_code=400, detail=f"Invalid status. Must be one of: {valid_statuses}")
        
        logger.info(f"Calling update_provider_status({provider_id}, {status})")
        success = await update_provider_status(provider_id, status)
        
        if success:
            logger.info(f"Successfully updated provider {provider_id} status to {status}")
            return {"success": True, "message": f"Provider status updated to {status}"}
        else:
            logger.error(f"Failed to update provider {provider_id} status")
            raise HTTPException(status_code=404, detail="Provider not found or update failed")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating provider {provider_id} status: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# ------------------------------------------------------------------
# Redis Cache Management and Monitoring Endpoints
# ------------------------------------------------------------------

@app.get("/health/redis")
async def redis_health_check():
    """Check Redis connectivity and status"""
    try:
        from services.redis_service import redis_service
        health_info = await redis_service.health_check()
        return health_info
    except ImportError as e:
        return {
            "status": "unavailable",
            "error": "Redis library not installed",
            "service": "redis",
            "details": str(e)
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "service": "redis"
        }

@app.get("/admin/redis-stats")
async def get_redis_statistics(current_user: Dict = Depends(get_current_admin)):
    """Get Redis cache statistics and performance metrics"""
    try:
        from services.redis_service import redis_service
        from services.user_tracking_cache_service import UserTrackingCacheService
        
        # Get general Redis stats
        redis = await redis_service.get_redis()
        info = await redis.info()
        
        # Get user tracking cache statistics
        tracking_stats = await UserTrackingCacheService.get_cache_statistics()
        
        return {
            "redis_server": {
                "status": "connected",
                "memory_usage": info.get("used_memory_human"),
                "memory_peak": info.get("used_memory_peak_human"),
                "connected_clients": info.get("connected_clients"),
                "total_commands": info.get("total_commands_processed"),
                "uptime_seconds": info.get("uptime_in_seconds"),
                "version": info.get("redis_version")
            },
            "user_tracking_cache": tracking_stats,
            "keyspace": info.get("db0", {}),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting Redis statistics: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.post("/admin/cache/invalidate")
async def invalidate_cache(
    cache_type: str = Query(..., description="Cache type to invalidate"),
    provider: Optional[str] = Query(None, description="Provider filter"),
    username: Optional[str] = Query(None, description="Username filter"),
    current_user: Dict = Depends(get_current_admin)
):
    """Manually invalidate cache entries"""
    try:
        from services.user_tracking_cache_service import UserTrackingCacheService
        
        if cache_type == "user_tracking":
            await UserTrackingCacheService.invalidate_tracking_cache(provider, username)
            message = f"Invalidated user tracking cache"
            if provider:
                message += f" for provider: {provider}"
            if username:
                message += f" for username: {username}"
                
            logger.info(f"Admin {current_user.get('username')} {message}")
            
            return {
                "success": True,
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(400, f"Unknown cache type: {cache_type}")
            
    except Exception as e:
        logger.error(f"Error invalidating cache: {e}")
        raise HTTPException(500, f"Failed to invalidate cache: {str(e)}")

@app.get("/admin/cache/performance")
async def get_cache_performance_metrics(current_user: Dict = Depends(get_current_admin)):
    """Get cache performance metrics and hit/miss ratios"""
    try:
        from services.redis_service import redis_service
        
        redis = await redis_service.get_redis()
        info = await redis.info()
        
        # Calculate hit ratio
        keyspace_hits = info.get("keyspace_hits", 0)
        keyspace_misses = info.get("keyspace_misses", 0)
        total_requests = keyspace_hits + keyspace_misses
        
        hit_ratio = (keyspace_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "cache_performance": {
                "hit_ratio_percent": round(hit_ratio, 2),
                "total_hits": keyspace_hits,
                "total_misses": keyspace_misses,
                "total_requests": total_requests
            },
            "memory_efficiency": {
                "used_memory": info.get("used_memory"),
                "used_memory_human": info.get("used_memory_human"),
                "used_memory_peak": info.get("used_memory_peak"),
                "used_memory_peak_human": info.get("used_memory_peak_human"),
                "fragmentation_ratio": info.get("mem_fragmentation_ratio")
            },
            "operations": {
                "total_commands": info.get("total_commands_processed"),
                "instantaneous_ops_per_sec": info.get("instantaneous_ops_per_sec"),
                "expired_keys": info.get("expired_keys"),
                "evicted_keys": info.get("evicted_keys")
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting cache performance metrics: {e}")
        raise HTTPException(500, f"Failed to get performance metrics: {str(e)}")

@app.get("/admin/performance/comparison")
async def get_performance_comparison(current_user: Dict = Depends(get_current_admin)):
    """Get real-time performance comparison between Redis cache and database"""
    try:
        from services.user_tracking_cache_service import UserTrackingCacheService
        from services.redis_service import redis_service
        import time
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "test_parameters": {
                "data_size": "100 tracking records",
                "test_type": "user_tracking_data",
                "measurement_unit": "milliseconds"
            }
        }
        
        # Test 1: Force database query (simulate cache miss)
        start_time = time.time()
        async with db_connection() as conn:
            query = """
                SELECT COUNT(*) as total_records
                FROM user_booking_tracking t
                LEFT JOIN centralized_screenshots s ON t.session_id = s.session_id
                GROUP BY t.session_id
                LIMIT 10000
            """
            await conn.fetch(query)
        db_time = (time.time() - start_time) * 1000
        
        # Test 2: Redis cache performance
        start_time = time.time()
        try:
            redis = await redis_service.get_redis()
            await redis.ping()
            await redis.get("test_performance_key")
            cache_time = (time.time() - start_time) * 1000
        except:
            cache_time = 0
        
        # Calculate performance metrics
        speed_improvement = (db_time / cache_time) if cache_time > 0 else 0
        time_saved = db_time - cache_time
        
        results.update({
            "database_query": {
                "response_time_ms": round(db_time, 1),
                "description": "Direct database query (simulated cache miss)",
                "queries_executed": "Multiple (N+1 problem without optimization)"
            },
            "redis_cache": {
                "response_time_ms": round(cache_time, 1),
                "description": "Redis cache lookup",
                "queries_executed": "0 (pure cache hit)"
            },
            "performance_comparison": {
                "speed_improvement_factor": round(speed_improvement, 1),
                "time_saved_ms": round(time_saved, 1),
                "efficiency_percentage": round(((time_saved / db_time) * 100), 1) if db_time > 0 else 0,
                "verdict": "EXCELLENT" if speed_improvement > 10 else "GOOD" if speed_improvement > 5 else "MODERATE"
            },
            "cache_statistics": await UserTrackingCacheService.get_cache_statistics()
        })
        
        return results
        
    except Exception as e:
        logger.error(f"Error getting performance comparison: {e}")
        raise HTTPException(500, f"Failed to get performance comparison: {str(e)}")

# ------------------------------------------------------------------

# Run the FastAPI app
if __name__ == "__main__":
    uvicorn.run("api:app", host="0.0.0.0", port=8000, reload=False) 
