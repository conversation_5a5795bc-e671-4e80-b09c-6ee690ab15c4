"""
Template management module for handling promotion templates in the database
"""

import logging
from typing import List, Dict, Optional
from db_service import db_connection

logger = logging.getLogger("database.template_manager")

async def get_templates_by_provider(provider: str) -> List[Dict]:
    """
    Get all templates for a specific provider
    
    Args:
        provider: Provider name (e.g., 'Studio', 'NCL', 'OneSource', 'cruisingpower')
        
    Returns:
        List of template dictionaries
    """
    async with db_connection() as conn:
        try:
            rows = await conn.fetch("""
                SELECT id, provider, template_id, template_content, created_at, updated_at
                FROM templates 
                WHERE provider = $1
                ORDER BY template_id
            """, provider)
            
            templates = []
            for row in rows:
                templates.append({
                    "id": row["id"],
                    "provider": row["provider"],
                    "template_id": row["template_id"],
                    "content": row["template_content"],
                    "created_at": row["created_at"],
                    "updated_at": row["updated_at"]
                })
            
            return templates
            
        except Exception as e:
            logger.error(f"Error fetching templates for provider {provider}: {e}")
            raise

async def get_all_templates() -> List[Dict]:
    """
    Get all templates from the database
    
    Returns:
        List of all template dictionaries
    """
    async with db_connection() as conn:
        try:
            rows = await conn.fetch("""
                SELECT id, provider, template_id, template_content, created_at, updated_at
                FROM templates 
                ORDER BY provider, template_id
            """)
            
            templates = []
            for row in rows:
                templates.append({
                    "id": row["id"],
                    "provider": row["provider"],
                    "template_id": row["template_id"],
                    "content": row["template_content"],
                    "created_at": row["created_at"],
                    "updated_at": row["updated_at"]
                })
            
            return templates
            
        except Exception as e:
            logger.error(f"Error fetching all templates: {e}")
            raise

async def add_template(provider: str, template_id: str, template_content: str) -> bool:
    """
    Add a new template to the database
    
    Args:
        provider: Provider name
        template_id: Template ID
        template_content: Template content
        
    Returns:
        True if successful, False otherwise
    """
    async with db_connection() as conn:
        try:
            await conn.execute("""
                INSERT INTO templates (provider, template_id, template_content, created_at, updated_at)
                VALUES ($1, $2, $3, CURRENT_TIMESTAMP::TEXT, CURRENT_TIMESTAMP::TEXT)
            """, provider, template_id, template_content)
            
            logger.info(f"Added template {template_id} for provider {provider}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding template {template_id} for provider {provider}: {e}")
            return False

async def update_template(template_id: int, provider: str, new_template_id: str, template_content: str) -> bool:
    """
    Update an existing template
    
    Args:
        template_id: Database ID of the template
        provider: Provider name
        new_template_id: New template ID
        template_content: New template content
        
    Returns:
        True if successful, False otherwise
    """
    async with db_connection() as conn:
        try:
            await conn.execute("""
                UPDATE templates 
                SET provider = $2, template_id = $3, template_content = $4, updated_at = CURRENT_TIMESTAMP::TEXT
                WHERE id = $1
            """, template_id, provider, new_template_id, template_content)
            
            logger.info(f"Updated template ID {template_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating template ID {template_id}: {e}")
            return False

async def delete_template(template_id: int) -> bool:
    """
    Delete a template from the database
    
    Args:
        template_id: Database ID of the template to delete
        
    Returns:
        True if successful, False otherwise
    """
    async with db_connection() as conn:
        try:
            await conn.execute("DELETE FROM templates WHERE id = $1", template_id)
            logger.info(f"Deleted template ID {template_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting template ID {template_id}: {e}")
            return False

async def get_template_by_id(template_id: int) -> Optional[Dict]:
    """
    Get a specific template by its database ID
    
    Args:
        template_id: Database ID of the template
        
    Returns:
        Template dictionary or None if not found
    """
    async with db_connection() as conn:
        try:
            row = await conn.fetchrow("""
                SELECT id, provider, template_id, template_content, created_at, updated_at
                FROM templates 
                WHERE id = $1
            """, template_id)
            
            if row:
                return {
                    "id": row["id"],
                    "provider": row["provider"],
                    "template_id": row["template_id"],
                    "content": row["template_content"],
                    "created_at": row["created_at"],
                    "updated_at": row["updated_at"]
                }
            return None
            
        except Exception as e:
            logger.error(f"Error fetching template ID {template_id}: {e}")
            return None 