# OceanMind

> **Human Augmented Intelligence for Cruise Booking Automation**

---

<!-- Badges (add CI, coverage, DockerHub, etc. as available) -->
![Python](https://img.shields.io/badge/python-3.9%2B-blue)
![FastAPI](https://img.shields.io/badge/api-FastAPI-green)
![Next.js](https://img.shields.io/badge/frontend-Next.js-blue)
![PostgreSQL](https://img.shields.io/badge/database-PostgreSQL-blue)
![MinIO](https://img.shields.io/badge/storage-MinIO-orange)

---

## Overview

**OceanMind** is a production-grade platform for automating cruise bookings across multiple providers, combining browser automation, AI-powered extraction, and robust media management. Designed for travel agencies, operators, and developers, it streamlines the entire booking workflow—from quote extraction to cabin selection, booking, and analytics—while capturing screenshots and videos for compliance and review.

- **Audience:** Travel agencies, automation engineers, data teams, and contributors.
- **Why OceanMind?**  
  - Unified automation for multiple cruise providers (Studio, NCL, Cruising Power)
  - Human-in-the-loop and AI-augmented extraction
  - End-to-end traceability with media capture
  - Modern, extensible Python/TypeScript stack

---

## Features

- **Studio Booking Automation:** End-to-end automation for Studio providers, including quote processing, cabin search, and parallel booking.
- **NCL & Cruising Power Integration:** Automated workflows for Norwegian Cruise Line and Royal Caribbean (Cruising Power) platforms.
- **Extraction Service:** Parses raw cruise details from text or URLs and structures them for booking.
- **Media Management:** Automated screenshot and video capture, storage in MinIO, and retrieval via API.
- **User & Session Management:** Role-based authentication, session tracking, and user activity monitoring.
- **Admin Analytics:** Booking metrics, user tracking, and system health dashboards.
- **Frontend Dashboard:** Next.js/React UI for booking, analytics, and admin operations.

---

## Architecture

```mermaid
flowchart TD
    A["User / Admin (Web UI)"]
    B["Next.js Frontend"]
    C["FastAPI Backend (api.py)"]
    D["Extraction & Booking Services\n(services/)"]
    E["Scraper Modules\n(Studio, NCL, Cruising Power)"]
    F["PostgreSQL Database"]
    G["MinIO Object Storage"]
    H["Google Generative AI"]

    A -->|"Browser"| B
    B -->|"REST API"| C
    C -->|"Async Tasks"| D
    D -->|"Orchestrates"| E
    D -->|"Stores metadata"| F
    D -->|"Uploads media"| G
    D -->|"LLM Extraction"| H
    E -->|"Booking, scraping, media capture"| D
    G -->|"Screenshots, Videos"| B
    F -->|"Booking, user, session data"| C
    H -->|"Extraction results"| D
    B -->|"Analytics, Admin"| C
    C -->|"User/session management"| F
    C -->|"Media links"| G
    C -->|"LLM API"| H
```

**How it works:**  
- Users interact via a modern Next.js dashboard.
- The FastAPI backend orchestrates extraction, booking, and media capture.
- Scraper modules automate browser actions for each provider.
- All booking/session data is stored in PostgreSQL; screenshots/videos in MinIO.
- Google Generative AI powers advanced extraction and automation.

---

## Quick Start

```bash
# 1. Clone the repository
git clone https://github.com/MLTech-AI/OceanMind-Prod-3.0.git
cd OceanMind-Prod-3.0

# 2. Install Python dependencies (backend)
python -m venv .venv
source .venv/bin/activate  # or .venv\Scripts\activate on Windows
pip install -r requirements.txt

# 3. Install frontend dependencies
cd nextjs-frontend
npm install

# 4. Set up environment variables (see below)
# 5. Start backend and frontend (in separate terminals)
python api.py
npm run dev
```

---

## Installation

### Backend (Python/FastAPI)

1. **Python 3.9+** required.
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Configure `.env` (see below).
4. Initialize the database:
   ```bash
   python -m db.postgres_schema
   ```
5. (Optional) Set up and test MinIO (see `MinIO_Setup.md`).

### Frontend (Next.js/React)

1. Node.js 18+ and npm required.
2. Install dependencies:
   ```bash
   cd nextjs-frontend
   npm install
   ```
3. Configure environment variables (see below).
4. Start the development server:
   ```bash
   npm run dev
   ```

---

## Usage Examples

### Booking via API (Python)

```python
import requests

token = "<your-access-token>"
headers = {"Authorization": f"Bearer {token}"}

# Extract cruise details
resp = requests.post(
    "http://localhost:8000/extraction",
    json={"provider": "NCL", "text_input": "...", "url_input": ""},
    headers=headers
)
details = resp.json()["details"]

# Start a booking
resp = requests.post(
    "http://localhost:8000/ncl-booking",
    json={
        "provider": "NCL",
        "cruise_details": details,
        "config": {},
        "request_id": "my-booking-001"
    },
    headers=headers
)
print(resp.json())
```

### Frontend (Next.js)

- Run `npm run dev` in `nextjs-frontend/`
- Access the dashboard at [http://localhost:3000](http://localhost:3000)
- Use the UI to extract, book, and view analytics

---

## Configuration Reference

### Environment Variables (`.env`)

```bash
# PostgreSQL
PG_HOST=localhost
PG_PORT=5432
PG_DATABASE=oceanmind
PG_USER=your_user
PG_PASSWORD=your_password

# MinIO
MINIO_ENDPOINT=***********:9000
MINIO_ACCESS_KEY=oceanmind_admin
MINIO_SECRET_KEY=OceanMind@MinIO2024
MINIO_SECURE=false
MINIO_SCREENSHOTS_BUCKET=oceanmind-screenshots
MINIO_VIDEOS_BUCKET=oceanmind-videos
MINIO_BACKUPS_BUCKET=oceanmind-backups

# Storage
USE_MINIO_STORAGE=true
STORAGE_FALLBACK_TO_DB=true

# Google Generative AI
GOOGLE_API_KEY=your_google_api_key

# FastAPI
SECRET_KEY=your_secret_key
```

- See `MinIO_Setup.md` for full MinIO setup.
- See `requirements.txt` and `nextjs-frontend/package.json` for all dependencies.

---

## Performance & Scalability

- **Async I/O:** Uses `asyncpg` and FastAPI for high concurrency.
- **Connection Pooling:** Database and MinIO connections are pooled.
- **Media Streaming:** Screenshots/videos are streamed from MinIO.
- **Scalability:** Horizontally scalable with stateless API and object storage.

---

## Contributing

1. Fork and clone the repo.
2. Use feature branches and submit pull requests.
3. Follow PEP8 for Python and Prettier/ESLint for TypeScript.
4. Add docstrings and comments to all new code.
5. Run `npm run lint` in `nextjs-frontend/` before submitting frontend changes.
6. Add/Update documentation as needed.

---

## Project Structure

A bird's-eye view of the main directories and their responsibilities:

```
Cruise-Dev-3.0/
├── Core/                   Utilities for browser setup, logging, and DB helpers.
├── Cruising_Power/         Automation modules for the Cruising Power provider.
├── NCL/                    Automation modules for the Norwegian Cruise Line provider.
├── Studio/                 Automation modules for the Studio provider.
├── database/               ORM-style database access layer and migrations.
├── db/                     Legacy DB schema and seed scripts (Postgres).
├── services/               Business logic for extraction, booking, media, and logging services.
├── nextjs-frontend/        Next.js/React SPA dashboard and frontend code.
├── api.py                  FastAPI entry point for the backend API.
├── config/                 Configuration modules (e.g., MinIO, environment setup).
├── requirements.txt        Python dependencies for the backend.
└── README.md               Project overview and setup instructions.
```

---

## Database Migrations & Seeding

- **Initialize Schema:**
  ```bash
  python -m db.postgres_schema
  ```
- **Seed Default Data:**
  ```bash
  python db/seed_admin.py && python db/seed_providers.py
  ```

---

## Troubleshooting

- **Port Conflicts:** Ensure ports `8000` (API) and `3000` (Frontend) are not in use.
- **MinIO Connectivity:** Verify `MINIO_ENDPOINT` and credentials in `.env`; use the `mc` CLI to test bucket access.
- **Browser Automation Failures:** Check that Chrome/Chromium is installed and available in your `PATH`. See `Core/browser_setup.py` for supported browsers.
- **Database Connection Errors:** Confirm your `PG_*` environment variables and that PostgreSQL is running.

---

## Additional Resources

- **MinIO Setup Guide:** [`MinIO_Setup.md`](MinIO_Setup.md)

---