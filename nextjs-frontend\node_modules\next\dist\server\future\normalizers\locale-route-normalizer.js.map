{"version": 3, "sources": ["../../../../src/server/future/normalizers/locale-route-normalizer.ts"], "names": ["LocaleRouteNormalizer", "constructor", "provider", "normalize", "pathname", "match", "analyze"], "mappings": ";;;;+BAMaA;;;eAAAA;;;AAAN,MAAMA;IACXC,YAA6BC,SAAwB;wBAAxBA;IAAyB;IAEtD;;;;;GAKC,GACD,AAAOC,UAAUC,QAAgB,EAAU;QACzC,MAAMC,QAAQ,IAAI,CAACH,QAAQ,CAACI,OAAO,CAACF;QACpC,OAAOC,MAAMD,QAAQ;IACvB;AACF"}