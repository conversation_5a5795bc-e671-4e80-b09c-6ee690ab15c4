"""
Session management functionality for tracking booking sessions across application components.
"""
import json
import logging
from datetime import datetime
from db_service import get_db_connection, db_connection

# Configure logging
logger = logging.getLogger("database.session_manager")

class SessionManager:
    """
    Centralized session management for consistent session IDs across application components.
    Implemented as a singleton to ensure a single instance across the application.
    """
    
    _instance = None  # Singleton instance
    
    @classmethod
    def get_instance(cls):
        """Get the singleton instance of SessionManager"""
        if cls._instance is None:
            cls._instance = SessionManager()
        return cls._instance
    
    def __init__(self):
        self.active_sessions = {}
        self.current_session_id = None
    
    async def create_session(self, request_id: str, provider: str) -> str:
        """Create a new session for a booking request and return the session ID"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        session_id = f"{request_id}_{timestamp}"
        
        # Store session metadata
        self.active_sessions[session_id] = {
            'request_id': request_id,
            'created_at': datetime.now(),
            'provider': provider,
            'status': 'created'
        }
        
        # Set this as the current session
        self.current_session_id = session_id
        
        # Register the session in database
        try:
            async with db_connection() as conn:
            
                now = datetime.now().isoformat()
                await conn.execute('''
                    INSERT INTO session_tracking 
                    (session_id, request_id, created_at, updated_at, status, provider, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                ''', 
                    session_id,
                    request_id,
                    now,
                    now,
                    'created',
                    provider,
                    '{}'
                )

                logger.info(f"Created and registered new session {session_id} for request {request_id}")
        except Exception as e:
            logger.error(f"Error registering session in database: {e}")

        
        return session_id
    
    async def get_session(self, request_id: str, provider: str) -> str:
        """Get existing session ID for a request or create a new one"""
        # If there's a current session and it matches the request_id, use it
        if self.current_session_id and self.active_sessions.get(self.current_session_id, {}).get('request_id') == request_id:
            return self.current_session_id
            
        # Try to find an active session for this request
        for session_id, metadata in self.active_sessions.items():
            if (metadata['request_id'] == request_id and 
                metadata.get('provider') == provider and 
                metadata['status'] != 'completed'):
                self.current_session_id = session_id
                return session_id
                
        # Check the database for existing session
        try:
            async with db_connection() as conn:
            
                result = await conn.fetchrow("""
                SELECT session_id FROM session_tracking 
                WHERE request_id = $1 AND provider = $2 AND status != 'completed'
                ORDER BY updated_at DESC LIMIT 1
                """, request_id, provider)

                if result and result['session_id']:
                    session_id = result['session_id']
                    # Add to local cache
                    self.active_sessions[session_id] = {
                        'request_id': request_id,
                        'created_at': datetime.now(),
                        'provider': provider,
                        'status': 'active'
                    }
                    self.current_session_id = session_id
                    return session_id
        except Exception as e:
            logger.error(f"Error checking for existing session in database: {e}")

            
        # No active session found, create a new one
        return await self.create_session(request_id, provider)
    
    def get_current_session(self) -> str:
        """Get the current active session ID"""
        return self.current_session_id
        
    def set_current_session(self, session_id: str):
        """Set the current active session ID"""
        self.current_session_id = session_id
        
    async def update_session_status(self, session_id: str, status: str, metadata: dict = None):
        """
        Update session status and metadata in the database
        
        Args:
            session_id: The session ID to update
            status: New status value
            metadata: Optional metadata to store with the session
        """
        from .utils import execute_with_retry
        
        async def update_status_transaction():
            async with db_connection() as conn:
                try:
                    # Check if session exists first
                    result = await conn.fetchrow(
                        "SELECT session_id FROM session_tracking WHERE session_id = $1",
                        session_id
                    )

                    if not result:
                        # Session doesn't exist, try to extract request_id and create it
                        parts = session_id.split('_')
                        if len(parts) > 1:
                            request_id = parts[0]
                            # Import here to avoid circular imports
                            from .utils import register_session
                            await register_session(request_id)
                        else:
                            logger.warning(f"Cannot update non-existent session {session_id}")
                            return False

                    # Update the session
                    metadata_json = json.dumps(metadata) if metadata else '{}'
                    await conn.execute(
                        '''
                        UPDATE session_tracking 
                        SET updated_at = $1, status = $2, metadata = $3
                        WHERE session_id = $4
                        ''', 
                        datetime.now().isoformat(),
                        status,
                        metadata_json,
                        session_id
                    )
                    return True
                except Exception as e:
                    logger.error(f"Error updating session status: {e}")
                    raise

        
        try:
            # Use our retry utility with exponential backoff
            return await execute_with_retry(update_status_transaction)
        except Exception as e:
            logger.error(f"Failed to update session status after multiple retries: {e}")
            return False

    async def get_session_status(self, session_id: str) -> dict:
        """
        Retrieve session status and metadata from the database
        """
        try:
            async with db_connection() as conn:
            
                row = await conn.fetchrow(
                    "SELECT status, metadata, updated_at FROM session_tracking WHERE session_id = $1",
                    session_id
                )

                if not row:
                    return {}

                return {
                    "status": row["status"],
                    "metadata": json.loads(row["metadata"]) if row["metadata"] else {},
                    "updated_at": row["updated_at"]
                }
        except Exception as e:
            logger.error(f"Error retrieving session status: {e}")
            return {}