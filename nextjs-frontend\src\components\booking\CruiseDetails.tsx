import React, { useState, useEffect } from 'react';
import { ProviderType } from './ProviderSelector';
import StudioEditForm from './StudioEditForm';
import NCLEditForm from './NCLEditForm';
import CruisingPowerEditForm from './CruisingPowerEditForm';
import OneSourceEditForm from './OneSourceEditForm';

interface CruiseDetailsProps {
  provider: ProviderType;
  details: any;
  requestId: string;
  onUpdate: (updatedDetails: any) => void;
  hideEditButton?: boolean;
  validationMode?: boolean;
}

// Helper function to safely render any value
const renderValue = (value: any): string => {
  if (value === null || value === undefined) {
    return '';
  }
  if (typeof value === 'object') {
    if (Array.isArray(value)) {
      return value.map(v => renderValue(v)).join(', ');
    }
    // For objects, convert to JSON string for display
    return JSON.stringify(value);
  }
  return String(value);
};

// Studio data formatter - converts array to labeled object
const formatStudioData = (data: string[]): Record<string, string> => {
  // Define labels for each index in the array
  const labels = [
    'Sailing Date',
    'Cruise Length (Nights)',
    'Ship ID',
    'Airport Code',
    'Total Passengers',
    'Adult Passengers',
    'Adult Count',
    'Cabin Type 1',
    'Cabin Type 2'
  ];

  // Create labeled object
  const formattedData: Record<string, string> = {};
  data.forEach((value, index) => {
    if (index < labels.length) {
      formattedData[labels[index]] = value;
    } else {
      formattedData[`Value ${index + 1}`] = value;
    }
  });

  return formattedData;
};

// Cruising Power data formatter - formats and flattens complex data structure
const formatCruisingPowerData = (data: any): Record<string, string> => {
  if (!data) return {};

  const formattedData: Record<string, string> = {};

  // Extract top-level simple properties
  Object.entries(data).forEach(([key, value]) => {
    // Skip these keys as they'll be handled specially
    if (['cabin_categories', 'cabins_with_passengers', 'original_text', 'request_id'].includes(key)) {
      return;
    }

    // Format simple values
    formattedData[formatLabel(key)] = renderValue(value);
  });

  // Format cabin categories specially
  if (data.cabin_categories && Array.isArray(data.cabin_categories)) {
    data.cabin_categories.forEach((category: any, index: number) => {
      const prefix = `Cabin Category ${index + 1}`;
      formattedData[`${prefix} - Type`] = category.type || '';
      formattedData[`${prefix} - Count`] = renderValue(category.count);
      formattedData[`${prefix} - Option Type`] = category.is_or_option ? 'OR Option' : 'AND Required';
    });
  }

  // Format cabins with passengers
  if (data.cabins_with_passengers && Array.isArray(data.cabins_with_passengers)) {
    data.cabins_with_passengers.forEach((cabin: any, index: number) => {
      const prefix = `Cabin ${index + 1}`;
      formattedData[`${prefix} - Type`] = cabin.type || '';
      formattedData[`${prefix} - Adults`] = renderValue(cabin.assigned_adults);
      formattedData[`${prefix} - Children`] = renderValue(cabin.assigned_children);
      formattedData[`${prefix} - Seniors`] = renderValue(cabin.assigned_seniors);
    });
  }

  return formattedData;
};

// Helper to format label from snake_case to Title Case
const formatLabel = (key: string): string => {
  return key
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

const CruiseDetails: React.FC<CruiseDetailsProps> = ({
  provider,
  details,
  requestId,
  onUpdate,
  hideEditButton = false,
  validationMode = false
}) => {
  const [formattedDetails, setFormattedDetails] = useState<any>(details);
  const [isEditing, setIsEditing] = useState(false);

  // Format details when they change or provider changes
  useEffect(() => {
    if (provider.startsWith('Studio') && Array.isArray(details)) {
      // Format Studio array data
      const formatted = formatStudioData(details);
      setFormattedDetails(formatted);
    } else if (provider === 'Cruising Power' && details) {
      // Format Cruising Power object data
      const formatted = formatCruisingPowerData(details);
      setFormattedDetails(formatted);
    } else {
      setFormattedDetails(details);
    }
  }, [details, provider]);

  // Simple validation to ensure we have details to display
  if (!details) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-lg border border-red-200">
        No cruise details available. Please try extracting again.
      </div>
    );
  }

  const renderStudioDetails = () => {
    // Display Studio details in a format similar to the edit form
    // Parse the Studio details array into the needed components
    if (!Array.isArray(details) || details.length < 5) {
      return (
        <div className="bg-yellow-50 p-4 text-yellow-800 rounded-lg border border-yellow-200">
          Incomplete Studio data format
        </div>
      );
    }

    const sailingDate = details[0] || 'N/A';
    const duration = details[1] || 'N/A';
    const shipId = details[2] || 'N/A';
    const airport = details[3] || 'N/A';
    const numCabins = parseInt(details[4]) || 1;

    // Extract passengers and categories
    const passengers = details.slice(5, 5 + numCabins);
    const categories = details.slice(5 + numCabins);

    // Calculate total passengers
    const totalPassengers = passengers.reduce((sum, val) => sum + parseInt(val || '2'), 0);

    return (
      <div className="p-1">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-2">
          <div className="p-2 border items-center h-12 flex gap-2 border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 text-lg">Sailing Date: </h5>
            <p className="text-gray-900 font-medium text-lg">{sailingDate}</p>
          </div>

          <div className="p-3 border items-center h-14 flex gap-3 border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 text-lg">Duration (nights): </h5>
            <p className="text-gray-900 font-medium text-lg">{duration}</p>
          </div>

          <div className="p-3 border items-center h-14 flex gap-3 border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 text-lg">Ship ID: </h5>
            <p className="text-gray-900 font-medium text-lg">{shipId}</p>
          </div>


          <div className="p-3 border items-center h-14 flex gap-3 border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 text-lg">Airport Code: </h5>
            <p className="text-gray-900 font-medium text-lg">{airport}</p>
          </div>

          <div className="p-3 border items-center h-14 flex gap-3 border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 text-lg">Number of Cabins: </h5>
            <p className="text-gray-900 font-medium text-lg">{numCabins}</p>
          </div>
        </div>

        <div className="mb-2 mt-2">
          <div className="flex items-center">
            
            <h4 className="text-lg font-bold text-gray-800">Cabin Details</h4>
          </div>
          <div className="mt-1 relative pb-2">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4 mt-3">
          {Array.from({ length: numCabins }).map((_, index) => (
            <div key={index} className="p-2">
              <div className="flex items-center mb-1">
                <div className="w-6 h-6 rounded-full bg-teal-600 text-white flex items-center justify-center mr-1 shadow-sm">
                  {index + 1}
                </div>
                <h5 className="font-semibold text-teal-800 text-base">Cabin {index + 1}</h5>
              </div>
              <div className="grid grid-cols-1 gap-2">
                <div className="p-2 bg-gradient-to-r from-teal-50 to-blue-50 rounded-lg shadow-sm border border-gray-200 flex justify-between items-center">
                  <h6 className="text-base font-medium text-gray-700">Number of Passengers:</h6>
                  <p className="text-teal-700 font-bold text-base">{passengers[index] || '2'}</p>
                </div>

                <div className="p-2 bg-gradient-to-r from-teal-50 to-blue-50 rounded-lg shadow-sm border border-gray-200 flex justify-between items-center">
                  <h6 className="text-base font-medium text-gray-700">Cabin Category:</h6>
                  <p className="text-teal-700 font-bold text-base">{categories[index] || 'INSIDE'}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-3 bg-gradient-to-r from-teal-50 to-blue-50 p-2 rounded-lg border border-teal-200 shadow-md">
          <div className="font-bold text-teal-800 text-lg mb-2 pb-1 border-b border-teal-100">Passenger Summary</div>
          {/* <div className="grid grid-cols-2 "> */}
          {/* <div className="bg-white p-2 rounded-lg shadow-md border border-gray-100 transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1 flex gap-3"> */}
          <div className='flex gap-2'>
            <span className="text-gray-600 text-md block">Total Passengers: </span>
            <p className="text-teal-700 font-bold text-md">{totalPassengers}</p>
          </div>

          {/* </div> */}
          {/* <div className="bg-white p-2 rounded-lg shadow-md border border-gray-100 transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1 flex gap-3"> */}
          <div className='flex gap-2'>
            <span className="text-gray-600 text-md block">Total Cabins: </span>
            <p className="text-teal-700 font-bold text-md">{numCabins}</p>
          </div>

          {/* </div> */}
          {/* </div> */}
        </div>
      </div>
    );
  };

  const renderNCLDetails = () => {
    // Display NCL details in a format similar to the edit form

    // Calculate distributed passengers across cabins
    const distributeCabinPassengers = () => {
      const numCabins = details.cabins || 1;
      const totalAdults = details.adults || 0;
      const totalChildren = details.children || 0;
      const totalInfants = details.infants || 0;

      let cabinDistribution = [];

      // Check if we have explicit cabin_passengers_dict
      if (details.cabin_passengers_dict && Object.keys(details.cabin_passengers_dict).length > 0) {
        // Use the explicit cabin passenger distribution
        for (let i = 1; i <= numCabins; i++) {
          if (details.cabin_passengers_dict[i]) {
            cabinDistribution.push({
              adults: details.cabin_passengers_dict[i].adults || 0,
              children: details.cabin_passengers_dict[i].children || 0,
              infants: details.cabin_passengers_dict[i].infants || 0,
              category: details.cabin_passengers_dict[i].category || 'INSIDE'
            });
          }
        }
      }
      // Check if we have cabin_passengers array
      else if (details.cabin_passengers && Array.isArray(details.cabin_passengers) &&
        details.cabin_passengers.length > 0) {
        cabinDistribution = details.cabin_passengers;
      }
      // Otherwise distribute passengers ourselves
      else {
        const isORCabinScenario =
          numCabins > 1 &&
          details.cabin_categories &&
          details.cabin_categories.length === numCabins &&
          details.cabins !== details.original_cabin_count;

        // For single cabin or OR scenario, assign all passengers to each cabin
        if (numCabins === 1 || isORCabinScenario) {
          for (let i = 0; i < numCabins; i++) {
            cabinDistribution.push({
              adults: totalAdults,
              children: totalChildren,
              infants: totalInfants
            });
          }
        }
        // Otherwise, distribute passengers evenly across cabins
        else {
          // Distribute adults
          const baseAdults = Math.floor(totalAdults / numCabins);
          const extraAdults = totalAdults % numCabins;

          // Distribute children
          const baseChildren = Math.floor(totalChildren / numCabins);
          const extraChildren = totalChildren % numCabins;

          // Distribute infants
          const baseInfants = Math.floor(totalInfants / numCabins);
          const extraInfants = totalInfants % numCabins;

          for (let i = 0; i < numCabins; i++) {
            cabinDistribution.push({
              adults: baseAdults + (i < extraAdults ? 1 : 0),
              children: baseChildren + (i < extraChildren ? 1 : 0),
              infants: baseInfants + (i < extraInfants ? 1 : 0)
            });
          }
        }
      }

      return cabinDistribution;
    };

    const cabinDistribution = distributeCabinPassengers();

    return (
      <div className="rounded-xl p-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-2">
          <div className="p-3 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 mb-1">Sailing Date</h5>
            <p className="text-gray-900 font-medium text-base">{details.travel_date || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 mb-1">Duration (nights)</h5>
            <p className="text-gray-900 font-medium text-base">{details.nights || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 mb-1">Ship Name</h5>
            <p className="text-gray-900 font-medium text-base">{details.ship_name || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 mb-1">Number of Cabins</h5>
            <p className="text-gray-900 font-medium text-base">{details.cabins || 'N/A'}</p>
          </div>
        </div>

        <div className="mb-2 mt-2">
          <div className="flex items-center">
            
            <h4 className="text-lg font-bold text-gray-800">Cabin Details</h4>
          </div>
          <div className="mt-1 relative pb-2">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
          </div>
        </div>

        {details.cabins && (
          Array.from({ length: Number(details.cabins) }).map((_, index) => {
            // Get category for this cabin
            let category = 'INSIDE';
            if (details.cabin_categories) {
              if (Array.isArray(details.cabin_categories)) {
                category = details.cabin_categories[index] || 'INSIDE';
              } else if (typeof details.cabin_categories === 'string') {
                category = details.cabin_categories;
              }
            } else if (details.cabin_passengers_dict && details.cabin_passengers_dict[index + 1]) {
              category = details.cabin_passengers_dict[index + 1].category || 'INSIDE';
            }

            // Get passenger info from our distributed calculation
            const passengerInfo = cabinDistribution[index] || { adults: 2, children: 0, infants: 0 };

            return (
              <div key={index} className="p-2  rounded-lg">
                <div className="flex items-center mb-1">
                  <div className="w-6 h-6 rounded-full bg-teal-600 text-white flex items-center justify-center mr-1">
                    {index + 1}
                  </div>
                  <h5 className="font-semibold text-teal-800 text-base">Cabin {index + 1}</h5>
                </div>

                <div className="mb-2 p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
                  <h6 className="text-sm font-medium text-gray-700 mb-0">Category</h6>
                  <p className="text-teal-700 font-bold text-base">{category}</p>
                </div>

                <div className="grid grid-cols-3 gap-2">
                  <div className="p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
                    <h6 className="text-xs font-medium text-gray-700">Adults</h6>
                    <p className="text-teal-700 font-bold text-base">{passengerInfo.adults}</p>
                  </div>

                  <div className="p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
                    <h6 className="text-xs font-medium text-gray-700">Children</h6>
                    <p className="text-teal-700 font-bold text-base">{passengerInfo.children}</p>
                  </div>

                  <div className="p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
                    <h6 className="text-xs font-medium text-gray-700">Infants</h6>
                    <p className="text-teal-700 font-bold text-base">{passengerInfo.infants}</p>
                  </div>
                </div>
              </div>
            );
          })
        )}

        <div className="mt-3 bg-gradient-to-br from-teal-100 h-22 to-blue-100  p-3 rounded-lg border border-teal-100">
          <div className="font-bold text-teal-800 text-lg mb-1 pb-1 border-b border-teal-100">Passenger Summary</div>
          <div className='flex gap-16'>
            <div className='flex gap-2'>
              <span className="text-gray-600 text-md block">Total Passengers: </span>
              <p className="text-teal-700 font-bold text-md">{details.total_passengers}</p>
            </div>

            <div className='flex gap-2'>
              <span className="text-gray-600 text-md block">Adults</span>
              <p className="text-teal-700 font-bold text-md">{details.adults || 0}</p>
            </div>

            <div className='flex gap-2'>
              <span className="text-gray-600 text-md block ">Children:</span>
              <p className="text-teal-700 font-bold text-md">{details.children || 0}</p>
            </div>

            <div className='flex gap-2'>
              <span className="text-gray-600 text-md block">Infants: </span>
              <p className="text-teal-700 font-bold text-md">{details.infants || 0}</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderCruisingPowerDetails = () => {
    // Display Cruising Power details in a format similar to the edit form
    return (
      <div className="rounded-xl p-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-2">
          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 mb-1">Ship Name</h5>
            <p className="text-gray-900 font-medium text-base">{details.ship_name || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 mb-1">Ship Brand</h5>
            <p className="text-gray-900 font-medium text-base">{details.ship_brand || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 mb-1">Departure Date</h5>
            <p className="text-gray-900 font-medium text-base">{details.departure_date || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 mb-1">Residency</h5>
            <p className="text-gray-900 font-medium text-base">{details.residency || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50 ">
            <h5 className="font-semibold text-teal-800 mb-1">Number of Cabins</h5>
            <p className="text-gray-900 font-medium text-base">{details.total_cabins || '1'}</p>
          </div>
        </div>

        <div className="mb-2 mt-2">
          <div className="flex items-center">
            
            <h4 className="text-lg font-bold text-gray-800">Cabin Details</h4>
          </div>
          <div className="mt-1 relative pb-2">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
          </div>
        </div>

        <div className="space-y-3 mb-4 mt-3">
          {details.cabins_with_passengers && details.cabins_with_passengers.map((cabin: any, index: number) => (
            <div key={index} className="p-2 rounded-lg">
              <div className="flex items-center mb-1">
                <div className="w-6 h-6 rounded-full bg-teal-600 text-white flex items-center justify-center mr-1 shadow-sm">
                  {index + 1}
                </div>
                <h5 className="font-semibold text-teal-800 text-base">Cabin {index + 1}</h5>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                <div className="md:col-span-1 p-2 bg-white rounded-lg shadow-sm border border-gray-200">
                  <h6 className="text-xs font-medium text-gray-700">Cabin Type</h6>
                  <p className="text-teal-700 font-bold text-base">{cabin.type || 'INSIDE'}</p>
                </div>

                <div className="p-2 bg-white rounded-lg shadow-sm border border-gray-200">
                  <h6 className="text-xs font-medium text-gray-700">Adults</h6>
                  <p className="text-teal-700 font-bold text-base">{cabin.assigned_adults || 0}</p>
                </div>

                <div className="p-2 bg-white rounded-lg shadow-sm border border-gray-200">
                  <h6 className="text-xs font-medium text-gray-700">Children</h6>
                  <p className="text-teal-700 font-bold text-base">{cabin.assigned_children || 0}</p>
                </div>

                <div className="p-2 bg-white rounded-lg shadow-sm border border-gray-200">
                  <h6 className="text-xs font-medium text-gray-700">Seniors</h6>
                  <p className="text-teal-700 font-bold text-base">{cabin.assigned_seniors || 0}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-2 bg-gradient-to-br from-teal-100 to-blue-100 p-2 rounded-lg border border-black/20">
          <div className="font-bold text-teal-800 text-lg mb-1 pb-1 border-b border-teal-100">Passenger Summary</div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="bg-white p-2 mb-2 rounded-lg border border-gray-200 transform transition-all duration-300 flex gap-3">
              <span className="text-gray-600 text-md block">Total Passengers: </span>
              <p className="text-teal-700 font-bold text-lg">{details.total_passengers || 0}</p>
            </div>
            <div className="bg-white p-2 mb-2 rounded-lg  border border-gray-200 transform transition-all duration-300  flex gap-3">
              <span className="text-gray-600 text-md block">Adults: </span>
              <p className="text-teal-700 font-bold text-lg">{details.adults || 0}</p>
            </div>
            <div className="bg-white p-2 mb-2 rounded-lg  border border-gray-200 transform transition-all duration-300 flex gap-3">
              <span className="text-gray-600 text-md block">Children/Seniors: </span>
              <p className="text-teal-700 font-bold text-lg">{(details.children || 0) + (details.seniors || 0)}</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderOneSourceDetails = () => {
    // Display OneSource details in a format similar to other providers
    return (
      <div className="rounded-xl p-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-2">
          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50">
            <h5 className="font-semibold text-teal-800 mb-1">Cruise Line</h5>
            <p className="text-gray-900 font-medium text-base">{details.cruise_line || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50">
            <h5 className="font-semibold text-teal-800 mb-1">Ship Name</h5>
            <p className="text-gray-900 font-medium text-base">{details.ship_name || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50">
            <h5 className="font-semibold text-teal-800 mb-1">Sailing Date</h5>
            <p className="text-gray-900 font-medium text-base">{details.date || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50">
            <h5 className="font-semibold text-teal-800 mb-1">Duration (nights)</h5>
            <p className="text-gray-900 font-medium text-base">{details.nights || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50">
            <h5 className="font-semibold text-teal-800 mb-1">Airport Code</h5>
            <p className="text-gray-900 font-medium text-base">{details.airport_code || 'N/A'}</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50">
            <h5 className="font-semibold text-teal-800 mb-1">Total Cabins</h5>
            <p className="text-gray-900 font-medium text-base">{details.cabin_count || 'N/A'}</p>
          </div>

          {/* Past Passenger Number - optional */}
          {details.past_passenger_number && (
            <div className="p-3 border border-gray-200 rounded-lg bg-gradient-to-r from-teal-50 to-blue-50">
              <h5 className="font-semibold text-teal-800 mb-1">Past Passenger Number</h5>
              <p className="text-gray-900 font-medium text-base">{details.past_passenger_number}</p>
            </div>
          )}
        </div>

        <div className="mb-2 mt-2">
          <div className="flex items-center">
            <h4 className="text-lg font-bold text-gray-800">Cabin Details</h4>
          </div>
          <div className="mt-1 relative pb-2">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
          </div>
        </div>

        <div className="space-y-3 mb-4 mt-3">
          {details.cabin_types && details.cabin_types.map((cabin: any, index: number) => (
            <div key={index} className="p-2 rounded-lg">
              <div className="flex items-center mb-1">
                <div className="w-6 h-6 rounded-full bg-teal-600 text-white flex items-center justify-center mr-1 shadow-sm">
                  {cabin.cabin_number}
                </div>
                <h5 className="font-semibold text-teal-800 text-base">Cabin {cabin.cabin_number}</h5>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                <div className="p-2 bg-white rounded-lg shadow-sm border border-gray-200">
                  <h6 className="text-xs font-medium text-gray-700">Cabin Type</h6>
                  <p className="text-teal-700 font-bold text-base">{cabin.cabin_type || 'N/A'}</p>
                </div>

                <div className="p-2 bg-white rounded-lg shadow-sm border border-gray-200">
                  <h6 className="text-xs font-medium text-gray-700">Passengers</h6>
                  <p className="text-teal-700 font-bold text-base">{cabin.passengers || 0}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mb-2">
          <div className="font-bold text-teal-800 text-lg mb-1 pb-1 border-b border-teal-100">Passenger Summary</div>
          <div className="flex gap-4">
            <div className="flex gap-2">
              <span className="text-gray-600 text-md block">Total Passengers:</span>
              <p className="text-teal-700 font-bold text-md">{details.passengers || 0}</p>
            </div>
            <div className="flex gap-2">
              <span className="text-gray-600 text-md block">Total Cabins:</span>
              <p className="text-teal-700 font-bold text-md">{details.cabin_count || 0}</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderDetailsBasedOnProvider = () => {
    // Check for Studio providers
    if (provider.startsWith('Studio')) {
      return renderStudioDetails();
    }

    // Other providers
    switch (provider) {
      case 'NCL':
        return renderNCLDetails();
      case 'Cruising Power':
        return renderCruisingPowerDetails();
      case 'OneSource':
        return renderOneSourceDetails();
      default:
        return <div>Unknown provider</div>;
    }
  };

  const renderEditFormBasedOnProvider = () => {
    // Check for Studio providers
    if (provider.startsWith('Studio')) {
      return (
        <StudioEditForm
          details={details}
          onSave={(updatedDetails) => {
            onUpdate(updatedDetails);
            setIsEditing(false);
          }}
          onCancel={() => setIsEditing(false)}
        />
      );
    }

    // Other providers
    switch (provider) {
      case 'NCL':
        return (
          <NCLEditForm
            details={details}
            requestId={requestId}
            onSave={(updatedDetails) => {
              onUpdate(updatedDetails);
              setIsEditing(false);
            }}
            onCancel={() => setIsEditing(false)}
          />
        );
      case 'Cruising Power':
        return (
          <CruisingPowerEditForm
            details={details}
            requestId={requestId}
            onSave={(updatedDetails) => {
              onUpdate(updatedDetails);
              setIsEditing(false);
            }}
            onCancel={() => setIsEditing(false)}
          />
        );
      case 'OneSource':
        return (
          <OneSourceEditForm
            details={details}
            requestId={requestId}
            onSave={(updatedDetails) => {
              onUpdate(updatedDetails);
              setIsEditing(false);
            }}
            onCancel={() => setIsEditing(false)}
          />
        );
      default:
        return <div>Edit form not available for this provider</div>;
    }
  };

  // Apply validation mode styles
  const containerStyles = validationMode 
    ? "mb-0 bg-white rounded-lg" 
    : "mb-6";
  
  const headerStyles = validationMode
    ? "mb-2 flex justify-between items-center border-b border-gray-200 pb-2"
    : "mb-3 flex justify-between items-center";
    
  const requestIdStyles = validationMode
    ? "bg-blue-50 px-2 py-1 rounded-lg border border-blue-200 flex items-center text-sm"
    : "bg-gradient-to-r from-teal-50 to-blue-50 px-2 py-1 rounded-lg border border-gray-700 flex items-center";

  return (
    <div className={containerStyles}>
      <div className={headerStyles}>
        <div className={requestIdStyles}>
          <span className={validationMode ? "text-blue-700 font-medium" : "text-gray-700 font-medium"}>Request ID:</span>
          <span className={validationMode ? "ml-1 text-blue-900 font-bold" : "ml-1 text-blue-900 font-bold"}>{requestId}</span>
        </div>

        <div>
          {!isEditing && !hideEditButton && (
            <button
              onClick={() => setIsEditing(true)}
              className="text-white px-3 py-1.5 rounded-lg shadow-lg flex items-center transition-all duration-300 hover:shadow-xl hover:scale-105"
              style={{
                background: 'linear-gradient(to right, #3A6073, #16222A)'
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
              Edit Details
            </button>
          )}
        </div>
      </div>

      {isEditing ? renderEditFormBasedOnProvider() : renderDetailsBasedOnProvider()}
    </div>
  );
};

export default CruiseDetails;