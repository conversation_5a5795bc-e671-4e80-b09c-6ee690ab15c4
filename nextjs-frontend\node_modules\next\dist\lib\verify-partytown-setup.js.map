{"version": 3, "sources": ["../../src/lib/verify-partytown-setup.ts"], "names": ["verifyPartytownSetup", "missingDependencyError", "dir", "packageManager", "getPkgManager", "FatalE<PERSON>r", "bold", "red", "cyan", "copyPartytownStaticFiles", "deps", "staticDir", "partytownLibDir", "path", "join", "hasPartytownLibDir", "fileExists", "FileType", "Directory", "promises", "rm", "recursive", "force", "copyLibFiles", "Promise", "resolve", "require", "resolved", "get", "targetDir", "partytownDeps", "hasNecessaryDependencies", "file", "pkg", "exportsRestrict", "missing", "length", "err", "Log", "warn", "console", "error", "message", "process", "exit"], "mappings": ";;;;+BA+DsBA;;;eAAAA;;;oBA/DG;4BACO;6DAEf;0CACwB;4BAEJ;4BACV;6DACN;+BACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9B,eAAeC,uBAAuBC,GAAW;IAC/C,MAAMC,iBAAiBC,IAAAA,4BAAa,EAACF;IAErC,MAAM,IAAIG,sBAAU,CAClBC,IAAAA,gBAAI,EACFC,IAAAA,eAAG,EACD,uHAGF,SACAD,IAAAA,gBAAI,EAAC,CAAC,oCAAoC,CAAC,IAC3C,SACA,CAAC,EAAE,EAAEA,IAAAA,gBAAI,EACPE,IAAAA,gBAAI,EACF,AAACL,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACnB,4BACA,wBAAuB,IAAK,2BAElC,CAAC,GACH,SACAG,IAAAA,gBAAI,EACF,CAAC,wEAAwE,EAAEE,IAAAA,gBAAI,EAC7E,uBACA,wBAAwB,CAAC,IAE7B;AAEN;AAEA,eAAeC,yBACbC,IAA2B,EAC3BC,SAAiB;IAEjB,MAAMC,kBAAkBC,aAAI,CAACC,IAAI,CAACH,WAAW;IAC7C,MAAMI,qBAAqB,MAAMC,IAAAA,sBAAU,EACzCJ,iBACAK,oBAAQ,CAACC,SAAS;IAGpB,IAAIH,oBAAoB;QACtB,MAAMI,YAAQ,CAACC,EAAE,CAACR,iBAAiB;YAAES,WAAW;YAAMC,OAAO;QAAK;IACpE;IAEA,MAAM,EAAEC,YAAY,EAAE,GAAG,MAAMC,QAAQC,OAAO,CAC5CC,QAAQb,aAAI,CAACC,IAAI,CAACJ,KAAKiB,QAAQ,CAACC,GAAG,CAAC,0BAA2B;IAGjE,MAAML,aAAaX;AACrB;AAEO,eAAeZ,qBACpBE,GAAW,EACX2B,SAAiB;IAEjB,IAAI;YAYEC;QAXJ,MAAMA,gBAAuC,MAAMC,IAAAA,kDAAwB,EACzE7B,KACA;YACE;gBACE8B,MAAM;gBACNC,KAAK;gBACLC,iBAAiB;YACnB;SACD;QAGH,IAAIJ,EAAAA,yBAAAA,cAAcK,OAAO,qBAArBL,uBAAuBM,MAAM,IAAG,GAAG;YACrC,MAAMnC,uBAAuBC;QAC/B,OAAO;YACL,IAAI;gBACF,MAAMO,yBAAyBqB,eAAeD;YAChD,EAAE,OAAOQ,KAAK;gBACZC,KAAIC,IAAI,CACN,CAAC,wFAAwF,EAAEjC,IAAAA,gBAAI,EAC7FE,IAAAA,gBAAI,EAAC,0BACL,8BAA8B,CAAC;YAErC;QACF;IACF,EAAE,OAAO6B,KAAK;QACZ,8EAA8E;QAC9E,IAAIA,eAAehC,sBAAU,EAAE;YAC7BmC,QAAQC,KAAK,CAACJ,IAAIK,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf;QACA,MAAMP;IACR;AACF"}