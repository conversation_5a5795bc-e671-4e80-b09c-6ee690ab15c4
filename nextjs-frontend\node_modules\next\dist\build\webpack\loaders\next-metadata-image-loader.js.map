{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-image-loader.ts"], "names": ["raw", "nextMetadataImageLoader", "content", "options", "getOptions", "type", "segment", "pageExtensions", "basePath", "resourcePath", "rootContext", "context", "name", "fileNameBase", "ext", "path", "parse", "useNumericSizes", "extension", "slice", "opts", "contentHash", "loaderUtils", "interpolateName", "interpolatedName", "isDynamicResource", "includes", "pageSegment", "hash<PERSON><PERSON><PERSON>", "pathnamePrefix", "normalizePathSep", "join", "mod", "Promise", "res", "rej", "loadModule", "err", "_source", "_sourceMap", "module", "exportedFieldsExcludingDefault", "dependencies", "filter", "dep", "constructor", "map", "field", "JSON", "stringify", "WEBPACK_RESOURCE_QUERIES", "metadataImageMeta", "imageSize", "getImageSize", "catch", "Error", "imageData", "imageExtMimeTypeMap", "width", "height", "sizes", "altPath", "dirname", "existsSync", "alt", "fs", "readFile"], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;IAyMYA,GAAG;eAAHA;;IACb,OAAsC;eAAtC;;;oBAnM2C;6DAC1B;qEACO;gCACK;0BACO;2BACK;kCACR;;;;;;AASjC,uBAAuB;AACvB,+EAA+E;AAC/E,eAAeC,wBAAmCC,OAAe;IAC/D,MAAMC,UAAmB,IAAI,CAACC,UAAU;IACxC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,cAAc,EAAEC,QAAQ,EAAE,GAAGL;IACpD,MAAM,EAAEM,YAAY,EAAEC,aAAaC,OAAO,EAAE,GAAG,IAAI;IACnD,MAAM,EAAEC,MAAMC,YAAY,EAAEC,GAAG,EAAE,GAAGC,aAAI,CAACC,KAAK,CAACP;IAC/C,MAAMQ,kBAAkBZ,SAAS,aAAaA,SAAS;IAEvD,IAAIa,YAAYJ,IAAIK,KAAK,CAAC;IAC1B,IAAID,cAAc,OAAO;QACvBA,YAAY;IACd;IAEA,MAAME,OAAO;QAAET;QAAST;IAAQ;IAEhC,gCAAgC;IAChC,MAAMmB,cACJhB,SAAS,YACL,KACAiB,qBAAW,CAACC,eAAe,CAAC,IAAI,EAAE,iBAAiBH;IAEzD,MAAMI,mBAAmBF,qBAAW,CAACC,eAAe,CAClD,IAAI,EACJ,gBACAH;IAGF,MAAMK,oBAAoBlB,eAAemB,QAAQ,CAACR;IAClD,MAAMS,cAAcF,oBAAoBZ,eAAeW;IACvD,MAAMI,YAAYP,cAAc,MAAMA,cAAc;IACpD,MAAMQ,iBAAiBC,IAAAA,kCAAgB,EAACf,aAAI,CAACgB,IAAI,CAACvB,UAAUF;IAE5D,IAAImB,mBAAmB;YAcnBO;QAbF,MAAMA,MAAM,MAAM,IAAIC,QAA8B,CAACC,KAAKC;YACxD,IAAI,CAACC,UAAU,CACb3B,cACA,CAAC4B,KAAmBC,SAAcC,YAAiBC;gBACjD,IAAIH,KAAK;oBACP,OAAOF,IAAIE;gBACb;gBACAH,IAAIM;YACN;QAEJ;QAEA,MAAMC,iCACJT,EAAAA,oBAAAA,IAAIU,YAAY,qBAAhBV,kBACIW,MAAM,CAAC,CAACC;YACR,OACE;gBACE;gBACA;aACD,CAAClB,QAAQ,CAACkB,IAAIC,WAAW,CAACjC,IAAI,KAC/B,UAAUgC,OACVA,IAAIhC,IAAI,KAAK;QAEjB,GACCkC,GAAG,CAAC,CAACF;YACJ,OAAOA,IAAIhC,IAAI;QACjB,OAAM,EAAE;QAEZ,0EAA0E;QAC1E,OAAO,CAAC;;MAEN,EAAE6B,+BACCK,GAAG,CAAC,CAACC,QAAU,CAAC,EAAEA,MAAM,KAAK,EAAEA,MAAM,CAAC,EACtChB,IAAI,CAAC,KAAK;WACR,EAAEiB,KAAKC,SAAS,CACrB,4EAA4E;QAC5E,8DAA8D;QAC9D,kEAAkE;QAClE,6EAA6E;QAC7E,WAAW;QACXxC,eAAe,MAAMyC,mCAAwB,CAACC,iBAAiB,EAC/D;;;;MAIA,EAAEV,+BACCK,GAAG,CAAC,CAACC,QAAU,CAAC,EAAEA,MAAM,GAAG,EAAEA,MAAM,CAAC,EACpChB,IAAI,CAAC,KAAK;;;;;2CAKwB,EAAEiB,KAAKC,SAAS,CACnDpB,gBACA,UAAU,EAAEmB,KAAKC,SAAS,CAACtB,aAAa;;;;;;;;6DAQa,EAAEqB,KAAKC,SAAS,CACjErB,WACA;;;;UAIF,EACEvB,SAAS,aAAaA,SAAS,cAC3B,wDACA,+CACL;;;;;;;;;;;;;;KAcN,CAAC;IACJ;IAEA,MAAM+C,YAAiD,MAAMC,IAAAA,4BAAY,EACvEnD,SACAgB,WACAoC,KAAK,CAAC,CAACjB,MAAQA;IAEjB,IAAIe,qBAAqBG,OAAO;QAC9B,MAAMlB,MAAMe;QACZf,IAAIzB,IAAI,GAAG;QACX,MAAMyB;IACR;IAEA,MAAMmB,YAA8C;QAClD,GAAItC,aAAauC,6BAAmB,IAAI;YACtCpD,MAAMoD,6BAAmB,CAACvC,UAA8C;QAC1E,CAAC;QACD,GAAID,mBAAmBmC,UAAUM,KAAK,IAAI,QAAQN,UAAUO,MAAM,IAAI,OAClEP,YACA;YACEQ,OACE,sFAAsF;YACtF,uEAAuE;YACvE,+DAA+D;YAC/D1C,cAAc,SACdkC,UAAUM,KAAK,IAAI,QACnBN,UAAUO,MAAM,IAAI,OAChB,CAAC,EAAEP,UAAUM,KAAK,CAAC,CAAC,EAAEN,UAAUO,MAAM,CAAC,CAAC,GACxC;QACR,CAAC;IACP;IACA,IAAItD,SAAS,eAAeA,SAAS,WAAW;QAC9C,MAAMwD,UAAU9C,aAAI,CAACgB,IAAI,CACvBhB,aAAI,CAAC+C,OAAO,CAACrD,eACbI,eAAe;QAGjB,IAAIkD,IAAAA,cAAU,EAACF,UAAU;YACvBL,UAAUQ,GAAG,GAAG,MAAMC,YAAE,CAACC,QAAQ,CAACL,SAAS;QAC7C;IACF;IAEA,OAAO,CAAC;;;;sBAIY,EAAEb,KAAKC,SAAS,CAACO,WAAW;yCACT,EAAER,KAAKC,SAAS,CACnDpB,gBACA,gBAAgB,EAAEmB,KAAKC,SAAS,CAACtB,aAAa;;;;sBAI9B,EAAEqB,KAAKC,SAAS,CAAC5C,SAAS,YAAY,KAAKuB,WAAW;;GAEzE,CAAC;AACJ;AAEO,MAAM5B,MAAM;MACnB,WAAeC"}