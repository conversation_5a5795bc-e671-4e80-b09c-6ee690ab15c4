"""
UI Logger Service for broadcasting UI logs to frontend.

This module provides functionality to maintain connected SSE clients
and broadcast UI logs to them in real-time.
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional
from fastapi import Request
from datetime import datetime
import os
from dotenv import load_dotenv
load_dotenv()
# Redis for cross-worker pub/sub
import redis.asyncio as aioredis
ENVIRONMENT = os.getenv('ENVIRONMENT')
# Redis connection URL – can be configured via env var
if ENVIRONMENT == 'production':
    REDIS_URL = os.getenv('REDIS_URL_PROD')
else:
    REDIS_URL = os.getenv('REDIS_URL_DEV')
from Core.ui_logger import set_ui_logger_service

# Configure logging
logger = logging.getLogger("ui_logger_service")

class UILoggerService:
    """
    Service for managing UI log broadcasting to connected clients.
    
    This service maintains a list of connected SSE clients and provides
    methods to broadcast log messages to them.
    """
    
    # Singleton instance
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """Get the singleton instance of UILoggerService"""
        if cls._instance is None:
            cls._instance = UILoggerService()
            # Set the service in the UI logger module for broadcasting
            set_ui_logger_service(cls._instance)
            logger.info("UI Logger Service registered with UI logger")
        return cls._instance
    
    def __init__(self):
        """Initialize with empty client list; queue and loop will be set when the app starts"""
        self.clients: List[Dict[str, Any]] = []
        self.log_queue = None  # will be created in the event loop via start_background_tasks
        self.loop = None       # will be set to the running event loop in start_background_tasks
        self.background_task = None
        
    def start_background_tasks(self, app):
        """Start background tasks when the application starts and bind to the running event loop"""
        # Capture the running loop so that logs can be scheduled onto it
        self.loop = asyncio.get_running_loop()
        # Create a fresh queue bound to this loop
        self.log_queue = asyncio.Queue()
        # Start task to forward local log_queue to connected clients
        self.background_task = self.loop.create_task(self._fan_out_local_queue())

        # Start Redis pub/sub listener so that logs from ALL workers are delivered
        self.loop.create_task(self._start_redis_listener())

        # Add callback to stop background task when app shuts down
        @app.on_event("shutdown")
        async def shutdown_event():
            if self.background_task:
                self.background_task.cancel()
            if hasattr(self, "redis"):
                await self.redis.close()
                
    # ------------------------------------------------------------------
    # Redis integration helpers
    # ------------------------------------------------------------------

    async def _start_redis_listener(self):
        """Background task: subscribe to Redis channel and forward to local queue."""
        try:
            self.redis = aioredis.from_url(REDIS_URL, decode_responses=True)
            pubsub = self.redis.pubsub()
            # Subscribe to both channels: ui_logs for normal logs and ui_attach for session-attachment updates
            await pubsub.subscribe("ui_logs", "ui_attach")

            async for msg in pubsub.listen():
                if msg["type"] != "message":
                    continue
                channel = msg.get("channel")
                try:
                    payload = json.loads(msg["data"])
                    if channel == "ui_logs":
                        # Forward log payload to the local fan-out queue
                        await self.log_queue.put(payload)
                    elif channel == "ui_attach":
                        # Apply session attachment broadcast originating from another worker
                        user_id = payload.get("user_id")
                        session_id = payload.get("session_id")
                        if user_id and session_id:
                            self._attach_session_to_user_local(user_id, session_id)
                except Exception as e:
                    logger.warning(f"Failed to process Redis message on {channel}: {e}")
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Redis listener stopped unexpectedly: {e}")

    # ------------------------------------------------------------------
    # Local fan-out
    # ------------------------------------------------------------------
    async def _fan_out_local_queue(self):
        """Take items from self.log_queue and forward to connected clients."""
        try:
            while True:
                log_data = await self.log_queue.get()

                session_id = log_data.get("session_id")
                for client in self.clients[:]:
                    try:
                        client_user_id = client.get("user_id")
                        client_session_id = client.get("session_id")

                        # Delivery rules:
                        # 1. If log has no session_id (general message) deliver to everyone.
                        # 2. If client has session_id attached, ensure it matches.
                        # 3. If client has no session_id yet, fall back to user_id match so that they still
                        #    receive messages that belong to them before attachment is completed.
                        if (
                            not session_id
                            or client_session_id == session_id
                            or (
                                client_session_id is None
                                and session_id
                                and client_user_id == log_data.get("user_id")
                            )
                        ):
                            # Non-blocking send; if the queue is full drop the oldest item first
                            try:
                                client["queue"].put_nowait(log_data)
                            except asyncio.QueueFull:
                                try:
                                    # Remove oldest to make space
                                    client["queue"].get_nowait()
                                except Exception:
                                    pass  # queue might have been cleared concurrently
                                try:
                                    client["queue"].put_nowait(log_data)
                                except asyncio.QueueFull:
                                    # If it is still full, give up for this client
                                    pass
                    except Exception as e:
                        logger.warning(f"Error sending to client: {e}")
                        try:
                            self.clients.remove(client)
                        except ValueError:
                            pass

                self.log_queue.task_done()
        except asyncio.CancelledError:
            logger.info("Fan-out task cancelled")
        except Exception as e:
            logger.error(f"Error in fan-out task: {e}")
    
    async def add_client(self, request: Request, user_id: Optional[str] = None, session_id: Optional[str] = None, is_admin: bool = False):
        """
        Add a new client to the subscribers list.
        
        Args:
            request: FastAPI request object
            user_id: Optional user ID for authenticated clients
            session_id: Optional session ID for the client
            is_admin: Whether the client is an admin user
            
        Returns:
            Queue: A queue for sending messages to this client
        """
        # Create a bounded queue for this client – prevents unbounded memory growth if
        # the browser stops reading. When full we will drop the oldest entry.
        client_queue = asyncio.Queue(maxsize=int(os.getenv("UI_LOGGER_QUEUE_MAXSIZE", "500")))
        
        # Add client info to the list
        client_info = {
            "queue": client_queue,
            "request": request,
            "connected_at": datetime.now().isoformat(),
            "user_id": user_id,
            "session_id": session_id,
            "is_admin": is_admin
        }
        self.clients.append(client_info)
        
        logger.info(f"Client connected. User ID: {user_id or 'anonymous'}, Total clients: {len(self.clients)}")
        return client_queue
        
    def remove_client(self, client_queue):
        """
        Remove a client when they disconnect.
        
        Args:
            client_queue: The queue associated with the client
        """
        for client in self.clients[:]:
            if client["queue"] == client_queue:
                user_id = client.get("user_id", "anonymous")
                self.clients.remove(client)
                logger.info(f"Client disconnected. User ID: {user_id}, Remaining clients: {len(self.clients)}")
                break
    
    async def broadcast_ui_log(self, log_data):
        """
        Add a UI log to the broadcast queue.
        
        Args:
            log_data: The log data to broadcast
        """
        """Publish log to Redis so every worker receives it."""
        try:
            if not hasattr(self, "redis"):
                # Lazy init if not started yet
                self.redis = aioredis.from_url(REDIS_URL)
            await self.redis.publish("ui_logs", json.dumps(log_data))
        except Exception as e:
            logger.warning(f"Failed to publish log to Redis: {e}")
            # Fallback to local queue so at least local clients receive it
            await self.log_queue.put(log_data)
    
    def get_client_count(self):
        """Get the number of connected clients"""
        return len(self.clients) 

    def _attach_session_to_user_local(self, user_id: str, session_id: str):
        """Helper that attaches a session_id to all queues belonging to the user, within this worker."""
        for client in self.clients:
            if client.get("user_id") == user_id:
                client["session_id"] = session_id

    # ------------------------------------------------------------------
    # Public API helpers
    # ------------------------------------------------------------------
    async def attach_session_to_user(self, user_id: str, session_id: str):
        """Attach a session_id to all active SSE clients for a given user.

        If the client lives in the same worker the update is applied
        immediately; we also publish an instruction on Redis so that
        other workers can update their in-memory state too.
        """
        # Local update first
        self._attach_session_to_user_local(user_id, session_id)

        # Broadcast to all other workers so they update as well
        try:
            if not hasattr(self, "redis"):
                self.redis = aioredis.from_url(REDIS_URL)
            await self.redis.publish("ui_attach", json.dumps({"user_id": user_id, "session_id": session_id}))
        except Exception as e:
            logger.warning(f"Failed to broadcast session attach: {e}") 

    async def debug_log(self, msg: str):
        logger.debug(f"[UILoggerService] {msg}") 