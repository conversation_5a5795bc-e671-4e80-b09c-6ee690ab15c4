import { useEffect, useRef, useState } from 'react';

interface Options {
  /** called when new data arrives */
  onMessage?: (data: any) => void;
  /** called when the connection is opened */
  onOpen?: () => void;
  /** called when the connection is permanently closed */
  onError?: (ev: Event) => void;
}

/**
 * useUILogger keeps one persistent SSE connection and allows later attachment
 * of a booking session via a lightweight REST call. This avoids dropping the
 * EventSource whenever the session_id becomes available.
 *
 * Usage:
 *   const { data, connected } = useUILogger(token, sessionId, { onMessage })
 */
export function useUILogger(
  jwtToken: string | null,
  sessionId: string | null,
  options: Options = {}
) {
  const eventSourceRef = useRef<EventSource | null>(null);
  const [connected, setConnected] = useState(false);

  // Open SSE connection once – it never depends on sessionId
  useEffect(() => {
    if (!jwtToken || eventSourceRef.current) return;

    const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || '';
    const url = `${apiBaseUrl}/ui-logs/stream?token=${encodeURIComponent(jwtToken)}`;
    const es = new EventSource(url);
    eventSourceRef.current = es;

    es.onopen = () => {
      setConnected(true);
      options.onOpen?.();
    };

    es.onmessage = (ev) => {
      try {
        const parsed = JSON.parse(ev.data);
        options.onMessage?.(parsed);
      } catch (e) {
        console.error('Failed to parse SSE data', e);
      }
    };

    es.onerror = (ev) => {
      console.error('SSE error', ev);
      setConnected(false);
      options.onError?.(ev);
    };

    return () => {
      es.close();
      setConnected(false);
      eventSourceRef.current = null;
    };
  }, [jwtToken]);

  // Whenever sessionId becomes available send attach request (once)
  useEffect(() => {
    if (!jwtToken || !sessionId) return;

    /* Build the same base URL we already use for normal REST calls
       1. If NEXT_PUBLIC_API_URL is defined we reuse it.
       2. Otherwise we fall back to '/api' so the call is routed through
          the existing Nginx /api → backend mapping. */
    const root = (process.env.NEXT_PUBLIC_API_URL || '').replace(/\/$/, '');
    const attachUrl = `${root}/api/ui-logs/attach-session`;

    fetch(attachUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${jwtToken}`,
      },
      body: JSON.stringify({ session_id: sessionId }),
    }).catch((e) => console.error('Failed to attach session to SSE', e));
  }, [jwtToken, sessionId]);

  return { connected };
}