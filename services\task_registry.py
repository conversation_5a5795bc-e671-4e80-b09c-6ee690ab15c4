"""
Task Registry for managing active booking sessions.

This module provides a centralized registry to track and cancel active booking tasks
by session ID, enabling proper cleanup when users reset bookings.
"""

import asyncio
import logging
from typing import Dict, Optional
from datetime import datetime

logger = logging.getLogger("services.task_registry")


class BookingTaskRegistry:
    """
    Singleton registry for tracking active booking tasks by session ID.
    
    This allows the system to cancel running booking processes when users
    reset bookings, ensuring proper cleanup of resources.
    """
    
    _instance = None
    _tasks: Dict[str, asyncio.Task] = {}
    _task_metadata: Dict[str, Dict] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> 'BookingTaskRegistry':
        """Get the singleton instance of the task registry."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def register_task(self, session_id: str, task: asyncio.Task, provider: str = None) -> None:
        """
        Register a booking task for a session.
        
        Args:
            session_id: Unique session identifier
            task: The asyncio task to register
            provider: Optional provider name for logging
        """
        self._tasks[session_id] = task
        self._task_metadata[session_id] = {
            'provider': provider,
            'registered_at': datetime.now(),
            'status': 'running'
        }
        logger.info(f"Registered task for session {session_id} (provider: {provider})")

        # Ensure the cross-worker cancellation listener is running
        try:
            loop = asyncio.get_running_loop()
            if not hasattr(self, '_listener_task') or self._listener_task is None or self._listener_task.done():
                self._listener_task = loop.create_task(self._listen_for_cancellations())
        except RuntimeError:
            # No running loop (should not happen inside FastAPI request)
            pass
    
    def get_task(self, session_id: str) -> Optional[asyncio.Task]:
        """
        Get the task for a session ID.
        
        Args:
            session_id: Session identifier
            
        Returns:
            The asyncio task if found, None otherwise
        """
        return self._tasks.get(session_id)
    
    def get_task_metadata(self, session_id: str) -> Optional[Dict]:
        """
        Get metadata for a task.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Task metadata if found, None otherwise
        """
        return self._task_metadata.get(session_id)
    
    async def cancel_task(self, session_id: str, timeout: float = 5.0) -> bool:
        """
        Cancel a booking task for a session.
        
        Args:
            session_id: Session identifier
            timeout: Maximum time to wait for cancellation
            
        Returns:
            True if task was cancelled successfully, False otherwise
        """
        task = self._tasks.get(session_id)
        if not task:
            logger.warning(f"No task found for session {session_id}")
            return False
        
        if task.done():
            logger.info(f"Task for session {session_id} already completed")
            self._cleanup_session(session_id)
            return True
        
        try:
            logger.info(f"Cancelling task for session {session_id}")
            task.cancel()
            
            # Wait for the task to be cancelled with timeout
            try:
                await asyncio.wait_for(task, timeout=timeout)
            except asyncio.TimeoutError:
                logger.warning(f"Task cancellation timed out for session {session_id}")
            except asyncio.CancelledError:
                logger.info(f"Task successfully cancelled for session {session_id}")
            
            self._cleanup_session(session_id)
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling task for session {session_id}: {e}")
            return False
    
    def _cleanup_session(self, session_id: str) -> None:
        """Clean up session data from registries."""
        self._tasks.pop(session_id, None)
        if session_id in self._task_metadata:
            self._task_metadata[session_id]['status'] = 'cleaned_up'
            # Keep metadata for a short time for debugging
            self._task_metadata.pop(session_id, None)
    
    def get_active_sessions(self) -> Dict[str, Dict]:
        """
        Get information about all active sessions.
        
        Returns:
            Dictionary of session_id -> metadata
        """
        active = {}
        for session_id, task in self._tasks.items():
            if not task.done():
                active[session_id] = self._task_metadata.get(session_id, {})
        return active
    
    def cleanup_completed_tasks(self) -> int:
        """
        Clean up completed tasks from the registry.
        
        Returns:
            Number of tasks cleaned up
        """
        completed_sessions = []
        for session_id, task in self._tasks.items():
            if task.done():
                completed_sessions.append(session_id)
        
        for session_id in completed_sessions:
            self._cleanup_session(session_id)
        
        if completed_sessions:
            logger.info(f"Cleaned up {len(completed_sessions)} completed tasks")
        
        return len(completed_sessions)


    async def _listen_for_cancellations(self) -> None:
        """Background task to listen for cross-worker cancellation events via Redis."""
        try:
            from services.redis_service import redis_service  # Local import to avoid unnecessary dependency if Redis disabled
            redis = await redis_service.get_redis()
            # Use a dedicated pub/sub channel for booking cancellations
            pubsub = redis.pubsub()
            await pubsub.subscribe("booking_cancel")
            logger.info("Subscribed to Redis channel 'booking_cancel' for cross-worker cancellations")

            async for message in pubsub.listen():
                # aioredis returns messages with 'type' field
                if message is None or message.get("type") != "message":
                    continue
                session_id_bytes = message.get("data")
                if not session_id_bytes:
                    continue
                try:
                    session_id = session_id_bytes.decode() if isinstance(session_id_bytes, (bytes, bytearray)) else str(session_id_bytes)
                except Exception:
                    session_id = str(session_id_bytes)
                logger.info(f"Received cross-worker cancellation request for session {session_id}")
                # Attempt to cancel locally if we own the task
                await self.cancel_task(session_id, timeout=0)
        except asyncio.CancelledError:
            # Graceful shutdown
            logger.info("Cancellation listener task cancelled - shutting down")
        except Exception as e:
            logger.error(f"Redis cancellation listener encountered an error: {e}")

    async def broadcast_cancel(self, session_id: str) -> None:
        """Publish a cancellation request so all workers attempt to cancel their local tasks."""
        try:
            from services.redis_service import redis_service
            redis = await redis_service.get_redis()
            await redis.publish("booking_cancel", session_id)
            logger.info(f"Broadcasted cancellation request for session {session_id} to all workers")
        except Exception as e:
            logger.error(f"Failed to broadcast cancellation for session {session_id}: {e}")

# Global instance
task_registry = BookingTaskRegistry.get_instance()