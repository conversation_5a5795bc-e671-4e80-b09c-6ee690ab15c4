from playwright.async_api import Page, expect
from Cruising_Power.screenshot_utils import take_scrolling_screenshot
import re
import asyncio
import os
import json
from playwright.async_api import TimeoutError, Error
from Core.ui_logger import ui_log

from loguru import logger


class CabinSelector:
    """
    Class for handling cabin selection functionality
    """
    def __init__(self, page, cabin_type=None, log_dir=None):
        """
        Initialize the cabin selector
        
        Args:
            page: Playwright Page instance
            cabin_type: The cabin type to select (e.g., "Suite", "Veranda", etc.) - Optional now
            log_dir: Kept for backward compatibility but not used
        """
        self.page = page
        self.cabin_type = cabin_type
        # We don't store log_dir anymore as we don't use it
        self.logger = self._setup_logger()
        self.request_id = None
        self.cabin_id = None
        self.session_id = None
        
        # Define predefined category mappings similar to NCL
        self.PREDEFINED_MAPPINGS = {
            'suite': 'suite',
            'veranda': 'veranda',
            'balcony': 'veranda',
            'outside': 'outside',
            'oceanview': 'outside',
            'ocean view': 'outside',
            'interior': 'interior',
            'inside': 'interior',
            'window': 'outside'
        }
    
    def _setup_logger(self):
        """
        Set up and configure the logger
        
        Returns:
            Configured logger instance
        """
        # Using loguru logger directly - no file handlers needed, only console
        return logger
    
    async def extract_all_cabin_data(self):
        """
        Extract data from all cabins across all tables regardless of category.
        
        Returns:
            dict: The extracted cabin data or error information
        """
        self.logger.info("Extracting all cabin data from tables...")
        ui_log("Analyzing cabin options — please hold on", session_id=self.session_id, cabin_id=self.cabin_id,step="evaluating_cabin_options", module="Cruising_Power")
        
        # Extremely simple JavaScript approach to get the raw table data
        try:
            # First check if the table exists
            table_exists = await self.page.evaluate("""
                !!document.getElementById('catAvailCategoryList')
            """)
            
            if not table_exists:
                self.logger.error("Table not found")
                return {"error": "Table not found"}
            
            # Get basic table structure
            rows_count = await self.page.evaluate("""
                document.querySelectorAll('#catAvailCategoryList tbody tr').length
            """)
            
            self.logger.info(f"Found {rows_count} rows in table")
            
            # Process each row one by one to avoid complex JavaScript
            cabins = []
            available_cabins = []
            gty_avl_cabins = []
            
            for i in range(rows_count):
                try:
                    # Get basic row data
                    row_data = await self.page.evaluate(f"""
                        (function() {{
                            const row = document.querySelectorAll('#catAvailCategoryList tbody tr')[{i}];
                            if (!row) return null;
                            
                            const cells = row.querySelectorAll('td');
                            if (!cells.length) return null;
                            
                            return {{
                                rowIndex: {i},
                                rowClass: row.className || "",
                                cabinType: cells[0] ? cells[0].textContent.trim() : "",
                                availability: cells[1] ? cells[1].textContent.trim() : "",
                                fareType: cells[2] ? cells[2].textContent.trim() : "",
                                priceText: cells[3] ? cells[3].textContent.trim() : ""
                            }};
                        }})()
                    """)
                    
                    if not row_data:
                        continue
                    
                    # Add computed fields
                    row_data["isAvailable"] = "AVL" in row_data["availability"] or "WLT" in row_data["availability"]
                    
                    # Extract price manually
                    price_text = row_data["priceText"]
                    numeric_part = "".join([c for c in price_text if c.isdigit() or c == '.'])
                    try:
                        row_data["price"] = float(numeric_part) if numeric_part else 0
                    except ValueError:
                        row_data["price"] = 0
                    
                    # Extract category code if possible
                    if row_data["rowClass"]:
                        class_words = row_data["rowClass"].split()
                        for word in class_words:
                            if len(word) >= 2 and len(word) <= 4 and word.isupper():
                                row_data["categoryCode"] = word
                                break
                        else:
                            row_data["categoryCode"] = ""
                    else:
                        row_data["categoryCode"] = ""
                    
                    # Add to cabins list
                    cabins.append(row_data)
                    
                    # Check if the cabin has "GTY" or "AVL" in its fareType
                    has_gty_or_avl = "GTY" in row_data["fareType"] or "AVL" in row_data["fareType"]
                    
                    # Add to available cabins if available
                    if row_data["isAvailable"] and row_data["price"] > 0:
                        available_cabins.append(row_data)
                    
                    # Add to GTY/AVL cabins list if it has GTY or AVL
                    if has_gty_or_avl and row_data["price"] > 0:
                        gty_avl_cabins.append(row_data)
                        
                except Exception as e:
                    self.logger.error(f"Error processing row {i}: {e}")
            
            # Sort available cabins by price
            available_cabins.sort(key=lambda x: x["price"])
            
            # Sort GTY/AVL cabins by price
            gty_avl_cabins.sort(key=lambda x: x["price"])
            
            result = {
                "totalFound": len(cabins),
                "availableCabins": len(gty_avl_cabins),  # Changed from available_cabins to gty_avl_cabins
                "cabins": gty_avl_cabins,  # Changed from cabins to gty_avl_cabins
                "cheapestCabins": gty_avl_cabins[:5] if gty_avl_cabins else []  # Changed from available_cabins to gty_avl_cabins
            }
            
            # Print some summary information
            self.logger.info(f"Found {len(cabins)} total cabins, {len(gty_avl_cabins)} with GTY/AVL fare types")
            
            if gty_avl_cabins:
                self.logger.info(f"Cheapest GTY/AVL cabin: {gty_avl_cabins[0]['cabinType']} at {gty_avl_cabins[0]['priceText']}")
                #ui_log(f"Found {len(gty_avl_cabins)} available cabins", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing cabin data extraction: {e}")
            return {"error": str(e)}
            
    async def print_cabin_table(self):
        """
        Print the full cabin table data for analysis
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info("Printing cabin table data...")
            
            # Extract all cabin data
            cabin_data = await self.extract_all_cabin_data()
            
            if "error" in cabin_data:
                self.logger.error(f"Failed to extract cabin data: {cabin_data['error']}")
                return False
                
            #ui_log(f"Showing {cabin_data['totalFound']} cabin options", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            
            # Print detailed information about each cabin
            # self.logger.info("\n=== CABIN TABLE DATA ===")
            # self.logger.info(f"Total cabins: {cabin_data['totalFound']}")
            # self.logger.info(f"Available cabins: {cabin_data['availableCabins']}")
            
            # # Print header
            # header = "Category | Cabin Type | Availability | Fare Type | Price | Available"
            # separator = "-" * len(header)
            # self.logger.info(separator)
            # self.logger.info(header)
            # self.logger.info(separator)
            
            # # Print each cabin's data
            # for cabin in cabin_data['cabins']:
            #     availability_status = "✓" if cabin['isAvailable'] else "✗"
            #     self.logger.info(f"{cabin['categoryCode']:8} | {cabin['cabinType']:15} | {cabin['availability']:12} | {cabin['fareType']:9} | {cabin['priceText']:10} | {availability_status:9}")
            
            return True
        except Exception as e:
            self.logger.error(f"Error printing cabin table: {e}")
            return False
        
    async def select_cheapest(self):
        """
        Finds and clicks on the cheapest available cabin with GTY or AVL fare type,
        prioritizing the user's specified cabin type or cabin code if provided.
        
        Returns:
            tuple: (success, message)
                success (bool): True if the operation was successful
                message (str): Information about the selected cabin or error message
        """
        try:
            category_selected = False
            search_all_categories = False
            
            # Check if we have a specific cabin code to search for
            if self.cabin_type and self.cabin_type.startswith("CABIN_CODE:"):
                cabin_code = self.cabin_type.replace("CABIN_CODE:", "")
                self.logger.info(f"\nLooking for cheapest available cabin with cabin code '{cabin_code}' and GTY or AVL fare type...")
                ui_log(f"Looking for the cheapest {cabin_code} cabin — kindly stand by", session_id=self.session_id, cabin_id=self.cabin_id, step="finding_cheapest_cabin_code", module="Cruising_Power")
                
                # For cabin codes, we need to search across all categories
                category_selected = await self.select_category(self.cabin_type)
                if not category_selected:
                    self.logger.warning(f"Could not select All Categories for cabin code search. Will try without category selection.")
            elif self.cabin_type:
                # Check if it's a predefined mapping first
                cabin_type_lower = self.cabin_type.lower().strip()
                
                if cabin_type_lower in self.PREDEFINED_MAPPINGS:
                    # Use predefined mapping
                    mapped_category = self.PREDEFINED_MAPPINGS[cabin_type_lower]
                    self.logger.info(f"Found '{self.cabin_type}' in predefined mappings, using '{mapped_category}' category")
                    ui_log(f"Looking for the cheapest {mapped_category} cabin — kindly stand by", session_id=self.session_id, cabin_id=self.cabin_id, step="finding_cheapest_cabin", module="Cruising_Power")
                    
                    # Select the mapped category
                    category_selected = await self.select_category(mapped_category)
                    if not category_selected:
                        self.logger.warning(f"Could not select {mapped_category} category. Will try all categories instead.")
                        search_all_categories = True
                else:
                    # Not in predefined mappings, search by name across all categories
                    self.logger.info(f"'{self.cabin_type}' not found in predefined mappings, will search by name across all categories")
                    ui_log(f"Searching for '{self.cabin_type}' cabin — kindly stand by", session_id=self.session_id, cabin_id=self.cabin_id, step="searching_cabin_by_name", module="Cruising_Power")
                    search_all_categories = True
            else:
                self.logger.info("\nLooking for cheapest available cabin with GTY or AVL fare type across all categories...")
                ui_log("Looking for the cheapest cabin across all categories — Kindly stand by", session_id=self.session_id, cabin_id=self.cabin_id, step="finding_cheapest_cabin_all_categories", module="Cruising_Power")
                search_all_categories = True
            
            # If we need to search all categories
            if search_all_categories:
                await self.select_all_categories()
            
            # Wait for the results table to be present with reduced timeout
            self.logger.info("Waiting for results table to load...")
            try:
                # Reduced timeout from 30 to 15 seconds
                await self.page.wait_for_selector("#catAvailCategoryList", timeout=15000)
            except Exception:
                self.logger.error("Results table not found. Trying alternate method to find cabins...")
                return False, "Results table not found"
            
            # Extract all cabin data
            cabin_data = await self.extract_all_cabin_data()
            
            if "error" in cabin_data:
                self.logger.error(f"Failed to extract cabin data: {cabin_data['error']}")
                ui_log("Failed to find cabin options — retrying now", session_id=self.session_id, cabin_id=self.cabin_id, step="retrying_cabin_search", module="Cruising_Power")
                return False, f"Failed to extract cabin data: {cabin_data['error']}"
                
            # Get cabins - extract_all_cabin_data now only returns cabins with GTY or AVL fare types
            available_cabins = cabin_data['cabins']
            
            # Filter by cabin code if provided
            if self.cabin_type and self.cabin_type.startswith("CABIN_CODE:"):
                cabin_code = self.cabin_type.replace("CABIN_CODE:", "")
                self.logger.info(f"Filtering cabins by cabin code: {cabin_code}")
                
                # Filter available cabins to only include those with the specified cabin code
                code_matching_cabins = []
                
                # Try exact match first
                for cabin in available_cabins:
                    cabin_type_text = cabin.get('cabinType', '').upper()
                    category_code = cabin.get('categoryCode', '').upper()
                    
                    # Check for exact match in category code or cabin type
                    if (cabin_code.upper() == category_code or 
                        cabin_code.upper() == cabin_type_text):
                        code_matching_cabins.append(cabin)
                        self.logger.info(f"Found exact match cabin: {cabin_type_text} (Code: {category_code})")
                
                # If no exact match, try prefix match
                if not code_matching_cabins:
                    self.logger.info(f"No exact match found for {cabin_code}, trying prefix match...")
                    for cabin in available_cabins:
                        cabin_type_text = cabin.get('cabinType', '').upper()
                        category_code = cabin.get('categoryCode', '').upper()
                        
                        # Check for prefix match
                        if (category_code.startswith(cabin_code.upper()) or 
                            cabin_type_text.startswith(cabin_code.upper())):
                            code_matching_cabins.append(cabin)
                            self.logger.info(f"Found prefix match cabin: {cabin_type_text} (Code: {category_code})")
                
                # If still no match, try contains match
                if not code_matching_cabins:
                    self.logger.info(f"No prefix match found for {cabin_code}, trying contains match...")
                    for cabin in available_cabins:
                        cabin_type_text = cabin.get('cabinType', '').upper()
                        category_code = cabin.get('categoryCode', '').upper()
                        
                        # Check for contains match
                        if (cabin_code.upper() in category_code or 
                            cabin_code.upper() in cabin_type_text):
                            code_matching_cabins.append(cabin)
                            self.logger.info(f"Found contains match cabin: {cabin_type_text} (Code: {category_code})")
                
                if code_matching_cabins:
                    available_cabins = code_matching_cabins
                    self.logger.info(f"Found {len(available_cabins)} cabins matching cabin code '{cabin_code}'")
                else:
                    self.logger.warning(f"No available cabins found with cabin code '{cabin_code}' - all may be closed (CLS) or on waitlist (WLT)")
                    ui_log(f"No available {cabin_code} cabins found — all may be closed or on waitlist", session_id=self.session_id, cabin_id=self.cabin_id, step="no_matching_cabins", module="Cruising_Power")
                    return False, f"No available cabins found with cabin code '{cabin_code}' - all may be closed (CLS) or on waitlist (WLT)"
            
            # If searching by name (not predefined or cabin code)
            elif self.cabin_type and self.cabin_type.lower().strip() not in self.PREDEFINED_MAPPINGS:
                # Use name-based search
                self.logger.info(f"Starting name-based search for '{self.cabin_type}'")
                matching_cabins = await self.find_cabin_by_name(available_cabins, self.cabin_type)
                if matching_cabins:
                    available_cabins = matching_cabins
                    self.logger.info(f"Found {len(available_cabins)} cabins matching '{self.cabin_type}': {[cabin.get('cabinType', 'Unknown') for cabin in available_cabins]}")
                else:
                    self.logger.warning(f"No cabins found matching '{self.cabin_type}', using all available cabins")
            
            if not available_cabins:
                # If we were looking in a specific category and found nothing, try all categories
                if self.cabin_type and not self.cabin_type.startswith("CABIN_CODE:") and not search_all_categories:
                    self.logger.warning(f"No available cabins with GTY or AVL fare type found. Trying all categories...")
                    # Reset to all categories
                    await self.select_all_categories()
                    
                    # Extract data again
                    cabin_data = await self.extract_all_cabin_data()
                    if "error" in cabin_data:
                        self.logger.error(f"Failed to extract cabin data: {cabin_data['error']}")
                        return False, f"Failed to extract cabin data: {cabin_data['error']}"
                    
                    available_cabins = cabin_data['cabins']
                    
                    # If searching by name, filter again
                    if self.cabin_type and self.cabin_type.lower().strip() not in self.PREDEFINED_MAPPINGS:
                        matching_cabins = await self.find_cabin_by_name(available_cabins, self.cabin_type)
                        if matching_cabins:
                            available_cabins = matching_cabins
                    
                    if not available_cabins:
                        self.logger.error("No available cabins found in any category. All cabins may be closed (CLS) or on waitlist (WLT)")
                        ui_log("No available cabins found — all cabins are either closed or on waitlist", session_id=self.session_id, cabin_id=self.cabin_id, step="no_available_cabins", module="Cruising_Power")
                        return False, "No available cabins found. All cabins may be closed (CLS) or on waitlist (WLT)"
                else:
                    self.logger.error("No available cabins found. All cabins may be closed (CLS) or on waitlist (WLT)")
                    ui_log("No available cabins found — all cabins are either closed or on waitlist", session_id=self.session_id, cabin_id=self.cabin_id, step="no_available_cabins", module="Cruising_Power")
                    return False, "No available cabins found. All cabins may be closed (CLS) or on waitlist (WLT)"
                
            # Get the cheapest cabin (already sorted by price in extract_all_cabin_data or find_cabin_by_name)
            cheapest_cabin = available_cabins[0]
            
            if self.cabin_type and self.cabin_type.startswith("CABIN_CODE:"):
                cabin_code = self.cabin_type.replace("CABIN_CODE:", "")
                self.logger.info(f"Found cheapest cabin with cabin code '{cabin_code}' and GTY/AVL fare type: {cheapest_cabin['cabinType']} at {cheapest_cabin['priceText']}")
            elif self.cabin_type:
                category_type = "NAME_SEARCH" if self.cabin_type.lower().strip() not in self.PREDEFINED_MAPPINGS else "PREDEFINED"
                self.logger.info(f"Found cheapest cabin with GTY/AVL fare type in {category_type}:{self.cabin_type} category: {cheapest_cabin['cabinType']} at {cheapest_cabin['priceText']}")
            else:
                self.logger.info(f"Found cheapest cabin with GTY/AVL fare type: {cheapest_cabin['cabinType']} at {cheapest_cabin['priceText']}")
            
            # Select the cheapest cabin using its row index
            select_script = f"""
            (function() {{
                try {{
                    const tbody = document.querySelector("#catAvailCategoryList > tbody");
                    if (!tbody) return false;
                    
                    const rows = tbody.querySelectorAll("tr");
                    if (!rows || !rows[{cheapest_cabin['rowIndex']}]) return false;
                    
                    const row = rows[{cheapest_cabin['rowIndex']}];
                    
                    // Try to find the radio button first
                    const radioInput = row.querySelector("input[type='radio']");
                    if (radioInput) {{
                        radioInput.click();
                        return true;
                    }}
                    
                    // Otherwise click the row itself
                    row.click();
                    return true;
                }} catch (e) {{
                    console.error("Error selecting cabin by index:", e);
                    return false;
                }}
            }})();
            """
            
            selection_success = await self.page.evaluate(select_script)
            
            if not selection_success:
                self.logger.error(f"Failed to select cabin at row index {cheapest_cabin['rowIndex']}")
                ui_log("Failed to select cabin — retrying now", session_id=self.session_id, cabin_id=self.cabin_id, step="retrying_cabin_selection", module="Cruising_Power")
                return False, f"Failed to select cabin at row index {cheapest_cabin['rowIndex']}"
                
            self.logger.info(f"Successfully selected cabin at row index {cheapest_cabin['rowIndex']}")
            
            # Look for the Continue button with shorter timeout
            try:
                self.logger.info("Looking for Continue button...")
                continue_button = await self.page.query_selector("a.button.alertable.getQuote")
                
                if continue_button:
                    self.logger.info("Found Continue button, clicking...")
                    await continue_button.click()
                else:
                    self.logger.warning("Continue button not found - selection may still have worked")
            except Exception as button_error:
                self.logger.warning(f"Error clicking Continue button: {button_error}")
            
            if self.cabin_type and self.cabin_type.startswith("CABIN_CODE:"):
                cabin_code = self.cabin_type.replace("CABIN_CODE:", "")
                category_msg = f" matching cabin code '{cabin_code}'"
            elif self.cabin_type:
                if self.cabin_type.lower().strip() in self.PREDEFINED_MAPPINGS:
                    category_msg = f" in {self.cabin_type} category"
                else:
                    category_msg = f" matching '{self.cabin_type}'"
            else:
                category_msg = ""
            
            message = f"Successfully selected cheapest {cheapest_cabin['cabinType']} cabin{category_msg} with {cheapest_cabin['fareType']} fare type at {cheapest_cabin['priceText']}"
            ui_log("Cabin selection complete — moving forward", session_id=self.session_id, cabin_id=self.cabin_id, step="cabin_selected", module="Cruising_Power")
            return True, message
            
        except Exception as e:
            self.logger.error(f"Error during cabin selection process: {e}")
            ui_log("Error selecting cabin — retrying now", session_id=self.session_id, cabin_id=self.cabin_id, step="error_selecting_cabin", module="Cruising_Power")
            return False, f"Error during cabin selection process: {e}"

    # Keep the select_cheapest_js method but update it to use the new approach
    async def select_cheapest_js(self):
        """
        Uses JavaScript to directly select the cheapest available cabin across all categories.
        
        Returns:
            tuple: (success, message)
                success (bool): True if the operation was successful
                message (str): Information about the selected cabin or error message
        """
        # This now just calls the main select_cheapest method since it already uses JavaScript
        return await self.select_cheapest()

    def map_cabin_type(self, cabin_type):
        """
        Maps the normalized cabin type to the appropriate value on the website,
        handling various naming conventions.
        
        Args:
            cabin_type: Normalized cabin type string
            
        Returns:
            List containing possible matching values for the dropdown
        """
        # Convert to lowercase for easier comparison
        cabin_type_lower = cabin_type.lower()
        
        # Only use exact matches for predefined categories
        # This prevents "Royal Suite" from being mapped to "Suite"
        if cabin_type_lower == "suite":
            return ["Suite", "Deluxe", "Suite Stateroom"]
        elif cabin_type_lower == "veranda":
            return ["Veranda", "Balcony", "Balcony Stateroom"]
        elif cabin_type_lower == "outside":
            return ["Outside", "Oceanview", "Ocean View", "Window"]
        elif cabin_type_lower == "interior":
            return ["Interior", "Inside"]
        else:
            # For non-predefined categories, return the original cabin type
            # This prevents incorrect partial matching
            return [cabin_type]
    
    async def find_matching_option(self, cabin_type):
        """
        Finds the option value in the dropdown that matches one of the possible cabin types.
        
        Args:
            cabin_type: Normalized cabin type
            
        Returns:
            The matching option value from the dropdown or the original cabin_type if no match
        """
        try:
            self.logger.info(f"Finding matching option for cabin type: {cabin_type}")
            
            # Get possible mappings for the cabin type
            possible_types = self.map_cabin_type(cabin_type)
            
            # Use JavaScript to get all options from the dropdown
            options_data = await self.page.evaluate("""
                () => {
                    const select = document.getElementById('categoryTypeFilter');
                    if (!select) return [];
                    
                    return Array.from(select.options).map(option => ({
                        text: option.text.trim(),
                        value: option.value
                    }));
                }
            """)
            
            # Check each option against our possible types
            for option in options_data:
                option_text = option['text']
                option_value = option['value']
                
                # Check if this option matches any of our possible types
                for possible_type in possible_types:
                    # Only match if it's an exact match or the option is one of the predefined categories
                    # This prevents "Royal Suite" from matching "Suite"
                    if (possible_type.lower() == option_text.lower() or 
                        possible_type.lower() == option_value.lower() or
                        (possible_type.lower() in ["suite", "veranda", "outside", "interior"] and
                         possible_type.lower() in option_text.lower())):
                        self.logger.info(f"Found matching option: '{option_text}' with value: '{option_value}'")
                        return option_value
                        
            # If no match found, return the first option value or the original cabin_type
            if options_data and len(options_data) > 0:
                self.logger.info(f"No exact match found for {cabin_type}, using first option: {options_data[0]['value']}")
                return options_data[0]['value']
            
            self.logger.info(f"No options found, using original cabin type: {cabin_type}")
            return cabin_type
            
        except Exception as e:
            self.logger.error(f"Error finding matching option: {e}")
            return cabin_type
    
    async def select_category(self, cabin_type):
        """
        Select the appropriate cabin category in the search results
        
        Args:
            cabin_type: Normalized cabin type ("Suite", "Veranda", "Outside", "Interior", "All Categories", or "CABIN_CODE:XX")
        
        Returns:
            Boolean indicating success or failure
        """
        try:
            self.logger.info(f"\nSelecting cabin category: {cabin_type}")
            
            # Check if this is a cabin code request
            if cabin_type.startswith("CABIN_CODE:"):
                cabin_code = cabin_type.replace("CABIN_CODE:", "")
                self.logger.info(f"Direct cabin code requested: {cabin_code}")
                ui_log(f"Searching for cabin code {cabin_code} — please hold on", session_id=self.session_id, cabin_id=self.cabin_id, step="searching_cabin_code", module="Cruising_Power")
                
                # For cabin codes, we need to search across all categories
                # Set to "All Categories" first to see all available cabins
                return await self.select_all_categories()
            
            ui_log(f"Selecting {cabin_type} cabin category — please hold on", session_id=self.session_id, cabin_id=self.cabin_id, step="choosing_cabin_category", module="Cruising_Power")
            
            # Wait for results page to load
            self.logger.info("Waiting for category filter to be ready...")
            try:
                # Wait for the filter element
                await self.page.wait_for_selector("#categoryTypeFilter", timeout=8000)
            except Exception:
                self.logger.warning("Could not find category filter with explicit wait, using fallback...")
                
            # Find the matching option in the dropdown
            matching_value = await self.find_matching_option(cabin_type)
            self.logger.info(f"Using value '{matching_value}' for selection")
            
            # Try JavaScript selection first (most reliable)
            try:
                self.logger.info("Trying JavaScript approach...")
                script = f"""
                (function() {{
                    const select = document.getElementById('categoryTypeFilter');
                    if (select) {{
                        select.value = '{matching_value}';
                        const event = new Event('change', {{ bubbles: true }});
                        select.dispatchEvent(event);
                        return true;
                    }}
                    return false;
                }})()
                """
                result = await self.page.evaluate(script)
                
                if result:
                    self.logger.info(f"Successfully set category to {matching_value} using JavaScript")
                    return True
                else:
                    self.logger.warning("JavaScript selection returned false")
            except Exception as e:
                self.logger.warning(f"JavaScript selection failed: {e}")
            
            # Alternative approach: try select_option method
            try:
                self.logger.info("Trying Playwright select_option method...")
                await self.page.select_option("#categoryTypeFilter", value=matching_value)
                self.logger.info(f"Successfully selected {matching_value} using select_option")
                return True
            except Exception as e:
                self.logger.warning(f"Could not select using select_option: {e}")
            
            self.logger.error("All category selection approaches failed")
            ui_log(f"Failed to select {cabin_type} category — retrying now", session_id=self.session_id, cabin_id=self.cabin_id, step="failed_to_select_cabin_category", module="Cruising_Power")
            return False
                
        except Exception as e:
            self.logger.error(f"Error selecting category: {e}")
            ui_log("Error selecting category — retrying now", session_id=self.session_id, cabin_id=self.cabin_id, step="error_selecting_category", module="Cruising_Power")
            return False
    
    async def select_all_categories(self):
        """
        Select "All Categories" to show all available cabins
        
        Returns:
            Boolean indicating success or failure
        """
        try:
            self.logger.info("Selecting All Categories to show all cabins")
            
            # Wait for results page to load
            self.logger.info("Waiting for category filter to be ready...")
            try:
                await self.page.wait_for_selector("#categoryTypeFilter", timeout=8000)
            except Exception:
                self.logger.warning("Could not find category filter with explicit wait, using fallback...")
                
            # Try JavaScript selection first (most reliable)
            try:
                self.logger.info("Trying JavaScript approach to select All Categories...")
                script = """
                (function() {
                    const select = document.getElementById('categoryTypeFilter');
                    if (select) {
                        // Look for "All" or first option
                        for (let i = 0; i < select.options.length; i++) {
                            const option = select.options[i];
                            if (option.text.toLowerCase().includes('all') || i === 0) {
                                select.value = option.value;
                                const event = new Event('change', { bubbles: true });
                                select.dispatchEvent(event);
                                return true;
                            }
                        }
                    }
                    return false;
                })()
                """
                result = await self.page.evaluate(script)
                
                if result:
                    self.logger.info("Successfully set category to All Categories using JavaScript")
                    return True
                else:
                    self.logger.warning("JavaScript selection returned false")
            except Exception as e:
                self.logger.warning(f"JavaScript selection failed: {e}")
            
            # Alternative approach: try select_option method with first option
            try:
                self.logger.info("Trying Playwright select_option method for All Categories...")
                await self.page.select_option("#categoryTypeFilter", index=0)
                self.logger.info("Successfully selected All Categories using select_option")
                return True
            except Exception as e:
                self.logger.warning(f"Could not select All Categories using select_option: {e}")
            
            self.logger.error("All category selection approaches failed")
            return False
                
        except Exception as e:
            self.logger.error(f"Error selecting All Categories: {e}")
            return False
    
    async def extract_all_categories_data(self):
        """
        Extract table data for all four main cabin categories (Suite, Veranda, Outside, Interior)
        by selecting each category one by one and extracting the table data.
        
        Returns:
            dict: Data from all four tables, organized by category
        """
        self.logger.info("Extracting table data for all cabin categories...")
        ui_log("Analyzing all cabin categories — please hold on", session_id=self.session_id, cabin_id=self.cabin_id, step="analyzing_all_cabin_categories", module="Cruising_Power")
        
        # Define the four main cabin categories
        categories = ["Suite", "Veranda", "Outside", "Interior"]
        
        # Dictionary to store results for each category
        all_categories_data = {}
        
        # Extract data for each category
        for category in categories:
            try:
                self.logger.info(f"\n--- Extracting data for {category} category ---")
                #ui_log(f"Checking {category} cabin options", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
                
                # Select the category
                category_selected = await self.select_category(category)
                
                if not category_selected:
                    self.logger.warning(f"Could not select category: {category}. Skipping...")
                    continue
                
                # Extract table data for this category
                category_data = await self.extract_all_cabin_data()
                
                if "error" in category_data:
                    self.logger.error(f"Error extracting data for {category}: {category_data['error']}")
                    continue
                
                # Store the data
                all_categories_data[category] = category_data
                
                self.logger.info(f"Successfully extracted data for {category}: {category_data['totalFound']} cabins, {category_data['availableCabins']} available")
                
                # If we have available cabins, log the cheapest one
                if category_data["availableCabins"] > 0 and len(category_data["cabins"]) > 0:
                    # Find the cheapest available cabin
                    available_cabins = [c for c in category_data["cabins"] if c.get("isAvailable", False)]
                    if available_cabins:
                        cheapest_cabin = min(available_cabins, key=lambda x: x.get("price", float("inf")))
                        self.logger.info(f"Cheapest {category} cabin: {cheapest_cabin.get('cabinType', 'Unknown')} at {cheapest_cabin.get('priceText', 'Unknown')}")
                
            except Exception as e:
                self.logger.error(f"Error processing {category} category: {e}")
                #ui_log(f"Error analyzing {category} cabins", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
        
        # Final summary
        if all_categories_data:
            for category, data in all_categories_data.items():
                self.logger.info(f"{category}: {data['totalFound']} cabins, {data['availableCabins']} available")
            
            # Print JSON data instead of saving to file
            # self.logger.info("Cabin data:")
            # self.logger.info(json.dumps(all_categories_data, indent=2))
            
            ui_log("Cabin analysis complete — ready to proceed", session_id=self.session_id, cabin_id=self.cabin_id, step="cabin_analysis_complete", module="Cruising_Power")
            
            return all_categories_data
        else:
            self.logger.warning("No category data was successfully extracted")
            ui_log("No cabin data found — please try again", session_id=self.session_id, cabin_id=self.cabin_id, step="no_cabin_data_found", module="Cruising_Power")
        
        return all_categories_data

    async def extract_all_cabin_categories_data(self):
        """
        Extract data from all cabin categories (Suite, Veranda, Outside, Interior).
        This method selects each cabin category one by one and extracts the data.
        
        Returns:
            dict: Dictionary with cabin category names as keys and lists of cabin data as values
        """
        self.logger.info("Starting extraction of data from all cabin categories...")
        
        # Define the four main cabin categories
        categories = ["Suite", "Veranda", "Outside", "Interior"]
        
        # Dictionary to store results for each category
        all_categories_data = {}
        
        # Extract data for each category
        for category in categories:
            try:
                self.logger.info(f"Processing {category} category...")
                
                # Select the category
                category_selected = await self.select_category(category)
                
                if not category_selected:
                    self.logger.warning(f"Failed to select {category} category. Skipping.")
                    all_categories_data[category] = {"error": f"Failed to select category"}
                    continue
                
                # Extract table data for this category
                category_data = await self.extract_all_cabin_data()
                
                if "error" in category_data:
                    self.logger.error(f"Error extracting data for {category}: {category_data['error']}")
                    all_categories_data[category] = {"error": category_data["error"]}
                    continue
                
                # Store the data
                all_categories_data[category] = category_data
                
                self.logger.info(f"Successfully extracted data for {category}: {category_data['totalFound']} cabins, {category_data['availableCabins']} available")
                
            except Exception as e:
                self.logger.error(f"Error processing {category} category: {e}")
                all_categories_data[category] = {"error": str(e)}
        
        # Final summary
        if all_categories_data:
            categories_with_data = [cat for cat, data in all_categories_data.items() 
                                    if isinstance(data, dict) and "error" not in data]
            
            #self.logger.info("\n=== Summary of All Categories ===")
            for category in categories_with_data:
                data = all_categories_data[category]
                self.logger.info(f"{category}: {data['totalFound']} cabins, {data['availableCabins']} available")
            
            # Print JSON data instead of saving to file
            self.logger.info("Cabin data:")
            self.logger.info(json.dumps(all_categories_data, indent=2))
            
            return all_categories_data
        else:
            self.logger.warning("Failed to extract data from any cabin category")
        
        return all_categories_data

    async def extract_category_data(self):
        """
        Extracts cabin data from the currently selected category.
        
        Returns:
            List of dictionaries containing cabin information
        """
        self.logger.info("Extracting data from current category")
        cabin_data = []
        
        try:
            # Wait for the table to be visible after category selection
            await self.page.wait_for_selector("table.cabin-cat-results-table")
            
            # Get all rows from the table
            rows = await self.page.query_selector_all("table.cabin-cat-results-table tbody tr")
            self.logger.info(f"Found {len(rows)} cabin rows in the current category")
            
            for row in rows:
                try:
                    cabin_info = {}
                    
                    # Extract cabin name/category
                    category_elem = await row.query_selector(".cabinCategoryDetails h3")
                    if category_elem:
                        cabin_info["category"] = (await category_elem.inner_text()).strip()
                    
                    # Extract cabin code
                    code_elem = await row.query_selector(".cabinCategoryDetails .guarantee span")
                    if code_elem:
                        cabin_info["code"] = (await code_elem.inner_text()).strip()
                    
                    # Extract price
                    price_elem = await row.query_selector(".price .priceAmount")
                    if price_elem:
                        price_text = (await price_elem.inner_text()).strip()
                        # Remove currency symbol and commas
                        price_clean = ''.join(c for c in price_text if c.isdigit() or c == '.')
                        try:
                            cabin_info["price"] = float(price_clean)
                        except ValueError:
                            cabin_info["price"] = price_text
                    
                    # Extract availability
                    avail_elem = await row.query_selector(".availability")
                    if avail_elem:
                        cabin_info["availability"] = (await avail_elem.inner_text()).strip()
                    
                    cabin_data.append(cabin_info)
                    
                except Exception as e:
                    self.logger.error(f"Error processing cabin row: {e}")
                    continue
            
            self.logger.info(f"Successfully extracted data for {len(cabin_data)} cabins")
            return cabin_data
            
        except Exception as e:
            self.logger.error(f"Error extracting category data: {e}")
            return cabin_data

    async def find_cabin_by_name(self, all_cabins_data, cabin_name):
        """
        Find a cabin by name using exact and fuzzy matching on cabin type text.
        Similar to NCL's implementation but adapted for Cruising Power's structure.
        
        Args:
            all_cabins_data: List of cabin dictionaries from extract_all_cabin_data
            cabin_name: The cabin name to search for (e.g., "royal suite", "large balcony")
            
        Returns:
            List of matching cabins sorted by price
        """
        if not cabin_name or not all_cabins_data:
            return []
            
        # Normalize the search term
        search_term = cabin_name.lower().strip()
        matching_cabins = []
        
        self.logger.info(f"Searching for '{cabin_name}' through {len(all_cabins_data)} cabins")
        self.logger.info(f"Sample cabin types: {[cabin.get('cabinType', 'Unknown') for cabin in all_cabins_data[:5]]}")
        
        # First, try exact matches in cabin type
        self.logger.info(f"Searching for exact match: '{cabin_name}'")
        for cabin in all_cabins_data:
            cabin_type_text = cabin.get('cabinType', '').lower()
            if search_term == cabin_type_text:
                self.logger.info(f"Found exact cabin type match: {cabin['cabinType']} at {cabin['priceText']}")
                matching_cabins.append(cabin)
        
        if matching_cabins:
            return sorted(matching_cabins, key=lambda x: x.get('price', float('inf')))
        
        # Second, try exact matches as complete words within cabin type
        import re
        self.logger.info(f"Searching for exact word match: '{cabin_name}'")
        for cabin in all_cabins_data:
            cabin_type_text = cabin.get('cabinType', '').lower()
            # Use word boundaries to match complete words only
            pattern = r'\b' + re.escape(search_term) + r'\b'
            if re.search(pattern, cabin_type_text):
                self.logger.info(f"Found exact word match: {cabin['cabinType']} at {cabin['priceText']}")
                matching_cabins.append(cabin)
        
        if matching_cabins:
            return sorted(matching_cabins, key=lambda x: x.get('price', float('inf')))
        
        # Third, try partial matches in cabin type
        self.logger.info(f"Searching for partial match: '{cabin_name}'")
        for cabin in all_cabins_data:
            cabin_type_text = cabin.get('cabinType', '').lower()
            if search_term in cabin_type_text:
                self.logger.info(f"Found partial match: {cabin['cabinType']} at {cabin['priceText']}")
                matching_cabins.append(cabin)
        
        if matching_cabins:
            return sorted(matching_cabins, key=lambda x: x.get('price', float('inf')))
        
        # Fourth, try multi-word matching with scoring
        search_words = search_term.split()
        if len(search_words) > 1:
            self.logger.info(f"Trying multi-word match for: {search_words}")
            best_matches = []
            
            for cabin in all_cabins_data:
                cabin_type_text = cabin.get('cabinType', '').lower()
                # Count how many search words are found in the cabin type
                matches = sum(1 for word in search_words if word in cabin_type_text)
                match_score = matches / len(search_words)  # Percentage of words matched
                
                if match_score >= 0.5:  # At least 50% match
                    best_matches.append((cabin, match_score))
                    self.logger.info(f"Found partial word match: {cabin['cabinType']} (score: {match_score:.2f})")
            
            if best_matches:
                # Sort by match score descending, then by price ascending
                best_matches.sort(key=lambda x: (-x[1], x[0].get('price', float('inf'))))
                return [match[0] for match in best_matches]
        
        self.logger.warning(f"No matching cabins found for '{cabin_name}' after all search strategies")
        return []


async def select_cheapest_cabin(page, cabin_type=None, request_id=None, cabin_id=None, session_id=None):
    """
    Wrapper function to select the cheapest available cabin
    
    Args:
        page: Playwright Page instance
        cabin_type: The cabin type to select (e.g., "Suite", "Veranda", etc.)
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
        
    Returns:
        tuple: (success, message)
    """
    selector = CabinSelector(page, cabin_type)
    selector.request_id = request_id
    selector.cabin_id = cabin_id
    selector.session_id = session_id
    return await selector.select_cheapest()


async def select_cheapest_cabin_js(page, cabin_type=None, request_id=None, cabin_id=None, session_id=None):
    """
    Wrapper function to select the cheapest available cabin using JavaScript
    
    Args:
        page: Playwright Page instance
        cabin_type: The cabin type to select (e.g., "Suite", "Veranda", etc.)
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
        
    Returns:
        tuple: (success, message)
    """
    selector = CabinSelector(page, cabin_type)
    selector.request_id = request_id
    selector.cabin_id = cabin_id
    selector.session_id = session_id
    return await selector.select_cheapest_js()


async def print_cabin_table(page, request_id=None, cabin_id=None, session_id=None):
    """
    Wrapper function to print the full cabin table
    
    Args:
        page: Playwright Page instance
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
        
    Returns:
        bool: True if successful, False otherwise
    """
    selector = CabinSelector(page, None)
    selector.request_id = request_id
    selector.cabin_id = cabin_id
    selector.session_id = session_id
    return await selector.print_cabin_table()


async def extract_all_categories(page, request_id=None, cabin_id=None, session_id=None):
    """
    Wrapper function to extract table data for all cabin categories
    
    Args:
        page: Playwright Page instance
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
        
    Returns:
        dict: Data from all categories
    """
    selector = CabinSelector(page, None)
    selector.request_id = request_id
    selector.cabin_id = cabin_id
    selector.session_id = session_id
    return await selector.extract_all_categories_data() 