diff --git a/Core/ui_logger.py b/Core/ui_logger.py
index da60d3a..47a78b9 100644
--- a/Core/ui_logger.py
+++ b/Core/ui_logger.py
@@ -37,7 +37,7 @@ def set_ui_logger_service(service):
     _ui_logger_service = service
 
 # Helper function for UI logs - optimized for socket transmission
-def ui_log(message, session_id=None, cabin_id=None, category=None, step=None, module=None):
+def ui_log(message, session_id=None, cabin_id=None, category=None, step=None, module=None, user_id=None):
     """
     Log a user-friendly message at UI level with minimal payload.
     
@@ -58,6 +58,8 @@ def ui_log(message, session_id=None, cabin_id=None, category=None, step=None, mo
     # Only add fields that have values to keep payload minimal
     if session_id:
         log_data["session_id"] = session_id
+    if user_id:
+        log_data["user_id"] = user_id
     if cabin_id:
         log_data["cabin_id"] = cabin_id
     if category:
diff --git a/api.py b/api.py
index 7ab9051..7d06d3c 100644
--- a/api.py
+++ b/api.py
@@ -28,6 +28,7 @@ from collections import defaultdict
 from db_service import get_db_connection, init_db_pool, db_connection, close_db_pool
 from services.media_service import media_service
 import io
+from sse_starlette.sse import EventSourceResponse
 
 # Add current directory to path for imports
 current_dir = os.path.dirname(os.path.abspath(__file__))
@@ -253,38 +254,44 @@ async def stream_ui_logs(request: Request, token: Optional[str] = None):
         is_admin=is_admin
     )
     
-    # SSE streaming response function
+    # SSE streaming response function using EventSourceResponse
     async def event_generator():
         try:
-            # Initial connection message
-            yield "data: {\"connected\": true, \"timestamp\": \"" + datetime.now().strftime("%H:%M:%S") + "\"}\n\n"
-            
+            # Initial connection message so frontend knows we are live
+            yield {
+                "data": json.dumps({
+                    "connected": True,
+                    "timestamp": datetime.now().strftime("%H:%M:%S")
+                })
+            }
+
             # Stream logs as they arrive
             while True:
-                # Wait for message from queue
                 message = await client_queue.get()
-                
-                # Yield message to client
-                yield message
-                
-                # Mark task as done
+
+                # Forward the log dictionary but convert to JSON string ourselves so that
+                # the browser always receives a valid, double-quoted JSON payload.
+                yield {"data": json.dumps(message)}
+
                 client_queue.task_done()
-                
+
         except asyncio.CancelledError:
             # Handle client disconnection
             ui_logger_service.remove_client(client_queue)
             logger.info("Client disconnected from SSE stream")
-    
-    # Return streaming response with appropriate headers
-    return StreamingResponse(
-        event_generator(), 
-        media_type="text/event-stream",
-        headers={
-            "Cache-Control": "no-cache",
-            "Connection": "keep-alive",
-            "X-Accel-Buffering": "no"  # Disable buffering in nginx
-        }
-    )
+
+    # Use EventSourceResponse with automatic heart-beats every 10 seconds to keep proxies alive
+    return EventSourceResponse(event_generator(), ping=10)
+
+# ------------------------------------------------------------------
+# ENDPOINT: attach session id to existing SSE connection (added after get_current_user)
+# ------------------------------------------------------------------
+from pydantic import BaseModel
+
+class AttachSessionRequest(BaseModel):
+    session_id: str
+
+# (removed duplicate attach_ui_session; single definition appears later)
 
 # Pydantic models for request validation
 class ExtractionRequest(BaseModel):
@@ -367,6 +374,25 @@ async def get_current_admin_or_subadmin(current_user: Dict = Depends(get_current
         )
     return current_user
 
+# ------------------------------------------------------------------
+# Attach session id to existing SSE connection (now correctly positioned)
+# ------------------------------------------------------------------
+class AttachSessionRequest(BaseModel):
+    session_id: str
+
+@app.post("/ui-logs/attach-session")
+@app.post("/api/ui-logs/attach-session", include_in_schema=False)
+async def attach_ui_session(data: AttachSessionRequest, current_user: Dict = Depends(get_current_user)):
+    logger.info(f"AttachSessionRequest received for user {current_user.get('username')} session {data.session_id}")
+    user_id = current_user.get("user_id") or current_user.get("id") or current_user.get("_id")
+    if not user_id:
+        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unable to determine user id")
+    await ui_logger_service.debug_log(f"attach_session_to_user called for user_id={user_id} session_id={data.session_id}")
+    await ui_logger_service.attach_session_to_user(user_id, data.session_id)
+    return {"attached": True, "session_id": data.session_id}
+
+# ------------------------------------------------------------------
+
 # Authentication and User Management Endpoints
 @app.post("/token", response_model=Token)
 async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
@@ -1041,7 +1067,7 @@ async def get_cruising_power_booking_results(
 
 @app.get("/screenshots/{session_id}", response_model=Dict[str, Any])
 async def get_screenshots(session_id: str, 
-                         limit: int = Query(50, description="Maximum number of screenshots to return"),
+                         limit: int = Query(500, description="Maximum number of screenshots to return"),
                          offset: int = Query(0, description="Number of screenshots to skip")):
     """Get screenshots saved for a given session ID with pagination."""
     try:
diff --git a/nextjs-frontend/src/contexts/LogContext.tsx b/nextjs-frontend/src/contexts/LogContext.tsx
index 1928165..93e0be0 100644
--- a/nextjs-frontend/src/contexts/LogContext.tsx
+++ b/nextjs-frontend/src/contexts/LogContext.tsx
@@ -1,5 +1,5 @@
 import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
-import { useEventSource } from '@/hooks/useEventSource';
+import { useUILogger } from "../hooks/useUILogger";
 import { getToken } from '@/services/auth';
 
 // Define the structure of a UI log
@@ -49,24 +49,11 @@ export function LogProvider({ children, apiBaseUrl = '' }: { children: ReactNode
   // Get authentication token
   const authToken = getToken();
   
-  // Build the SSE URL with authentication and session ID
-  const sseUrl = new URL(`${apiBaseUrl}/ui-logs/stream`, window.location.origin);
-  
-  // Add auth token if available
-  if (authToken) {
-    sseUrl.searchParams.append('token', authToken);
-  }
-  
-  // Add session ID if available
-  if (activeSessionId) {
-    sseUrl.searchParams.append('session_id', activeSessionId);
-  }
-  
-  // Connect to SSE endpoint with auth token
-  const { isConnected } = useEventSource(sseUrl.toString(), {
+  // Persist a single SSE connection; attach session when it becomes available
+  const { connected: isConnected } = useUILogger(authToken, activeSessionId, {
     onMessage: (newLog: UILog) => {
       // Skip initial connection message
-      if (newLog.connected) return;
+      if ((newLog as any).connected) return;
       
       // Only process logs that have a module name
       // Exclude logs without module names
@@ -111,12 +98,9 @@ export function LogProvider({ children, apiBaseUrl = '' }: { children: ReactNode
     },
   });
 
-  // Reconnect when the activeSessionId changes
+  // Clear logs when session changes (but keep SSE alive)
   useEffect(() => {
-    // The useEventSource hook will reconnect automatically when the URL changes
     console.log(`Active session ID updated: ${activeSessionId || 'none'}`);
-    
-    // Clear logs when session changes
     setLogs([]);
     setActiveCabins([]);
   }, [activeSessionId]);
diff --git a/requirements.txt b/requirements.txt
index ea25047..5c992a6 100644
--- a/requirements.txt
+++ b/requirements.txt
@@ -1,6 +1,7 @@
 # Web Framework
 fastapi>=0.95.1
 uvicorn>=0.22.0
+sse-starlette>=2.4.1  # Added for robust SSE streaming with heartbeat support
 gunicorn
 # Data Validation & Parsing
 pydantic
@@ -30,6 +31,12 @@ google.generativeai
 
 # AWS Services
 aioboto3
+redis[hiredis] >= 4.6      # async/await API, includes .from_url()
 
 # Database
-asyncpg
\ No newline at end of file
+asyncpg
+# Monitoring & Analytics
+psutil
+pandas
+matplotlib
+flask
diff --git a/services/ui_logger_service.py b/services/ui_logger_service.py
index a3425a1..cd185fe 100644
--- a/services/ui_logger_service.py
+++ b/services/ui_logger_service.py
@@ -11,6 +11,13 @@ import json
 from typing import Dict, List, Any, Optional
 from fastapi import Request
 from datetime import datetime
+import os
+
+# Redis for cross-worker pub/sub
+import redis.asyncio as aioredis
+
+# Redis connection URL – can be configured via env var
+REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
 from Core.ui_logger import set_ui_logger_service
 
 # Configure logging
@@ -50,62 +57,96 @@ class UILoggerService:
         self.loop = asyncio.get_running_loop()
         # Create a fresh queue bound to this loop
         self.log_queue = asyncio.Queue()
-        # Start the broadcasting task in the background on this loop
-        self.background_task = self.loop.create_task(self.broadcast_logs())
-        
+        # Start task to forward local log_queue to connected clients
+        self.background_task = self.loop.create_task(self._fan_out_local_queue())
+
+        # Start Redis pub/sub listener so that logs from ALL workers are delivered
+        self.loop.create_task(self._start_redis_listener())
+
         # Add callback to stop background task when app shuts down
         @app.on_event("shutdown")
         async def shutdown_event():
             if self.background_task:
                 self.background_task.cancel()
+            if hasattr(self, "redis"):
+                await self.redis.close()
                 
-    async def broadcast_logs(self):
-        """Background task that broadcasts logs to all connected clients"""
+    # ------------------------------------------------------------------
+    # Redis integration helpers
+    # ------------------------------------------------------------------
+
+    async def _start_redis_listener(self):
+        """Background task: subscribe to Redis channel and forward to local queue."""
+        try:
+            self.redis = aioredis.from_url(REDIS_URL, decode_responses=True)
+            pubsub = self.redis.pubsub()
+            # Subscribe to both channels: ui_logs for normal logs and ui_attach for session-attachment updates
+            await pubsub.subscribe("ui_logs", "ui_attach")
+
+            async for msg in pubsub.listen():
+                if msg["type"] != "message":
+                    continue
+                channel = msg.get("channel")
+                try:
+                    payload = json.loads(msg["data"])
+                    if channel == "ui_logs":
+                        # Forward log payload to the local fan-out queue
+                        await self.log_queue.put(payload)
+                    elif channel == "ui_attach":
+                        # Apply session attachment broadcast originating from another worker
+                        user_id = payload.get("user_id")
+                        session_id = payload.get("session_id")
+                        if user_id and session_id:
+                            self._attach_session_to_user_local(user_id, session_id)
+                except Exception as e:
+                    logger.warning(f"Failed to process Redis message on {channel}: {e}")
+        except asyncio.CancelledError:
+            pass
+        except Exception as e:
+            logger.error(f"Redis listener stopped unexpectedly: {e}")
+
+    # ------------------------------------------------------------------
+    # Local fan-out
+    # ------------------------------------------------------------------
+    async def _fan_out_local_queue(self):
+        """Take items from self.log_queue and forward to connected clients."""
         try:
             while True:
-                # Get the next log message from the queue
                 log_data = await self.log_queue.get()
-                
-                # Get session_id from log data if it exists
+
                 session_id = log_data.get("session_id")
-                
-                # Broadcast to all connected clients
-                for client in self.clients[:]:  # Use a copy to allow safe removal
+                for client in self.clients[:]:
                     try:
-                        # Only send log if:
-                        # 1. Log has no session_id (system log)
-                        # 2. Client has no user_id (anonymous/system client)
-                        # 3. The session_id matches the user's session
-                        # Note: Admin users now also only see their own logs
                         client_user_id = client.get("user_id")
                         client_session_id = client.get("session_id")
-                        
-                        # System logs (no session_id) are visible to everyone
-                        if not session_id:
-                            # Format message as SSE
-                            message = f"data: {json.dumps(log_data)}\n\n"
-                            await client["queue"].put(message)
-                        # User specific logs are only visible to the user with matching session_id
-                        elif client_session_id == session_id:
-                            # Format message as SSE
-                            message = f"data: {json.dumps(log_data)}\n\n"
-                            await client["queue"].put(message)
-                            
+
+                        # Delivery rules:
+                        # 1. If log has no session_id (general message) deliver to everyone.
+                        # 2. If client has session_id attached, ensure it matches.
+                        # 3. If client has no session_id yet, fall back to user_id match so that they still
+                        #    receive messages that belong to them before attachment is completed.
+                        if (
+                            not session_id
+                            or client_session_id == session_id
+                            or (
+                                client_session_id is None
+                                and session_id
+                                and client_user_id == log_data.get("user_id")
+                            )
+                        ):
+                            await client["queue"].put(log_data)
                     except Exception as e:
-                        # If sending fails, remove client from list
                         logger.warning(f"Error sending to client: {e}")
                         try:
                             self.clients.remove(client)
                         except ValueError:
                             pass
-                
-                # Mark task as done in the queue
+
                 self.log_queue.task_done()
         except asyncio.CancelledError:
-            # Handle task cancellation gracefully
-            logger.info("Broadcast task cancelled")
+            logger.info("Fan-out task cancelled")
         except Exception as e:
-            logger.error(f"Error in broadcast task: {e}")
+            logger.error(f"Error in fan-out task: {e}")
     
     async def add_client(self, request: Request, user_id: Optional[str] = None, session_id: Optional[str] = None, is_admin: bool = False):
         """
@@ -158,9 +199,47 @@ class UILoggerService:
         Args:
             log_data: The log data to broadcast
         """
-        # Add log to the queue for broadcasting
-        await self.log_queue.put(log_data)
-        
+        """Publish log to Redis so every worker receives it."""
+        try:
+            if not hasattr(self, "redis"):
+                # Lazy init if not started yet
+                self.redis = aioredis.from_url(REDIS_URL)
+            await self.redis.publish("ui_logs", json.dumps(log_data))
+        except Exception as e:
+            logger.warning(f"Failed to publish log to Redis: {e}")
+            # Fallback to local queue so at least local clients receive it
+            await self.log_queue.put(log_data)
+    
     def get_client_count(self):
         """Get the number of connected clients"""
-        return len(self.clients) 
\ No newline at end of file
+        return len(self.clients) 
+
+    def _attach_session_to_user_local(self, user_id: str, session_id: str):
+        """Helper that attaches a session_id to all queues belonging to the user, within this worker."""
+        for client in self.clients:
+            if client.get("user_id") == user_id:
+                client["session_id"] = session_id
+
+    # ------------------------------------------------------------------
+    # Public API helpers
+    # ------------------------------------------------------------------
+    async def attach_session_to_user(self, user_id: str, session_id: str):
+        """Attach a session_id to all active SSE clients for a given user.
+
+        If the client lives in the same worker the update is applied
+        immediately; we also publish an instruction on Redis so that
+        other workers can update their in-memory state too.
+        """
+        # Local update first
+        self._attach_session_to_user_local(user_id, session_id)
+
+        # Broadcast to all other workers so they update as well
+        try:
+            if not hasattr(self, "redis"):
+                self.redis = aioredis.from_url(REDIS_URL)
+            await self.redis.publish("ui_attach", json.dumps({"user_id": user_id, "session_id": session_id}))
+        except Exception as e:
+            logger.warning(f"Failed to broadcast session attach: {e}") 
+
+    async def debug_log(self, msg: str):
+        logger.debug(f"[UILoggerService] {msg}") 
\ No newline at end of file
