import { API_BASE_URL } from './api';
import { ProviderType } from '../components/booking/ProviderSelector';

// User types
export interface User {
  user_id: string;
  username: string;
  email: string;
  full_name: string;
  role: 'admin' | 'sub_admin' | 'user';
  agency: string; // Dynamic agency values
  portal_access: string[];
  provider_access?: ProviderType[];
  created_at: string;
  last_login?: string;
  status?: 'pending' | 'approved' | 'rejected' | 'suspended';
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: User;
}

export interface RegisterData {
  username: string;
  password: string;
  email: string;
  full_name: string;
  role: 'admin' | 'sub_admin' | 'user';
  agency: 'STUDIO' | 'CNM';
  portal_access: string[];
}

export interface UpdateUserData {
  email?: string;
  full_name?: string;
  role?: 'admin' | 'sub_admin' | 'user';
  agency?: string;
  portal_access?: string[];
  provider_access?: ProviderType[];
  password?: string;
}

export interface Provider {
  provider_id: number;
  name: string;
  agency: string; // Changed from 'STUDIO' | 'CNM' to string to support dynamic agencies
  description: string;
  status: 'active' | 'coming_soon' | 'maintenance';
}

// Token expiry timer
let tokenExpiryTimer: NodeJS.Timeout | null = null;

// Function to decode JWT token and get expiration time
const decodeJWT = (token: string): { exp?: number } | null => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return null;
  }
};

// Function to check if token is expired
const isTokenExpired = (token: string): boolean => {
  const decoded = decodeJWT(token);
  if (!decoded || !decoded.exp) {
    return true; // If we can't decode or no expiration, consider it expired
  }
  
  const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
  return decoded.exp < currentTime;
};

// Function to get time until token expires (in milliseconds)
const getTimeUntilExpiry = (token: string): number => {
  const decoded = decodeJWT(token);
  if (!decoded || !decoded.exp) {
    return 0;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  const timeUntilExpiry = (decoded.exp - currentTime) * 1000; // Convert to milliseconds
  return Math.max(0, timeUntilExpiry);
};

// Function to set up automatic logout when token expires
const setupTokenExpiryCheck = (token: string): void => {
  // Clear any existing timer
  if (tokenExpiryTimer) {
    clearTimeout(tokenExpiryTimer);
  }
  
  // Check if token is already expired
  if (isTokenExpired(token)) {
    logout();
    alert('Session expired. Please log in again.');
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
    return;
  }
  
  // Get time until expiry
  const timeUntilExpiry = getTimeUntilExpiry(token);
  
  // Set timer to logout when token expires
  tokenExpiryTimer = setTimeout(() => {
    logout();
    alert('Session expired. Please log in again.');
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
  }, timeUntilExpiry);
  
  console.log(`Token will expire in ${Math.round(timeUntilExpiry / 1000)} seconds`);
};

// Helper for authentication headers
const getAuthHeaders = (): Record<string, string> => {
  const token = getToken();
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// API calls with authentication header
export const authFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const headers = {
    ...options.headers,
    ...getAuthHeaders(),
  };
  
  const response = await fetch(url, { ...options, headers });
  
  // Check for 401 Unauthorized - token expired or invalid
  if (response.status === 401) {
    // Clear token and user data
    logout();
    
    // Show session expired message
    alert('Session expired. Please log in again.');
    
    // Redirect to home page
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
    
    throw new Error('Session expired. Please log in again.');
  }
  
  return response;
};

// Auth service functions
export const login = async (username: string, password: string): Promise<LoginResponse> => {
  try {
    // The API expects form data for OAuth2 compatibility
    const formData = new URLSearchParams();
    formData.append('username', username);
    formData.append('password', password);

    const response = await fetch(`${API_BASE_URL}/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Login failed');
    }

    const data: LoginResponse = await response.json();
    
    // Store the token in localStorage for later use
    localStorage.setItem('token', data.access_token);
    localStorage.setItem('user', JSON.stringify(data.user));
    
    // Set up automatic token expiry check
    setupTokenExpiryCheck(data.access_token);
    
    return data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

export const logout = async (): Promise<void> => {
  // Simply clear local storage - no need for API call
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  
  // Clear token expiry timer if it exists
  if (tokenExpiryTimer) {
    clearTimeout(tokenExpiryTimer);
    tokenExpiryTimer = null;
  }
};

export const getToken = (): string | null => {
  if (typeof window === 'undefined') {
    return null; // Return null when running on the server
  }
  return localStorage.getItem('token');
};

export const getCurrentUser = (): User | null => {
  if (typeof window === 'undefined') {
    return null; // Return null when running on the server
  }
  
  const userJson = localStorage.getItem('user');
  if (!userJson) return null;
  
  try {
    return JSON.parse(userJson) as User;
  } catch (e) {
    console.error('Error parsing user data:', e);
    return null;
  }
};

export const isAuthenticated = (): boolean => {
  const token = getToken();
  if (!token) return false;
  
  // Check if token is expired
  if (isTokenExpired(token)) {
    logout(); // Clean up expired token
    return false;
  }
  
  return true;
};

// Function to initialize token expiry check (call this on app startup)
export const initializeTokenExpiryCheck = (): void => {
  const token = getToken();
  if (token && !isTokenExpired(token)) {
    setupTokenExpiryCheck(token);
  }
};

// User management
export const registerUser = async (userData: RegisterData): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/public/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Registration failed');
    }

    return await response.json();
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
};

export const getUsers = async (): Promise<User[]> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/users`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch users');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

export const getUser = async (userId: string): Promise<User> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/users/${userId}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch user');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching user ${userId}:`, error);
    throw error;
  }
};

export const updateUser = async (userId: string, userData: UpdateUserData): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/users/${userId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Update failed');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error updating user ${userId}:`, error);
    throw error;
  }
};

export const deleteUser = async (userId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/users/${userId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Delete failed');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error deleting user ${userId}:`, error);
    throw error;
  }
};

export const updateUserRole = async (userId: string, role: 'admin' | 'sub_admin' | 'user'): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/users/${userId}/role?role=${role}`, {
      method: 'PUT',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Role update failed');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error updating role for user ${userId}:`, error);
    throw error;
  }
};

export const getProviders = async (agency?: 'STUDIO' | 'CNM'): Promise<Provider[]> => {
  try {
    const url = agency ? `${API_BASE_URL}/providers?agency=${agency}` : `${API_BASE_URL}/providers`;
    const response = await authFetch(url);

    if (!response.ok) {
      throw new Error('Failed to fetch providers');
    }

    const data: Provider[] = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching providers:', error);
    throw error;
  }
};

export const createProvider = async (provider: Omit<Provider, 'provider_id'>): Promise<{ success: boolean; message: string; provider_id: number }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/providers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(provider),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Provider creation failed');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating provider:', error);
    throw error;
  }
};

export const updateProvider = async (providerId: number, provider: Omit<Provider, 'provider_id'>): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/providers/${providerId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(provider),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Provider update failed');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error updating provider ${providerId}:`, error);
    throw error;
  }
};

export const deleteProvider = async (providerId: number): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/providers/${providerId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Provider deletion failed');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error deleting provider ${providerId}:`, error);
    throw error;
  }
};

export const approveUser = async (userId: string, data: UpdateUserData): Promise<{ success: boolean; message: string }> => {
  try {
    // Add status to the data
    const approveData = {
      ...data,
      status: 'approved'
    };

    const response = await authFetch(`${API_BASE_URL}/users/${userId}/approve`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(approveData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'User approval failed');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error approving user ${userId}:`, error);
    throw error;
  }
};

export const rejectUser = async (userId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/users/${userId}/reject`, {
      method: 'PUT',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'User rejection failed');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error rejecting user ${userId}:`, error);
    throw error;
  }
};

export const getPendingUsers = async (): Promise<User[]> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/users/pending`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch pending users');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching pending users:', error);
    throw error;
  }
};

export const suspendUser = async (userId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/users/${userId}/suspend`, {
      method: 'PUT',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'User suspension failed');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error suspending user ${userId}:`, error);
    throw error;
  }
};

export const unsuspendUser = async (userId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/users/${userId}/unsuspend`, {
      method: 'PUT',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'User unsuspension failed');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error unsuspending user ${userId}:`, error);
    throw error;
  }
};

export const updateProviderStatus = async (providerId: number, status: 'active' | 'coming_soon' | 'maintenance'): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/api/providers/${providerId}/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Provider status update failed');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error updating provider ${providerId} status:`, error);
    throw error;
  }
};

export const fetchAgencies = async (): Promise<string[]> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/api/agencies`);

    if (!response.ok) {
      console.warn('Failed to fetch agencies from API, using fallback');
      return ['STUDIO', 'CNM']; // Return fallback instead of throwing
    }

    const data = await response.json();
    return data.agencies || ['STUDIO', 'CNM'];
  } catch (error) {
    console.error('Error fetching agencies:', error);
    return ['STUDIO', 'CNM']; // Return fallback instead of throwing
  }
};