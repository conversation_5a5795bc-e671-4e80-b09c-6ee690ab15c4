import os
import google.generativeai as genai
from dotenv import load_dotenv
import re
from loguru import logger
# Load environment variables
load_dotenv()

# Configure the Gemini API
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=GEMINI_API_KEY)

# Set up the model
model = genai.GenerativeModel('gemini-2.0-flash')

class CommentAnalyzer:
    """
    A class for extracting and analyzing comments from cruise information text.
    """
    
    def __init__(self):
        """Initialize the CommentAnalyzer"""
        # Set up the model
        self.model = genai.GenerativeModel('gemini-2.0-flash')
    
    def extract_comments(self, text):
        """
        Extract comments from the cruise information input.
        Comments are typically after all the structured data.
        
        Args:
            text: Full cruise information text input
            
        Returns:
            str: Extracted comments or empty string if none found
        """
        # Look for a "Comments:" section
        comments_match = re.search(r'Comments?:(.+?)(?:\n\n|\Z)', text, re.DOTALL | re.IGNORECASE)
        if comments_match:
            return comments_match.group(1).strip()
        
        # If no explicit comments section, look for text after all structured data
        # But be more careful about what we consider comments
        lines = text.split('\n')
        structured_data_ended = False
        last_structured_line = -1
        
        # First pass: identify where structured data ends
        for i, line in enumerate(lines):
            # A line with key-value format indicates structured data
            if ':' in line and not line.strip().startswith('#'):
                last_structured_line = i
        
        # If we found structured data, look at what comes after
        if last_structured_line >= 0 and last_structured_line + 1 < len(lines):
            # Get potential comments (everything after structured data)
            potential_comments = lines[last_structured_line + 1:]
            # Filter out empty lines and lines that look like headers/separators
            filtered_comments = [line for line in potential_comments 
                               if line.strip() and not re.match(r'^[-=_*#]+$', line.strip())
                               and not re.match(r'^\d+\s*$', line.strip())]
            
            if filtered_comments:
                return '\n'.join(filtered_comments).strip()
        
        # If we get here, no comments found
        return ""

    def analyze_comments_for_promotions(self, comments, cruise_info):
        """
        Use Gemini to analyze comments and determine if they involve perks or promotions.
        
        Args:
            comments: String containing comments from the user
            cruise_info: Dictionary with cruise information
            
        Returns:
            dict: Information about required promotions and perks
        """
        if not comments:
            return {"needs_promotion": False, "reason": "No comments provided"}
        
        # Check if this is a Celebrity cruise (this feature only applies to Celebrity)
        ship_brand = cruise_info.get('ship_brand', '').strip().lower()
        if 'celebrity' not in ship_brand:
            return {"needs_promotion": False, "reason": "Not a Celebrity cruise"}
        
        # Prepare prompt for Gemini
        prompt = f"""
                You are analyzing a comment from a cruise booking to determine if the customer is asking about perks, beverage packages, or special promotions.

                Cruise Information:
                - Ship Brand: {cruise_info.get('ship_brand', 'Unknown')}
                - Ship Name: {cruise_info.get('ship_name', 'Unknown')}

                Customer Comment:
                "{comments}"

                Based only on this comment, I need you to:
                1. Determine if the customer is asking about or interested in any perks, beverage packages, special promotions, or rate codes.
                2. If yes, specify which type: perks, beverages, WiFi, gratuities, or other.
                3. Respond in JSON format only with the following structure:
                {{
                "needs_promotion": true/false,
                "promotion_type": "perks/beverages/wifi/gratuities/other",
                "reason": "brief explanation of your reasoning"
                }}
        """

        try:
            # Generate response from Gemini
            response = self.model.generate_content(prompt)
            result_text = response.text.strip()
            
            # Extract JSON from response if necessary (sometimes the model adds extra text)
            import json
            if '{' in result_text and '}' in result_text:
                json_start = result_text.find('{')
                json_end = result_text.rfind('}') + 1
                json_str = result_text[json_start:json_end]
                result = json.loads(json_str)
            else:
                # Default response if JSON parsing fails
                result = {
                    "needs_promotion": False, 
                    "promotion_type": "none", 
                    "reason": "Could not parse model response"
                }
            
            return result
        except Exception as e:
            logger.error(f"Error analyzing comments with Gemini: {e}")
            return {"needs_promotion": False, "reason": f"Error analyzing with Gemini: {str(e)}"}


# Wrapper functions for backward compatibility
def extract_comments(text):
    """Backward compatibility wrapper for extract_comments"""
    analyzer = CommentAnalyzer()
    return analyzer.extract_comments(text)

def analyze_comments_for_promotions(comments, cruise_info):
    """Backward compatibility wrapper for analyze_comments_for_promotions"""
    analyzer = CommentAnalyzer()
    return analyzer.analyze_comments_for_promotions(comments, cruise_info) 