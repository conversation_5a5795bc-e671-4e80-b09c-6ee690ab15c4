"""
OneSource Booking Module

This module handles the booking flow after successful login to OneSource.
It includes navigation to booking pages, cruise line selection, and booking creation.
"""

import asyncio
import random
from loguru import logger
from Core.ui_logger import ui_log


class OneSourceBooking:
    """
    Class for handling OneSource booking operations after login
    """
    
    def __init__(self, page, cruise_details, session_id=None, cabin_id=None):
        """
        Initialize the OneSource booking handler
        
        Args:
            page: Playwright Page instance
            cruise_details: Dictionary containing cruise details
            session_id: Session ID for tracking
            cabin_id: Cabin ID for tracking
        """
        self.page = page
        self.cruise_details = cruise_details
        self.session_id = session_id
        self.cabin_id = cabin_id
        
        # Cruise line mapping for OneSource
        self.cruise_line_mapping = {
            'cunard': 'CU',
            'cunard line': 'CU',
            'princess': 'PC',
            'princess cruises': 'PC',
            'holland america': 'HA',
            'holland america line': 'HA',
            'p&o cruises australia': 'PA',
            'p&o australia': 'PA',
            'seabourn': 'SB',
            'seabourn cruise line': 'SB'
        }
    
    async def start_booking_process(self):
        """
        Start the complete booking process after login
        
        Returns:
            dict: Result of the booking process
        """
        try:
            logger.info("Starting OneSource booking process")
            ui_log("Starting booking process", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_id,
                  step="booking_start", 
                  module="OneSource")
            
            # Step 1: Click on Book & manage cruises button
            if not await self._click_book_manage_cruises():
                return {"success": False, "error": "Failed to click Book & manage cruises button"}
            
            # Step 2: Select cruise line
            if not await self._select_cruise_line():
                return {"success": False, "error": "Failed to select cruise line"}
            
            # Step 3: Click create booking button
            if not await self._click_create_booking():
                return {"success": False, "error": "Failed to click create booking button"}
            
            return {"success": True, "message": "Booking process completed", "page": self.page}
            
        except Exception as e:
            logger.error(f"Error in OneSource booking process: {e}")
            ui_log(f"Error in booking process: {e}", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_id,
                  step="booking_error", 
                  module="OneSource")
            return {"success": False, "error": str(e)}
    
    async def _click_book_manage_cruises(self):
        """
        Click on the Book & manage cruises button
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Clicking Book & manage cruises button")
            ui_log("Navigating to booking section", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_id,
                  step="navigating_booking", 
                  module="OneSource")
            
            # Minimal delay to ensure DOM is stable
            await self._random_delay(0.05, 0.15)
            
            # Try multiple selectors for the Book & manage cruises button
            selectors = [
                '//*[@id="__next"]/div/div/div[2]/div/header/div[2]/li[6]/button',
                '#__next > div > div > div.row > div > header > div.Header_nav-list-sidebar__WTPRN.Header_open__SLWrV.bg-white > li:nth-child(6) > button',
                'button:has-text("Book & manage cruises")',
                'li:nth-child(6) > button'
            ]
            
            button_clicked = False
            new_page_promise = None
            
            for selector in selectors:
                try:
                    if selector.startswith('//'):
                        # XPath selector
                        button = await self.page.wait_for_selector(f"xpath={selector}", timeout=2000)
                    else:
                        # CSS selector
                        button = await self.page.wait_for_selector(selector, timeout=2000)
                    
                    if button:
                        # Set up listener for new page before clicking
                        new_page_promise = self.page.context.wait_for_event('page')
                        
                        await button.click()
                        button_clicked = True
                        logger.info(f"Successfully clicked Book & manage cruises button with selector: {selector}")
                        break
                        
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            if not button_clicked:
                logger.error("Failed to find Book & manage cruises button")
                return False
            
            # Wait for new tab to open
            try:
                if new_page_promise:
                    logger.info("Waiting for new tab to open...")
                    new_page = await new_page_promise
                    self.page = new_page

                    # Block images in the new tab for faster loading
                    try:
                        from Core.browser_setup import AsyncBrowserSetup
                        _tmp_setup = AsyncBrowserSetup()
                        await _tmp_setup.setup_resource_blocking(self.page, logger, block=True)
                    except Exception as rb_err:
                        logger.debug(f"Resource blocking setup on new tab failed: {rb_err}")
                    
                else:
                    logger.info("No new page promise, checking for additional tabs...")
                    await self._random_delay(0.1, 0.25)
                    all_pages = self.page.context.pages
                    if len(all_pages) > 1:
                        self.page = all_pages[-1]
                        
                        # Removed image blocking on switched tab
                        
                        logger.info(f"Switched to newest tab. Current URL: {self.page.url}")
            
            except Exception as e:
                logger.warning(f"Error waiting for new tab: {e}")
                # Fallback: wait and check for new tabs
                await self._random_delay(0.3, 0.6)
                all_pages = self.page.context.pages
                if len(all_pages) > 1:
                    self.page = all_pages[-1]
                    
                    # Removed image blocking on fallback switched tab
                    
                    logger.info(f"Fallback: switched to newest tab. Current URL: {self.page.url}")
            
            # Wait for page to load completely
            await self._random_delay(0.1, 0.25)
            
            return True
            
        except Exception as e:
            logger.error(f"Error clicking Book & manage cruises button: {e}")
            return False
    
    async def _select_cruise_line(self):
        """
        Select the appropriate cruise line from the dropdown
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Selecting cruise line")
            ui_log("Selecting cruise line", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_id,
                  step="selecting_cruise_line", 
                  module="OneSource")
            
            # Get cruise line from extracted details
            cruise_line = self.cruise_details.get('cruise_line', '').lower().strip()
            
            if not cruise_line:
                logger.error("No cruise line found in cruise details")
                return False
            
            # Map cruise line to OneSource code
            cruise_line_code = self._get_cruise_line_code(cruise_line)
            
            if not cruise_line_code:
                logger.error(f"Unknown cruise line: {cruise_line}")
                return False
            
            logger.info(f"Selecting cruise line: {cruise_line} (code: {cruise_line_code})")
            
            # Brief wait for dropdown to appear after navigation
            await self._random_delay(0.2, 0.4)
            
            # Try multiple approaches to find the dropdown
            dropdown_found = False
            
            # Approach 1: Direct selectors
            selectors = [
                '#CRUISE_LINES',
                'select[name="CRUISE_LINES"]',
                'select[id="CRUISE_LINES"]',
                'select.arial11_13h_333333_form_elements'
            ]
            
            for selector in selectors:
                try:
                    dropdown = await self.page.wait_for_selector(selector, timeout=3000)
                    if dropdown:
                        await dropdown.select_option(value=cruise_line_code)
                        dropdown_found = True
                        logger.info(f"Successfully selected cruise line with selector: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            # Approach 2: XPath selectors
            if not dropdown_found:
                xpath_selectors = [
                    '//*[@id="CRUISE_LINES"]',
                    '//select[@name="CRUISE_LINES"]',
                    '//select[contains(@class, "arial11_13h_333333_form_elements")]'
                ]
                
                for selector in xpath_selectors:
                    try:
                        dropdown = await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        if dropdown:
                            await dropdown.select_option(value=cruise_line_code)
                            dropdown_found = True
                            logger.info(f"Successfully selected cruise line with XPath: {selector}")
                            break
                    except Exception as e:
                        logger.debug(f"XPath selector {selector} failed: {e}")
                        continue
            
            # Approach 3: Find by option content
            if not dropdown_found:
                try:
                    # Look for select that contains cruise line options
                    selects = await self.page.query_selector_all('select')
                    
                    for i, select in enumerate(selects):
                        try:
                            # Check if this select has cruise line options
                            options = await select.query_selector_all('option')
                            
                            for option in options:
                                value = await option.get_attribute('value')
                                text = await option.text_content()
                                
                                # Check if this looks like a cruise line option
                                if value in ['CU', 'PC', 'HA', 'PA', 'SB'] or any(cruise in text.lower() for cruise in ['princess', 'cunard', 'holland', 'seabourn']):
                                    # Try to select our cruise line
                                    await select.select_option(value=cruise_line_code)
                                    dropdown_found = True
                                    logger.info("Successfully selected cruise line using content-based detection")
                                    break
                            
                            if dropdown_found:
                                break
                                
                        except Exception as e:
                            logger.debug(f"Error checking select {i+1}: {e}")
                            continue
                            
                except Exception as e:
                    logger.error(f"Error in approach 3: {e}")
            
            # Approach 4: Wait and retry with longer timeout
            if not dropdown_found:
                await self._random_delay(1.5, 2.5)  # Reduced from 3-5 seconds
                
                try:
                    dropdown = await self.page.wait_for_selector('#CRUISE_LINES', timeout=10000)
                    if dropdown:
                        await dropdown.select_option(value=cruise_line_code)
                        dropdown_found = True
                        logger.info("Successfully selected cruise line after extended wait")
                except Exception as e:
                    logger.error(f"Extended wait approach failed: {e}")
            
            if not dropdown_found:
                logger.error("Failed to find cruise line dropdown with all approaches")
                return False
            
            # Wait for any page changes after selection
            await self._random_delay(0.1, 0.25)
            
            return True
            
        except Exception as e:
            logger.error(f"Error selecting cruise line: {e}")
            return False
    

    
    async def _click_create_booking(self):
        """
        Click on the Create Booking button
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Clicking Create Booking button")
            ui_log("Creating booking", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_id,
                  step="creating_booking", 
                  module="OneSource")
            
            # Try multiple selectors for the Create Booking button
            selectors = [
                '#bookingTab > table.arial11_16h_333333_content.menu-box-color > tbody > tr:nth-child(2) > td:nth-child(1) > a',
                '//*[@id="bookingTab"]/table[1]/tbody/tr[2]/td[1]/a',
                'a.capsule_btn:has-text("Create Booking")',
                'a[href*="submitPage(\'AVAL\')"]'
            ]
            
            button_clicked = False
            for selector in selectors:
                try:
                    if selector.startswith('//'):
                        # XPath selector
                        button = await self.page.wait_for_selector(f"xpath={selector}", timeout=5000)
                    else:
                        # CSS selector
                        button = await self.page.wait_for_selector(selector, timeout=5000)
                    
                    if button:
                        await button.click()
                        button_clicked = True
                        logger.info(f"Successfully clicked Create Booking button")
                        break
                        
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            if not button_clicked:
                logger.error("Failed to find Create Booking button")
                return False
            
            # Wait for page navigation or changes
            await self._random_delay(1, 1.5)  # Reduced from 2-3 seconds since images are blocked
            
            # Check if we're on the right page for form filling
            try:
                page_content = await self.page.content()
                if "CMRDLT" in page_content:
                    logger.info("✅ Form container found - ready for form filling")
                elif "book" in page_content.lower() or "cruise" in page_content.lower():
                    logger.info("📄 Page contains booking/cruise content but no form container yet")
                else:
                    logger.warning("⚠️ Page content doesn't seem to contain booking form")
            except Exception as e:
                logger.warning(f"Error checking page content: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error clicking Create Booking button: {e}")
            return False
    
    def _get_cruise_line_code(self, cruise_line):
        """
        Get the OneSource cruise line code from the cruise line name
        
        Args:
            cruise_line: Cruise line name from extracted details
            
        Returns:
            str: OneSource cruise line code or None if not found
        """
        cruise_line_lower = cruise_line.lower().strip()
        
        # Direct mapping lookup
        if cruise_line_lower in self.cruise_line_mapping:
            return self.cruise_line_mapping[cruise_line_lower]
        
        # Fuzzy matching for partial matches
        for key, value in self.cruise_line_mapping.items():
            if key in cruise_line_lower or cruise_line_lower in key:
                return value
        
        # Check for common variations
        if 'princess' in cruise_line_lower:
            return 'PC'
        elif 'cunard' in cruise_line_lower:
            return 'CU'
        elif 'holland' in cruise_line_lower or 'america' in cruise_line_lower:
            return 'HA'
        elif 'p&o' in cruise_line_lower or 'australia' in cruise_line_lower:
            return 'PA'
        elif 'seabourn' in cruise_line_lower:
            return 'SB'
        
        return None
    
    async def _random_delay(self, min_seconds=0.5, max_seconds=2.0):
        """Add a random delay to simulate human behavior"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)


# Convenience function for backward compatibility
async def process_booking(page, cruise_details, session_id=None, cabin_id=None):
    """
    Convenience function to process OneSource booking
    
    Args:
        page: Playwright Page instance
        cruise_details: Dictionary containing cruise details
        session_id: Session ID for tracking
        cabin_id: Cabin ID for tracking
        
    Returns:
        dict: Result of the booking process
    """
    booking_handler = OneSourceBooking(page, cruise_details, session_id, cabin_id)
    result = await booking_handler.start_booking_process()
    # Return both result and updated page object
    if result.get("success", False) and "page" in result:
        return result
    else:
        # If booking failed, still return the result but without page update
        return result 