"""
Category Scraper module for cruise booking category operations.

This module provides functionality to extract and process cruise cabin category
information from booking websites. It handles finding available categories,
normalizing category types, and extracting category codes with their prices.
"""

from datetime import datetime
from loguru import logger
from Core.ui_logger import ui_log

class CategoryScraper:
    """
    A class for scraping and processing cruise cabin categories.
    
    This class provides methods to extract information about available cabin
    categories, their types, codes, and prices from cruise booking pages. It
    relies on <PERSON><PERSON> for browser automation to navigate and extract data
    from web elements.
    """

    async def get_category_info(self):
        """
        Extract detailed information about all available cabin categories.
        
        This method finds all category containers on the page and extracts
        the title and code for each available cabin category.
        
        Returns:
            list: A list of dictionaries, each containing 'title' and 'code'
                 for a cabin category, or an empty list if extraction fails
        """
        try:
            ui_log("Retrieving category information - Kindly stand by", session_id=self.session_id, step="category_info", module="Studio")
            logger.info("Retrieving all category information")

            # Wait for category containers to be visible before proceeding
            await self.page.wait_for_selector(
                '.grouped-category-container-wrapper', state="visible"
            )

            # Get all category containers from the page
            category_containers = await self.page.locator(
                '.grouped-category-container-wrapper'
            ).all()

            logger.info(f"Found {len(category_containers)} category containers")

            all_categories = []

            # Process each category container to extract information
            for category in category_containers:
                try:
                    # Extract title and code elements
                    title_element = category.locator('.grouped-category-title').first
                    code_element = category.locator('.room-category-block').first

                    # Get the text content of these elements
                    category_title = await title_element.text_content()
                    category_title = category_title.strip()
                    category_code = await code_element.text_content()
                    category_code = category_code.strip()

                    # Add to the collection of categories
                    all_categories.append(
                        {'title': category_title, 'code': category_code}
                    )

                    logger.info(f"Category: {category_code} - {category_title}")

                except Exception as e:
                    logger.error(f"Failed to extract category data: {str(e)}")
                    continue  # Skip this category but continue with others

            logger.info(f"Successfully extracted {len(all_categories)} categories")
            return all_categories

        except Exception as e:
            logger.error(f"Category info extraction failed: {str(e)}")
            return []  # Return empty list on failure

    async def get_available_category_types(self, cabin_id):
        """
        Get a list of available standardized cabin category types.
        
        This method examines the page for category panels and normalizes the 
        category names to standard types (e.g., "INSIDE", "OCEANVIEW", "BALCONY", "SUITE").
        It also handles error cases where no categories are available.
        
        Returns:
            list: A list of standardized category type strings, or an empty list if
                 no categories are available
        """
        try:
            # First check if there's an explicit "No categories available" error message
            error_selector = ".message.message-error:has-text('No categories available')"
            error_message = self.page.locator(error_selector)
            if await error_message.count() > 0:
                error_text = await error_message.text_content()
                logger.warning(f"Sailing unavailable: {error_text}")
                ui_log("This sailing can’t be booked at the moment, Try again later", session_id=self.session_id, cabin_id=cabin_id, step="Sailing_Unavailable", module="Studio")

                # Take screenshot of error for troubleshooting
                screenshot_error = self.page.locator(".main-area-container").first
                await self.optimize_screenshot(
                    screenshot_error,
                    f"No_Categories_Available_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png", 
                    cabin_id,
                    quality=50
                )
                return []

            # Wait for category panels to be visible
            await self.page.wait_for_selector(
                "div.form-group[data-results-view='grouped'], div.panel.is-itemized",
                state="visible",
                timeout=30000
            )

            # Get all panel elements
            panels = await self.page.locator("div.panel.is-itemized").all()

            # Track found categories and get list of valid standard categories for validation
            available_categories = []
            valid_categories = self.category_normalizer.get_standard_categories()

            # Process each panel to extract category information
            for panel in panels:
                try:
                    # Get panel title element
                    title_element = panel.locator(".panel-title").first
                    if await title_element.count() > 0:
                        # Extract and normalize the category type
                        category = await title_element.text_content()
                        category = category.strip().upper()
                        normalized_category = self.category_normalizer.normalize_category(
                            category
                        )

                        # Only add valid categories that haven't been added yet
                        if normalized_category in valid_categories and normalized_category not in available_categories:
                            available_categories.append(normalized_category)
                            logger.info(f"Found category: {normalized_category}")
                except Exception as e:
                    logger.warning(f"Panel processing error: {e}")

            # Handle case where no categories were found
            if not available_categories:
                logger.warning("No categories found")
                screenshot_no_category = self.page.locator(".main-area-container").first
                await self.optimize_screenshot(
                    screenshot_no_category,
                    f"No_Categories_Available_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
                    cabin_id,
                    quality=50
                )
            else:
                # Take screenshot of available categories
                screenshot_category = self.page.locator(".main-area-container").first
                await self.optimize_screenshot(
                    screenshot_category,
                    f"category_selection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
                    cabin_id,
                    quality=50
                )

                logger.info(f"Available categories: {available_categories}")
                ui_log("Fetching category information — hang tight", session_id=self.session_id, cabin_id=cabin_id, step="category_info", module="Studio")
            return available_categories

        except Exception as e:
            logger.error(f"Category detection failed: {e}")
            return []

    async def get_category_codes(self, category_type):
        """
        Get specific category codes and prices for a given category type.
        
        This method searches for panels matching the specified category type 
        and extracts the available category codes with their prices.
        
        Args:
            category_type (str): The category type to search for (e.g., "BALCONY")
            
        Returns:
            list: A list of dictionaries containing 'code' and 'price' for each 
                 matching category, or None if no matching categories found
        """
        try:
            logger.info(f"Retrieving codes for {category_type} category")

            # Wait for cabin type panels to be visible
            cabin_types = await self.page.wait_for_selector(
                '.panel.is-itemized', state="visible", timeout=30000
            )
            cabin_types = await self.page.locator('.panel.is-itemized').all()

            # Check each cabin panel to find matching category
            for cabin in cabin_types:
                # Get the panel title and clean it up for consistent matching
                raw_cabin_title = await cabin.locator('.panel-title').first.text_content()
                # Normalize whitespace for consistency
                cabin_title = ' '.join(
                    raw_cabin_title.strip().replace('\n', ' ').split()
                )

                # Convert to uppercase for case-insensitive comparison
                cabin_title_upper = cabin_title.upper()
                category_type_upper = category_type.upper()

                # Match using multiple strategies:
                # 1. Category type is contained in the cabin title
                # 2. Cabin title is contained in the category type
                # 3. Normalized categories match
                if (category_type_upper in cabin_title_upper
                        or cabin_title_upper in category_type_upper
                        or self.category_normalizer.normalize_category(cabin_title)
                        == self.category_normalizer.normalize_category(category_type)):

                    # Get just the cabin type part for logging (before "starting from")
                    log_cabin_title = cabin_title.split("starting from")[0].strip()
                    logger.info(
                        f"Matched category '{category_type}' with panel title '{log_cabin_title}'"
                    )

                    # Get all categories within this panel
                    categories = await cabin.locator('.grouped-category-container-wrapper'
                                               ).all()
                    category_info = []

                    # Extract code and price for each category in this panel
                    for category in categories:
                        try:
                            # Get the category code
                            code = await category.locator('.room-category-block'
                                                    ).first.text_content()
                            code = code.strip()

                            # Extract and clean the price
                            raw_price = await category.locator('.price').first.text_content()
                            clean_price = ' '.join(
                                raw_price.strip().replace('\n', ' ').split()
                            )

                            # Extract the category title for Suite/JR Suite distinction
                            title_text = ""
                            try:
                                # Try multiple selectors to find the descriptive title
                                title_selectors = [
                                    '.grouped-category-title',
                                    '.stateroom-name', 
                                    '.category-title',
                                    '.room-type-title'
                                ]
                                for selector in title_selectors:
                                    title_elem = category.locator(selector).first
                                    if await title_elem.count() > 0:
                                        title_text = (await title_elem.text_content() or "").strip()
                                        break
                                
                                # If no title found in category, use the panel title as fallback
                                if not title_text:
                                    title_text = cabin_title
                                    
                            except Exception:
                                # Fallback to panel title if individual title extraction fails
                                title_text = cabin_title

                            # Add to category info list with title
                            category_info.append({
                                'code': code, 
                                'price': clean_price,
                                'title': title_text
                            })
                            logger.info(
                                f"Found code: {code} (Price: {clean_price}) - {title_text}"
                            )
                        except Exception as e:
                            logger.error(f"Failed to extract code data: {str(e)}")
                            continue

                    logger.info(
                        f"Found {len(category_info)} codes for {category_type}"
                    )
                    return category_info

            # If we get here, no matching categories were found
            logger.warning(f"Category type '{category_type}' not found")
            return None

        except Exception as e:
            logger.error(f"Category code retrieval failed: {str(e)}")
            return None
