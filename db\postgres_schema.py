"""
Database Schema and Initialization Module

This module defines the PostgreSQL database schema and provides functionality
to initialize, migrate, and seed the database.

Usage:
    To set up the complete database (create tables, apply migrations, seed admin):
    $ python -m db.postgres_schema

All database initialization functionality is self-contained in this file.
"""

import logging
import asyncio
from db.seed_admin import seed_admin
from db.seed_providers import seed_providers
from db_service import get_db_connection, db_connection

logger = logging.getLogger("database.postgres_schema")
logging.basicConfig(level=logging.INFO)

async def migrate_to_minio_storage():
    """
    Migrate existing database schema to support MinIO storage
    This adds new columns without breaking existing functionality
    """
    async with db_connection() as conn:
    
        try:
            # Add MinIO columns to centralized_screenshots table if they don't exist
            await conn.execute("""
            DO $$ 
            BEGIN
                BEGIN
                    ALTER TABLE centralized_screenshots ADD COLUMN minio_url TEXT;
                EXCEPTION
                    WHEN duplicate_column THEN NULL;
                END;
                
                BEGIN
                    ALTER TABLE centralized_screenshots ADD COLUMN minio_bucket TEXT;
                EXCEPTION
                    WHEN duplicate_column THEN NULL;
                END;
                
                BEGIN
                    ALTER TABLE centralized_screenshots ADD COLUMN minio_object_key TEXT;
                EXCEPTION
                    WHEN duplicate_column THEN NULL;
                END;
                
                BEGIN
                    ALTER TABLE centralized_screenshots ADD COLUMN file_size_kb INTEGER DEFAULT 0;
                EXCEPTION
                    WHEN duplicate_column THEN NULL;
                END;
            END $$;
            """)
            
            # Add MinIO columns to centralized_videos table if they don't exist
            await conn.execute("""
            DO $$ 
            BEGIN
                BEGIN
                    ALTER TABLE centralized_videos ADD COLUMN minio_url TEXT;
                EXCEPTION
                    WHEN duplicate_column THEN NULL;
                END;
                
                BEGIN
                    ALTER TABLE centralized_videos ADD COLUMN minio_bucket TEXT;
                EXCEPTION
                    WHEN duplicate_column THEN NULL;
                END;
                
                BEGIN
                    ALTER TABLE centralized_videos ADD COLUMN minio_object_key TEXT;
                EXCEPTION
                    WHEN duplicate_column THEN NULL;
                END;
                
                BEGIN
                    ALTER TABLE centralized_videos ADD COLUMN file_size_kb INTEGER DEFAULT 0;
                EXCEPTION
                    WHEN duplicate_column THEN NULL;
                END;
            END $$;
            """)
            
            logger.info("Successfully migrated database schema for MinIO storage")
            
        except Exception as e:
            logger.error(f"Error migrating database schema: {e}")
            raise

async def migrate_issue_codes_column():
    """Ensure issue_codes JSONB column exists and is JSONB"""
    async with db_connection() as conn:
        await conn.execute("""
        DO $$
        BEGIN
            -- If column doesn't exist add as JSONB
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name='user_booking_tracking' AND column_name='issue_codes') THEN
                ALTER TABLE user_booking_tracking ADD COLUMN issue_codes JSONB NOT NULL DEFAULT '[]'::jsonb;
            ELSE
                -- If column exists but is not JSONB, convert
                IF (SELECT data_type FROM information_schema.columns 
                    WHERE table_name='user_booking_tracking' AND column_name='issue_codes') <> 'jsonb' THEN
                    ALTER TABLE user_booking_tracking 
                        ALTER COLUMN issue_codes TYPE JSONB USING (
                            CASE
                                WHEN issue_codes IS NULL THEN '[]'::jsonb
                                WHEN pg_typeof(issue_codes)::text = 'jsonb' THEN issue_codes
                                ELSE to_jsonb(issue_codes)
                            END);
                END IF;
            END IF;
        END $$;
        """)

async def migrate_studio_discounts():
    """
    Add discount_text column to studio_cabins table
    This column will store discount information extracted from mobile dialogs
    """
    async with db_connection() as conn:
        try:
            # Add discount_text column to studio_cabins table if it doesn't exist
            await conn.execute("""
            DO $$ 
            BEGIN
                BEGIN
                    ALTER TABLE studio_cabins ADD COLUMN discount_text TEXT DEFAULT 'NA';
                EXCEPTION
                    WHEN duplicate_column THEN NULL;
                END;
            END $$;
            """)
            
            logger.info("Successfully added discount_text column to studio_cabins table")
            
        except Exception as e:
            logger.error(f"Error migrating studio_cabins table for discounts: {e}")
            raise

async def migrate_templates_table():
    """
    Create templates table if it doesn't exist
    This table will store template data from Excel worksheets for different providers
    """
    async with db_connection() as conn:
        try:
            # Create templates table if it doesn't exist
            await conn.execute("""
            CREATE TABLE IF NOT EXISTS templates (
                id SERIAL PRIMARY KEY,
                provider TEXT NOT NULL,
                template_id TEXT NOT NULL,
                template_content TEXT NOT NULL,
                created_at TEXT NOT NULL DEFAULT (CURRENT_TIMESTAMP::TEXT),
                updated_at TEXT NOT NULL DEFAULT (CURRENT_TIMESTAMP::TEXT)
            );
            """)
            
            # Create indexes for templates table
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_templates_provider ON templates(provider);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_templates_template_id ON templates(template_id);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_templates_provider_template_id ON templates(provider, template_id);")
            
            logger.info("Successfully created templates table and indexes")
            
        except Exception as e:
            logger.error(f"Error creating templates table: {e}")
            raise

async def initialize_database():
    async with db_connection() as conn:

    # users table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS users (
            user_id TEXT PRIMARY KEY,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT NOT NULL,
            agency TEXT NOT NULL,
            portal_access TEXT NOT NULL,
            provider_access TEXT NOT NULL DEFAULT '[]',
            created_at TEXT NOT NULL,
            last_login TEXT,
            status TEXT DEFAULT 'pending'
        );
        """)

        # providers table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS providers (
            provider_id SERIAL PRIMARY KEY,
            name TEXT NOT NULL,
            agency TEXT NOT NULL,
            description TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'active'
        );
        """)
        # Migration: ensure provider_id auto-increment sequence exists for existing table
        await conn.execute("""
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.sequences WHERE sequence_name = 'providers_provider_id_seq'
            ) THEN
                CREATE SEQUENCE providers_provider_id_seq;
                ALTER SEQUENCE providers_provider_id_seq OWNED BY providers.provider_id;
            END IF;
        END
        $$;
        """)
        # Set sequence last_value to max(provider_id) so next nextval = max+1
        # For empty tables, start sequence at 1 (false means next nextval will be 1)
        # For tables with data, start sequence at max_id (false means next nextval will be max_id+1)
        await conn.execute("""
        DO $$
        DECLARE
            max_id INTEGER;
        BEGIN
            SELECT COALESCE(MAX(provider_id), 0) INTO max_id FROM providers;
            IF max_id = 0 THEN
                -- Empty table: set sequence to start at 1
                PERFORM setval('providers_provider_id_seq', 1, false);
            ELSE
                -- Table has data: set sequence to continue from max_id
                PERFORM setval('providers_provider_id_seq', max_id, true);
            END IF;
        END $$;
        """)
        # Ensure provider_id uses sequence by default
        await conn.execute("""
        ALTER TABLE providers ALTER COLUMN provider_id SET DEFAULT nextval('providers_provider_id_seq');
        """)

        # Migration: add status column to existing providers table
        await conn.execute("""
        DO $$
        BEGIN
            BEGIN
                ALTER TABLE providers ADD COLUMN status TEXT NOT NULL DEFAULT 'active';
            EXCEPTION WHEN duplicate_column THEN NULL;
            END;
        END
        $$;
        """)

        # studio_bookings table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS studio_bookings (
            id SERIAL PRIMARY KEY,
            request_id TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            total_cabins INTEGER,
            grand_total REAL,
            execution_time REAL,
            overall_status INTEGER,
            session_id TEXT
        );
    """)

        # studio_cabins table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS studio_cabins (
            id SERIAL PRIMARY KEY,
            booking_id INTEGER,
            cabin_number INTEGER,
            category_type TEXT,
            category_code TEXT,
            selected_rate TEXT,
            selected_cabin TEXT,
            passenger_count INTEGER,
            cabin_total REAL,
            status TEXT,
            error TEXT,
            discount_text TEXT DEFAULT 'NA',
            FOREIGN KEY (booking_id) REFERENCES studio_bookings(id) ON DELETE CASCADE
        );
        """)

        # cruising_power_bookings table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS cruising_power_bookings (
            id SERIAL PRIMARY KEY,
            request_id TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            overall_status INTEGER,
            execution_time REAL,
            session_id TEXT
        );
        """)

        # cruising_power_cabins table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS cruising_power_cabins (
            id SERIAL PRIMARY KEY,
            booking_id INTEGER,
            cabin_number INTEGER,
            cabin_type TEXT,
            normalized_type TEXT,
            passengers_adults INTEGER,
            passengers_children INTEGER,
            passengers_seniors INTEGER,
            passengers_total INTEGER,
            timestamp TEXT,
            success INTEGER,
            cabin_allocation TEXT,
            total_price TEXT,
            onboard_credit TEXT,
            error TEXT,
            FOREIGN KEY (booking_id) REFERENCES cruising_power_bookings(id) ON DELETE CASCADE
        );
        """)

    # ncl_bookings table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS ncl_bookings (
            id SERIAL PRIMARY KEY,
            request_id TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            booking_mode TEXT,
            total_cabins INTEGER,
            execution_time REAL,
            total_passengers INTEGER,
            total_cost TEXT,
            total_final_price REAL,
            session_id TEXT
        );
        """)

        # ncl_cabins table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS ncl_cabins (
            id SERIAL PRIMARY KEY,
            booking_id INTEGER,
            cabin_id INTEGER,
            ship_name TEXT,
            travel_date TEXT,
            nights INTEGER,
            passengers_total INTEGER,
            passengers_adults INTEGER,
            passengers_children INTEGER,
            passengers_infants INTEGER,
            category_code TEXT,
            reservation_total REAL,
            non_comm_fare REAL,
            savings REAL,
            insurance REAL,
            dining_package REAL,
            beverage_package REAL,
            govt_tax REAL,
            onboard_credit REAL,
            commission_amount REAL,
            final_price REAL,
            current_promos TEXT,
            timestamp TEXT,
            soda_package REAL DEFAULT 0.0,
            FOREIGN KEY (booking_id) REFERENCES ncl_bookings(id) ON DELETE CASCADE
        );
        """)

        # onesource_bookings table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS onesource_bookings (
            id SERIAL PRIMARY KEY,
            request_id TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            total_cabins INTEGER,
            grand_total REAL,
            execution_time REAL,
            overall_status INTEGER,
            session_id TEXT,
            is_or_case BOOLEAN DEFAULT FALSE
        );
        """)

        # onesource_cabins table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS onesource_cabins (
            id SERIAL PRIMARY KEY,
            booking_id INTEGER,
            cabin_number INTEGER,
            category_type TEXT,
            category_code TEXT,
            selected_rate_code TEXT,
            passenger_count INTEGER,
            commission REAL,
            gross_fare REAL,
            onboard_credit REAL,
            cabin_total REAL,
            status TEXT,
            error TEXT,
            FOREIGN KEY (booking_id) REFERENCES onesource_bookings(id) ON DELETE CASCADE
        );
        """)

        # Migration: add onboard_credit column to existing onesource_cabins table
        await conn.execute("""
        DO $$
        BEGIN
            BEGIN
                ALTER TABLE onesource_cabins ADD COLUMN onboard_credit REAL DEFAULT 0.0;
            EXCEPTION WHEN duplicate_column THEN NULL;
            END;
        END
        $$;
        """)

        # Migration: add is_or_case column to existing onesource_bookings table
        await conn.execute("""
        DO $$
        BEGIN
            BEGIN
                ALTER TABLE onesource_bookings ADD COLUMN is_or_case BOOLEAN DEFAULT FALSE;
            EXCEPTION WHEN duplicate_column THEN NULL;
            END;
        END
        $$;
        """)

        # session_tracking table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS session_tracking (
            session_id TEXT PRIMARY KEY,
            request_id TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            status TEXT NOT NULL,
            provider TEXT,
            metadata TEXT
        );
        """)

        # centralized_screenshots table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS centralized_screenshots (
            id SERIAL PRIMARY KEY,
            booking_id INTEGER NULL,
            request_id TEXT NOT NULL,
            session_id TEXT NOT NULL,
            provider TEXT NOT NULL,
            cabin_id INTEGER NULL,
            screenshot_type TEXT,
            timestamp TEXT NOT NULL,
            minio_url TEXT,
            minio_bucket TEXT,
            minio_object_key TEXT,
            file_name TEXT,
            file_size_kb INTEGER DEFAULT 0,
            booking_table TEXT,
            booking_table_id INTEGER NULL,
            -- Legacy column for backward compatibility (will be removed later)
            image_data BYTEA
        );
        """)

        # user_booking_tracking table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS user_booking_tracking (
            session_id TEXT PRIMARY KEY,
            username TEXT NOT NULL,
            provider TEXT NOT NULL,
            request_id TEXT NOT NULL,
            val_status TEXT NOT NULL DEFAULT 'reprocessed',
            val_mark TEXT,
            as_per_feature TEXT,
            val_comment TEXT,
            issue_codes JSONB NOT NULL DEFAULT '[]'::jsonb,
            timestamp TEXT NOT NULL
        );
        """)

        # input_text_storage table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS input_text_storage (
            id SERIAL PRIMARY KEY,
            request_id TEXT NOT NULL,
            text_input TEXT NOT NULL,
            url_input TEXT,
            created_at TEXT NOT NULL,
            retention_days INTEGER DEFAULT 90
        );
        """)

        # centralized_videos table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS centralized_videos (
            id SERIAL PRIMARY KEY,
            booking_id INTEGER NULL,
            request_id TEXT NOT NULL,
            session_id TEXT NOT NULL,
            provider TEXT NOT NULL,
            cabin_id INTEGER NULL,
            video_type TEXT,
            timestamp TEXT NOT NULL,
            minio_url TEXT,
            minio_bucket TEXT,
            minio_object_key TEXT,
            file_name TEXT,
            duration INTEGER,
            format TEXT,
            file_size_kb INTEGER DEFAULT 0,
            booking_table TEXT,
            booking_table_id INTEGER NULL,
            -- Legacy column for backward compatibility (will be removed later)
            video_data BYTEA,
            size_kb INTEGER
        );
        """)

        # templates table
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS templates (
            id SERIAL PRIMARY KEY,
            provider TEXT NOT NULL,
            template_id TEXT NOT NULL,
            template_content TEXT NOT NULL,
            created_at TEXT NOT NULL DEFAULT (CURRENT_TIMESTAMP::TEXT),
            updated_at TEXT NOT NULL DEFAULT (CURRENT_TIMESTAMP::TEXT)
        );
        """)

        # Create indexes
        index_statements = [
            "CREATE INDEX IF NOT EXISTS idx_cp_bookings_session ON cruising_power_bookings(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_cp_bookings_request ON cruising_power_bookings(request_id)",
            "CREATE INDEX IF NOT EXISTS idx_cp_cabins_booking ON cruising_power_cabins(booking_id)",
            "CREATE INDEX IF NOT EXISTS idx_session_tracking_request ON session_tracking(request_id, provider, updated_at)",
            "CREATE INDEX IF NOT EXISTS idx_screenshots_provider_session ON centralized_screenshots(provider, session_id)",
            "CREATE INDEX IF NOT EXISTS idx_studio_bookings_session ON studio_bookings(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_studio_bookings_request ON studio_bookings(request_id)",
            "CREATE INDEX IF NOT EXISTS idx_studio_cabins_booking ON studio_cabins(booking_id)",
            "CREATE INDEX IF NOT EXISTS idx_ncl_bookings_session ON ncl_bookings(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_ncl_bookings_request ON ncl_bookings(request_id)",
            "CREATE INDEX IF NOT EXISTS idx_ncl_cabins_booking ON ncl_cabins(booking_id)",
            "CREATE INDEX IF NOT EXISTS idx_studio_bookings_req_sess ON studio_bookings(request_id, session_id)",
            "CREATE INDEX IF NOT EXISTS idx_onesource_bookings_session ON onesource_bookings(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_onesource_bookings_request ON onesource_bookings(request_id)",
            "CREATE INDEX IF NOT EXISTS idx_onesource_cabins_booking ON onesource_cabins(booking_id)",
            "CREATE INDEX IF NOT EXISTS idx_onesource_bookings_req_sess ON onesource_bookings(request_id, session_id)",
            "CREATE INDEX IF NOT EXISTS idx_templates_provider ON templates(provider)",
            "CREATE INDEX IF NOT EXISTS idx_templates_template_id ON templates(template_id)",
            "CREATE INDEX IF NOT EXISTS idx_templates_provider_template_id ON templates(provider, template_id)"
        ]
        for stmt in index_statements:
            await conn.execute(stmt + ";")        
        # Call the seed functions - these should be updated to be async as well
        await seed_admin()
        await seed_providers()

async def setup_database():
    """Set up a complete database from scratch including tables and admin account"""
    try:
        await initialize_database()
        await migrate_to_minio_storage()
        await migrate_studio_discounts()
        await migrate_templates_table()
        await migrate_issue_codes_column()
        logger.info("Database setup completed successfully.")
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(setup_database()) 