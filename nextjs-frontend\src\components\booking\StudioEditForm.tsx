import React, { useState, useEffect } from 'react';

interface StudioEditFormProps {
  details: string[];
  onSave: (updatedDetails: string[]) => void;
  onCancel: () => void;
}

const StudioEditForm: React.FC<StudioEditFormProps> = ({ details, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    sailingDate: '',
    duration: '',
    shipId: '',
    airport: '',
    numCabins: 1,
    passengers: [] as string[],
    categories: [] as string[]
  });

  // Initialize form data from the provided details
  useEffect(() => {
    if (details && details.length >= 5) {
      const sailing_date = details[0];
      const duration = details[1];
      const ship_id = details[2];
      const airport = details[3];
      const num_cabins = parseInt(details[4]);

      const passengers = details.slice(5, 5 + num_cabins);
      const categories = details.slice(5 + num_cabins);

      setFormData({
        sailingDate: sailing_date,
        duration: duration,
        shipId: ship_id,
        airport: airport,
        numCabins: num_cabins,
        passengers: [...passengers],
        categories: [...categories]
      });
    }
  }, [details]);

  // Handle changes to basic details
  const handleBasicChange = (field: string, value: string) => {
    setFormData({ ...formData, [field]: value });
  };

  // Handle changes to the number of cabins
  const handleNumCabinsChange = (num: number) => {
    const newFormData = { ...formData, numCabins: num };

    // Adjust passengers array if needed
    while (newFormData.passengers.length < num) {
      newFormData.passengers.push("2"); // Default 2 passengers
    }

    // Adjust categories array if needed
    while (newFormData.categories.length < num) {
      newFormData.categories.push("INSIDE"); // Default INSIDE category
    }

    // Trim if number of cabins decreased
    if (newFormData.passengers.length > num) {
      newFormData.passengers = newFormData.passengers.slice(0, num);
      newFormData.categories = newFormData.categories.slice(0, num);
    }

    setFormData(newFormData);
  };

  // Handle passenger count change
  const handlePassengerChange = (index: number, value: string) => {
    const newPassengers = [...formData.passengers];
    newPassengers[index] = value;
    setFormData({ ...formData, passengers: newPassengers });
  };

  // Handle category change
  const handleCategoryChange = (index: number, value: string) => {
    const newCategories = [...formData.categories];
    newCategories[index] = value.toUpperCase();
    setFormData({ ...formData, categories: newCategories });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const updatedDetails = [
      formData.sailingDate,
      formData.duration,
      formData.shipId,
      formData.airport,
      formData.numCabins.toString(),
      ...formData.passengers,
      ...formData.categories
    ];

    onSave(updatedDetails);
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="mb-6">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 mr-2 text-teal-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <h3 className="text-2xl font-bold text-gray-800">Edit Cruise Details</h3>
          </div>
          <div className="mt-2 relative">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-20 h-0.5 bg-gray-800"></div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-6">
          <div>
            <label className="block text-base font-medium text-gray-700 mb-2">
              Sailing Date (MM-DD-YYYY)
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white text-base"
              value={formData.sailingDate}
              onChange={(e) => handleBasicChange('sailingDate', e.target.value)}
              required
            />
          </div>

          <div className="relative">
            <label className="block text-base font-medium text-gray-700 mb-2">
              Duration (nights)
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white text-base"
              value={formData.duration}
              onChange={(e) => handleBasicChange('duration', e.target.value)}
              required
            />
          </div>


          <div className="relative">
            <label className="block text-base font-medium text-gray-700 mb-2">
              Ship ID
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white text-base"
              value={formData.shipId}
              onChange={(e) => handleBasicChange('shipId', e.target.value)}
              required
            />
          </div>

          <div className="relative">
            <label className="block text-base font-medium text-gray-700 mb-2">
              Airport Code
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white text-base"
              value={formData.airport}
              onChange={(e) => handleBasicChange('airport', e.target.value)}
              required
            />
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-base font-medium text-gray-700 mb-2">
            Number of Cabins
          </label>
          <input
            type="number"
            className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white text-base"
            min="1"
            max="10"
            value={formData.numCabins}
            onChange={(e) => handleNumCabinsChange(parseInt(e.target.value) || 1)}
            required
          />
        </div>


        <div className="mb-4 mt-8">
          <div className="flex items-center">
            <div className="w-4 h-4 rounded-full bg-teal-500 mr-2"></div>
            <h3 className="text-xl font-bold text-gray-800">Cabin Details</h3>
          </div>
          <div className="mt-2 relative">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
          </div>
        </div>

        <div className="space-y-5 my-5">
          {Array.from({ length: formData.numCabins }).map((_, index) => (
            <div
              key={index}
              className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 rounded-full bg-teal-700 text-white flex items-center justify-center mr-2 shadow-sm">
                  {index + 1}
                </div>
                <h5 className="font-semibold text-teal-800 text-lg">Cabin {index + 1}</h5>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-base font-medium text-gray-700 mb-2">
                    Number of Passengers
                  </label>
                  <input
                    type="number"
                    className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white text-base"
                    min="1"
                    max="5"
                    value={formData.passengers[index] || '2'}
                    onChange={(e) => handlePassengerChange(index, e.target.value)}
                    required
                  />
                </div>

                <div>
                  <label className="block text-base font-medium text-gray-700 mb-2">
                    Cabin Category
                  </label>
                  <select
                    className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white text-base"
                    value={formData.categories[index] || 'INSIDE'}
                    onChange={(e) => handleCategoryChange(index, e.target.value)}
                    required
                  >
                    <option value="INSIDE">INSIDE</option>
                    <option value="OCEANVIEW">OCEANVIEW</option>
                    <option value="BALCONY">BALCONY</option>
                    <option value="SUITE">SUITE</option>
                  </select>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            className="px-5 py-2.5 text-white rounded-lg shadow-md bg-gradient-to-r from-gray-700 to-gray-900 hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200 text-base font-medium"
            onClick={onCancel}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-5 py-2.5 text-white rounded-lg shadow-md bg-gradient-to-r from-teal-700 to-slate-800 hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200 text-base font-medium"
          >
            Save Changes
          </button>
        </div>
      </form>
    </div>
  );
};

export default StudioEditForm; 