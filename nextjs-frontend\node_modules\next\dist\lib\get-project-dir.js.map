{"version": 3, "sources": ["../../src/lib/get-project-dir.ts"], "names": ["getProjectDir", "dir", "resolvedDir", "path", "resolve", "realDir", "realpathSync", "toLowerCase", "Log", "warn", "err", "code", "detectedTypo", "detectTypo", "Object", "keys", "commands", "error", "process", "exit"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;6DANC;0BACQ;6DACJ;4BACM;0BACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,SAASA,cAAcC,GAAY;IACxC,IAAI;QACF,MAAMC,cAAcC,aAAI,CAACC,OAAO,CAACH,OAAO;QACxC,MAAMI,UAAUC,IAAAA,sBAAY,EAACJ;QAE7B,IACEA,gBAAgBG,WAChBH,YAAYK,WAAW,OAAOF,QAAQE,WAAW,IACjD;YACAC,KAAIC,IAAI,CACN,CAAC,kDAAkD,EAAEP,YAAY,aAAa,EAAEG,QAAQ,gFAAgF,CAAC;QAE7K;QAEA,OAAOA;IACT,EAAE,OAAOK,KAAU;QACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;YACzB,IAAI,OAAOV,QAAQ,UAAU;gBAC3B,MAAMW,eAAeC,IAAAA,sBAAU,EAACZ,KAAKa,OAAOC,IAAI,CAACC,kBAAQ;gBAEzD,IAAIJ,cAAc;oBAChBJ,KAAIS,KAAK,CACP,CAAC,MAAM,EAAEhB,IAAI,qCAAqC,EAAEW,aAAa,EAAE,CAAC;oBAEtEM,QAAQC,IAAI,CAAC;gBACf;YACF;YAEAX,KAAIS,KAAK,CACP,CAAC,uDAAuD,EAAEd,aAAI,CAACC,OAAO,CACpEH,OAAO,KACP,CAAC;YAELiB,QAAQC,IAAI,CAAC;QACf;QACA,MAAMT;IACR;AACF"}