"""
Provider Seeding Module

This module seeds the initial provider data into the database.
"""

import logging
import asyncio
from db_service import get_db_connection, db_connection

logger = logging.getLogger("database.seed_providers")

async def seed_providers():
    """
    Seed the providers table with initial provider data
    """
    # Define the providers to seed
    providers = [
        {
            "provider_id": 1,
            "name": "Studio.Res.CabinCloseOut",
            "agency": "STUDIO",
            "description": "Studio Reservation Cabin CloseOut",
            "status": "active"
        },
        {
            "provider_id": 2,
            "name": "NCL",
            "agency": "CNM",
            "description": "Norwegian Cruise Line",
            "status": "active"
        },
        {
            "provider_id": 3,
            "name": "Cruising Power",
            "agency": "CNM",
            "description": "Royal Caribbean & Celebrity Cruise",
            "status": "active"
        },
        {
            "provider_id": 4,
            "name": "OneSource",
            "agency": "CNM",
            "description": "Princess & Holland Cruise",
            "status": "coming_soon"
        },
        {
            "provider_id": 5,
            "name": "Studio.Sales.CabinCloseOut",
            "agency": "STUDIO",
            "description": "Studio Sales Cabin CloseOut",
            "status": "active"
        },
        {
            "provider_id": 6,
            "name": "U R Clubs",
            "agency": "HP",
            "description": "U R Clubs",
            "status": "maintenance"
        },
        {
            "provider_id": 7,
            "name": "GG",
            "agency": "HP",
            "description": "Golden Grass",
            "status": "coming_soon"
        }
    ]
    
    try:
        async with db_connection() as conn:
        
            # Check if any providers already exist
            row = await conn.fetchrow("SELECT COUNT(*) FROM providers")
            count = row[0]

            if count > 0:
                logger.info(f"Found {count} existing providers, skipping seeding.")
                return

            # Insert providers
            for provider in providers:
                await conn.execute(
                    """
                    INSERT INTO providers (provider_id, name, agency, description, status)
                    VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (provider_id) DO NOTHING
                    """,
                    provider["provider_id"],
                    provider["name"],
                    provider["agency"],
                    provider["description"],
                    provider["status"]
                )

            logger.info(f"Successfully seeded {len(providers)} providers")

    except Exception as e:
        logger.error(f"Error seeding providers: {e}")
        raise

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(seed_providers()) 