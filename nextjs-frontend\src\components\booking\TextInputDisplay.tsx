import React, { useState, useEffect, useRef } from 'react';
import { getStoredInputText } from '../../services/api';
import { ProviderType } from './ProviderSelector';

interface TextInputDisplayProps {
  provider: ProviderType;
  requestId: string;
}

const TextInputDisplay: React.FC<TextInputDisplayProps> = ({ provider, requestId }) => {
  const [textData, setTextData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Prevent duplicate API calls
  const hasFetchedRef = useRef<string | null>(null);

  useEffect(() => {
    // Prevent duplicate calls for the same request
    if (hasFetchedRef.current === requestId) {
      return;
    }
    
    const fetchTextData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getStoredInputText(requestId);
        setTextData(data);
        hasFetchedRef.current = requestId; // Mark as fetched
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load text input');
        console.error('Error fetching stored text input:', err);
      } finally {
        setLoading(false);
      }
    };

    if (requestId) {
      fetchTextData();
    }
  }, [requestId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-6">
        <div className="flex items-center gap-3">
          <svg className="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span className="text-gray-600">Loading original text input...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center gap-2 text-red-600">
          <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <p className="font-semibold">Error loading text input</p>
        </div>
        <p className="text-red-600 text-sm mt-1">{error}</p>
      </div>
    );
  }

  if (!textData) {
    return (
      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <p className="text-gray-500 text-center">No original text input available for this request</p>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch {
      return dateString;
    }
  };

  return (
    <div className="space-y-2">
      {/* Header with metadata */}
      <div className="p-1 bg-gray-500/30">
        <div className="mx-2 grid grid-cols-1 md:grid-cols-2 gap-1 text-sm">
          <div>
            <span className="font-bold text-blue-700">Request ID:</span>
            <span className="ml-2 text-black-700 font-semibold text-md">{textData.request_id}</span>
          </div>
          <div>
            <span className="font-bold text-blue-700">Provider:</span>
            <span className="ml-2 text-gray-700">{provider}</span>
          </div>
          <div>
            <span className="font-semibold text-blue-700">Submitted:</span>
            <span className="ml-2 text-gray-700 text-xs">{formatDate(textData.created_at)}</span>
          </div>
        </div>
      </div>

      {/* Original text input */}
      <div className="bg-gray-50">
        <div className="p-2">
            <pre className="whitespace-pre-wrap text-sm text-gray-900 font-mono leading-relaxed">
              {textData.text_input}
            </pre>
        </div>
      </div>

      {/* URL Input if available */}
      {textData.url_input && provider.includes("Studio") && (
        <div className="bg-white border border-gray-300 rounded-lg overflow-hidden">
          <div className="p-2">
              <a 
                href={textData.url_input} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline break-all text-sm"
              >
                {textData.url_input}
              </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default TextInputDisplay; 