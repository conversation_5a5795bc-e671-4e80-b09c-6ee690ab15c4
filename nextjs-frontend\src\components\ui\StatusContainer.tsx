import React, { useState, useRef, useEffect } from 'react';
import { useLogContext, UILog } from '../../contexts/LogContext';
import { ProviderType } from '../booking/ProviderSelector';

interface StatusContainerProps {
  onStatusChange?: (status: string) => void;
  provider: ProviderType;
  isReprocessing?: boolean;
}

// Define milestone type
type MilestoneMap = {
  [key: string]: number;
};

const StatusContainer = ({ onStatusChange, provider, isReprocessing = false }: StatusContainerProps) => {
  const [statusText, setStatusText] = useState<string>('Live Status will appear here - Stay Tuned!');
  const [progress, setProgress] = useState<number>(0);
  const statusRef = useRef<HTMLDivElement>(null);
  const { logs } = useLogContext();
  
  // Reset progress when reprocessing starts
  useEffect(() => {
    if (isReprocessing) {
      setProgress(0);
      setStatusText('Live Status will appear here - Stay Tuned!');
    }
  }, [isReprocessing]);

  // Define milestone logs and their corresponding progress percentages for each provider
  const getProviderMilestones = (): MilestoneMap => {
    switch (provider) {
      case 'Studio.Sales.CabinCloseOut':
      case 'Studio.Res.CabinCloseOut':
        return {
          // Studio milestone mappings based on info.md
          'Initializing the browser — getting ready to roll': 5,
          'Browser setup complete — let\'s move forward': 10,
          'Browser setup encountered an issue': 8,
          'Fetching page content, hang tight': 15,
          'The website is loading a bit slower than usual — please hang tight': 18,
          'All set! The page has loaded successfully': 25,
          'Page is up, but the content we\'re looking for didn\'t load': 20,
          'Page failed to load — trying to recover': 22,
          'Waiting for the page to load — almost there': 23,
          'Page failed to load, but recovery is in progress — please stand by': 24,
          'Fetching category information — hang tight': 30,
          'This sailing can\'t be booked at the moment, Try again later': 32,
          'Hang tight — we\'re processing your booking now': 35,
          'Working on processing cabin': 38,  // Partial match for "Working on processing cabin {cabin_number}"
          'Getting the cabin booking underway': 40,
          'Chosen rate code:': 42,  // Partial match for "Chosen rate code: {clean_rate_name}"
          'Searching for available cabins': 45,
          'Cabin': 47,  // Partial match for "Cabin {selected_cabin['cabin_number']} Selected"
          'Could not continue to cabin selection. Retrying': 48,
          'Verifying cabin selection': 50,
          'There was a problem selecting your cabin. We\'re selecting an alternative now': 52,
          'The system couldn\'t find a suitable alternative cabin': 53,
          'Cabin selected successfully': 55,
          'Got the price for cabin': 85,  // Partial match for "Got the price for cabin {cabin_number} — thanks for waiting"
          'Oops! Booking for cabin': 90,  // Partial match for "Oops! Booking for cabin {cabin_number} didn't go through this time"
          'Oops! Booking didn\'t go through this time': 95,
          'Success! Your booking is all set': 100
        };
      case 'NCL':
        return {
          // NCL milestone mappings based on info.md
          'Browser setup complete — ready to move forward': 10,
          'Browser setup failed — trying to recover': 8,
          'I am logged in successfully — ready to get started': 25,
          'I couldn\'t log in — retrying now': 22,
          'Cruise selected successfully — all set': 40,
          'Selecting cabin category — hang tight': 55,
          'Cabin category selected successfully — moving ahead': 70,
          'Promotions applied successfully — enjoy your savings': 85,
          'No promotions could be selected right now — please try again': 80,
          'Price retrieved successfully — thanks for waiting': 100,
          'There was an error during the booking process — please try again': 90
        };
      case 'Cruising Power':
        return {
          // Cruising Power milestone mappings based on info.md
          'Initializing browser — just a moment': 5,
          'Browser setup complete — let\'s get started': 10,
          'Navigating to the login page — one moment': 15,
          'Entering credentials — just a moment': 18,
          'I\'m logged in successfully — let\'s get started': 25,
          'Login failed — retrying now': 22,
          'There was an error during login — retrying now': 23,
          'Starting cruise search — hang tight': 30,
          'Oops! There was an error during the search — retrying now': 32,
          'Search results are in — verifying now': 40,
          'Analyzing cabin options — please hold on': 45,
          'Analyzing all cabin categories — please hold on': 50,
          'Cabin analysis complete — ready to proceed': 55,
          'Cabin selection complete — moving forward': 65,
          'Error selecting cabin — retrying now': 62,
          'Failed to select cabin — retrying now': 63,
          'Error selecting category — retrying now': 57,
          'Retrieving price information — just a moment': 70,
          'Getting your price quote — please hold on': 75,
          'Calculating your onboard credit — just a moment': 80,
          'Pricing information retrieved — thanks for your patience': 90,
          'There was an error while getting the price quote — retrying now': 85,
          'Error retrieving detailed pricing — retrying now': 87,
          'No cabin data found — please try again': 60
        };
      case 'OneSource':
        return {
          // OneSource milestone mappings
          'Starting OneSource booking process': 5,
          'Processing': 10,
          'Filling booking form': 15,
          'Booking form filled successfully': 25,
          'Starting category selection': 30,
          'Category selection completed successfully': 45,
          'Calculated final price': 70,
          'Booking completed successfully': 85,
          'OneSource booking completed successfully': 100,
          'Booking failed: No cabins processed successfully': 100,
          'OneSource booking failed: No cabins processed successfully': 100,
          'Total execution time': 95
        };
      default:
        return {};
    }
  };

  // Update progress based on logs or reset when logs are cleared
  useEffect(() => {
    // If logs array is empty, reset the status bar
    if (!logs.length) {
      setProgress(0);
      setStatusText('Live Status will appear here - Stay Tuned!');
      return;
    }
    
    const milestones = getProviderMilestones();
    let highestProgress = 0;
    let latestLogMessage = '';
    
    // Find the highest progress milestone reached
    logs.forEach(log => {
      // Check for exact matches
      if (log.message in milestones) {
        const milestoneProgress = milestones[log.message];
        if (milestoneProgress > highestProgress) {
          highestProgress = milestoneProgress;
          latestLogMessage = log.message;
        }
      } else {
        // Check for partial matches (contains)
        for (const [milestone, milestoneProgress] of Object.entries(milestones)) {
          if (log.message.includes(milestone) && milestoneProgress > highestProgress) {
            highestProgress = milestoneProgress;
            latestLogMessage = log.message;
          }
        }
      }
    });
    
    // Update progress and status text
    if (highestProgress > progress) {
      setProgress(highestProgress);
      setStatusText(latestLogMessage);
      
      if (onStatusChange) {
        onStatusChange(latestLogMessage);
      }
    }
  }, [logs, provider, progress, onStatusChange]);

  // Scroll to bottom of status container when new content is added
  useEffect(() => {
    if (statusRef.current) {
      statusRef.current.scrollTop = statusRef.current.scrollHeight;
    }
  }, [statusText]);

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-2">
      <h3 className="text-lg font-semibold mb-0">Status</h3>
      <div className="mt-0 relative pb-2 mb-2">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
          </div>
      <div 
        ref={statusRef}
        className="h-14 overflow-y-auto mb-2 ml-2 mr-2 p-2 bg-white rounded"
      >
        <p className="whitespace-pre-line">{statusText}</p>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-4">
        <div 
          className="bg-blue-600 h-4 rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
    </div>
  );
};

export default StatusContainer; 