{"version": 3, "sources": ["../../../../src/client/components/router-reducer/get-prefetch-cache-entry-status.ts"], "names": ["getPrefetchEntryCacheStatus", "FIVE_MINUTES", "THIRTY_SECONDS", "PrefetchCacheEntryStatus", "fresh", "reusable", "expired", "stale", "kind", "prefetchTime", "lastUsedTime", "Date", "now"], "mappings": ";;;;;;;;;;;;;;;;;;IAYgBA,2BAA2B;eAA3BA;;;AAVhB,MAAMC,eAAe,IAAI,KAAK;AAC9B,MAAMC,iBAAiB,KAAK;IAErB;UAAKC,wBAAwB;IAAxBA,yBACVC,WAAAA;IADUD,yBAEVE,cAAAA;IAFUF,yBAGVG,aAAAA;IAHUH,yBAIVI,WAAAA;GAJUJ,6BAAAA;AAOL,SAASH,4BAA4B,KAIvB;IAJuB,IAAA,EAC1CQ,IAAI,EACJC,YAAY,EACZC,YAAY,EACO,GAJuB;IAK1C,yFAAyF;IACzF,IAAIC,KAAKC,GAAG,KAAK,AAACF,CAAAA,uBAAAA,eAAgBD,YAAW,IAAKP,gBAAgB;QAChE,OAAOQ,eAZE,aADH;IAgBR;IAEA,wGAAwG;IACxG,IAAIF,SAAS,QAAQ;QACnB,IAAIG,KAAKC,GAAG,KAAKH,eAAeR,cAAc;YAC5C,OAlBI;QAmBN;IACF;IAEA,oHAAoH;IACpH,IAAIO,SAAS,QAAQ;QACnB,IAAIG,KAAKC,GAAG,KAAKH,eAAeR,cAAc;YAC5C,OA3BO;QA4BT;IACF;IAEA,OA9BU;AA+BZ"}