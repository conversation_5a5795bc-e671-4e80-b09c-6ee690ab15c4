/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Ccomponents%5Cui%5CSessionTimer.tsx&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Ccontext%5CAuthContext.tsx&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Ccomponents%5Cui%5CSessionTimer.tsx&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Ccontext%5CAuthContext.tsx&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-poppins\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/SessionTimer.tsx */ \"(app-pages-browser)/./src/components/ui/SessionTimer.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(app-pages-browser)/./src/context/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Ccomponents%5Cui%5CSessionTimer.tsx&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Ccontext%5CAuthContext.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"3cb36c7b3e08\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OTJkYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjNjYjM2YzdiM2UwOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/SessionTimer.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/SessionTimer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/AuthContext */ \"(app-pages-browser)/./src/context/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst SessionTimer = ()=>{\n    _s();\n    const [timeRemaining, setTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isLoggedIn, getToken, logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Function to decode JWT and get expiration time\n    const getTokenExpiration = ()=>{\n        try {\n            const token = getToken();\n            if (!token) return 0;\n            // Decode JWT token (simple base64 decode for the payload)\n            const payload = JSON.parse(atob(token.split(\".\")[1]));\n            return payload.exp || 0;\n        } catch (error) {\n            console.error(\"Error decoding token:\", error);\n            return 0;\n        }\n    };\n    // Calculate remaining time in seconds\n    const calculateTimeRemaining = ()=>{\n        const exp = getTokenExpiration();\n        const now = Math.floor(Date.now() / 1000);\n        return Math.max(0, exp - now);\n    };\n    // Format time as MM:SS\n    const formatTime = (seconds)=>{\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = seconds % 60;\n        return \"\".concat(minutes.toString().padStart(2, \"0\"), \":\").concat(remainingSeconds.toString().padStart(2, \"0\"));\n    };\n    // Get color based on remaining time\n    const getTimerColor = (seconds)=>{\n        if (seconds > 600) return \"text-green-600 bg-green-50 border-green-200\"; // > 10 minutes\n        if (seconds > 300) return \"text-yellow-600 bg-yellow-50 border-yellow-200\"; // > 5 minutes\n        if (seconds > 120) return \"text-orange-600 bg-orange-50 border-orange-200\"; // > 2 minutes\n        return \"text-red-600 bg-red-50 border-red-200\"; // < 2 minutes\n    };\n    // Update timer every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoggedIn) {\n            setIsVisible(false);\n            return;\n        }\n        const updateTimer = ()=>{\n            const remaining = calculateTimeRemaining();\n            setTimeRemaining(remaining);\n            // Show timer when less than 30 minutes remaining\n            setIsVisible(remaining > 0 && remaining < 1800);\n            // Auto logout when token expires\n            if (remaining <= 0) {\n                logout();\n            }\n            // Show warning notifications\n            if (remaining === 300) {\n                alert(\"Your session will expire in 5 minutes.\");\n            } else if (remaining === 120) {\n                alert(\"Your session will expire in 2 minutes.\");\n            } else if (remaining === 60) {\n                alert(\"Your session will expire in 1 minute.\");\n            }\n        };\n        // Update immediately\n        updateTimer();\n        // Set up interval to update every second\n        const interval = setInterval(updateTimer, 1000);\n        return ()=>clearInterval(interval);\n    }, [\n        isLoggedIn,\n        getToken,\n        logout\n    ]);\n    // Don't render if not logged in or timer not visible\n    if (!isLoggedIn || !isVisible) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-28 xs:top-24 sm:top-20 md:top-4 right-0.5 xs:right-1 sm:right-2 md:right-4 z-30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"px-1 xs:px-1.5 sm:px-2 md:px-3 py-0.5 xs:py-1 sm:py-1.5 md:py-2 rounded-sm xs:rounded-md md:rounded-lg border shadow-lg transition-all duration-300 \".concat(getTimerColor(timeRemaining)),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-0.5 xs:space-x-1 md:space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-2.5 h-2.5 xs:w-3 xs:h-3 md:w-4 md:h-4 flex-shrink-0\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OceanMind-Prod-3.0-1\\\\nextjs-frontend\\\\src\\\\components\\\\ui\\\\SessionTimer.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OceanMind-Prod-3.0-1\\\\nextjs-frontend\\\\src\\\\components\\\\ui\\\\SessionTimer.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs md:text-sm font-medium whitespace-nowrap\",\n                            children: formatTime(timeRemaining)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OceanMind-Prod-3.0-1\\\\nextjs-frontend\\\\src\\\\components\\\\ui\\\\SessionTimer.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OceanMind-Prod-3.0-1\\\\nextjs-frontend\\\\src\\\\components\\\\ui\\\\SessionTimer.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, undefined),\n                timeRemaining < 300 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs mt-1 opacity-75 hidden md:block\",\n                    children: \"Session expires soon\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OceanMind-Prod-3.0-1\\\\nextjs-frontend\\\\src\\\\components\\\\ui\\\\SessionTimer.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OceanMind-Prod-3.0-1\\\\nextjs-frontend\\\\src\\\\components\\\\ui\\\\SessionTimer.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OceanMind-Prod-3.0-1\\\\nextjs-frontend\\\\src\\\\components\\\\ui\\\\SessionTimer.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SessionTimer, \"lnba1XiaT++2qyOn3/KlVAyOAH0=\", false, function() {\n    return [\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SessionTimer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SessionTimer);\nvar _c;\n$RefreshReg$(_c, \"SessionTimer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/SessionTimer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/auth */ \"(app-pages-browser)/./src/services/auth.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n// Create the Auth Context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [accessibleProviders, setAccessibleProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAgencies, setAllAgencies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [accessibleAgencies, setAccessibleAgencies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user is logged in when the app loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = ()=>{\n            try {\n                if ((0,_services_auth__WEBPACK_IMPORTED_MODULE_3__.isAuthenticated)()) {\n                    const userData = (0,_services_auth__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                    setUser(userData);\n                }\n            } catch (error) {\n                console.error(\"Auth check error:\", error);\n                // If we can't get user data, log them out\n                handleLogout();\n            } finally{\n                setLoading(false);\n            }\n        };\n        checkAuth();\n    }, []);\n    // Fetch providers user has access to\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadProviders = async ()=>{\n            if (user) {\n                try {\n                    const providers = await (0,_services_api__WEBPACK_IMPORTED_MODULE_4__.fetchProviders)();\n                    setAccessibleProviders(providers);\n                } catch (e) {\n                    console.error(\"Error loading accessible providers:\", e);\n                    setAccessibleProviders([]);\n                }\n            } else {\n                setAccessibleProviders([]);\n            }\n        };\n        loadProviders();\n    }, [\n        user\n    ]);\n    // Derive unique accessible agencies from providers\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const agencies = Array.from(new Set((accessibleProviders || []).map((p)=>p.agency)));\n        setAccessibleAgencies(agencies);\n    }, [\n        accessibleProviders\n    ]);\n    // Fetch all agencies for admin\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAgencies = async ()=>{\n            try {\n                const agencies = await (0,_services_api__WEBPACK_IMPORTED_MODULE_4__.fetchAgencies)();\n                setAllAgencies(agencies);\n            } catch (e) {\n                console.error(\"Error loading all agencies:\", e);\n                setAllAgencies([]);\n            }\n        };\n        loadAgencies();\n    }, []);\n    const handleLogin = async (username, password)=>{\n        setLoading(true);\n        try {\n            const response = await (0,_services_auth__WEBPACK_IMPORTED_MODULE_3__.login)(username, password);\n            setUser(response.user);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        await (0,_services_auth__WEBPACK_IMPORTED_MODULE_3__.logout)();\n        setUser(null);\n        router.push(\"/\");\n    };\n    // Check if user has access to a specific provider\n    const hasProviderAccess = (provider)=>{\n        if (!user) return false;\n        // Previously admin could access all providers, now we respect their assigned access too\n        // Check if user has this provider in their provider_access array\n        return Array.isArray(user.provider_access) && user.provider_access.includes(provider);\n    };\n    // Get all agencies the user has access to based on their provider access\n    const getAccessibleAgencies = ()=>{\n        if (!user) return [];\n        // Admin has access to all agencies\n        if (user.role === \"admin\") return allAgencies;\n        // Return unique agencies derived from API-fetched providers\n        return accessibleAgencies;\n    };\n    // Check if user can manage another user\n    const canManageUser = (targetUser)=>{\n        if (!user) return false;\n        // Admins can manage all users\n        if (user.role === \"admin\") return true;\n        // Sub-admins cannot manage admins\n        if (user.role === \"sub_admin\" && targetUser.role === \"admin\") return false;\n        // Sub-admins can only manage users within their agency\n        if (user.role === \"sub_admin\") {\n            const userAgencies = getAccessibleAgencies();\n            const targetUserAgency = targetUser.agency;\n            return targetUserAgency ? userAgencies.includes(targetUserAgency) : false;\n        }\n        return false;\n    };\n    // Check if user can access a specific agency\n    const canAccessAgency = (agency)=>{\n        if (!user) return false;\n        // Admins can access all agencies\n        if (user.role === \"admin\") return true;\n        // Sub-admins can only access their assigned agencies\n        if (user.role === \"sub_admin\") {\n            const accessibleAgencies = getAccessibleAgencies();\n            return accessibleAgencies.includes(agency);\n        }\n        return false;\n    };\n    // Derived state\n    const isLoggedIn = user !== null;\n    const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === \"admin\";\n    const isSubAdmin = (user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"sub_admin\";\n    const isSubAdminOnly = (user === null || user === void 0 ? void 0 : user.role) === \"sub_admin\";\n    // Create the context value\n    const contextValue = {\n        user,\n        isLoggedIn,\n        isAdmin,\n        isSubAdmin,\n        login: handleLogin,\n        logout: handleLogout,\n        loading,\n        getCurrentUser: _services_auth__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser,\n        getToken: _services_auth__WEBPACK_IMPORTED_MODULE_3__.getToken,\n        hasProviderAccess,\n        getAccessibleAgencies,\n        canManageUser,\n        canAccessAgency,\n        isSubAdminOnly\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OceanMind-Prod-3.0-1\\\\nextjs-frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 197,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AuthProvider, \"4mhxOhotio0qT6sWtGCabsDKyjM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Custom hook to use the auth context\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_BASE_URL: function() { return /* binding */ API_BASE_URL; },\n/* harmony export */   cancelBooking: function() { return /* binding */ cancelBooking; },\n/* harmony export */   extractDetails: function() { return /* binding */ extractDetails; },\n/* harmony export */   fetchAgencies: function() { return /* binding */ fetchAgencies; },\n/* harmony export */   fetchProviders: function() { return /* binding */ fetchProviders; },\n/* harmony export */   getBookingStatus: function() { return /* binding */ getBookingStatus; },\n/* harmony export */   getCruisingPowerResults: function() { return /* binding */ getCruisingPowerResults; },\n/* harmony export */   getDiscountData: function() { return /* binding */ getDiscountData; },\n/* harmony export */   getNCLResults: function() { return /* binding */ getNCLResults; },\n/* harmony export */   getOneSourceResults: function() { return /* binding */ getOneSourceResults; },\n/* harmony export */   getResultsView: function() { return /* binding */ getResultsView; },\n/* harmony export */   getScreenshots: function() { return /* binding */ getScreenshots; },\n/* harmony export */   getStoredInputText: function() { return /* binding */ getStoredInputText; },\n/* harmony export */   getStudioResults: function() { return /* binding */ getStudioResults; },\n/* harmony export */   getUserTrackingData: function() { return /* binding */ getUserTrackingData; },\n/* harmony export */   startBooking: function() { return /* binding */ startBooking; },\n/* harmony export */   submitValidation: function() { return /* binding */ submitValidation; },\n/* harmony export */   updateProviderStatus: function() { return /* binding */ updateProviderStatus; }\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(app-pages-browser)/./src/services/auth.ts\");\n\n// API configuration\nconst API_BASE_URL = \"https://localhost:8000\";\n// Helper function to add auth token to headers\nconst getAuthHeaders = ()=>{\n    const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getToken)();\n    const headers = {};\n    if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    }\n    return headers;\n};\n// Helper function to handle 401 responses\nconst handleUnauthorized = ()=>{\n    // Import logout function dynamically to avoid circular imports\n    const { logout } = __webpack_require__(/*! ./auth */ \"(app-pages-browser)/./src/services/auth.ts\");\n    logout();\n    // Show session expired message\n    alert(\"Session expired. Please log in again.\");\n    // Redirect to home page\n    if (true) {\n        window.location.href = \"/\";\n    }\n};\n// Enhanced fetch function that handles 401 responses\nconst authFetch = async function(url) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...getAuthHeaders(),\n        ...options.headers\n    };\n    const response = await fetch(url, {\n        ...options,\n        headers\n    });\n    // Check for 401 Unauthorized\n    if (response.status === 401) {\n        handleUnauthorized();\n        throw new Error(\"Session expired. Please log in again.\");\n    }\n    return response;\n};\n// Extraction service\nconst extractDetails = async (provider, data)=>{\n    try {\n        console.log(\"Extracting details for provider: \".concat(provider), data);\n        const response = await authFetch(\"\".concat(API_BASE_URL, \"/extraction\"), {\n            method: \"POST\",\n            body: JSON.stringify({\n                provider,\n                text_input: data.textInput,\n                url_input: data.urlInput,\n                request_id: data.requestId\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n        }\n        const result = await response.json();\n        console.log(\"Extraction response:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"Error in extraction service:\", error);\n        throw error;\n    }\n};\n// Booking service\nconst startBooking = async (provider, cruiseDetails, config, requestId)=>{\n    try {\n        let endpoint = \"\";\n        // Adjust config as needed for specific providers\n        const finalConfig = {\n            ...config,\n            provider\n        };\n        // Log booking request parameters\n        console.log(\"Booking request for \".concat(provider, \" with config:\"), finalConfig);\n        // Handle different providers\n        if (provider.startsWith(\"Studio\")) {\n            endpoint = \"/studio-booking\";\n        } else {\n            switch(provider){\n                case \"NCL\":\n                    endpoint = \"/ncl-booking\";\n                    break;\n                case \"Cruising Power\":\n                    endpoint = \"/cruising-power-booking\";\n                    break;\n                case \"OneSource\":\n                    endpoint = \"/onesource-booking\";\n                    break;\n                default:\n                    throw new Error(\"Unknown provider: \".concat(provider));\n            }\n        }\n        const response = await authFetch(\"\".concat(API_BASE_URL).concat(endpoint), {\n            method: \"POST\",\n            body: JSON.stringify({\n                provider,\n                cruise_details: cruiseDetails,\n                config: finalConfig,\n                request_id: requestId\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(\"API Error \".concat(response.status, \": \").concat(errorText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error in booking service:\", error);\n        throw error;\n    }\n};\n// Get screenshots\nconst getScreenshots = async (sessionId)=>{\n    try {\n        const response = await authFetch(\"\".concat(API_BASE_URL, \"/screenshots/\").concat(sessionId));\n        if (!response.ok) {\n            throw new Error(\"API Error: \".concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching screenshots:\", error);\n        throw error;\n    }\n};\n// Get booking status\nconst getBookingStatus = async (sessionId)=>{\n    try {\n        const response = await authFetch(\"\".concat(API_BASE_URL, \"/booking-status/\").concat(sessionId));\n        if (!response.ok) {\n            throw new Error(\"API Error: \".concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching booking status:\", error);\n        throw error;\n    }\n};\n// Cancel booking\nconst cancelBooking = async function(sessionId) {\n    let reset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n    try {\n        const response = await authFetch(\"\".concat(API_BASE_URL, \"/cancel-booking/\").concat(sessionId, \"?reset=\").concat(reset), {\n            method: \"POST\"\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(\"API Error \".concat(response.status, \": \").concat(errorText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error cancelling booking:\", error);\n        throw error;\n    }\n};\n// Get Studio booking results\nconst getStudioResults = async (requestId, sessionId)=>{\n    try {\n        const url = sessionId ? \"\".concat(API_BASE_URL, \"/studio-results/\").concat(requestId, \"?session_id=\").concat(sessionId) : \"\".concat(API_BASE_URL, \"/studio-results/\").concat(requestId);\n        const response = await authFetch(url);\n        if (!response.ok) {\n            throw new Error(\"API Error: \".concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching Studio results:\", error);\n        throw error;\n    }\n};\n// Get NCL booking results\nconst getNCLResults = async (requestId, sessionId)=>{\n    try {\n        const url = sessionId ? \"\".concat(API_BASE_URL, \"/ncl-results/\").concat(requestId, \"?session_id=\").concat(sessionId) : \"\".concat(API_BASE_URL, \"/ncl-results/\").concat(requestId);\n        const response = await authFetch(url);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.detail || \"API Error: \".concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching NCL results:\", error);\n        throw error;\n    }\n};\n// Get Cruising Power booking results\nconst getCruisingPowerResults = async (requestId, sessionId)=>{\n    try {\n        const url = sessionId ? \"\".concat(API_BASE_URL, \"/cruising-power-results/\").concat(requestId, \"?session_id=\").concat(sessionId) : \"\".concat(API_BASE_URL, \"/cruising-power-results/\").concat(requestId);\n        const response = await authFetch(url);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.detail || \"API Error: \".concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching Cruising Power results:\", error);\n        throw error;\n    }\n};\n// Get OneSource booking results\nconst getOneSourceResults = async (requestId, sessionId)=>{\n    try {\n        const url = sessionId ? \"\".concat(API_BASE_URL, \"/onesource-results/\").concat(requestId, \"?session_id=\").concat(sessionId) : \"\".concat(API_BASE_URL, \"/onesource-results/\").concat(requestId);\n        const response = await authFetch(url);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.detail || \"API Error: \".concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching OneSource results:\", error);\n        throw error;\n    }\n};\nconst getResultsView = async (provider, requestId, sessionId)=>{\n    try {\n        const query = new URLSearchParams({\n            provider\n        });\n        if (sessionId) query.append(\"session_id\", sessionId);\n        const response = await authFetch(\"\".concat(API_BASE_URL, \"/results-view/\").concat(requestId, \"?\").concat(query.toString()));\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.detail || \"API Error: \".concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching consolidated results view:\", error);\n        throw error;\n    }\n};\nconst submitValidation = async (data)=>{\n    try {\n        const response = await authFetch(\"\".concat(API_BASE_URL, \"/api/user-tracking/validate\"), {\n            method: \"PUT\",\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n        }\n        const result = await response.json();\n        return result;\n    } catch (error) {\n        console.error(\"Error submitting validation:\", error);\n        throw error;\n    }\n};\n// Input Text Storage functions\nconst getStoredInputText = async (requestId)=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/input-text/\").concat(requestId), {\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"API Error: \".concat(response.statusText));\n        }\n        const result = await response.json();\n        return result.data;\n    } catch (error) {\n        console.error(\"Error fetching stored input text:\", error);\n        throw error;\n    }\n};\n// Get user tracking data with filters\nconst getUserTrackingData = async (provider, username)=>{\n    try {\n        const queryParams = new URLSearchParams();\n        if (provider && provider !== \"all\") queryParams.append(\"provider\", provider);\n        if (username) queryParams.append(\"username\", username);\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/user-tracking/data?\").concat(queryParams.toString()), {\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.detail || \"API Error: \".concat(response.statusText));\n        }\n        const result = await response.json();\n        if (!result.success) {\n            throw new Error(result.detail || \"Failed to fetch tracking data\");\n        }\n        return result.data;\n    } catch (error) {\n        console.error(\"Error fetching user tracking data:\", error);\n        throw error;\n    }\n};\nconst fetchProviders = async ()=>{\n    try {\n        const response = await authFetch(\"\".concat(API_BASE_URL, \"/api/providers\"), {\n            method: \"GET\"\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch providers\");\n        }\n        const data = await response.json();\n        return data.providers;\n    } catch (error) {\n        console.error(\"Error fetching providers:\", error);\n        throw error;\n    }\n};\nconst fetchAgencies = async ()=>{\n    try {\n        const response = await authFetch(\"\".concat(API_BASE_URL, \"/api/agencies\"), {\n            method: \"GET\"\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch agencies\");\n        }\n        const data = await response.json();\n        return data.agencies;\n    } catch (error) {\n        console.error(\"Error fetching agencies:\", error);\n        throw error;\n    }\n};\n/**\r\n * Update a provider's status.\r\n * @param providerId The ID of the provider to update\r\n * @param status One of 'active' | 'coming_soon' | 'maintenance' | 'deprecated'\r\n */ const updateProviderStatus = async (providerId, status)=>{\n    try {\n        const response = await authFetch(\"\".concat(API_BASE_URL, \"/api/providers/\").concat(providerId, \"/status\"), {\n            method: \"PUT\",\n            body: JSON.stringify({\n                status\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to update provider status\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating provider status:\", error);\n        throw error;\n    }\n};\n// Get discount data for Studio bookings\nconst getDiscountData = async (requestId, sessionId)=>{\n    try {\n        let url = \"\".concat(API_BASE_URL, \"/api/discount-data/\").concat(requestId);\n        if (sessionId) {\n            url += \"?session_id=\".concat(sessionId);\n        }\n        const response = await authFetch(url, {\n            method: \"GET\"\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.detail || \"API Error: \".concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error getting discount data:\", error);\n        throw error;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/auth.ts":
/*!******************************!*\
  !*** ./src/services/auth.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   approveUser: function() { return /* binding */ approveUser; },\n/* harmony export */   authFetch: function() { return /* binding */ authFetch; },\n/* harmony export */   createProvider: function() { return /* binding */ createProvider; },\n/* harmony export */   deleteProvider: function() { return /* binding */ deleteProvider; },\n/* harmony export */   deleteUser: function() { return /* binding */ deleteUser; },\n/* harmony export */   fetchAgencies: function() { return /* binding */ fetchAgencies; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   getPendingUsers: function() { return /* binding */ getPendingUsers; },\n/* harmony export */   getProviders: function() { return /* binding */ getProviders; },\n/* harmony export */   getToken: function() { return /* binding */ getToken; },\n/* harmony export */   getUser: function() { return /* binding */ getUser; },\n/* harmony export */   getUsers: function() { return /* binding */ getUsers; },\n/* harmony export */   initializeTokenExpiryCheck: function() { return /* binding */ initializeTokenExpiryCheck; },\n/* harmony export */   isAuthenticated: function() { return /* binding */ isAuthenticated; },\n/* harmony export */   login: function() { return /* binding */ login; },\n/* harmony export */   logout: function() { return /* binding */ logout; },\n/* harmony export */   registerUser: function() { return /* binding */ registerUser; },\n/* harmony export */   rejectUser: function() { return /* binding */ rejectUser; },\n/* harmony export */   suspendUser: function() { return /* binding */ suspendUser; },\n/* harmony export */   unsuspendUser: function() { return /* binding */ unsuspendUser; },\n/* harmony export */   updateProvider: function() { return /* binding */ updateProvider; },\n/* harmony export */   updateProviderStatus: function() { return /* binding */ updateProviderStatus; },\n/* harmony export */   updateUser: function() { return /* binding */ updateUser; },\n/* harmony export */   updateUserRole: function() { return /* binding */ updateUserRole; }\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(app-pages-browser)/./src/services/api.ts\");\n\n// Token expiry timer\nlet tokenExpiryTimer = null;\n// Function to decode JWT token and get expiration time\nconst decodeJWT = (token)=>{\n    try {\n        const base64Url = token.split(\".\")[1];\n        const base64 = base64Url.replace(/-/g, \"+\").replace(/_/g, \"/\");\n        const jsonPayload = decodeURIComponent(atob(base64).split(\"\").map(function(c) {\n            return \"%\" + (\"00\" + c.charCodeAt(0).toString(16)).slice(-2);\n        }).join(\"\"));\n        return JSON.parse(jsonPayload);\n    } catch (error) {\n        console.error(\"Error decoding JWT token:\", error);\n        return null;\n    }\n};\n// Function to check if token is expired\nconst isTokenExpired = (token)=>{\n    const decoded = decodeJWT(token);\n    if (!decoded || !decoded.exp) {\n        return true; // If we can't decode or no expiration, consider it expired\n    }\n    const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds\n    return decoded.exp < currentTime;\n};\n// Function to get time until token expires (in milliseconds)\nconst getTimeUntilExpiry = (token)=>{\n    const decoded = decodeJWT(token);\n    if (!decoded || !decoded.exp) {\n        return 0;\n    }\n    const currentTime = Math.floor(Date.now() / 1000);\n    const timeUntilExpiry = (decoded.exp - currentTime) * 1000; // Convert to milliseconds\n    return Math.max(0, timeUntilExpiry);\n};\n// Function to set up automatic logout when token expires\nconst setupTokenExpiryCheck = (token)=>{\n    // Clear any existing timer\n    if (tokenExpiryTimer) {\n        clearTimeout(tokenExpiryTimer);\n    }\n    // Check if token is already expired\n    if (isTokenExpired(token)) {\n        logout();\n        alert(\"Session expired. Please log in again.\");\n        if (true) {\n            window.location.href = \"/\";\n        }\n        return;\n    }\n    // Get time until expiry\n    const timeUntilExpiry = getTimeUntilExpiry(token);\n    // Set timer to logout when token expires\n    tokenExpiryTimer = setTimeout(()=>{\n        logout();\n        alert(\"Session expired. Please log in again.\");\n        if (true) {\n            window.location.href = \"/\";\n        }\n    }, timeUntilExpiry);\n    console.log(\"Token will expire in \".concat(Math.round(timeUntilExpiry / 1000), \" seconds\"));\n};\n// Helper for authentication headers\nconst getAuthHeaders = ()=>{\n    const token = getToken();\n    return token ? {\n        \"Authorization\": \"Bearer \".concat(token)\n    } : {};\n};\n// API calls with authentication header\nconst authFetch = async function(url) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const headers = {\n        ...options.headers,\n        ...getAuthHeaders()\n    };\n    const response = await fetch(url, {\n        ...options,\n        headers\n    });\n    // Check for 401 Unauthorized - token expired or invalid\n    if (response.status === 401) {\n        // Clear token and user data\n        logout();\n        // Show session expired message\n        alert(\"Session expired. Please log in again.\");\n        // Redirect to home page\n        if (true) {\n            window.location.href = \"/\";\n        }\n        throw new Error(\"Session expired. Please log in again.\");\n    }\n    return response;\n};\n// Auth service functions\nconst login = async (username, password)=>{\n    try {\n        // The API expects form data for OAuth2 compatibility\n        const formData = new URLSearchParams();\n        formData.append(\"username\", username);\n        formData.append(\"password\", password);\n        const response = await fetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/token\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Login failed\");\n        }\n        const data = await response.json();\n        // Store the token in localStorage for later use\n        localStorage.setItem(\"token\", data.access_token);\n        localStorage.setItem(\"user\", JSON.stringify(data.user));\n        // Set up automatic token expiry check\n        setupTokenExpiryCheck(data.access_token);\n        return data;\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        throw error;\n    }\n};\nconst logout = async ()=>{\n    // Simply clear local storage - no need for API call\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    // Clear token expiry timer if it exists\n    if (tokenExpiryTimer) {\n        clearTimeout(tokenExpiryTimer);\n        tokenExpiryTimer = null;\n    }\n};\nconst getToken = ()=>{\n    if (false) {}\n    return localStorage.getItem(\"token\");\n};\nconst getCurrentUser = ()=>{\n    if (false) {}\n    const userJson = localStorage.getItem(\"user\");\n    if (!userJson) return null;\n    try {\n        return JSON.parse(userJson);\n    } catch (e) {\n        console.error(\"Error parsing user data:\", e);\n        return null;\n    }\n};\nconst isAuthenticated = ()=>{\n    const token = getToken();\n    if (!token) return false;\n    // Check if token is expired\n    if (isTokenExpired(token)) {\n        logout(); // Clean up expired token\n        return false;\n    }\n    return true;\n};\n// Function to initialize token expiry check (call this on app startup)\nconst initializeTokenExpiryCheck = ()=>{\n    const token = getToken();\n    if (token && !isTokenExpired(token)) {\n        setupTokenExpiryCheck(token);\n    }\n};\n// User management\nconst registerUser = async (userData)=>{\n    try {\n        const response = await fetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/public/register\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Registration failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        throw error;\n    }\n};\nconst getUsers = async ()=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/users\"));\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Failed to fetch users\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching users:\", error);\n        throw error;\n    }\n};\nconst getUser = async (userId)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/users/\").concat(userId));\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Failed to fetch user\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching user \".concat(userId, \":\"), error);\n        throw error;\n    }\n};\nconst updateUser = async (userId, userData)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/users/\").concat(userId), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Update failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating user \".concat(userId, \":\"), error);\n        throw error;\n    }\n};\nconst deleteUser = async (userId)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/users/\").concat(userId), {\n            method: \"DELETE\"\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Delete failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error deleting user \".concat(userId, \":\"), error);\n        throw error;\n    }\n};\nconst updateUserRole = async (userId, role)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/users/\").concat(userId, \"/role?role=\").concat(role), {\n            method: \"PUT\"\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Role update failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating role for user \".concat(userId, \":\"), error);\n        throw error;\n    }\n};\nconst getProviders = async (agency)=>{\n    try {\n        const url = agency ? \"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/providers?agency=\").concat(agency) : \"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/providers\");\n        const response = await authFetch(url);\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch providers\");\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error fetching providers:\", error);\n        throw error;\n    }\n};\nconst createProvider = async (provider)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/providers\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(provider)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Provider creation failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating provider:\", error);\n        throw error;\n    }\n};\nconst updateProvider = async (providerId, provider)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/providers/\").concat(providerId), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(provider)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Provider update failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating provider \".concat(providerId, \":\"), error);\n        throw error;\n    }\n};\nconst deleteProvider = async (providerId)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/providers/\").concat(providerId), {\n            method: \"DELETE\"\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Provider deletion failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error deleting provider \".concat(providerId, \":\"), error);\n        throw error;\n    }\n};\nconst approveUser = async (userId, data)=>{\n    try {\n        // Add status to the data\n        const approveData = {\n            ...data,\n            status: \"approved\"\n        };\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/users/\").concat(userId, \"/approve\"), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(approveData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"User approval failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error approving user \".concat(userId, \":\"), error);\n        throw error;\n    }\n};\nconst rejectUser = async (userId)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/users/\").concat(userId, \"/reject\"), {\n            method: \"PUT\"\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"User rejection failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error rejecting user \".concat(userId, \":\"), error);\n        throw error;\n    }\n};\nconst getPendingUsers = async ()=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/users/pending\"));\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Failed to fetch pending users\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching pending users:\", error);\n        throw error;\n    }\n};\nconst suspendUser = async (userId)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/users/\").concat(userId, \"/suspend\"), {\n            method: \"PUT\"\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"User suspension failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error suspending user \".concat(userId, \":\"), error);\n        throw error;\n    }\n};\nconst unsuspendUser = async (userId)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/users/\").concat(userId, \"/unsuspend\"), {\n            method: \"PUT\"\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"User unsuspension failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error unsuspending user \".concat(userId, \":\"), error);\n        throw error;\n    }\n};\nconst updateProviderStatus = async (providerId, status)=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/api/providers/\").concat(providerId, \"/status\"), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                status\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.detail || \"Provider status update failed\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating provider \".concat(providerId, \" status:\"), error);\n        throw error;\n    }\n};\nconst fetchAgencies = async ()=>{\n    try {\n        const response = await authFetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_BASE_URL, \"/api/agencies\"));\n        if (!response.ok) {\n            console.warn(\"Failed to fetch agencies from API, using fallback\");\n            return [\n                \"STUDIO\",\n                \"CNM\"\n            ]; // Return fallback instead of throwing\n        }\n        const data = await response.json();\n        return data.agencies || [\n            \"STUDIO\",\n            \"CNM\"\n        ];\n    } catch (error) {\n        console.error(\"Error fetching agencies:\", error);\n        return [\n            \"STUDIO\",\n            \"CNM\"\n        ]; // Return fallback instead of throwing\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9hdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBcUM7QUFvRHJDLHFCQUFxQjtBQUNyQixJQUFJQyxtQkFBMEM7QUFFOUMsdURBQXVEO0FBQ3ZELE1BQU1DLFlBQVksQ0FBQ0M7SUFDakIsSUFBSTtRQUNGLE1BQU1DLFlBQVlELE1BQU1FLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtRQUNyQyxNQUFNQyxTQUFTRixVQUFVRyxPQUFPLENBQUMsTUFBTSxLQUFLQSxPQUFPLENBQUMsTUFBTTtRQUMxRCxNQUFNQyxjQUFjQyxtQkFBbUJDLEtBQUtKLFFBQVFELEtBQUssQ0FBQyxJQUFJTSxHQUFHLENBQUMsU0FBU0MsQ0FBQztZQUMxRSxPQUFPLE1BQU0sQ0FBQyxPQUFPQSxFQUFFQyxVQUFVLENBQUMsR0FBR0MsUUFBUSxDQUFDLEdBQUUsRUFBR0MsS0FBSyxDQUFDLENBQUM7UUFDNUQsR0FBR0MsSUFBSSxDQUFDO1FBRVIsT0FBT0MsS0FBS0MsS0FBSyxDQUFDVjtJQUNwQixFQUFFLE9BQU9XLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsT0FBTztJQUNUO0FBQ0Y7QUFFQSx3Q0FBd0M7QUFDeEMsTUFBTUUsaUJBQWlCLENBQUNsQjtJQUN0QixNQUFNbUIsVUFBVXBCLFVBQVVDO0lBQzFCLElBQUksQ0FBQ21CLFdBQVcsQ0FBQ0EsUUFBUUMsR0FBRyxFQUFFO1FBQzVCLE9BQU8sTUFBTSwyREFBMkQ7SUFDMUU7SUFFQSxNQUFNQyxjQUFjQyxLQUFLQyxLQUFLLENBQUNDLEtBQUtDLEdBQUcsS0FBSyxPQUFPLDBCQUEwQjtJQUM3RSxPQUFPTixRQUFRQyxHQUFHLEdBQUdDO0FBQ3ZCO0FBRUEsNkRBQTZEO0FBQzdELE1BQU1LLHFCQUFxQixDQUFDMUI7SUFDMUIsTUFBTW1CLFVBQVVwQixVQUFVQztJQUMxQixJQUFJLENBQUNtQixXQUFXLENBQUNBLFFBQVFDLEdBQUcsRUFBRTtRQUM1QixPQUFPO0lBQ1Q7SUFFQSxNQUFNQyxjQUFjQyxLQUFLQyxLQUFLLENBQUNDLEtBQUtDLEdBQUcsS0FBSztJQUM1QyxNQUFNRSxrQkFBa0IsQ0FBQ1IsUUFBUUMsR0FBRyxHQUFHQyxXQUFVLElBQUssTUFBTSwwQkFBMEI7SUFDdEYsT0FBT0MsS0FBS00sR0FBRyxDQUFDLEdBQUdEO0FBQ3JCO0FBRUEseURBQXlEO0FBQ3pELE1BQU1FLHdCQUF3QixDQUFDN0I7SUFDN0IsMkJBQTJCO0lBQzNCLElBQUlGLGtCQUFrQjtRQUNwQmdDLGFBQWFoQztJQUNmO0lBRUEsb0NBQW9DO0lBQ3BDLElBQUlvQixlQUFlbEIsUUFBUTtRQUN6QitCO1FBQ0FDLE1BQU07UUFDTixJQUFJLElBQWtCLEVBQWE7WUFDakNDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1FBQ3pCO1FBQ0E7SUFDRjtJQUVBLHdCQUF3QjtJQUN4QixNQUFNUixrQkFBa0JELG1CQUFtQjFCO0lBRTNDLHlDQUF5QztJQUN6Q0YsbUJBQW1Cc0MsV0FBVztRQUM1Qkw7UUFDQUMsTUFBTTtRQUNOLElBQUksSUFBa0IsRUFBYTtZQUNqQ0MsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7UUFDekI7SUFDRixHQUFHUjtJQUVIVixRQUFRb0IsR0FBRyxDQUFDLHdCQUEyRCxPQUFuQ2YsS0FBS2dCLEtBQUssQ0FBQ1gsa0JBQWtCLE9BQU07QUFDekU7QUFFQSxvQ0FBb0M7QUFDcEMsTUFBTVksaUJBQWlCO0lBQ3JCLE1BQU12QyxRQUFRd0M7SUFDZCxPQUFPeEMsUUFBUTtRQUFFLGlCQUFpQixVQUFnQixPQUFOQTtJQUFRLElBQUksQ0FBQztBQUMzRDtBQUVBLHVDQUF1QztBQUNoQyxNQUFNeUMsWUFBWSxlQUFPQztRQUFhQywyRUFBdUIsQ0FBQztJQUNuRSxNQUFNQyxVQUFVO1FBQ2QsR0FBR0QsUUFBUUMsT0FBTztRQUNsQixHQUFHTCxnQkFBZ0I7SUFDckI7SUFFQSxNQUFNTSxXQUFXLE1BQU1DLE1BQU1KLEtBQUs7UUFBRSxHQUFHQyxPQUFPO1FBQUVDO0lBQVE7SUFFeEQsd0RBQXdEO0lBQ3hELElBQUlDLFNBQVNFLE1BQU0sS0FBSyxLQUFLO1FBQzNCLDRCQUE0QjtRQUM1QmhCO1FBRUEsK0JBQStCO1FBQy9CQyxNQUFNO1FBRU4sd0JBQXdCO1FBQ3hCLElBQUksSUFBa0IsRUFBYTtZQUNqQ0MsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7UUFDekI7UUFFQSxNQUFNLElBQUlhLE1BQU07SUFDbEI7SUFFQSxPQUFPSDtBQUNULEVBQUU7QUFFRix5QkFBeUI7QUFDbEIsTUFBTUksUUFBUSxPQUFPQyxVQUFrQkM7SUFDNUMsSUFBSTtRQUNGLHFEQUFxRDtRQUNyRCxNQUFNQyxXQUFXLElBQUlDO1FBQ3JCRCxTQUFTRSxNQUFNLENBQUMsWUFBWUo7UUFDNUJFLFNBQVNFLE1BQU0sQ0FBQyxZQUFZSDtRQUU1QixNQUFNTixXQUFXLE1BQU1DLE1BQU0sR0FBZ0IsT0FBYmpELDhDQUFZQSxFQUFDLFdBQVM7WUFDcEQwRCxRQUFRO1lBQ1JYLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FZLE1BQU1KO1FBQ1I7UUFFQSxJQUFJLENBQUNQLFNBQVNZLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1iLFNBQVNjLElBQUk7WUFDckMsTUFBTSxJQUFJWCxNQUFNVSxVQUFVRSxNQUFNLElBQUk7UUFDdEM7UUFFQSxNQUFNQyxPQUFzQixNQUFNaEIsU0FBU2MsSUFBSTtRQUUvQyxnREFBZ0Q7UUFDaERHLGFBQWFDLE9BQU8sQ0FBQyxTQUFTRixLQUFLRyxZQUFZO1FBQy9DRixhQUFhQyxPQUFPLENBQUMsUUFBUWpELEtBQUttRCxTQUFTLENBQUNKLEtBQUtLLElBQUk7UUFFckQsc0NBQXNDO1FBQ3RDckMsc0JBQXNCZ0MsS0FBS0csWUFBWTtRQUV2QyxPQUFPSDtJQUNULEVBQUUsT0FBTzdDLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdCQUFnQkE7UUFDOUIsTUFBTUE7SUFDUjtBQUNGLEVBQUU7QUFFSyxNQUFNZSxTQUFTO0lBQ3BCLG9EQUFvRDtJQUNwRCtCLGFBQWFLLFVBQVUsQ0FBQztJQUN4QkwsYUFBYUssVUFBVSxDQUFDO0lBRXhCLHdDQUF3QztJQUN4QyxJQUFJckUsa0JBQWtCO1FBQ3BCZ0MsYUFBYWhDO1FBQ2JBLG1CQUFtQjtJQUNyQjtBQUNGLEVBQUU7QUFFSyxNQUFNMEMsV0FBVztJQUN0QixJQUFJLEtBQWtCLEVBQWEsRUFFbEM7SUFDRCxPQUFPc0IsYUFBYU0sT0FBTyxDQUFDO0FBQzlCLEVBQUU7QUFFSyxNQUFNQyxpQkFBaUI7SUFDNUIsSUFBSSxLQUFrQixFQUFhLEVBRWxDO0lBRUQsTUFBTUMsV0FBV1IsYUFBYU0sT0FBTyxDQUFDO0lBQ3RDLElBQUksQ0FBQ0UsVUFBVSxPQUFPO0lBRXRCLElBQUk7UUFDRixPQUFPeEQsS0FBS0MsS0FBSyxDQUFDdUQ7SUFDcEIsRUFBRSxPQUFPQyxHQUFHO1FBQ1Z0RCxRQUFRRCxLQUFLLENBQUMsNEJBQTRCdUQ7UUFDMUMsT0FBTztJQUNUO0FBQ0YsRUFBRTtBQUVLLE1BQU1DLGtCQUFrQjtJQUM3QixNQUFNeEUsUUFBUXdDO0lBQ2QsSUFBSSxDQUFDeEMsT0FBTyxPQUFPO0lBRW5CLDRCQUE0QjtJQUM1QixJQUFJa0IsZUFBZWxCLFFBQVE7UUFDekIrQixVQUFVLHlCQUF5QjtRQUNuQyxPQUFPO0lBQ1Q7SUFFQSxPQUFPO0FBQ1QsRUFBRTtBQUVGLHVFQUF1RTtBQUNoRSxNQUFNMEMsNkJBQTZCO0lBQ3hDLE1BQU16RSxRQUFRd0M7SUFDZCxJQUFJeEMsU0FBUyxDQUFDa0IsZUFBZWxCLFFBQVE7UUFDbkM2QixzQkFBc0I3QjtJQUN4QjtBQUNGLEVBQUU7QUFFRixrQkFBa0I7QUFDWCxNQUFNMEUsZUFBZSxPQUFPQztJQUNqQyxJQUFJO1FBQ0YsTUFBTTlCLFdBQVcsTUFBTUMsTUFBTSxHQUFnQixPQUFiakQsOENBQVlBLEVBQUMscUJBQW1CO1lBQzlEMEQsUUFBUTtZQUNSWCxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBWSxNQUFNMUMsS0FBS21ELFNBQVMsQ0FBQ1U7UUFDdkI7UUFFQSxJQUFJLENBQUM5QixTQUFTWSxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNYixTQUFTYyxJQUFJO1lBQ3JDLE1BQU0sSUFBSVgsTUFBTVUsVUFBVUUsTUFBTSxJQUFJO1FBQ3RDO1FBRUEsT0FBTyxNQUFNZixTQUFTYyxJQUFJO0lBQzVCLEVBQUUsT0FBTzNDLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHVCQUF1QkE7UUFDckMsTUFBTUE7SUFDUjtBQUNGLEVBQUU7QUFFSyxNQUFNNEQsV0FBVztJQUN0QixJQUFJO1FBQ0YsTUFBTS9CLFdBQVcsTUFBTUosVUFBVSxHQUFnQixPQUFiNUMsOENBQVlBLEVBQUM7UUFFakQsSUFBSSxDQUFDZ0QsU0FBU1ksRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTWIsU0FBU2MsSUFBSTtZQUNyQyxNQUFNLElBQUlYLE1BQU1VLFVBQVVFLE1BQU0sSUFBSTtRQUN0QztRQUVBLE9BQU8sTUFBTWYsU0FBU2MsSUFBSTtJQUM1QixFQUFFLE9BQU8zQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1FBQ3ZDLE1BQU1BO0lBQ1I7QUFDRixFQUFFO0FBRUssTUFBTTZELFVBQVUsT0FBT0M7SUFDNUIsSUFBSTtRQUNGLE1BQU1qQyxXQUFXLE1BQU1KLFVBQVUsR0FBeUJxQyxPQUF0QmpGLDhDQUFZQSxFQUFDLFdBQWdCLE9BQVBpRjtRQUUxRCxJQUFJLENBQUNqQyxTQUFTWSxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNYixTQUFTYyxJQUFJO1lBQ3JDLE1BQU0sSUFBSVgsTUFBTVUsVUFBVUUsTUFBTSxJQUFJO1FBQ3RDO1FBRUEsT0FBTyxNQUFNZixTQUFTYyxJQUFJO0lBQzVCLEVBQUUsT0FBTzNDLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHVCQUE4QixPQUFQOEQsUUFBTyxNQUFJOUQ7UUFDaEQsTUFBTUE7SUFDUjtBQUNGLEVBQUU7QUFFSyxNQUFNK0QsYUFBYSxPQUFPRCxRQUFnQkg7SUFDL0MsSUFBSTtRQUNGLE1BQU05QixXQUFXLE1BQU1KLFVBQVUsR0FBeUJxQyxPQUF0QmpGLDhDQUFZQSxFQUFDLFdBQWdCLE9BQVBpRixTQUFVO1lBQ2xFdkIsUUFBUTtZQUNSWCxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBWSxNQUFNMUMsS0FBS21ELFNBQVMsQ0FBQ1U7UUFDdkI7UUFFQSxJQUFJLENBQUM5QixTQUFTWSxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNYixTQUFTYyxJQUFJO1lBQ3JDLE1BQU0sSUFBSVgsTUFBTVUsVUFBVUUsTUFBTSxJQUFJO1FBQ3RDO1FBRUEsT0FBTyxNQUFNZixTQUFTYyxJQUFJO0lBQzVCLEVBQUUsT0FBTzNDLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHVCQUE4QixPQUFQOEQsUUFBTyxNQUFJOUQ7UUFDaEQsTUFBTUE7SUFDUjtBQUNGLEVBQUU7QUFFSyxNQUFNZ0UsYUFBYSxPQUFPRjtJQUMvQixJQUFJO1FBQ0YsTUFBTWpDLFdBQVcsTUFBTUosVUFBVSxHQUF5QnFDLE9BQXRCakYsOENBQVlBLEVBQUMsV0FBZ0IsT0FBUGlGLFNBQVU7WUFDbEV2QixRQUFRO1FBQ1Y7UUFFQSxJQUFJLENBQUNWLFNBQVNZLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1iLFNBQVNjLElBQUk7WUFDckMsTUFBTSxJQUFJWCxNQUFNVSxVQUFVRSxNQUFNLElBQUk7UUFDdEM7UUFFQSxPQUFPLE1BQU1mLFNBQVNjLElBQUk7SUFDNUIsRUFBRSxPQUFPM0MsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsdUJBQThCLE9BQVA4RCxRQUFPLE1BQUk5RDtRQUNoRCxNQUFNQTtJQUNSO0FBQ0YsRUFBRTtBQUVLLE1BQU1pRSxpQkFBaUIsT0FBT0gsUUFBZ0JJO0lBQ25ELElBQUk7UUFDRixNQUFNckMsV0FBVyxNQUFNSixVQUFVLEdBQXlCcUMsT0FBdEJqRiw4Q0FBWUEsRUFBQyxXQUE2QnFGLE9BQXBCSixRQUFPLGVBQWtCLE9BQUxJLE9BQVE7WUFDcEYzQixRQUFRO1FBQ1Y7UUFFQSxJQUFJLENBQUNWLFNBQVNZLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1iLFNBQVNjLElBQUk7WUFDckMsTUFBTSxJQUFJWCxNQUFNVSxVQUFVRSxNQUFNLElBQUk7UUFDdEM7UUFFQSxPQUFPLE1BQU1mLFNBQVNjLElBQUk7SUFDNUIsRUFBRSxPQUFPM0MsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQXVDLE9BQVA4RCxRQUFPLE1BQUk5RDtRQUN6RCxNQUFNQTtJQUNSO0FBQ0YsRUFBRTtBQUVLLE1BQU1tRSxlQUFlLE9BQU9DO0lBQ2pDLElBQUk7UUFDRixNQUFNMUMsTUFBTTBDLFNBQVMsR0FBb0NBLE9BQWpDdkYsOENBQVlBLEVBQUMsc0JBQTJCLE9BQVB1RixVQUFXLEdBQWdCLE9BQWJ2Riw4Q0FBWUEsRUFBQztRQUNwRixNQUFNZ0QsV0FBVyxNQUFNSixVQUFVQztRQUVqQyxJQUFJLENBQUNHLFNBQVNZLEVBQUUsRUFBRTtZQUNoQixNQUFNLElBQUlULE1BQU07UUFDbEI7UUFFQSxNQUFNYSxPQUFtQixNQUFNaEIsU0FBU2MsSUFBSTtRQUM1QyxPQUFPRTtJQUNULEVBQUUsT0FBTzdDLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsTUFBTUE7SUFDUjtBQUNGLEVBQUU7QUFFSyxNQUFNcUUsaUJBQWlCLE9BQU9DO0lBQ25DLElBQUk7UUFDRixNQUFNekMsV0FBVyxNQUFNSixVQUFVLEdBQWdCLE9BQWI1Qyw4Q0FBWUEsRUFBQyxlQUFhO1lBQzVEMEQsUUFBUTtZQUNSWCxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBWSxNQUFNMUMsS0FBS21ELFNBQVMsQ0FBQ3FCO1FBQ3ZCO1FBRUEsSUFBSSxDQUFDekMsU0FBU1ksRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTWIsU0FBU2MsSUFBSTtZQUNyQyxNQUFNLElBQUlYLE1BQU1VLFVBQVVFLE1BQU0sSUFBSTtRQUN0QztRQUVBLE9BQU8sTUFBTWYsU0FBU2MsSUFBSTtJQUM1QixFQUFFLE9BQU8zQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE1BQU1BO0lBQ1I7QUFDRixFQUFFO0FBRUssTUFBTXVFLGlCQUFpQixPQUFPQyxZQUFvQkY7SUFDdkQsSUFBSTtRQUNGLE1BQU16QyxXQUFXLE1BQU1KLFVBQVUsR0FBNkIrQyxPQUExQjNGLDhDQUFZQSxFQUFDLGVBQXdCLE9BQVgyRixhQUFjO1lBQzFFakMsUUFBUTtZQUNSWCxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBWSxNQUFNMUMsS0FBS21ELFNBQVMsQ0FBQ3FCO1FBQ3ZCO1FBRUEsSUFBSSxDQUFDekMsU0FBU1ksRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTWIsU0FBU2MsSUFBSTtZQUNyQyxNQUFNLElBQUlYLE1BQU1VLFVBQVVFLE1BQU0sSUFBSTtRQUN0QztRQUVBLE9BQU8sTUFBTWYsU0FBU2MsSUFBSTtJQUM1QixFQUFFLE9BQU8zQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBc0MsT0FBWHdFLFlBQVcsTUFBSXhFO1FBQ3hELE1BQU1BO0lBQ1I7QUFDRixFQUFFO0FBRUssTUFBTXlFLGlCQUFpQixPQUFPRDtJQUNuQyxJQUFJO1FBQ0YsTUFBTTNDLFdBQVcsTUFBTUosVUFBVSxHQUE2QitDLE9BQTFCM0YsOENBQVlBLEVBQUMsZUFBd0IsT0FBWDJGLGFBQWM7WUFDMUVqQyxRQUFRO1FBQ1Y7UUFFQSxJQUFJLENBQUNWLFNBQVNZLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1iLFNBQVNjLElBQUk7WUFDckMsTUFBTSxJQUFJWCxNQUFNVSxVQUFVRSxNQUFNLElBQUk7UUFDdEM7UUFFQSxPQUFPLE1BQU1mLFNBQVNjLElBQUk7SUFDNUIsRUFBRSxPQUFPM0MsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQXNDLE9BQVh3RSxZQUFXLE1BQUl4RTtRQUN4RCxNQUFNQTtJQUNSO0FBQ0YsRUFBRTtBQUVLLE1BQU0wRSxjQUFjLE9BQU9aLFFBQWdCakI7SUFDaEQsSUFBSTtRQUNGLHlCQUF5QjtRQUN6QixNQUFNOEIsY0FBYztZQUNsQixHQUFHOUIsSUFBSTtZQUNQZCxRQUFRO1FBQ1Y7UUFFQSxNQUFNRixXQUFXLE1BQU1KLFVBQVUsR0FBeUJxQyxPQUF0QmpGLDhDQUFZQSxFQUFDLFdBQWdCLE9BQVBpRixRQUFPLGFBQVc7WUFDMUV2QixRQUFRO1lBQ1JYLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FZLE1BQU0xQyxLQUFLbUQsU0FBUyxDQUFDMEI7UUFDdkI7UUFFQSxJQUFJLENBQUM5QyxTQUFTWSxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNYixTQUFTYyxJQUFJO1lBQ3JDLE1BQU0sSUFBSVgsTUFBTVUsVUFBVUUsTUFBTSxJQUFJO1FBQ3RDO1FBRUEsT0FBTyxNQUFNZixTQUFTYyxJQUFJO0lBQzVCLEVBQUUsT0FBTzNDLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHdCQUErQixPQUFQOEQsUUFBTyxNQUFJOUQ7UUFDakQsTUFBTUE7SUFDUjtBQUNGLEVBQUU7QUFFSyxNQUFNNEUsYUFBYSxPQUFPZDtJQUMvQixJQUFJO1FBQ0YsTUFBTWpDLFdBQVcsTUFBTUosVUFBVSxHQUF5QnFDLE9BQXRCakYsOENBQVlBLEVBQUMsV0FBZ0IsT0FBUGlGLFFBQU8sWUFBVTtZQUN6RXZCLFFBQVE7UUFDVjtRQUVBLElBQUksQ0FBQ1YsU0FBU1ksRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTWIsU0FBU2MsSUFBSTtZQUNyQyxNQUFNLElBQUlYLE1BQU1VLFVBQVVFLE1BQU0sSUFBSTtRQUN0QztRQUVBLE9BQU8sTUFBTWYsU0FBU2MsSUFBSTtJQUM1QixFQUFFLE9BQU8zQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyx3QkFBK0IsT0FBUDhELFFBQU8sTUFBSTlEO1FBQ2pELE1BQU1BO0lBQ1I7QUFDRixFQUFFO0FBRUssTUFBTTZFLGtCQUFrQjtJQUM3QixJQUFJO1FBQ0YsTUFBTWhELFdBQVcsTUFBTUosVUFBVSxHQUFnQixPQUFiNUMsOENBQVlBLEVBQUM7UUFFakQsSUFBSSxDQUFDZ0QsU0FBU1ksRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTWIsU0FBU2MsSUFBSTtZQUNyQyxNQUFNLElBQUlYLE1BQU1VLFVBQVVFLE1BQU0sSUFBSTtRQUN0QztRQUVBLE9BQU8sTUFBTWYsU0FBU2MsSUFBSTtJQUM1QixFQUFFLE9BQU8zQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO1FBQy9DLE1BQU1BO0lBQ1I7QUFDRixFQUFFO0FBRUssTUFBTThFLGNBQWMsT0FBT2hCO0lBQ2hDLElBQUk7UUFDRixNQUFNakMsV0FBVyxNQUFNSixVQUFVLEdBQXlCcUMsT0FBdEJqRiw4Q0FBWUEsRUFBQyxXQUFnQixPQUFQaUYsUUFBTyxhQUFXO1lBQzFFdkIsUUFBUTtRQUNWO1FBRUEsSUFBSSxDQUFDVixTQUFTWSxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNYixTQUFTYyxJQUFJO1lBQ3JDLE1BQU0sSUFBSVgsTUFBTVUsVUFBVUUsTUFBTSxJQUFJO1FBQ3RDO1FBRUEsT0FBTyxNQUFNZixTQUFTYyxJQUFJO0lBQzVCLEVBQUUsT0FBTzNDLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUFnQyxPQUFQOEQsUUFBTyxNQUFJOUQ7UUFDbEQsTUFBTUE7SUFDUjtBQUNGLEVBQUU7QUFFSyxNQUFNK0UsZ0JBQWdCLE9BQU9qQjtJQUNsQyxJQUFJO1FBQ0YsTUFBTWpDLFdBQVcsTUFBTUosVUFBVSxHQUF5QnFDLE9BQXRCakYsOENBQVlBLEVBQUMsV0FBZ0IsT0FBUGlGLFFBQU8sZUFBYTtZQUM1RXZCLFFBQVE7UUFDVjtRQUVBLElBQUksQ0FBQ1YsU0FBU1ksRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTWIsU0FBU2MsSUFBSTtZQUNyQyxNQUFNLElBQUlYLE1BQU1VLFVBQVVFLE1BQU0sSUFBSTtRQUN0QztRQUVBLE9BQU8sTUFBTWYsU0FBU2MsSUFBSTtJQUM1QixFQUFFLE9BQU8zQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBa0MsT0FBUDhELFFBQU8sTUFBSTlEO1FBQ3BELE1BQU1BO0lBQ1I7QUFDRixFQUFFO0FBRUssTUFBTWdGLHVCQUF1QixPQUFPUixZQUFvQnpDO0lBQzdELElBQUk7UUFDRixNQUFNRixXQUFXLE1BQU1KLFVBQVUsR0FBaUMrQyxPQUE5QjNGLDhDQUFZQSxFQUFDLG1CQUE0QixPQUFYMkYsWUFBVyxZQUFVO1lBQ3JGakMsUUFBUTtZQUNSWCxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBWSxNQUFNMUMsS0FBS21ELFNBQVMsQ0FBQztnQkFBRWxCO1lBQU87UUFDaEM7UUFFQSxJQUFJLENBQUNGLFNBQVNZLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1iLFNBQVNjLElBQUk7WUFDckMsTUFBTSxJQUFJWCxNQUFNVSxVQUFVRSxNQUFNLElBQUk7UUFDdEM7UUFFQSxPQUFPLE1BQU1mLFNBQVNjLElBQUk7SUFDNUIsRUFBRSxPQUFPM0MsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQXNDLE9BQVh3RSxZQUFXLGFBQVd4RTtRQUMvRCxNQUFNQTtJQUNSO0FBQ0YsRUFBRTtBQUVLLE1BQU1pRixnQkFBZ0I7SUFDM0IsSUFBSTtRQUNGLE1BQU1wRCxXQUFXLE1BQU1KLFVBQVUsR0FBZ0IsT0FBYjVDLDhDQUFZQSxFQUFDO1FBRWpELElBQUksQ0FBQ2dELFNBQVNZLEVBQUUsRUFBRTtZQUNoQnhDLFFBQVFpRixJQUFJLENBQUM7WUFDYixPQUFPO2dCQUFDO2dCQUFVO2FBQU0sRUFBRSxzQ0FBc0M7UUFDbEU7UUFFQSxNQUFNckMsT0FBTyxNQUFNaEIsU0FBU2MsSUFBSTtRQUNoQyxPQUFPRSxLQUFLc0MsUUFBUSxJQUFJO1lBQUM7WUFBVTtTQUFNO0lBQzNDLEVBQUUsT0FBT25GLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsT0FBTztZQUFDO1lBQVU7U0FBTSxFQUFFLHNDQUFzQztJQUNsRTtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL3NlcnZpY2VzL2F1dGgudHM/YjgwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBUElfQkFTRV9VUkwgfSBmcm9tICcuL2FwaSc7XHJcbmltcG9ydCB7IFByb3ZpZGVyVHlwZSB9IGZyb20gJy4uL2NvbXBvbmVudHMvYm9va2luZy9Qcm92aWRlclNlbGVjdG9yJztcclxuXHJcbi8vIFVzZXIgdHlwZXNcclxuZXhwb3J0IGludGVyZmFjZSBVc2VyIHtcclxuICB1c2VyX2lkOiBzdHJpbmc7XHJcbiAgdXNlcm5hbWU6IHN0cmluZztcclxuICBlbWFpbDogc3RyaW5nO1xyXG4gIGZ1bGxfbmFtZTogc3RyaW5nO1xyXG4gIHJvbGU6ICdhZG1pbicgfCAnc3ViX2FkbWluJyB8ICd1c2VyJztcclxuICBhZ2VuY3k6IHN0cmluZzsgLy8gRHluYW1pYyBhZ2VuY3kgdmFsdWVzXHJcbiAgcG9ydGFsX2FjY2Vzczogc3RyaW5nW107XHJcbiAgcHJvdmlkZXJfYWNjZXNzPzogUHJvdmlkZXJUeXBlW107XHJcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xyXG4gIGxhc3RfbG9naW4/OiBzdHJpbmc7XHJcbiAgc3RhdHVzPzogJ3BlbmRpbmcnIHwgJ2FwcHJvdmVkJyB8ICdyZWplY3RlZCcgfCAnc3VzcGVuZGVkJztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBMb2dpblJlc3BvbnNlIHtcclxuICBhY2Nlc3NfdG9rZW46IHN0cmluZztcclxuICB0b2tlbl90eXBlOiBzdHJpbmc7XHJcbiAgdXNlcjogVXNlcjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBSZWdpc3RlckRhdGEge1xyXG4gIHVzZXJuYW1lOiBzdHJpbmc7XHJcbiAgcGFzc3dvcmQ6IHN0cmluZztcclxuICBlbWFpbDogc3RyaW5nO1xyXG4gIGZ1bGxfbmFtZTogc3RyaW5nO1xyXG4gIHJvbGU6ICdhZG1pbicgfCAnc3ViX2FkbWluJyB8ICd1c2VyJztcclxuICBhZ2VuY3k6ICdTVFVESU8nIHwgJ0NOTSc7XHJcbiAgcG9ydGFsX2FjY2Vzczogc3RyaW5nW107XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXBkYXRlVXNlckRhdGEge1xyXG4gIGVtYWlsPzogc3RyaW5nO1xyXG4gIGZ1bGxfbmFtZT86IHN0cmluZztcclxuICByb2xlPzogJ2FkbWluJyB8ICdzdWJfYWRtaW4nIHwgJ3VzZXInO1xyXG4gIGFnZW5jeT86IHN0cmluZztcclxuICBwb3J0YWxfYWNjZXNzPzogc3RyaW5nW107XHJcbiAgcHJvdmlkZXJfYWNjZXNzPzogUHJvdmlkZXJUeXBlW107XHJcbiAgcGFzc3dvcmQ/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUHJvdmlkZXIge1xyXG4gIHByb3ZpZGVyX2lkOiBudW1iZXI7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGFnZW5jeTogc3RyaW5nOyAvLyBDaGFuZ2VkIGZyb20gJ1NUVURJTycgfCAnQ05NJyB0byBzdHJpbmcgdG8gc3VwcG9ydCBkeW5hbWljIGFnZW5jaWVzXHJcbiAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICBzdGF0dXM6ICdhY3RpdmUnIHwgJ2NvbWluZ19zb29uJyB8ICdtYWludGVuYW5jZSc7XHJcbn1cclxuXHJcbi8vIFRva2VuIGV4cGlyeSB0aW1lclxyXG5sZXQgdG9rZW5FeHBpcnlUaW1lcjogTm9kZUpTLlRpbWVvdXQgfCBudWxsID0gbnVsbDtcclxuXHJcbi8vIEZ1bmN0aW9uIHRvIGRlY29kZSBKV1QgdG9rZW4gYW5kIGdldCBleHBpcmF0aW9uIHRpbWVcclxuY29uc3QgZGVjb2RlSldUID0gKHRva2VuOiBzdHJpbmcpOiB7IGV4cD86IG51bWJlciB9IHwgbnVsbCA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGJhc2U2NFVybCA9IHRva2VuLnNwbGl0KCcuJylbMV07XHJcbiAgICBjb25zdCBiYXNlNjQgPSBiYXNlNjRVcmwucmVwbGFjZSgvLS9nLCAnKycpLnJlcGxhY2UoL18vZywgJy8nKTtcclxuICAgIGNvbnN0IGpzb25QYXlsb2FkID0gZGVjb2RlVVJJQ29tcG9uZW50KGF0b2IoYmFzZTY0KS5zcGxpdCgnJykubWFwKGZ1bmN0aW9uKGMpIHtcclxuICAgICAgcmV0dXJuICclJyArICgnMDAnICsgYy5jaGFyQ29kZUF0KDApLnRvU3RyaW5nKDE2KSkuc2xpY2UoLTIpO1xyXG4gICAgfSkuam9pbignJykpO1xyXG4gICAgXHJcbiAgICByZXR1cm4gSlNPTi5wYXJzZShqc29uUGF5bG9hZCk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlY29kaW5nIEpXVCB0b2tlbjonLCBlcnJvcik7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcbn07XHJcblxyXG4vLyBGdW5jdGlvbiB0byBjaGVjayBpZiB0b2tlbiBpcyBleHBpcmVkXHJcbmNvbnN0IGlzVG9rZW5FeHBpcmVkID0gKHRva2VuOiBzdHJpbmcpOiBib29sZWFuID0+IHtcclxuICBjb25zdCBkZWNvZGVkID0gZGVjb2RlSldUKHRva2VuKTtcclxuICBpZiAoIWRlY29kZWQgfHwgIWRlY29kZWQuZXhwKSB7XHJcbiAgICByZXR1cm4gdHJ1ZTsgLy8gSWYgd2UgY2FuJ3QgZGVjb2RlIG9yIG5vIGV4cGlyYXRpb24sIGNvbnNpZGVyIGl0IGV4cGlyZWRcclxuICB9XHJcbiAgXHJcbiAgY29uc3QgY3VycmVudFRpbWUgPSBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKTsgLy8gQ3VycmVudCB0aW1lIGluIHNlY29uZHNcclxuICByZXR1cm4gZGVjb2RlZC5leHAgPCBjdXJyZW50VGltZTtcclxufTtcclxuXHJcbi8vIEZ1bmN0aW9uIHRvIGdldCB0aW1lIHVudGlsIHRva2VuIGV4cGlyZXMgKGluIG1pbGxpc2Vjb25kcylcclxuY29uc3QgZ2V0VGltZVVudGlsRXhwaXJ5ID0gKHRva2VuOiBzdHJpbmcpOiBudW1iZXIgPT4ge1xyXG4gIGNvbnN0IGRlY29kZWQgPSBkZWNvZGVKV1QodG9rZW4pO1xyXG4gIGlmICghZGVjb2RlZCB8fCAhZGVjb2RlZC5leHApIHtcclxuICAgIHJldHVybiAwO1xyXG4gIH1cclxuICBcclxuICBjb25zdCBjdXJyZW50VGltZSA9IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xyXG4gIGNvbnN0IHRpbWVVbnRpbEV4cGlyeSA9IChkZWNvZGVkLmV4cCAtIGN1cnJlbnRUaW1lKSAqIDEwMDA7IC8vIENvbnZlcnQgdG8gbWlsbGlzZWNvbmRzXHJcbiAgcmV0dXJuIE1hdGgubWF4KDAsIHRpbWVVbnRpbEV4cGlyeSk7XHJcbn07XHJcblxyXG4vLyBGdW5jdGlvbiB0byBzZXQgdXAgYXV0b21hdGljIGxvZ291dCB3aGVuIHRva2VuIGV4cGlyZXNcclxuY29uc3Qgc2V0dXBUb2tlbkV4cGlyeUNoZWNrID0gKHRva2VuOiBzdHJpbmcpOiB2b2lkID0+IHtcclxuICAvLyBDbGVhciBhbnkgZXhpc3RpbmcgdGltZXJcclxuICBpZiAodG9rZW5FeHBpcnlUaW1lcikge1xyXG4gICAgY2xlYXJUaW1lb3V0KHRva2VuRXhwaXJ5VGltZXIpO1xyXG4gIH1cclxuICBcclxuICAvLyBDaGVjayBpZiB0b2tlbiBpcyBhbHJlYWR5IGV4cGlyZWRcclxuICBpZiAoaXNUb2tlbkV4cGlyZWQodG9rZW4pKSB7XHJcbiAgICBsb2dvdXQoKTtcclxuICAgIGFsZXJ0KCdTZXNzaW9uIGV4cGlyZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uJyk7XHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnLyc7XHJcbiAgICB9XHJcbiAgICByZXR1cm47XHJcbiAgfVxyXG4gIFxyXG4gIC8vIEdldCB0aW1lIHVudGlsIGV4cGlyeVxyXG4gIGNvbnN0IHRpbWVVbnRpbEV4cGlyeSA9IGdldFRpbWVVbnRpbEV4cGlyeSh0b2tlbik7XHJcbiAgXHJcbiAgLy8gU2V0IHRpbWVyIHRvIGxvZ291dCB3aGVuIHRva2VuIGV4cGlyZXNcclxuICB0b2tlbkV4cGlyeVRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICBsb2dvdXQoKTtcclxuICAgIGFsZXJ0KCdTZXNzaW9uIGV4cGlyZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uJyk7XHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnLyc7XHJcbiAgICB9XHJcbiAgfSwgdGltZVVudGlsRXhwaXJ5KTtcclxuICBcclxuICBjb25zb2xlLmxvZyhgVG9rZW4gd2lsbCBleHBpcmUgaW4gJHtNYXRoLnJvdW5kKHRpbWVVbnRpbEV4cGlyeSAvIDEwMDApfSBzZWNvbmRzYCk7XHJcbn07XHJcblxyXG4vLyBIZWxwZXIgZm9yIGF1dGhlbnRpY2F0aW9uIGhlYWRlcnNcclxuY29uc3QgZ2V0QXV0aEhlYWRlcnMgPSAoKTogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9PiB7XHJcbiAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbigpO1xyXG4gIHJldHVybiB0b2tlbiA/IHsgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCB9IDoge307XHJcbn07XHJcblxyXG4vLyBBUEkgY2FsbHMgd2l0aCBhdXRoZW50aWNhdGlvbiBoZWFkZXJcclxuZXhwb3J0IGNvbnN0IGF1dGhGZXRjaCA9IGFzeW5jICh1cmw6IHN0cmluZywgb3B0aW9uczogUmVxdWVzdEluaXQgPSB7fSk6IFByb21pc2U8UmVzcG9uc2U+ID0+IHtcclxuICBjb25zdCBoZWFkZXJzID0ge1xyXG4gICAgLi4ub3B0aW9ucy5oZWFkZXJzLFxyXG4gICAgLi4uZ2V0QXV0aEhlYWRlcnMoKSxcclxuICB9O1xyXG4gIFxyXG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godXJsLCB7IC4uLm9wdGlvbnMsIGhlYWRlcnMgfSk7XHJcbiAgXHJcbiAgLy8gQ2hlY2sgZm9yIDQwMSBVbmF1dGhvcml6ZWQgLSB0b2tlbiBleHBpcmVkIG9yIGludmFsaWRcclxuICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDEpIHtcclxuICAgIC8vIENsZWFyIHRva2VuIGFuZCB1c2VyIGRhdGFcclxuICAgIGxvZ291dCgpO1xyXG4gICAgXHJcbiAgICAvLyBTaG93IHNlc3Npb24gZXhwaXJlZCBtZXNzYWdlXHJcbiAgICBhbGVydCgnU2Vzc2lvbiBleHBpcmVkLiBQbGVhc2UgbG9nIGluIGFnYWluLicpO1xyXG4gICAgXHJcbiAgICAvLyBSZWRpcmVjdCB0byBob21lIHBhZ2VcclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvJztcclxuICAgIH1cclxuICAgIFxyXG4gICAgdGhyb3cgbmV3IEVycm9yKCdTZXNzaW9uIGV4cGlyZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uJyk7XHJcbiAgfVxyXG4gIFxyXG4gIHJldHVybiByZXNwb25zZTtcclxufTtcclxuXHJcbi8vIEF1dGggc2VydmljZSBmdW5jdGlvbnNcclxuZXhwb3J0IGNvbnN0IGxvZ2luID0gYXN5bmMgKHVzZXJuYW1lOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPExvZ2luUmVzcG9uc2U+ID0+IHtcclxuICB0cnkge1xyXG4gICAgLy8gVGhlIEFQSSBleHBlY3RzIGZvcm0gZGF0YSBmb3IgT0F1dGgyIGNvbXBhdGliaWxpdHlcclxuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xyXG4gICAgZm9ybURhdGEuYXBwZW5kKCd1c2VybmFtZScsIHVzZXJuYW1lKTtcclxuICAgIGZvcm1EYXRhLmFwcGVuZCgncGFzc3dvcmQnLCBwYXNzd29yZCk7XHJcblxyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9L3Rva2VuYCwge1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24veC13d3ctZm9ybS11cmxlbmNvZGVkJyxcclxuICAgICAgfSxcclxuICAgICAgYm9keTogZm9ybURhdGEsXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5kZXRhaWwgfHwgJ0xvZ2luIGZhaWxlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGRhdGE6IExvZ2luUmVzcG9uc2UgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICBcclxuICAgIC8vIFN0b3JlIHRoZSB0b2tlbiBpbiBsb2NhbFN0b3JhZ2UgZm9yIGxhdGVyIHVzZVxyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3Rva2VuJywgZGF0YS5hY2Nlc3NfdG9rZW4pO1xyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3VzZXInLCBKU09OLnN0cmluZ2lmeShkYXRhLnVzZXIpKTtcclxuICAgIFxyXG4gICAgLy8gU2V0IHVwIGF1dG9tYXRpYyB0b2tlbiBleHBpcnkgY2hlY2tcclxuICAgIHNldHVwVG9rZW5FeHBpcnlDaGVjayhkYXRhLmFjY2Vzc190b2tlbik7XHJcbiAgICBcclxuICAgIHJldHVybiBkYXRhO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdMb2dpbiBlcnJvcjonLCBlcnJvcik7XHJcbiAgICB0aHJvdyBlcnJvcjtcclxuICB9XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgbG9nb3V0ID0gYXN5bmMgKCk6IFByb21pc2U8dm9pZD4gPT4ge1xyXG4gIC8vIFNpbXBseSBjbGVhciBsb2NhbCBzdG9yYWdlIC0gbm8gbmVlZCBmb3IgQVBJIGNhbGxcclxuICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndG9rZW4nKTtcclxuICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlcicpO1xyXG4gIFxyXG4gIC8vIENsZWFyIHRva2VuIGV4cGlyeSB0aW1lciBpZiBpdCBleGlzdHNcclxuICBpZiAodG9rZW5FeHBpcnlUaW1lcikge1xyXG4gICAgY2xlYXJUaW1lb3V0KHRva2VuRXhwaXJ5VGltZXIpO1xyXG4gICAgdG9rZW5FeHBpcnlUaW1lciA9IG51bGw7XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGdldFRva2VuID0gKCk6IHN0cmluZyB8IG51bGwgPT4ge1xyXG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykge1xyXG4gICAgcmV0dXJuIG51bGw7IC8vIFJldHVybiBudWxsIHdoZW4gcnVubmluZyBvbiB0aGUgc2VydmVyXHJcbiAgfVxyXG4gIHJldHVybiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRDdXJyZW50VXNlciA9ICgpOiBVc2VyIHwgbnVsbCA9PiB7XHJcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICByZXR1cm4gbnVsbDsgLy8gUmV0dXJuIG51bGwgd2hlbiBydW5uaW5nIG9uIHRoZSBzZXJ2ZXJcclxuICB9XHJcbiAgXHJcbiAgY29uc3QgdXNlckpzb24gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpO1xyXG4gIGlmICghdXNlckpzb24pIHJldHVybiBudWxsO1xyXG4gIFxyXG4gIHRyeSB7XHJcbiAgICByZXR1cm4gSlNPTi5wYXJzZSh1c2VySnNvbikgYXMgVXNlcjtcclxuICB9IGNhdGNoIChlKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIHVzZXIgZGF0YTonLCBlKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBpc0F1dGhlbnRpY2F0ZWQgPSAoKTogYm9vbGVhbiA9PiB7XHJcbiAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbigpO1xyXG4gIGlmICghdG9rZW4pIHJldHVybiBmYWxzZTtcclxuICBcclxuICAvLyBDaGVjayBpZiB0b2tlbiBpcyBleHBpcmVkXHJcbiAgaWYgKGlzVG9rZW5FeHBpcmVkKHRva2VuKSkge1xyXG4gICAgbG9nb3V0KCk7IC8vIENsZWFuIHVwIGV4cGlyZWQgdG9rZW5cclxuICAgIHJldHVybiBmYWxzZTtcclxuICB9XHJcbiAgXHJcbiAgcmV0dXJuIHRydWU7XHJcbn07XHJcblxyXG4vLyBGdW5jdGlvbiB0byBpbml0aWFsaXplIHRva2VuIGV4cGlyeSBjaGVjayAoY2FsbCB0aGlzIG9uIGFwcCBzdGFydHVwKVxyXG5leHBvcnQgY29uc3QgaW5pdGlhbGl6ZVRva2VuRXhwaXJ5Q2hlY2sgPSAoKTogdm9pZCA9PiB7XHJcbiAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbigpO1xyXG4gIGlmICh0b2tlbiAmJiAhaXNUb2tlbkV4cGlyZWQodG9rZW4pKSB7XHJcbiAgICBzZXR1cFRva2VuRXhwaXJ5Q2hlY2sodG9rZW4pO1xyXG4gIH1cclxufTtcclxuXHJcbi8vIFVzZXIgbWFuYWdlbWVudFxyXG5leHBvcnQgY29uc3QgcmVnaXN0ZXJVc2VyID0gYXN5bmMgKHVzZXJEYXRhOiBSZWdpc3RlckRhdGEpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgbWVzc2FnZTogc3RyaW5nIH0+ID0+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9L3B1YmxpYy9yZWdpc3RlcmAsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICB9LFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh1c2VyRGF0YSksXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5kZXRhaWwgfHwgJ1JlZ2lzdHJhdGlvbiBmYWlsZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdSZWdpc3RyYXRpb24gZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGdldFVzZXJzID0gYXN5bmMgKCk6IFByb21pc2U8VXNlcltdPiA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aEZldGNoKGAke0FQSV9CQVNFX1VSTH0vdXNlcnNgKTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5kZXRhaWwgfHwgJ0ZhaWxlZCB0byBmZXRjaCB1c2VycycpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHVzZXJzOicsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRVc2VyID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxVc2VyPiA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aEZldGNoKGAke0FQSV9CQVNFX1VSTH0vdXNlcnMvJHt1c2VySWR9YCk7XHJcblxyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEuZGV0YWlsIHx8ICdGYWlsZWQgdG8gZmV0Y2ggdXNlcicpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGZldGNoaW5nIHVzZXIgJHt1c2VySWR9OmAsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1cGRhdGVVc2VyID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nLCB1c2VyRGF0YTogVXBkYXRlVXNlckRhdGEpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgbWVzc2FnZTogc3RyaW5nIH0+ID0+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhdXRoRmV0Y2goYCR7QVBJX0JBU0VfVVJMfS91c2Vycy8ke3VzZXJJZH1gLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BVVCcsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICB9LFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh1c2VyRGF0YSksXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5kZXRhaWwgfHwgJ1VwZGF0ZSBmYWlsZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBFcnJvciB1cGRhdGluZyB1c2VyICR7dXNlcklkfTpgLCBlcnJvcik7XHJcbiAgICB0aHJvdyBlcnJvcjtcclxuICB9XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgZGVsZXRlVXNlciA9IGFzeW5jICh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfT4gPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF1dGhGZXRjaChgJHtBUElfQkFTRV9VUkx9L3VzZXJzLyR7dXNlcklkfWAsIHtcclxuICAgICAgbWV0aG9kOiAnREVMRVRFJyxcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmRldGFpbCB8fCAnRGVsZXRlIGZhaWxlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGRlbGV0aW5nIHVzZXIgJHt1c2VySWR9OmAsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1cGRhdGVVc2VyUm9sZSA9IGFzeW5jICh1c2VySWQ6IHN0cmluZywgcm9sZTogJ2FkbWluJyB8ICdzdWJfYWRtaW4nIHwgJ3VzZXInKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aEZldGNoKGAke0FQSV9CQVNFX1VSTH0vdXNlcnMvJHt1c2VySWR9L3JvbGU/cm9sZT0ke3JvbGV9YCwge1xyXG4gICAgICBtZXRob2Q6ICdQVVQnLFxyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEuZGV0YWlsIHx8ICdSb2xlIHVwZGF0ZSBmYWlsZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBFcnJvciB1cGRhdGluZyByb2xlIGZvciB1c2VyICR7dXNlcklkfTpgLCBlcnJvcik7XHJcbiAgICB0aHJvdyBlcnJvcjtcclxuICB9XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0UHJvdmlkZXJzID0gYXN5bmMgKGFnZW5jeT86ICdTVFVESU8nIHwgJ0NOTScpOiBQcm9taXNlPFByb3ZpZGVyW10+ID0+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgdXJsID0gYWdlbmN5ID8gYCR7QVBJX0JBU0VfVVJMfS9wcm92aWRlcnM/YWdlbmN5PSR7YWdlbmN5fWAgOiBgJHtBUElfQkFTRV9VUkx9L3Byb3ZpZGVyc2A7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF1dGhGZXRjaCh1cmwpO1xyXG5cclxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggcHJvdmlkZXJzJyk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZGF0YTogUHJvdmlkZXJbXSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgIHJldHVybiBkYXRhO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwcm92aWRlcnM6JywgZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGNyZWF0ZVByb3ZpZGVyID0gYXN5bmMgKHByb3ZpZGVyOiBPbWl0PFByb3ZpZGVyLCAncHJvdmlkZXJfaWQnPik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmc7IHByb3ZpZGVyX2lkOiBudW1iZXIgfT4gPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF1dGhGZXRjaChgJHtBUElfQkFTRV9VUkx9L3Byb3ZpZGVyc2AsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICB9LFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShwcm92aWRlciksXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5kZXRhaWwgfHwgJ1Byb3ZpZGVyIGNyZWF0aW9uIGZhaWxlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHByb3ZpZGVyOicsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1cGRhdGVQcm92aWRlciA9IGFzeW5jIChwcm92aWRlcklkOiBudW1iZXIsIHByb3ZpZGVyOiBPbWl0PFByb3ZpZGVyLCAncHJvdmlkZXJfaWQnPik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfT4gPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF1dGhGZXRjaChgJHtBUElfQkFTRV9VUkx9L3Byb3ZpZGVycy8ke3Byb3ZpZGVySWR9YCwge1xyXG4gICAgICBtZXRob2Q6ICdQVVQnLFxyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgfSxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocHJvdmlkZXIpLFxyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEuZGV0YWlsIHx8ICdQcm92aWRlciB1cGRhdGUgZmFpbGVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihgRXJyb3IgdXBkYXRpbmcgcHJvdmlkZXIgJHtwcm92aWRlcklkfTpgLCBlcnJvcik7XHJcbiAgICB0aHJvdyBlcnJvcjtcclxuICB9XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgZGVsZXRlUHJvdmlkZXIgPSBhc3luYyAocHJvdmlkZXJJZDogbnVtYmVyKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aEZldGNoKGAke0FQSV9CQVNFX1VSTH0vcHJvdmlkZXJzLyR7cHJvdmlkZXJJZH1gLCB7XHJcbiAgICAgIG1ldGhvZDogJ0RFTEVURScsXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5kZXRhaWwgfHwgJ1Byb3ZpZGVyIGRlbGV0aW9uIGZhaWxlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGRlbGV0aW5nIHByb3ZpZGVyICR7cHJvdmlkZXJJZH06YCwgZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGFwcHJvdmVVc2VyID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nLCBkYXRhOiBVcGRhdGVVc2VyRGF0YSk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfT4gPT4ge1xyXG4gIHRyeSB7XHJcbiAgICAvLyBBZGQgc3RhdHVzIHRvIHRoZSBkYXRhXHJcbiAgICBjb25zdCBhcHByb3ZlRGF0YSA9IHtcclxuICAgICAgLi4uZGF0YSxcclxuICAgICAgc3RhdHVzOiAnYXBwcm92ZWQnXHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aEZldGNoKGAke0FQSV9CQVNFX1VSTH0vdXNlcnMvJHt1c2VySWR9L2FwcHJvdmVgLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BVVCcsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICB9LFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShhcHByb3ZlRGF0YSksXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5kZXRhaWwgfHwgJ1VzZXIgYXBwcm92YWwgZmFpbGVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihgRXJyb3IgYXBwcm92aW5nIHVzZXIgJHt1c2VySWR9OmAsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCByZWplY3RVc2VyID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aEZldGNoKGAke0FQSV9CQVNFX1VSTH0vdXNlcnMvJHt1c2VySWR9L3JlamVjdGAsIHtcclxuICAgICAgbWV0aG9kOiAnUFVUJyxcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmRldGFpbCB8fCAnVXNlciByZWplY3Rpb24gZmFpbGVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihgRXJyb3IgcmVqZWN0aW5nIHVzZXIgJHt1c2VySWR9OmAsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRQZW5kaW5nVXNlcnMgPSBhc3luYyAoKTogUHJvbWlzZTxVc2VyW10+ID0+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhdXRoRmV0Y2goYCR7QVBJX0JBU0VfVVJMfS91c2Vycy9wZW5kaW5nYCk7XHJcblxyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEuZGV0YWlsIHx8ICdGYWlsZWQgdG8gZmV0Y2ggcGVuZGluZyB1c2VycycpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHBlbmRpbmcgdXNlcnM6JywgZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IHN1c3BlbmRVc2VyID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aEZldGNoKGAke0FQSV9CQVNFX1VSTH0vdXNlcnMvJHt1c2VySWR9L3N1c3BlbmRgLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BVVCcsXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5kZXRhaWwgfHwgJ1VzZXIgc3VzcGVuc2lvbiBmYWlsZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBFcnJvciBzdXNwZW5kaW5nIHVzZXIgJHt1c2VySWR9OmAsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1bnN1c3BlbmRVc2VyID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aEZldGNoKGAke0FQSV9CQVNFX1VSTH0vdXNlcnMvJHt1c2VySWR9L3Vuc3VzcGVuZGAsIHtcclxuICAgICAgbWV0aG9kOiAnUFVUJyxcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmRldGFpbCB8fCAnVXNlciB1bnN1c3BlbnNpb24gZmFpbGVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihgRXJyb3IgdW5zdXNwZW5kaW5nIHVzZXIgJHt1c2VySWR9OmAsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1cGRhdGVQcm92aWRlclN0YXR1cyA9IGFzeW5jIChwcm92aWRlcklkOiBudW1iZXIsIHN0YXR1czogJ2FjdGl2ZScgfCAnY29taW5nX3Nvb24nIHwgJ21haW50ZW5hbmNlJyk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfT4gPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF1dGhGZXRjaChgJHtBUElfQkFTRV9VUkx9L2FwaS9wcm92aWRlcnMvJHtwcm92aWRlcklkfS9zdGF0dXNgLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BVVCcsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICB9LFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHN0YXR1cyB9KSxcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmRldGFpbCB8fCAnUHJvdmlkZXIgc3RhdHVzIHVwZGF0ZSBmYWlsZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBFcnJvciB1cGRhdGluZyBwcm92aWRlciAke3Byb3ZpZGVySWR9IHN0YXR1czpgLCBlcnJvcik7XHJcbiAgICB0aHJvdyBlcnJvcjtcclxuICB9XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgZmV0Y2hBZ2VuY2llcyA9IGFzeW5jICgpOiBQcm9taXNlPHN0cmluZ1tdPiA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aEZldGNoKGAke0FQSV9CQVNFX1VSTH0vYXBpL2FnZW5jaWVzYCk7XHJcblxyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBmZXRjaCBhZ2VuY2llcyBmcm9tIEFQSSwgdXNpbmcgZmFsbGJhY2snKTtcclxuICAgICAgcmV0dXJuIFsnU1RVRElPJywgJ0NOTSddOyAvLyBSZXR1cm4gZmFsbGJhY2sgaW5zdGVhZCBvZiB0aHJvd2luZ1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICByZXR1cm4gZGF0YS5hZ2VuY2llcyB8fCBbJ1NUVURJTycsICdDTk0nXTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgYWdlbmNpZXM6JywgZXJyb3IpO1xyXG4gICAgcmV0dXJuIFsnU1RVRElPJywgJ0NOTSddOyAvLyBSZXR1cm4gZmFsbGJhY2sgaW5zdGVhZCBvZiB0aHJvd2luZ1xyXG4gIH1cclxufTsiXSwibmFtZXMiOlsiQVBJX0JBU0VfVVJMIiwidG9rZW5FeHBpcnlUaW1lciIsImRlY29kZUpXVCIsInRva2VuIiwiYmFzZTY0VXJsIiwic3BsaXQiLCJiYXNlNjQiLCJyZXBsYWNlIiwianNvblBheWxvYWQiLCJkZWNvZGVVUklDb21wb25lbnQiLCJhdG9iIiwibWFwIiwiYyIsImNoYXJDb2RlQXQiLCJ0b1N0cmluZyIsInNsaWNlIiwiam9pbiIsIkpTT04iLCJwYXJzZSIsImVycm9yIiwiY29uc29sZSIsImlzVG9rZW5FeHBpcmVkIiwiZGVjb2RlZCIsImV4cCIsImN1cnJlbnRUaW1lIiwiTWF0aCIsImZsb29yIiwiRGF0ZSIsIm5vdyIsImdldFRpbWVVbnRpbEV4cGlyeSIsInRpbWVVbnRpbEV4cGlyeSIsIm1heCIsInNldHVwVG9rZW5FeHBpcnlDaGVjayIsImNsZWFyVGltZW91dCIsImxvZ291dCIsImFsZXJ0Iiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwic2V0VGltZW91dCIsImxvZyIsInJvdW5kIiwiZ2V0QXV0aEhlYWRlcnMiLCJnZXRUb2tlbiIsImF1dGhGZXRjaCIsInVybCIsIm9wdGlvbnMiLCJoZWFkZXJzIiwicmVzcG9uc2UiLCJmZXRjaCIsInN0YXR1cyIsIkVycm9yIiwibG9naW4iLCJ1c2VybmFtZSIsInBhc3N3b3JkIiwiZm9ybURhdGEiLCJVUkxTZWFyY2hQYXJhbXMiLCJhcHBlbmQiLCJtZXRob2QiLCJib2R5Iiwib2siLCJlcnJvckRhdGEiLCJqc29uIiwiZGV0YWlsIiwiZGF0YSIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJhY2Nlc3NfdG9rZW4iLCJzdHJpbmdpZnkiLCJ1c2VyIiwicmVtb3ZlSXRlbSIsImdldEl0ZW0iLCJnZXRDdXJyZW50VXNlciIsInVzZXJKc29uIiwiZSIsImlzQXV0aGVudGljYXRlZCIsImluaXRpYWxpemVUb2tlbkV4cGlyeUNoZWNrIiwicmVnaXN0ZXJVc2VyIiwidXNlckRhdGEiLCJnZXRVc2VycyIsImdldFVzZXIiLCJ1c2VySWQiLCJ1cGRhdGVVc2VyIiwiZGVsZXRlVXNlciIsInVwZGF0ZVVzZXJSb2xlIiwicm9sZSIsImdldFByb3ZpZGVycyIsImFnZW5jeSIsImNyZWF0ZVByb3ZpZGVyIiwicHJvdmlkZXIiLCJ1cGRhdGVQcm92aWRlciIsInByb3ZpZGVySWQiLCJkZWxldGVQcm92aWRlciIsImFwcHJvdmVVc2VyIiwiYXBwcm92ZURhdGEiLCJyZWplY3RVc2VyIiwiZ2V0UGVuZGluZ1VzZXJzIiwic3VzcGVuZFVzZXIiLCJ1bnN1c3BlbmRVc2VyIiwidXBkYXRlUHJvdmlkZXJTdGF0dXMiLCJmZXRjaEFnZW5jaWVzIiwid2FybiIsImFnZW5jaWVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={686:(e,r,t)=>{var n=t(808);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},808:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(686);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"variable":"--font-inter","display":"swap"}],"variableName":"inter"} ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\",\"variable\":\"__variable_e8ce0c\"};\n    if(true) {\n      // 1754394629566\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdLFwidmFyaWFibGVcIjpcIi0tZm9udC1pbnRlclwiLFwiZGlzcGxheVwiOlwic3dhcFwifV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsZ0ZBQWdGO0FBQzNHLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUF5SixjQUFjLHNEQUFzRDtBQUMzUCxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/YjFjYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wic3R5bGVcIjp7XCJmb250RmFtaWx5XCI6XCInX19JbnRlcl9lOGNlMGMnLCAnX19JbnRlcl9GYWxsYmFja19lOGNlMGMnXCIsXCJmb250U3R5bGVcIjpcIm5vcm1hbFwifSxcImNsYXNzTmFtZVwiOlwiX19jbGFzc05hbWVfZThjZTBjXCIsXCJ2YXJpYWJsZVwiOlwiX192YXJpYWJsZV9lOGNlMGNcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc1NDM5NDYyOTU2NlxuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCJDOi9Vc2Vycy9sYWtzaGl0YS52eWFzL0Rlc2t0b3AvT2NlYW5NaW5kLVByb2QtMy4wLTEvbmV4dGpzLWZyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"}":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Poppins","arguments":[{"weight":["400","500","600","700"],"subsets":["latin"],"variable":"--font-poppins","display":"swap"}],"variableName":"poppins"} ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Poppins_6bee3b', '__Poppins_Fallback_6bee3b'\",\"fontStyle\":\"normal\"},\"className\":\"__className_6bee3b\",\"variable\":\"__variable_6bee3b\"};\n    if(true) {\n      // 1754394629568\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIlBvcHBpbnNcIixcImFyZ3VtZW50c1wiOlt7XCJ3ZWlnaHRcIjpbXCI0MDBcIixcIjUwMFwiLFwiNjAwXCIsXCI3MDBcIl0sXCJzdWJzZXRzXCI6W1wibGF0aW5cIl0sXCJ2YXJpYWJsZVwiOlwiLS1mb250LXBvcHBpbnNcIixcImRpc3BsYXlcIjpcInN3YXBcIn1dLFwidmFyaWFibGVOYW1lXCI6XCJwb3BwaW5zXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsb0ZBQW9GO0FBQy9HLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUF5SixjQUFjLHNEQUFzRDtBQUMzUCxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/ODJjYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wic3R5bGVcIjp7XCJmb250RmFtaWx5XCI6XCInX19Qb3BwaW5zXzZiZWUzYicsICdfX1BvcHBpbnNfRmFsbGJhY2tfNmJlZTNiJ1wiLFwiZm9udFN0eWxlXCI6XCJub3JtYWxcIn0sXCJjbGFzc05hbWVcIjpcIl9fY2xhc3NOYW1lXzZiZWUzYlwiLFwidmFyaWFibGVcIjpcIl9fdmFyaWFibGVfNmJlZTNiXCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NTQzOTQ2Mjk1NjhcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiQzovVXNlcnMvbGFrc2hpdGEudnlhcy9EZXNrdG9wL09jZWFuTWluZC1Qcm9kLTMuMC0xL25leHRqcy1mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe[incompatible-use] This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement$1(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement$1(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner$1.current && self && ReactCurrentOwner$1.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner$1.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner$1.current, props);\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    if (type.$$typeof === REACT_CLIENT_REFERENCE) {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV$1(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (hasOwnProperty.call(props, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(props).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV = jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/Yjk2NiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/navigation.js":
/*!*****************************************!*\
  !*** ./node_modules/next/navigation.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQUEsK0pBQStEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L25hdmlnYXRpb24uanM/YTEzYiJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/navigation.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Ccomponents%5Cui%5CSessionTimer.tsx&modules=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5COceanMind-Prod-3.0-1%5Cnextjs-frontend%5Csrc%5Ccontext%5CAuthContext.tsx&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);