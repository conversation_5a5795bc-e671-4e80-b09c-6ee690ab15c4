'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../context/AuthContext';
import {
  User,
  // Provider types
  Provider,
  getUsers,
  updateUser,
  deleteUser,
  updateUserRole,
  getProviders,
  createProvider,
  deleteProvider,
  getPendingUsers,
  approveUser,
  rejectUser,
  suspendUser,
  unsuspendUser,
  fetchAgencies,
  updateProvider,
  updateProviderStatus
} from '../../services/auth';
import { ProviderType } from '../../components/booking/ProviderSelector';
import { getUserTrackingData, UserTrackingData } from '../../services/api';
import { parseIssueCodes, getIssueLabel } from '../../utils/issueHelpers';
import { ProcessedRequestAnalytics } from './analytics';
import ResultsViewModal from '../../components/booking/ResultsViewModal';

// Define the provider option type
type ProviderOption = {
  value: ProviderType;
  label: string;
  agency: string;
  disabled?: boolean;
};

// Types for admin panel state
type TabType = 'dashboard' | 'pending' | 'users' | 'providers' | 'processed';
type ApprovalData = {
  role: 'user' | 'sub_admin' | 'admin';
  agencies: string[];
  portalAccess: string[];
  providerAccess: ProviderType[];
};

// Create a custom multi-select dropdown component
interface MultiSelectDropdownProps {
  options: { value: string; label: string }[];
  selected: string[];
  onChange: (selected: string[]) => void;
}

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({ options, selected, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOption = (value: string) => {
    if (selected.includes(value)) {
      onChange(selected.filter(item => item !== value));
    } else {
      onChange([...selected, value]);
    }
  };

  return (
    <div className="relative">
      <div
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 cursor-pointer flex justify-between items-center"
      >
        <span className="text-gray-700">
          {selected.length === 0 ? 'Select agencies' : selected.join(', ')}
        </span>
        <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>
      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200">
          {options.map(option => (
            <div
              key={option.value}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
              onClick={() => toggleOption(option.value)}
            >
              <input
                type="checkbox"
                checked={selected.includes(option.value)}
                readOnly
                className="mr-2 h-4 w-4"
              />
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default function AdminPanel() {
  const router = useRouter();
  const { isAdmin, isSubAdmin, isLoggedIn, loading, user: currentUser, canManageUser, canAccessAgency, isSubAdminOnly, getAccessibleAgencies } = useAuth();

  // Main data states
  const [users, setUsers] = useState<User[]>([]);
  const [pendingUsers, setPendingUsers] = useState<User[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [agencies, setAgencies] = useState<string[]>(['STUDIO', 'CNM']);

  // Dynamically build providerOptions from API-fetched providers
  const providerOptions: ProviderOption[] = useMemo(
    () => providers.map(p => ({
      value: p.name as ProviderType,
      label: p.description || p.name,
      agency: p.agency
    })),
    [providers]
  );

  // UI states
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Form states
  const [newProvider, setNewProvider] = useState({
    name: '',
    agency: 'STUDIO',
    description: '',
    status: 'active' as 'active' | 'coming_soon' | 'maintenance'
  });
  const [customAgency, setCustomAgency] = useState('');
  const [showCustomAgency, setShowCustomAgency] = useState(false);
  
  // Provider editing states
  const [editingProvider, setEditingProvider] = useState<Provider | null>(null);
  const [editProvider, setEditProvider] = useState({
    name: '',
    agency: '',
    description: '',
    status: 'active' as 'active' | 'coming_soon' | 'maintenance'
  });
  const [editCustomAgency, setEditCustomAgency] = useState('');
  const [showEditCustomAgency, setShowEditCustomAgency] = useState(false);

  // User management states
  const [userApprovalData, setUserApprovalData] = useState<Record<string, ApprovalData>>({});
  const [userProviderAccess, setUserProviderAccess] = useState<Record<string, ProviderType[]>>({});

  // Tracking data states
  const [trackingData, setTrackingData] = useState<UserTrackingData[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<string>('all');
  const [availableUsers, setAvailableUsers] = useState<string[]>([]);
  const [searchUsername, setSearchUsername] = useState<string>('');
  const [isLoadingTracking, setIsLoadingTracking] = useState(false);
  const [trackingError, setTrackingError] = useState<string | null>(null);
  const [processedViewMode, setProcessedViewMode] = useState<'table' | 'analytics'>('table');
  const [numDays, setNumDays] = useState(0); // 0 for all data
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);
  const [customDays, setCustomDays] = useState<string>('');


  // Add state for managing user agencies selection in the edit mode
  const [userAgenciesEdit, setUserAgenciesEdit] = useState<Record<string, string[]>>({});
  
  // State for expanded comments and request IDs
  const [expandedComments, setExpandedComments] = useState<Record<string, boolean>>({});
  const [expandedRequestIds, setExpandedRequestIds] = useState<Record<string, boolean>>({});
  const [expandedIssues, setExpandedIssues] = useState<Record<string, boolean>>({});

  // Result view modal state
  const [resultModalOpen, setResultModalOpen] = useState(false);
  const [selectedResult, setSelectedResult] = useState<{ provider: ProviderType; requestId: string; sessionId: string } | null>(null);

  const handleViewResult = (provider: string, requestId: string, sessionId: string) => {
    setSelectedResult({ provider: provider as ProviderType, requestId, sessionId });
    setResultModalOpen(true);
  };

  const handleCloseResultModal = () => {
    setResultModalOpen(false);
    setSelectedResult(null);
  };

  // ====== Pagination state for processed tracking table ======
  const rowsPerPage = 100;
  const [currentPage, setCurrentPage] = useState(1);

  // Reset to first page whenever fresh data arrive or filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [trackingData]);

  const totalPages = Math.max(1, Math.ceil(trackingData.length / rowsPerPage));

  const paginatedTrackingData = useMemo(() => {
    const start = (currentPage - 1) * rowsPerPage;
    return trackingData.slice(start, start + rowsPerPage);
  }, [trackingData, currentPage]);

  const canPrev = currentPage > 1;
  const canNext = currentPage < totalPages;

  const gotoPrev = () => canPrev && setCurrentPage(p => p - 1);
  const gotoNext = () => canNext && setCurrentPage(p => p + 1);

  // Function to toggle comment expansion
  const toggleCommentExpansion = (sessionId: string) => {
    setExpandedComments(prev => ({
      ...prev,
      [sessionId]: !prev[sessionId]
    }));
  };

  // Function to toggle request ID expansion
  const toggleRequestIdExpansion = (sessionId: string) => {
    setExpandedRequestIds(prev => ({
      ...prev,
      [sessionId]: !prev[sessionId]
    }));
  };

  const toggleIssueExpansion = (sessionId: string) => {
    setExpandedIssues(prev => ({
      ...prev,
      [sessionId]: !prev[sessionId]
    }));
  };

  // Function to truncate comment text
  const truncateComment = (comment: string, maxLength: number = 50) => {
    if (!comment || comment.length <= maxLength) return comment;
    return comment.substring(0, maxLength) + '...';
  };

  // Function to parse session ID into request ID and timestamp
  const parseSessionId = (sessionId: string) => {
    // Session ID format: Quote_1_20250711114301
    // Split by underscore and find the last part which should be the timestamp
    const parts = sessionId.split('_');
    if (parts.length >= 2) {
      const lastPart = parts[parts.length - 1];
      // Check if last part is a timestamp (14 digits: YYYYMMDDHHMMSS)
      if (lastPart.length === 14 && /^\d{14}$/.test(lastPart)) {
        const rawTimestamp = lastPart;
        const requestId = parts.slice(0, -1).join('_');
        
        // Parse timestamp: YYYYMMDDHHMMSS
        const year = rawTimestamp.substring(0, 4);
        const month = rawTimestamp.substring(4, 6);
        const day = rawTimestamp.substring(6, 8);
        const hour = rawTimestamp.substring(8, 10);
        const minute = rawTimestamp.substring(10, 12);
        const second = rawTimestamp.substring(12, 14);
        
        // Create date object and format as IST
        const date = new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}`);
        
        // Format date and time separately for IST (Indian Standard Time)
        const istDate = date.toLocaleDateString('en-IN', {
          timeZone: 'Asia/Kolkata',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
        
        const istTime = date.toLocaleTimeString('en-IN', {
          timeZone: 'Asia/Kolkata',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });
        
        return { requestId, date: istDate, time: istTime };
      }
    }
    // If parsing fails, return the original session ID as request ID
    return { requestId: sessionId, date: '', time: '' };
  };

  // Initialize the agencies edit state and provider access when users are loaded
  useEffect(() => {
    if (users.length > 0) {
      const initialAgenciesEdit: Record<string, string[]> = {};
      const initialProviderAccess: Record<string, ProviderType[]> = {};
      
      // Get all available provider values for non-admin users
      const nonAdminProviderValues = providers
        .filter(provider => provider.status !== 'coming_soon')
        .map(provider => provider.name as ProviderType);
      
      // Get ALL provider values for admin users (including coming soon)
      const allProviderValues = providers
        .map(provider => provider.name as ProviderType);
      
      users.forEach(user => {
        // For admin users, assign all providers by default (including coming soon)
        if (user.role === 'admin') {
          initialAgenciesEdit[user.user_id] = ['STUDIO', 'CNM']; // Admin gets access to all agencies
          initialProviderAccess[user.user_id] = allProviderValues; // Admin gets ALL providers including coming soon
        } else {
          // For non-admin users, use their existing settings
          initialAgenciesEdit[user.user_id] = user.agency ? [user.agency] : [];
          initialProviderAccess[user.user_id] = user.provider_access || [];
        }
      });
      
      setUserAgenciesEdit(initialAgenciesEdit);
      setUserProviderAccess(initialProviderAccess);
    }
  }, [users, providerOptions]);

  // Redirect if not admin or sub-admin
  useEffect(() => {
    if (!loading && !isSubAdmin) {
      router.push('/');
    }
  }, [isSubAdmin, loading, router]);

  // Load data when the component mounts
  useEffect(() => {
    if (isSubAdmin) {
      fetchData();
    }
  }, [isSubAdmin]);

  // Generic error handler
  const handleError = (err: any, message: string) => {
    console.error(`${message}:`, err);
    setError(message);
    setIsLoading(false);
  };

  // Success message handler
  const showSuccess = (message: string) => {
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  // Fetch all required data for the admin panel
  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch all data in parallel
      const [usersData, pendingUsersData, providersData, agenciesData] = await Promise.all([
        getUsers(),
        getPendingUsers(),
        getProviders(),
        fetchAgencies().catch(() => ['STUDIO', 'CNM']) // fallback on error
      ]);

      setUsers(usersData);
      setPendingUsers(pendingUsersData);
      setProviders(providersData);
      setAgencies(agenciesData);

      // Initialize user approval data for pending users
      const initialApprovalData: Record<string, ApprovalData> = {};
      pendingUsersData.forEach(user => {
        initialApprovalData[user.user_id] = {
          role: 'user',
          agencies: [user.agency as 'STUDIO' | 'CNM'],
          portalAccess: [],
          providerAccess: []
        };
      });
      setUserApprovalData(initialApprovalData);

    } catch (err: any) {
      console.error('Error fetching data:', err);
      setError(`Failed to load data: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };


  // Helper function to check if user can change role
  const canChangeRole = (userId: string, currentRole: string, newRole: string, currentUser: any): { canChange: boolean; reason?: string } => {
    // 1. Sub-admin cannot change their own role
    if (currentUser?.user_id === userId && currentUser.role === 'sub_admin') {
      return { canChange: false, reason: 'Sub-admins cannot change their own role' };
    }
    
    // 2. Admin cannot change their own role if they are the only admin
    if (currentUser?.user_id === userId && currentUser.role === 'admin' && newRole !== 'admin') {
      const adminCount = users.filter(u => u.role === 'admin').length;
      if (adminCount <= 1) {
        return { canChange: false, reason: 'Cannot change role - you are the only admin in the system' };
      }
    }
    
    // 3. Sub-admins cannot promote users to admin
    if (currentUser?.role === 'sub_admin' && newRole === 'admin') {
      return { canChange: false, reason: 'Sub-admins cannot promote users to admin role' };
    }
    
    // 4. Sub-admins cannot manage users outside their agency
    if (currentUser?.role === 'sub_admin') {
      const targetUser = users.find(u => u.user_id === userId);
      if (targetUser && !canManageUser(targetUser)) {
        return { canChange: false, reason: 'You can only manage users within your assigned agencies' };
      }
    }
    
    return { canChange: true };
  };

  // User management actions
  const handleUpdateRole = async (userId: string, newRole: 'admin' | 'sub_admin' | 'user') => {
    try {
      setIsLoading(true);
      
      // Get the current user being updated
      const targetUser = users.find(u => u.user_id === userId);
      if (!targetUser) {
        throw new Error('User not found');
      }
      
      // Check if role change is allowed
      const roleCheck = canChangeRole(userId, targetUser.role, newRole, currentUser);
      if (!roleCheck.canChange) {
        setError(roleCheck.reason || 'Role change not allowed');
        return;
      }
      
      // If promoting to admin, automatically assign all providers
      let updatedProviderAccess = userProviderAccess[userId] || [];
      let updatedAgencies = userAgenciesEdit[userId] || [];
      
      if (newRole === 'admin') {
        // Get ALL provider values INCLUDING coming soon ones for admin users
        const allProviderValues = providerOptions
          .map(provider => provider.value);
          
        updatedProviderAccess = allProviderValues;
        updatedAgencies = ['STUDIO', 'CNM']; // Admin gets access to all agencies
        
        // Update the provider access and agencies states
        setUserProviderAccess(prev => ({
          ...prev,
          [userId]: updatedProviderAccess
        }));
        
        setUserAgenciesEdit(prev => ({
          ...prev,
          [userId]: updatedAgencies
        }));
        
        // Also update the user's provider access in the database
        await updateUser(userId, {
          role: newRole,
          provider_access: updatedProviderAccess,
          agency: updatedAgencies[0] || 'STUDIO' // Convert to single agency for backend
        });
      } else {
        // Just update the role if not promoting to admin
        await updateUserRole(userId, newRole);
      }

      // Update the user in the list
      setUsers(users.map(user =>
        user.user_id === userId ? { 
          ...user, 
          role: newRole,
          provider_access: updatedProviderAccess,
          agency: updatedAgencies[0] || user.agency // Convert to single agency for backend
        } : user
      ));

      showSuccess('User role updated successfully');
    } catch (err) {
      handleError(err, 'Failed to update user role');
    } finally {
      setIsLoading(false);
    }
  };

  // Update the handleUpdateUserProviders function to include agencies
  const handleUpdateUserProviders = async (userId: string) => {
    try {
      setIsLoading(true);
      const selectedProviders = userProviderAccess[userId] || [];
      const selectedAgencies = userAgenciesEdit[userId] || [];

      await updateUser(userId, {
        provider_access: selectedProviders,
        agency: selectedAgencies[0] || 'STUDIO' // Convert array to single agency for backend
      });

      // Update the user in the list
      setUsers(users.map(user =>
        user.user_id === userId ? { 
          ...user, 
          provider_access: selectedProviders,
          agencies: selectedAgencies
        } : user
      ));

      showSuccess('User provider access updated successfully');
    } catch (err) {
      handleError(err, 'Failed to update user provider access');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      setIsLoading(true);
      
      // Check if current user can manage this user
      const targetUser = users.find(u => u.user_id === userId);
      if (targetUser && !canManageUser(targetUser)) {
        setError('You can only delete users within your assigned agencies');
        return;
      }
      
      await deleteUser(userId);

      // Remove the user from the list
      setUsers(users.filter(user => user.user_id !== userId));
      showSuccess('User deleted successfully');
    } catch (err) {
      const errorMessage = err?.toString().includes('Admin accounts cannot be deleted')
        ? 'Admin accounts cannot be deleted'
        : 'Failed to delete user';
      handleError(err, errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Portal management actions
  const handleCreateProvider = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Use custom agency if provided, otherwise use selected agency
      const agencyToUse = showCustomAgency && customAgency.trim() 
        ? customAgency.trim() 
        : newProvider.agency;

      // Check if current user can access this agency
      if (!showCustomAgency && !canAccessAgency(agencyToUse)) {
        setError('You can only create providers for your assigned agencies');
        return;
      }
      
      const providerData = {
        name: newProvider.name,
        agency: agencyToUse,
        description: newProvider.description,
        status: newProvider.status
      };

      const result = await createProvider(providerData);

      // Add the new provider to the list
      const newProviderWithId: Provider = { 
        ...providerData, 
        provider_id: result.provider_id
      };
      setProviders([...providers, newProviderWithId]);

      // Update agencies list if a new custom agency was added
      if (showCustomAgency && customAgency.trim() && !agencies.includes(customAgency.trim())) {
        setAgencies([...agencies, customAgency.trim()]);
      }

      // Reset the form
      setNewProvider({
        name: '',
        agency: agencies[0] || 'STUDIO',
        description: '',
        status: 'active' as 'active' | 'coming_soon' | 'maintenance'
      });
      setCustomAgency('');
      setShowCustomAgency(false);

      showSuccess('Provider created successfully');
    } catch (err) {
      handleError(err, 'Failed to create provider');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProvider = async (providerId: number) => {
    if (!confirm('Are you sure you want to delete this provider?')) return;

    try {
      setIsLoading(true);
      
      // Check if current user can access this provider's agency
      const provider = providers.find(p => p.provider_id === providerId);
      if (provider && !canAccessAgency(provider.agency)) {
        setError('You can only delete providers for your assigned agencies');
        return;
      }
      
      await deleteProvider(providerId);

      // Remove the provider from the list
      setProviders(providers.filter(provider => provider.provider_id !== providerId));
      showSuccess('Provider deleted successfully');
    } catch (err) {
      handleError(err, 'Failed to delete provider');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditProvider = (provider: Provider) => {
    setEditingProvider(provider);
    setEditProvider({
      name: provider.name,
      agency: provider.agency,
      description: provider.description,
      status: provider.status
    });
    setEditCustomAgency('');
    setShowEditCustomAgency(false);
  };

  const handleCancelEdit = () => {
    setEditingProvider(null);
    setEditProvider({ name: '', agency: '', description: '', status: 'active' as 'active' | 'coming_soon' | 'maintenance' });
    setEditCustomAgency('');
    setShowEditCustomAgency(false);
  };

  const handleUpdateProvider = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingProvider) return;

    setIsLoading(true);
    setError(null);

    try {
      // Use custom agency if provided, otherwise use selected agency
      const agencyToUse = showEditCustomAgency && editCustomAgency.trim() 
        ? editCustomAgency.trim() 
        : editProvider.agency;

      // Check if current user can access this agency
      if (!showEditCustomAgency && !canAccessAgency(agencyToUse)) {
        setError('You can only update providers for your assigned agencies');
        return;
      }
      
      const updatedProviderData = {
        name: editProvider.name,
        agency: agencyToUse,
        description: editProvider.description,
        status: editProvider.status
      };

      await updateProvider(editingProvider.provider_id, updatedProviderData);

      // Update the provider in the list
      const updatedProviders = providers.map(p => 
        p.provider_id === editingProvider.provider_id 
          ? { ...p, ...updatedProviderData }
          : p
      );
      setProviders(updatedProviders);

      // Update agencies list if a new custom agency was added
      if (showEditCustomAgency && editCustomAgency.trim() && !agencies.includes(editCustomAgency.trim())) {
        setAgencies([...agencies, editCustomAgency.trim()]);
      }

      // Reset editing state
      handleCancelEdit();
      showSuccess('Provider updated successfully');
    } catch (err) {
      handleError(err, 'Failed to update provider');
    } finally {
      setIsLoading(false);
    }
  };

  // User approval/rejection actions
  const handleApproveUser = async (userId: string) => {
    try {
      setIsLoading(true);
      const userData = userApprovalData[userId];

      if (!userData) {
        throw new Error('User approval data not found');
      }

      // If approving as admin, automatically assign all providers
      let providerAccess = userData.providerAccess;
      let agencies = userData.agencies;
      
      if (userData.role === 'admin') {
        // Get ALL provider values INCLUDING coming soon ones for admin users
        const allProviderValues = providers
          .map(provider => provider.name as ProviderType);
          
        providerAccess = allProviderValues;
        agencies = ['STUDIO', 'CNM']; // Admin gets access to all agencies
      }

      await approveUser(userId, {
        role: userData.role,
        agency: agencies[0] || 'STUDIO', // Convert array to single agency for backend
        portal_access: userData.portalAccess,
        provider_access: providerAccess
      });

      // Remove from pending list
      setPendingUsers(pendingUsers.filter(user => user.user_id !== userId));

      // Refresh the users list
      const updatedUsers = await getUsers();
      setUsers(updatedUsers);

      showSuccess('User approved successfully');
    } catch (err) {
      handleError(err, 'Failed to approve user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectUser = async (userId: string) => {
    if (!confirm('Are you sure you want to reject this user registration?')) return;

    try {
      setIsLoading(true);
      await rejectUser(userId);

      // Remove from pending list
      setPendingUsers(pendingUsers.filter(user => user.user_id !== userId));
      showSuccess('User rejected successfully');
    } catch (err) {
      handleError(err, 'Failed to reject user');
    } finally {
      setIsLoading(false);
    }
  };

  // Update user approval data
  const updateUserApprovalData = (userId: string, field: string, value: any) => {
    setUserApprovalData(prev => {
      const updatedData = {
        ...prev[userId],
        [field]: value
      };
      
      // If role is being updated to admin, automatically assign all providers and agencies
      if (field === 'role' && value === 'admin') {
        // Get ALL provider values INCLUDING coming soon ones for admin users
        const allProviderValues = providerOptions
          .map(provider => provider.value);
          
        updatedData.providerAccess = allProviderValues;
        updatedData.agencies = ['STUDIO', 'CNM']; // Admin gets access to all agencies
      }
      
      return {
        ...prev,
        [userId]: updatedData
      };
    });
  };

  // Toggle access helpers
  const handlePortalAccessToggle = (userId: string, portalId: string) => {
    const currentAccess = userApprovalData[userId]?.portalAccess || [];
    const newAccess = currentAccess.includes(portalId)
      ? currentAccess.filter(id => id !== portalId)
      : [...currentAccess, portalId];

    updateUserApprovalData(userId, 'portalAccess', newAccess);
  };

  // Helper function to get agencies associated with user's providers
  const getProviderAgencies = useCallback((providerAccess: ProviderType[]) => {
    if (!providerAccess || providerAccess.length === 0) return [];
    
    const agencySet = new Set<string>();
    
    providerAccess.forEach(providerName => {
      const provider = providerOptions.find(p => p.value === providerName);
      if (provider) {
        agencySet.add(provider.agency);
      }
    });
    
    return Array.from(agencySet);
  }, [providerOptions]);

  const handleProviderAccessToggle = useCallback((userId: string, provider: ProviderType, isPending: boolean = false) => {
    // Find the provider object to get its agency
    const providerObj = providerOptions.find(p => p.value === provider);
    if (!providerObj) return;
    
    // Skip if the provider is disabled for sub-admin
    if (isSubAdminOnly && currentUser?.provider_access && !currentUser.provider_access.includes(provider)) {
      return;
    }
    
    if (isPending) {
      // For pending users
      const currentAccess = userApprovalData[userId]?.providerAccess || [];
      const newAccess = currentAccess.includes(provider)
        ? currentAccess.filter(p => p !== provider)
        : [...currentAccess, provider];

      updateUserApprovalData(userId, 'providerAccess', newAccess);
      
      // Update agencies based on selected providers
      const newAgencies = getProviderAgencies(newAccess);
      
      // Ensure we have at least one agency
      if (newAgencies.length === 0 && newAccess.length > 0) {
        // If we have providers but no agencies were detected, use the agency of the current provider
        newAgencies.push(providerObj.agency);
      }
      
      updateUserApprovalData(userId, 'agencies', newAgencies);
    } else {
      // For existing users
      const currentAccess = userProviderAccess[userId] || [];
      const newAccess = currentAccess.includes(provider)
        ? currentAccess.filter(p => p !== provider)
        : [...currentAccess, provider];

      setUserProviderAccess(prev => ({
        ...prev,
        [userId]: newAccess
      }));
      
      // Update agencies based on selected providers
      const newAgencies = getProviderAgencies(newAccess);
      
      // Ensure we have at least one agency
      if (newAgencies.length === 0 && newAccess.length > 0) {
        // If we have providers but no agencies were detected, use the agency of the current provider
        newAgencies.push(providerObj.agency);
      }
      
      setUserAgenciesEdit(prev => ({
        ...prev,
        [userId]: newAgencies as string[]
      }));
    }
  }, [userApprovalData, userProviderAccess, providerOptions, getProviderAgencies, isSubAdminOnly, currentUser]);

  // Helper functions for filtering data
  const getAgencyPricingProviders = useCallback((agency: 'STUDIO' | 'CNM') => {
    // Filter pricing providers (from state) by agency
    return providers.filter(provider => provider.agency === agency);
  }, [providers]);

  // Fetch tracking data
  const fetchTrackingData = useCallback(async () => {
    try {
      setIsLoadingTracking(true);
      setTrackingError(null);

      // Use selectedUser if it's not 'all', otherwise use searchUsername
      const usernameFilter = selectedUser !== 'all' ? selectedUser : (searchUsername || undefined);

      const data = await getUserTrackingData(
        selectedProvider,
        usernameFilter
      );
      setTrackingData(data);

      // Only fetch all data for user dropdown if we don't already have it and we're viewing all data
      if (availableUsers.length === 0 && selectedProvider === 'all' && selectedUser === 'all' && !searchUsername) {
        // If we just fetched all data, use it; otherwise make a separate call
        const uniqueUsers = [...new Set(data.map(item => item.username))].sort();
        setAvailableUsers(uniqueUsers);
      } else if (availableUsers.length === 0) {
        // Only make separate call if we really need all users but didn't fetch all data
        const allData = await getUserTrackingData('all', undefined);
        const uniqueUsers = [...new Set(allData.map(item => item.username))].sort();
        setAvailableUsers(uniqueUsers);
      }
    } catch (error) {
      console.error('Error fetching tracking data:', error);
      setTrackingError(error instanceof Error ? error.message : 'Failed to fetch tracking data');
    } finally {
      setIsLoadingTracking(false);
    }
  }, [selectedProvider, selectedUser, searchUsername, availableUsers.length]);

  // Load tracking data when component mounts, tab changes, or filters change
  useEffect(() => {
    if (activeTab === 'processed' && isSubAdmin) {
      fetchTrackingData();
    }
  }, [activeTab, isSubAdmin, selectedProvider, selectedUser, searchUsername, fetchTrackingData]);

  // Helper function to get providers from selected agencies
  const getProvidersFromAgencies = useCallback((agencies: string[]) => {
    if (agencies.length === 0) return [];
    
    // Get all providers for the selected agencies and convert to ProviderOption format
    const agencyProviders = providers
      .filter((provider: Provider) => agencies.includes(provider.agency))
      .map((provider: Provider) => ({
        value: provider.name as ProviderType,
        label: provider.description || provider.name,
        agency: provider.agency,
        status: provider.status,
        disabled: false
      }));
    
    // If user is admin, return all providers for the selected agencies
    if (isAdmin) {
      return agencyProviders;
    }
    
    // For sub-admins, mark providers they don't have access to as disabled
    if (isSubAdminOnly && currentUser?.provider_access) {
      return agencyProviders.map((provider) => ({
        ...provider,
        disabled: !currentUser.provider_access?.includes(provider.value)
      }));
    }
    
    return agencyProviders;
  }, [providers, isAdmin, isSubAdminOnly, currentUser]);

  // Add handler functions for suspending and unsuspending users
  const handleSuspendUser = async (userId: string) => {
    if (!confirm('Are you sure you want to suspend this user? They will no longer be able to log in.')) return;

    try {
      setIsLoading(true);
      
      // Check if current user can manage this user
      const targetUser = users.find(u => u.user_id === userId);
      if (targetUser && !canManageUser(targetUser)) {
        setError('You can only suspend users within your assigned agencies');
        return;
      }
      
      await suspendUser(userId);

      // Update the user in the list
      setUsers(users.map(user =>
        user.user_id === userId ? { ...user, status: 'suspended' } : user
      ));
      showSuccess('User suspended successfully');
    } catch (err) {
      const errorMessage = err?.toString().includes('Admin accounts cannot be suspended')
        ? 'Admin accounts cannot be suspended'
        : 'Failed to suspend user';
      handleError(err, errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnsuspendUser = async (userId: string) => {
    try {
      setIsLoading(true);
      
      // Check if current user can manage this user
      const targetUser = users.find(u => u.user_id === userId);
      if (targetUser && !canManageUser(targetUser)) {
        setError('You can only unsuspend users within your assigned agencies');
        return;
      }
      
      await unsuspendUser(userId);

      // Update the user in the list
      setUsers(users.map(user =>
        user.user_id === userId ? { ...user, status: 'approved' } : user
      ));
      showSuccess('User unsuspended successfully');
    } catch (err) {
      handleError(err, 'Failed to unsuspend user');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle provider status update
  const handleUpdateProviderStatus = async (providerId: number, newStatus: 'active' | 'coming_soon' | 'maintenance') => {
    try {
      setIsLoading(true);
      await updateProviderStatus(providerId, newStatus);
      
      // Refresh provider data from database to ensure consistency
      const updatedProviders = await getProviders();
      setProviders(updatedProviders);
      
      showSuccess(`Provider status updated to ${newStatus}`);
    } catch (err: any) {
      handleError(err, 'Failed to update provider status');
    } finally {
      setIsLoading(false);
    }
  };

  // const downloadCSV = () => {
  //   const filteredData = numDays > 0 ? trackingData.filter(row => {
  //     const rowDate = new Date(row.timestamp);
  //     const cutoff = new Date();
  //     cutoff.setDate(cutoff.getDate() - numDays);
  //     return rowDate >= cutoff;
  //   }) : trackingData;

  //   const headers = 'Request ID,Date,Time,Provider,Username,Execution Time,Validation Status,Mark,Feature,Comment,Screenshot Count\n';
  //   const rows = filteredData.map(row => {
  //     const { requestId, date, time } = parseSessionId(row.session_id);
  //     return `"${requestId}","${date}","${time}","${row.provider}","${row.username}","${row.execution_time || ''}","${row.val_status ? 'Valid' : 'Invalid'}","${row.val_mark || ''}","${row.as_per_feature !== undefined && row.as_per_feature !== '' ? row.as_per_feature : (row.val_mark === '1' ? '1' : '')}","${row.val_comment || ''}","${row.screenshot_count || 0}"`;
  //   }).join('\n');
  //   const csv = headers + rows;
  //   const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  //   const link = document.createElement('a');
  //   const url = URL.createObjectURL(blob);
  //   link.setAttribute('href', url);
  //   link.setAttribute('download', 'processed_requests.csv');
  //   link.style.visibility = 'hidden';
  //   document.body.appendChild(link);
  //   link.click();
  //   document.body.removeChild(link);
  // };

  // CSV download function
  const downloadCSV = () => {
    const filteredData = numDays > 0 ? trackingData.filter(row => {
      const rowDate = new Date(row.timestamp);
      const cutoff = new Date();
      cutoff.setDate(cutoff.getDate() - numDays);
      return rowDate >= cutoff;
    }) : trackingData;

    const headers = 'Request ID,Date,Time,Provider,Username,Execution Time,Validation Status,Mark,Feature,Issues,Comment\n';
    const rows = filteredData.map(row => {
      const { requestId, date, time } = parseSessionId(row.session_id);
      const issueLabels = parseIssueCodes(row.issue_codes).map(ic => getIssueLabel(ic.code)).join(';');
      return `"${requestId}","${date}","${time}","${row.provider}","${row.username}","${row.execution_time || ''}","${row.val_status ? 'Valid' : 'Invalid'}","${row.val_mark || ''}","${row.as_per_feature !== undefined && row.as_per_feature !== '' ? row.as_per_feature : (row.val_mark === '1' ? '1' : '')}","${issueLabels}","${row.val_comment || ''}"`;
    }).join('\n');
    const csv = headers + rows;
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'processed_requests.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Loading and auth check
  if (loading || !isLoggedIn) {
    return <div className="container mx-auto p-8 text-center">Loading...</div>;
  }

  if (!isSubAdmin) {
    return null; // Will redirect via useEffect
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: "#e6e6e6" }}>
      <div className="shadow-lg" style={{ background: "linear-gradient(to right, #2C5364, #203A43, #0F2027)" }}>
        <div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between ">
          <h1 className="text-2xl font-bold text-white flex items-center">
            <svg className="w-6 h-6 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Admin Dashboard
          </h1>
          <button
            onClick={() => router.push('/')}
            className="bg-gradient-to-r from-blue-100 to-green-100 text-black px-4 py-1.5 rounded-full shadow-lg hover:from-blue-200 hover:to-green-200 transition-all duration-300 text-base"
          >
            Launch
          </button>
        </div>
      </div>

      <div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Success message */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 border-l-4 border-green-500 text-green-700 rounded-md flex items-center shadow-sm">
            <svg className="h-5 w-5 mr-3 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>{successMessage}</span>
          </div>
        )}

        {/* Error message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 text-red-700 rounded-md flex items-center shadow-sm">
            <svg className="h-5 w-5 mr-3 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <span>{error}</span>
          </div>
        )}

        <div className="flex flex-col md:flex-row md:items-center md:justify-between ">
          {/* Tabs */}
          <div className="flex bg-white text-md rounded-xl shadow-md overflow-hidden border border-gray-200 mb-4 md:mb-0">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`px-9 py-2 font-medium transition-colors relative ${activeTab === 'dashboard'
                ? 'text-teal-600 bg-teal-50'
                : 'text-gray-600 hover:bg-slate-200'
                }`}
            >
              Dashboard
              {activeTab === 'dashboard' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500 to-blue-500"></div>
              )}
            </button>
            <button
              onClick={() => setActiveTab('pending')}
              className={`px-9 py-2  font-medium transition-colors relative ${activeTab === 'pending'
                ? 'text-teal-600 bg-teal-50'
                : 'text-gray-600 hover:bg-slate-200'
                }`}
            >
              Pending Approvals
              {pendingUsers.length > 0 && (
                <span className="ml-2 px-2 py-0.5 text-xs font-semibold rounded-full bg-amber-100 text-amber-700">{pendingUsers.length}</span>
              )}
              {activeTab === 'pending' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500 to-blue-500"></div>
              )}
            </button>
            <button
              onClick={() => setActiveTab('users')}
              className={`px-9 py-3 font-medium transition-colors relative ${activeTab === 'users'
                ? 'text-teal-600 bg-teal-50'
                : 'text-gray-600 hover:bg-slate-200'
                }`}
            >
              Users
              {activeTab === 'users' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500 to-blue-500"></div>
              )}
            </button>
            <button
              onClick={() => setActiveTab('providers')}
              className={`px-9 py-3 font-medium transition-colors relative ${activeTab === 'providers'
                ? 'text-teal-600 bg-teal-50'
                : 'text-gray-600 hover:bg-slate-200'
                }`}
            >
              Providers
              {activeTab === 'providers' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500 to-blue-500"></div>
              )}
            </button>
            <button
              onClick={() => setActiveTab('processed')}
              className={`px-9 py-3 font-medium transition-colors relative ${activeTab === 'processed'
                ? 'text-teal-600 bg-teal-50'
                : 'text-gray-600 hover:bg-slate-200'
                }`}
            >
              Processed Request
              {activeTab === 'processed' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500 to-blue-500"></div>
              )}
            </button>
          </div>

          <div>
            {activeTab === 'processed' ? (
              // Separate refresh button for processed tab
              <button
                onClick={fetchTrackingData}
                disabled={isLoadingTracking}
                style={{ background: "linear-gradient(to right, #3498db, #2c3e50)" }}
                className="text-white px-5 py-2.5 rounded-lg shadow-sm flex items-center gap-2 hover:shadow-md transition-all disabled:opacity-70 disabled:cursor-not-allowed"
              >
                {isLoadingTracking ? (
                  <>
                    <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Refreshing...
                  </>
                ) : (
                  <>
                    <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Refresh
                  </>
                )}
              </button>
            ) : (
              // Common refresh button for other tabs
              <button
                onClick={fetchData}
                disabled={isLoading}
                style={{ background: "linear-gradient(to right, #3498db, #2c3e50)" }}
                className="text-white px-5 py-2.5 rounded-lg shadow-md flex items-center gap-2 hover:shadow-gray-500 transition-all disabled:opacity-70 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Refreshing...
                  </>
                ) : (
                  <>
                    <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Refresh
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Tab content */}
      <div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8">
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            {/* Welcome Section */}
            <div className="bg-gradient-to-r from-teal-50 to-blue-50 rounded-xl p-6 border border-teal-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-800">
                    Welcome, {currentUser?.full_name}
                  </h2>
                  <p className="text-gray-600 mt-1">
                    {isSubAdminOnly 
                      ? `Managing ${getAccessibleAgencies().join(', ')} operations`
                      : 'Managing all system operations'
                    }
                  </p>
                </div>
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-teal-600">
                      {isSubAdminOnly 
                        ? getAccessibleAgencies().join(', ') 
                        : 'ALL'
                      }
                    </div>
                    <div className="text-sm text-gray-500">
                      {isSubAdminOnly 
                        ? (getAccessibleAgencies().length > 1 ? 'Your Agencies' : 'Your Agency')
                        : 'All Agencies'
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Users Under Management */}
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Users You Manage</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {users.filter(u => canManageUser(u)).length}
                    </p>
                  </div>
                  <div className="bg-blue-100 rounded-lg p-3">
                    <svg className="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a1.5 1.5 0 01-3 0V5.196a1.5 1.5 0 013 0v3.093z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <div className="text-sm text-gray-500">
                    of {users.length} total users
                  </div>
                </div>
              </div>

              {/* Pending Approvals */}
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
                    <p className="text-3xl font-bold text-amber-600">
                      {pendingUsers.length}
                    </p>
                  </div>
                  <div className="bg-amber-100 rounded-lg p-3">
                    <svg className="h-6 w-6 text-amber-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  {pendingUsers.length > 0 && (
                    <button
                      onClick={() => setActiveTab('pending')}
                      className="text-sm text-amber-600 hover:text-amber-700 font-medium"
                    >
                      Review Now →
                    </button>
                  )}
                </div>
              </div>

              {/* Providers Managed */}
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Providers Available</p>
                    <p className="text-3xl font-bold text-green-600">
                      {providers.filter(p => canAccessAgency(p.agency)).length}
                    </p>
                  </div>
                  <div className="bg-green-100 rounded-lg p-3">
                    <svg className="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => setActiveTab('providers')}
                    className="text-sm text-green-600 hover:text-green-700 font-medium"
                  >
                    Manage Providers →
                  </button>
                </div>
              </div>

              {/* Your Permissions */}
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Your Role</p>
                    <p className="text-2xl font-bold text-purple-600 capitalize">
                      {currentUser?.role?.replace('_', ' ')}
                    </p>
                  </div>
                  <div className="bg-purple-100 rounded-lg p-3">
                    <svg className="h-6 w-6 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <div className="text-sm text-gray-500">
                    Provider Access: {currentUser?.provider_access?.length || 0}
                  </div>
                </div>
              </div>
            </div>

            {/* Permissions Overview */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* What You Can Do */}
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                  What You Can Do
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center text-green-700">
                    <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm">
                      {isSubAdminOnly 
                        ? `Manage users within ${getAccessibleAgencies().join(', ')} ${getAccessibleAgencies().length > 1 ? 'agencies' : 'agency'}`
                        : 'Manage all users in the system'
                      }
                    </span>
                  </div>
                  <div className="flex items-center text-green-700">
                    <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm">Approve/reject user registrations</span>
                  </div>
                  <div className="flex items-center text-green-700">
                    <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm">
                      {isSubAdminOnly 
                        ? `Create/manage providers for ${getAccessibleAgencies().join(', ')}`
                        : 'Create/manage providers for all agencies'
                      }
                    </span>
                  </div>
                  <div className="flex items-center text-green-700">
                    <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm">Suspend/unsuspend users</span>
                  </div>
                  <div className="flex items-center text-green-700">
                    <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm">View processing analytics</span>
                  </div>
                </div>
              </div>

              {/* Limitations */}
              {isSubAdminOnly && (
                <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                    Limitations
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center text-red-700">
                      <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm">Cannot manage admin users</span>
                    </div>
                    <div className="flex items-center text-red-700">
                      <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm">Cannot promote users to admin</span>
                    </div>
                    <div className="flex items-center text-red-700">
                      <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm">Cannot change your own role</span>
                    </div>
                    <div className="flex items-center text-red-700">
                      <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm">Limited to {currentUser?.agency} agency only</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <div className="w-2 h-2 rounded-full bg-teal-500 mr-2"></div>
                Quick Actions
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button
                  onClick={() => setActiveTab('pending')}
                  className="bg-amber-50 hover:bg-amber-100 border border-amber-200 rounded-lg p-4 text-left transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-amber-700">Review Pending</div>
                      <div className="text-sm text-amber-600">{pendingUsers.length} awaiting</div>
                    </div>
                    <svg className="h-5 w-5 text-amber-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
                
                <button
                  onClick={() => setActiveTab('users')}
                  className="bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg p-4 text-left transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-blue-700">Manage Users</div>
                      <div className="text-sm text-blue-600">{users.filter(u => canManageUser(u)).length} under your management</div>
                    </div>
                    <svg className="h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
                
                <button
                  onClick={() => setActiveTab('providers')}
                  className="bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg p-4 text-left transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-green-700">Manage Providers</div>
                      <div className="text-sm text-green-600">{providers.filter(p => canAccessAgency(p.agency)).length} available</div>
                    </div>
                    <svg className="h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
                
                <button
                  onClick={() => setActiveTab('processed')}
                  className="bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg p-4 text-left transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-purple-700">View Analytics</div>
                      <div className="text-sm text-purple-600">Processing data</div>
                    </div>
                    <svg className="h-5 w-5 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'pending' && (
          <div className="bg-white rounded-xl shadow-md p-6">
            <div className="mb-6 pb-3 relative">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-teal-500 to-blue-500 mr-3"></div>
                <h2 className="text-xl font-semibold text-gray-800">Pending User Approvals</h2>
              </div>
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200"></div>
              <div className="absolute bottom-0 left-0 w-full h-[3px]" style={{ background: "linear-gradient(to right, #4ecdc4 20%, #1b6989 20%)" }}></div>
            </div>

            {isLoading ? (
              <div className="bg-gray-50 rounded-lg p-6 animate-pulse">
                <div className="flex justify-center items-center p-8">
                  <svg className="animate-spin h-8 w-8 text-teal-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className="ml-3 text-gray-600 font-medium">Loading pending users...</span>
                </div>
              </div>
            ) : pendingUsers.length === 0 ? (
              <div style={{ backgroundColor: "#f1fbff" }} className="p-8 rounded-lg text-center">
                <svg className="h-12 w-12 text-blue-400 mx-auto mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-lg font-medium text-blue-700">No pending user registrations</p>
                <p className="text-sm text-blue-600 mt-1">All user registration requests have been processed</p>
              </div>
            ) : (
              <div className="space-y-6">
                {pendingUsers.map(user => (
                  <div key={user.user_id} style={{ backgroundColor: "#f1fbff" }} className="rounded-lg border-b-2 border-blue-800 overflow-hidden transition-all hover:shadow-md">
                    {/* <div className="flex flex-col md:flex-row justify-between items-start p-4 border-b border-gray-200"> */}
                    <div className=''>
                      <div className='p-3'>
                        <h3 className="text-lg font-semibold text-gray-800">{user.full_name}</h3>
                        <p className="text-gray-600">{user.email}</p>
                        <div className='flex justify-between'>
                          <div className="flex flex-wrap gap-3 mt-2 ">
                            <span className="text-gray-500 text-sm flex items-center">
                              <svg className="h-4 w-4 mr-1 text-teal-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                              {user.username}
                            </span>
                            <span className="text-gray-500 text-sm flex items-center">
                              <svg className="h-4 w-4 mr-1 text-teal-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              {new Date(user.created_at).toLocaleString()}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="p-3 bg-white">
                        <h4 className="text-lg font-medium mb-4 text-gray-800 flex items-center">
                          <div className="w-2 h-2 rounded-full bg-teal-500 mr-2"></div>
                          Assign Role and Permissions
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="mb-4">
                            <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                              <svg className="h-4 w-4 text-teal-500 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                              </svg>
                              Role
                            </label>
                            <select
                              value={userApprovalData[user.user_id]?.role || 'user'}
                              onChange={(e) => updateUserApprovalData(user.user_id, 'role', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                              <option value="user">User</option>
                              <option value="sub_admin">Sub Admin</option>
                              <option value="admin" disabled={isSubAdminOnly}>
                                {isSubAdminOnly ? 'Admin (Not Available)' : 'Admin'}
                              </option>
                            </select>
                          </div>
                          <div className="mb-4">
                            <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                              <svg className="h-4 w-4 text-teal-500 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                              </svg>
                              Agency Access
                            </label>
                            <MultiSelectDropdown
                              options={(agencies || []).map(agency => ({ value: agency, label: agency }))}
                              selected={userApprovalData[user.user_id]?.agencies || []}
                              onChange={(selectedAgencies) => {
                                updateUserApprovalData(user.user_id, 'agencies', selectedAgencies);
                              }}
                            />
                          </div>
                        </div>

                        <div className='flex flex-row items-center justify-between mt-4'>
                          <div className="mt-4 ">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Provider Access
                            </label>
                            <div className="border border-gray-300 rounded-md p-3">
                              {userApprovalData[user.user_id]?.agencies && userApprovalData[user.user_id].agencies.length > 0 ? (
                                getProvidersFromAgencies(userApprovalData[user.user_id].agencies).map(provider => {
                                  const isChecked = (userApprovalData[user.user_id]?.providerAccess || []).includes(provider.value);
                                  
                                  return (
                                    <div key={provider.value} className={`flex items-center mb-2 ${isChecked ? 'bg-blue-50 p-1 rounded' : ''}`}>
                                      <input
                                        type="checkbox"
                                        id={`provider-${user.user_id}-${provider.value}`}
                                        checked={isChecked}
                                        onChange={() => handleProviderAccessToggle(user.user_id, provider.value, true)}
                                        disabled={(provider.status === 'coming_soon' && userApprovalData[user.user_id]?.role !== 'admin') || provider.disabled === true}
                                        className={`h-4 w-4 focus:ring-blue-500 border-gray-300 rounded ${
                                          (provider.status === 'coming_soon' && userApprovalData[user.user_id]?.role !== 'admin') || provider.disabled === true ? 'opacity-50 cursor-not-allowed' : 'text-blue-600'
                                        }`}
                                      />
                                      <label htmlFor={`provider-${user.user_id}-${provider.value}`}
                                        className={`ml-2 block text-sm ${
                                          (provider.status === 'coming_soon' && userApprovalData[user.user_id]?.role !== 'admin') || provider.disabled === true ? 'text-gray-400' : 
                                          isChecked ? 'text-blue-700 font-medium' : 'text-gray-700'
                                        }`}>
                                        {provider.label}
                                        {provider.status === 'coming_soon' && <span className="text-sm text-yellow-600 ml-1">(Coming Soon)</span>}
                                        {provider.disabled === true && <span className="text-xs text-red-500 ml-1">(No Access)</span>}
                                        <span className="text-xs text-gray-500 ml-1">({provider.agency})</span>
                                        {isChecked && <span className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded ml-1">Selected</span>}
                                      </label>
                                    </div>
                                  );
                                })
                              ) : (
                                <div className="text-sm text-gray-500 py-2 text-center">
                                  Please select at least one agency to view providers
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex space-x-3 mt-3 md:mt-14 ">
                            <button
                              onClick={() => handleApproveUser(user.user_id)}
                              disabled={isLoading || (userApprovalData[user.user_id]?.providerAccess || []).length === 0}
                              style={{ background: "linear-gradient(to right, #2ecc71, #27ae60)" }}
                              className="text-white px-5 py-2 rounded-md hover:shadow-slate-600 shadow-md transition-all disabled:opacity-70 disabled:cursor-not-allowed"
                            >
                              Approve
                            </button>
                            <button
                              onClick={() => handleRejectUser(user.user_id)}
                              disabled={isLoading}
                              style={{ background: "linear-gradient(to right, #e74c3c, #c0392b)" }}
                              className="text-white px-5 py-2 rounded-md hover:shadow-slate-600 shadow-md transition-all disabled:opacity-70 disabled:cursor-not-allowed"
                            >
                              Reject
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <div className="bg-white rounded-xl shadow-md p-6 mb-20">
            <div className="mb-6 pb-3 relative">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-teal-500 to-blue-500 mr-3"></div>
                <h2 className="text-xl font-semibold text-gray-800">User Management</h2>
              </div>
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200"></div>
              <div className="absolute bottom-0 left-0 w-full h-[3px]" style={{ background: "linear-gradient(to right, #4ecdc4 20%, #1e3a8a 20%)" }}></div>
            </div>

            {isLoading ? (
              <div className="bg-gray-50 rounded-lg p-6 animate-pulse">
                <div className="flex justify-center items-center p-8">
                  <svg className="animate-spin h-8 w-8 text-teal-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className="ml-3 text-gray-600 font-medium">Loading users...</span>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {users.map(user => (
                  <div key={user.user_id} className="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 transition-all hover:shadow-lg mb-6 pb-1 relative">
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-sky-200 to-teal-300"></div>
                    <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-200">
                      <div className="flex flex-col md:flex-row justify-between items-start">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-800">{user.full_name}</h3>
                          <p className="text-gray-600">{user.email}</p>
                          <div className="flex flex-wrap gap-3 mt-2">
                            <span className="text-gray-500 text-sm flex items-center">
                              <svg className="h-4 w-4 mr-1 text-teal-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                              {user.username}
                            </span>
                            <span className="text-gray-500 text-sm flex items-center">
                              <svg className="h-4 w-4 mr-1 text-teal-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              {new Date(user.created_at).toLocaleString()}
                            </span>
                          </div>
                        </div>
                        <div className="flex space-x-3 mt-4 md:mt-0">
                          <select
                            value={user.role}
                            onChange={(e) => {
                              const newRole = e.target.value as 'admin' | 'sub_admin' | 'user';
                              const roleCheck = canChangeRole(user.user_id, user.role, newRole, currentUser);
                              if (!roleCheck.canChange) {
                                setError(roleCheck.reason || 'Role change not allowed');
                                return;
                              }
                              handleUpdateRole(user.user_id, newRole);
                            }}
                            disabled={isLoading || (isSubAdminOnly && !canManageUser(user))}
                            className={`px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 ${
                              (isSubAdminOnly && !canManageUser(user)) ? 'opacity-50 cursor-not-allowed' : ''
                            }`}
                            title={(() => {
                              if (currentUser?.user_id === user.user_id && currentUser.role === 'sub_admin') {
                                return 'Sub-admins cannot change their own role';
                              }
                              if (currentUser?.user_id === user.user_id && currentUser.role === 'admin') {
                                const adminCount = users.filter(u => u.role === 'admin').length;
                                if (adminCount <= 1) {
                                  return 'Cannot change role - you are the only admin in the system';
                                }
                              }
                              return undefined;
                            })()}
                          >
                            <option value="user">User</option>
                            <option value="sub_admin">Sub Admin</option>
                            <option value="admin" disabled={isSubAdminOnly}>
                              {isSubAdminOnly ? 'Admin (Not Available)' : 'Admin'}
                            </option>
                          </select>
                          
                          {user.status === 'suspended' ? (
                            <button
                              onClick={() => handleUnsuspendUser(user.user_id)}
                              disabled={isLoading || (isSubAdminOnly && !canManageUser(user))}
                              title={(isSubAdminOnly && !canManageUser(user)) ? 'You can only manage users within your agencies' : 'Unsuspend user'}
                              style={{ background: "linear-gradient(to right, #3498db, #2c3e50)" }}
                              className={`text-white px-4 py-2 rounded-md shadow-sm hover:shadow-md transition-all ${
                                (isSubAdminOnly && !canManageUser(user)) ? 'opacity-50 cursor-not-allowed' : 'disabled:opacity-70 disabled:cursor-not-allowed'
                              }`}
                            >
                              Unsuspend
                            </button>
                          ) : (
                            <button
                              onClick={() => handleSuspendUser(user.user_id)}
                              disabled={isLoading || user.role === 'admin' || (isSubAdminOnly && !canManageUser(user))}
                              title={
                                user.role === 'admin' ? 'Admin accounts cannot be suspended' : 
                                (isSubAdminOnly && !canManageUser(user)) ? 'You can only manage users within your agencies' : 
                                'Suspend user'
                              }
                              style={{ background: "linear-gradient(to right, #f39c12, #d35400)" }}
                              className={`text-white px-4 py-2 rounded-md shadow-sm hover:shadow-md transition-all ${
                                user.role === 'admin' || (isSubAdminOnly && !canManageUser(user)) ? 'opacity-50 cursor-not-allowed' : 'disabled:opacity-70 disabled:cursor-not-allowed'
                              }`}
                            >
                              Suspend
                            </button>
                          )}
                          
                          <button
                            onClick={() => handleDeleteUser(user.user_id)}
                            disabled={isLoading || user.role === 'admin' || (isSubAdminOnly && !canManageUser(user))}
                            title={
                              user.role === 'admin' ? 'Admin accounts cannot be deleted' : 
                              (isSubAdminOnly && !canManageUser(user)) ? 'You can only manage users within your agencies' : 
                              'Delete user'
                            }
                            style={{ background: "linear-gradient(to right, #e74c3c, #c0392b)" }}
                            className={`text-white px-4 py-2 rounded-md shadow-sm hover:shadow-md transition-all ${
                              user.role === 'admin' || (isSubAdminOnly && !canManageUser(user)) ? 'opacity-50 cursor-not-allowed' : 'disabled:opacity-70 disabled:cursor-not-allowed'
                            }`}
                          >
                            Delete
                          </button>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-1">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-teal-500 mr-2"></div>
                          <span className="font-medium text-gray-700">Accessed Agency(s):</span>
                          {user.provider_access && user.provider_access.length > 0 ? (
                            <span className="ml-2 px-3 py-1 bg-gray-100 rounded-md text-gray-700">
                              {getProviderAgencies(user.provider_access).join(', ')}
                            </span>
                          ) : (
                            <span className="ml-2 text-gray-500 text-sm">No providers assigned</span>
                          )}
                        </div>
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-teal-500 mr-2"></div>
                          <span className="font-medium text-gray-700">Agency Access:</span>
                          <div className="ml-2 w-full max-w-xs">
                            <MultiSelectDropdown
                              options={(agencies || []).map(agency => ({ value: agency, label: agency }))}
                              selected={userAgenciesEdit[user.user_id] || []}
                              onChange={(selectedAgencies) => {
                                setUserAgenciesEdit({
                                  ...userAgenciesEdit,
                                  [user.user_id]: selectedAgencies as any[]
                                });
                                
                                // Update providers based on selected agencies
                                const availableProviders = getProvidersFromAgencies(selectedAgencies);
                                const availableProviderValues = availableProviders.map(p => p.value);
                                
                                // Filter current provider access to only those from selected agencies
                                const currentAccess = userProviderAccess[user.user_id] || [];
                                const filteredAccess = currentAccess.filter(p => availableProviderValues.includes(p));
                                
                                setUserProviderAccess(prev => ({
                                  ...prev,
                                  [user.user_id]: filteredAccess
                                }));
                              }}
                            />
                          </div>
                        </div>
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-teal-500 mr-2"></div>
                          <span className="font-medium text-gray-700">Status:</span>
                          <span className={`ml-2 px-3 py-1 text-sm font-medium rounded-full ${
                            user.status === 'approved' ? 'bg-green-100 text-green-800' :
                            user.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            user.status === 'suspended' ? 'bg-orange-100 text-orange-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {user.status || 'approved'}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-3">
                      <div className="flex justify-between items-center mb-1">
                        <h4 className="text-lg font-medium text-gray-800 flex items-center">
                          <div className="w-2 h-2 rounded-full bg-teal-500 mr-2"></div>
                          Provider Access
                        </h4>
                        <button
                          onClick={() => handleUpdateUserProviders(user.user_id)}
                          disabled={isLoading}
                          style={{ background: "linear-gradient(to right, #3498db, #2c3e50)" }}
                          className="text-white px-4 py-2 rounded-md shadow-sm hover:shadow-md transition-all disabled:opacity-70 disabled:cursor-not-allowed text-sm"
                        >
                          Save Changes
                        </button>
                      </div>

                      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                          {(userAgenciesEdit[user.user_id] && userAgenciesEdit[user.user_id].length > 0) ? (
                            getProvidersFromAgencies(userAgenciesEdit[user.user_id]).map(provider => {
                              // Check if this provider is assigned to the user
                              const isAssigned = (user.provider_access || []).includes(provider.value);
                              // Use the current state from userProviderAccess for the checkbox
                              const isChecked = (userProviderAccess[user.user_id] || []).includes(provider.value);
                              
                              return (
                                <div key={provider.value} className={`flex items-center p-2 hover:bg-white rounded-md transition-colors ${isAssigned ? 'bg-blue-50' : ''}`}>
                                  <input
                                    type="checkbox"
                                    id={`provider-existing-${user.user_id}-${provider.value}`}
                                    checked={isChecked}
                                    onChange={() => handleProviderAccessToggle(user.user_id, provider.value)}
                                    disabled={(provider.status === 'coming_soon' && user.role !== 'admin') || (provider.status === 'maintenance' && user.role !== 'admin') || isLoading}
                                    className={`h-4 w-4 focus:ring-teal-500 border-gray-300 rounded ${
                                      (provider.status === 'coming_soon' && user.role !== 'admin') || (provider.status === 'maintenance' && user.role !== 'admin') ? 'opacity-50 cursor-not-allowed' : 'text-teal-600'
                                    }`}
                                  />
                                  <label htmlFor={`provider-existing-${user.user_id}-${provider.value}`}
                                    className={`ml-2 text-sm ${
                                      (provider.status === 'coming_soon' && user.role !== 'admin') || (provider.status === 'maintenance' && user.role !== 'admin') ? 'text-gray-400' : 
                                      isAssigned ? 'text-blue-700 font-medium' : 'text-gray-700'
                                    }`}>
                                    {provider.label}
                                    {provider.status === 'coming_soon' && <span className="text-xs text-amber-600 ml-1 font-medium">(Coming Soon)</span>}
                                    {provider.status === 'maintenance' && <span className="text-xs text-red-600 ml-1 font-medium">(Maintenance)</span>}
                                    <span className="text-xs text-gray-500 ml-1">({provider.agency})</span>
                                  </label>
                                </div>
                              );
                            })
                          ) : (
                            <div className="text-sm text-gray-500 py-4 text-center">
                              Please select at least one agency to view providers
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {users.length === 0 && (
                  <div className="bg-white p-8 rounded-lg shadow-sm text-center">
                    <svg className="h-12 w-12 text-gray-300 mx-auto mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <p className="text-lg font-medium text-gray-700">No users found</p>
                    <p className="text-sm text-gray-500 mt-1">There are no users in the system</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Providers Tab */}
        {activeTab === 'providers' && (
          <div className="bg-white rounded-xl shadow-md p-6 mb-20">
            <div className="mb-6 pb-3 relative">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-teal-500 to-blue-500 mr-3"></div>
                <h2 className="text-xl font-semibold text-gray-800">Providers</h2>
              </div>
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200"></div>
              <div className="absolute bottom-0 left-0 w-full h-[3px]" style={{ background: "linear-gradient(to right, #4ecdc4 20%, #1e3a8a 20%)" }}></div>
            </div>

            {/* Create Provider Form */}
            <div className="bg-gray-50 p-6 rounded-lg mb-8 border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-2 h-2 rounded-full bg-teal-500 mr-2"></div>
                <h3 className="text-lg font-medium text-gray-800">Create New Provider</h3>
              </div>
              <form onSubmit={handleCreateProvider}>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                      <svg className="h-4 w-4 text-teal-500 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Provider Name
                    </label>
                    <input
                      type="text"
                      value={newProvider.name}
                      onChange={(e) => setNewProvider({ ...newProvider, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      required
                      disabled={isLoading}
                      placeholder="Enter provider name"
                    />
                  </div>

                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                      <svg className="h-4 w-4 text-teal-500 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                      Agency
                    </label>
                    {!showCustomAgency ? (
                      <div className="space-y-2">
                        <select
                          value={newProvider.agency}
                          onChange={(e) => setNewProvider({ ...newProvider, agency: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                          required
                          disabled={isLoading}
                        >
                          {(agencies || []).map(agency => (
                            <option key={agency} value={agency}>{agency}</option>
                          ))}
                        </select>
                        <button
                          type="button"
                          onClick={() => setShowCustomAgency(true)}
                          className="text-teal-600 hover:text-teal-800 text-sm font-medium"
                        >
                          + Add New Agency
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={customAgency}
                          onChange={(e) => setCustomAgency(e.target.value)}
                          placeholder="Enter new agency name"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                          required
                          disabled={isLoading}
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setShowCustomAgency(false);
                            setCustomAgency('');
                          }}
                          className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                        >
                          Use Existing Agency
                        </button>
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                      <svg className="h-4 w-4 text-teal-500 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                      </svg>
                      Description
                    </label>
                    <input
                      type="text"
                      value={newProvider.description}
                      onChange={(e) => setNewProvider({ ...newProvider, description: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      required
                      disabled={isLoading}
                      placeholder="Enter provider description"
                    />
                  </div>

                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                      <svg className="h-4 w-4 text-teal-500 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Status
                    </label>
                    <select
                      value={newProvider.status}
                      onChange={(e) => setNewProvider({ ...newProvider, status: e.target.value as 'active' | 'coming_soon' | 'maintenance' })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      required
                      disabled={isLoading}
                    >
                      <option value="active">Active</option>
                      <option value="coming_soon">Coming Soon</option>
                      <option value="maintenance">Maintenance</option>
                    </select>
                  </div>
                </div>

                <div className="mt-6 flex justify-center">
                  <button
                    type="submit"
                    disabled={isLoading}
                    style={{ background: "linear-gradient(to right, #3498db, #2c3e50)" }}
                    className="text-white px-6 py-2 rounded-md shadow-sm hover:shadow-md transition-all disabled:opacity-70 disabled:cursor-not-allowed flex items-center"
                  >
                    <svg className="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    {isLoading ? 'Creating...' : 'Create Provider'}
                  </button>
                </div>
              </form>
            </div>

            {/* Edit Provider Form */}
            {editingProvider && (
              <div className="bg-white shadow-lg rounded-lg overflow-hidden border border-gray-300 mb-6">
                <div className="flex items-center justify-between p-4 bg-yellow-50 border-b border-gray-200">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                    <h3 className="text-lg font-medium text-gray-800">Edit Provider: {editingProvider?.name}</h3>
                  </div>
                  <button
                    onClick={handleCancelEdit}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <form onSubmit={handleUpdateProvider} className="p-6 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label className="flex items-center mb-2">
                        <div className="w-2 h-2 rounded-full bg-teal-500 mr-2"></div>
                        <span className="text-sm font-medium text-gray-700">Provider Name</span>
                      </label>
                      <input
                        type="text"
                        value={editProvider.name}
                        onChange={(e) => setEditProvider({ ...editProvider, name: e.target.value })}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="flex items-center mb-2">
                        <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                        <span className="text-sm font-medium text-gray-700">Agency</span>
                      </label>
                      {showEditCustomAgency ? (
                        <div className="space-y-2">
                          <input
                            type="text"
                            value={editCustomAgency}
                            onChange={(e) => setEditCustomAgency(e.target.value)}
                            placeholder="Enter new agency name"
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => {
                              setShowEditCustomAgency(false);
                              setEditCustomAgency('');
                            }}
                            className="text-sm text-blue-600 hover:text-blue-800"
                          >
                            Use Existing Agency
                          </button>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <select
                            value={editProvider.agency}
                            onChange={(e) => setEditProvider({ ...editProvider, agency: e.target.value })}
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                            required
                          >
                            {(agencies || []).map(agency => (
                              <option key={agency} value={agency}>{agency}</option>
                            ))}
                          </select>
                          <button
                            type="button"
                            onClick={() => setShowEditCustomAgency(true)}
                            className="text-sm text-teal-600 hover:text-teal-800"
                          >
                            + Add New Agency
                          </button>
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="flex items-center mb-2">
                        <div className="w-2 h-2 rounded-full bg-purple-500 mr-2"></div>
                        <span className="text-sm font-medium text-gray-700">Description</span>
                      </label>
                      <input
                        type="text"
                        value={editProvider.description}
                        onChange={(e) => setEditProvider({ ...editProvider, description: e.target.value })}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="flex items-center mb-2">
                        <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                        <span className="text-sm font-medium text-gray-700">Status</span>
                      </label>
                      <select
                        value={editProvider.status}
                        onChange={(e) => setEditProvider({ ...editProvider, status: e.target.value as 'active' | 'coming_soon' | 'maintenance' })}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                        required
                      >
                        <option value="active">Active</option>
                        <option value="coming_soon">Coming Soon</option>
                        <option value="maintenance">Maintenance</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <button
                      type="button"
                      onClick={handleCancelEdit}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-teal-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="flex items-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 disabled:opacity-50"
                    >
                      <svg className="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      Update Provider
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* List of Providers */}
            <div className="bg-white shadow-lg rounded-lg overflow-hidden border border-gray-300">
              <div className="flex items-center p-4 bg-gray-50 border-b border-gray-200">
                <div className="w-2 h-2 rounded-full bg-teal-500 mr-2"></div>
                <h3 className="text-lg font-medium text-gray-800">Provider List</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agency</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {providers.map((provider) => (
                      <tr key={provider.provider_id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-800">{provider.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-medium rounded ${provider.agency === 'STUDIO' ? 'bg-blue-100 text-blue-800' : 
                            provider.agency === 'CNM' ? 'bg-purple-100 text-purple-800' : 
                            provider.agency === 'HP' ? 'bg-orange-100 text-orange-800' : 
                            'bg-gray-100 text-gray-800'
                            }`}>
                            {provider.agency}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-600">{provider.description}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            value={provider.status}
                            onChange={(e) => handleUpdateProviderStatus(provider.provider_id, e.target.value as 'active' | 'coming_soon' | 'maintenance')}
                            disabled={isLoading}
                            className={`px-2 py-1 text-xs font-medium rounded border-0 focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                              provider.status === 'active' ? 'bg-green-100 text-green-800' :
                              provider.status === 'coming_soon' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}
                          >
                            <option value="active">Active</option>
                            <option value="coming_soon">Coming Soon</option>
                            <option value="maintenance">Maintenance</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => handleEditProvider(provider)}
                              disabled={isLoading}
                              className="text-blue-600 hover:text-blue-900 disabled:opacity-50 transition-colors px-2 py-1"
                            >
                              <span className="flex items-center justify-center">
                                <svg className="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                Edit
                              </span>
                            </button>
                            <button
                              onClick={() => handleDeleteProvider(provider.provider_id)}
                              disabled={isLoading}
                              className="text-red-600 hover:text-red-900 disabled:opacity-50 transition-colors px-2 py-1"
                            >
                              <span className="flex items-center justify-center">
                                <svg className="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                Delete
                              </span>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                    {providers.length === 0 && (
                      <tr>
                        <td colSpan={5} className="px-6 py-8 text-center">
                          <div className="flex flex-col items-center justify-center text-gray-500">
                            <svg className="h-12 w-12 text-gray-300 mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                            </svg>
                            <p className="text-lg font-medium">No providers found</p>
                            <p className="text-sm mt-1">Create a new provider using the form above</p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Processed Tab */}
        {activeTab === 'processed' && (
          <div className="bg-white rounded-xl shadow-md p-6">
            <div className="mb-4 pb-3 relative">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-gradient-to-r from-teal-500 to-blue-500 mr-3"></div>
                  <h2 className="text-xl font-semibold text-gray-800">Processed Request</h2>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setProcessedViewMode('table')}
                    className={`px-3 py-1.5 rounded-l-md border border-teal-500
                      ${processedViewMode === 'table' ? 'bg-teal-500 text-white' : 'bg-white text-teal-600'}`}
                  >
                    <span className="flex items-center">
                      <svg className="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                      </svg>
                      Table View
                    </span>
                  </button>
                  <button
                    onClick={() => setProcessedViewMode('analytics')}
                    className={`px-3 py-1.5 rounded-r-md border border-teal-500
                      ${processedViewMode === 'analytics' ? 'bg-teal-500 text-white' : 'bg-white text-teal-600'}`}
                  >
                    <span className="flex items-center">
                      <svg className="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      Analytics
                    </span>
                  </button>
                  <button
                    onClick={() => setShowDownloadOptions(true)}
                    className="px-3 py-1.5 rounded-md border border-green-500 bg-green-500 text-white hover:bg-green-600"
                  >
                    <span className="flex items-center">
                      <svg className="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      Download CSV
                    </span>
                  </button>
                </div>
              </div>
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200"></div>
              <div className="absolute bottom-0 left-0 w-full h-[3px]" style={{ background: "linear-gradient(to right, #4ecdc4 20%, #1e3a8a 20%)" }}></div>
            </div>

            {trackingError && (
              <div className="mb-4 p-4 bg-red-50 border-l-4 border-red-500 text-red-700">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm">{trackingError}</p>
                  </div>
                  <div className="ml-auto pl-3">
                    <div className="-mx-1.5 -my-1.5">
                      <button
                        onClick={() => setTrackingError(null)}
                        className="inline-flex rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        <span className="sr-only">Dismiss</span>
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-4">
              <div className="flex items-center justify-between mb-5">
                <div className="flex items-center gap-6">
                  <div>
                    <p className="text-teal-600 text-sm bg-teal-50 mb-1 px-2 py-1 rounded">Select provider</p>
                    <select
                      className="border border-teal-500 rounded px-4 py-1 text-gray-700 focus:outline-none focus:ring-2 focus:ring-teal-500 bg-slate-50"
                      value={selectedProvider}
                      onChange={(e) => {
                        setSelectedProvider(e.target.value);
                      }}
                    >
                      <option value="all">All Providers</option>
                      <option value="studio">Studio</option>
                      <option value="cruising_power">Cruising Power</option>
                      <option value="ncl">NCL</option>
                    </select>
                  </div>

                  <div>
                    <p className="text-teal-600 text-sm bg-teal-50 mb-1 px-2 py-1 rounded">Select user</p>
                    <select
                      className="border border-teal-500 rounded px-4 py-1 text-gray-700 focus:outline-none focus:ring-2 focus:ring-teal-500 bg-slate-50"
                      value={selectedUser}
                      onChange={(e) => {
                        setSelectedUser(e.target.value);
                        if (e.target.value !== 'all') {
                          setSearchUsername('');
                        }
                      }}
                    >
                      <option value="all">All Users</option>
                      {availableUsers.map(username => (
                        <option key={username} value={username}>{username}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <p className="text-teal-600 text-sm bg-teal-50 mb-1 px-2 py-1 rounded">Search by Username</p>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Search by username..."
                      value={searchUsername}
                      onChange={(e) => {
                        setSearchUsername(e.target.value);
                        if (e.target.value && selectedUser !== 'all') {
                          setSelectedUser('all');
                        }
                      }}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          fetchTrackingData();
                        }
                      }}
                      className="border border-slate-500 rounded px-3 py-1 w-[290px] text-gray-700 focus:outline-none focus:ring-2 focus:ring-teal-500"
                      disabled={selectedUser !== 'all'}
                    />
                    <button
                      onClick={fetchTrackingData}
                      disabled={isLoadingTracking}
                      className={`px-4 py-1 text-white rounded transition-colors ${isLoadingTracking
                        ? 'bg-teal-400 cursor-not-allowed'
                        : 'bg-teal-600 hover:bg-teal-700'
                        }`}
                    >
                      {isLoadingTracking ? (
                        <div className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Searching...
                        </div>
                      ) : 'Search'}
                    </button>
                  </div>
                </div>
              </div>

              {processedViewMode === 'table' ? (
                /* Table View */
                <div className="overflow-x-auto">
                  <table className="w-full bg-gray-50 rounded-md shadow-sm border border-gray-400" style={{tableLayout: 'fixed'}}>
                    <thead>
                      <tr className="bg-cyan-700 text-white text-left">
                        <th className="py-2 px-4 border-r border-teal-500 w-16">S.No</th>
                        <th className="py-2 px-4 border-r border-teal-500 w-1/12">Username</th>
                        <th className="py-2 px-4 border-r border-teal-500 break-words" style={{width: '130px'}}>Provider</th>
                        <th className="py-2 px-4 border-r border-teal-500 break-words" style={{width: '120px'}}>Request ID</th>
                        <th className="py-2 px-4 border-r border-teal-500 w-24">Date</th>
                        <th className="py-2 px-4 border-r border-teal-500 w-20">Time</th>
                        <th className="py-2 px-4 border-r border-teal-500 w-24">Execution Time</th>
                        <th className="py-2 px-4 border-r border-teal-500 w-28">Validation Status</th>
                        <th className="py-2 px-4 border-r border-teal-500 w-24">As per Flow</th>
                        <th className="py-2 px-4 border-r border-teal-500 w-24">As per Feature</th>
                        <th className="py-2 px-4 border-r border-teal-500 w-40">Issues</th>
                        <th className="py-2 px-4 border-r border-teal-500 w-32">Comments</th>
                        <th className="py-2 px-4 w-24">View Result</th>
                      </tr>
                    </thead>
                    <tbody>
                      {isLoadingTracking ? (
                        <tr>
                          <td colSpan={13} className="text-center py-4">
                            <div className="flex justify-center items-center">
                              <svg className="animate-spin h-5 w-5 mr-3 text-teal-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Loading tracking data...
                            </div>
                          </td>
                        </tr>
                      ) : trackingData.length === 0 ? (
                        <tr>
                          <td colSpan={13} className="text-center py-4">
                            No tracking data found
                          </td>
                        </tr>
                      ) : (
                        paginatedTrackingData.map((item, index) => {
                          const { requestId, date, time } = parseSessionId(item.session_id);
                          return (
                            <tr key={item.session_id} className="border-b border-gray-200 hover:bg-teal-50">
                              <td className="py-2 px-4 border-r border-gray-100">{(currentPage - 1) * rowsPerPage + index + 1}</td>
                              <td className="py-2 px-4 border-r border-gray-100">{item.username}</td>
                              <td className="py-2 px-4 border-r border-gray-100 break-words" style={{width: '130px'}}>{item.provider}</td>
                                <td className="py-2 px-4 border-r border-gray-100 break-words" style={{width: '120px'}}>
                                  {requestId && requestId.length > 15 ? (
                                    <div>
                                      <span>
                                        {expandedRequestIds[item.session_id] 
                                          ? requestId 
                                          : truncateComment(requestId, 15)
                                        }
                                      </span>
                                      <button
                                        onClick={() => toggleRequestIdExpansion(item.session_id)}
                                        className="ml-1 text-blue-600 hover:text-blue-800 text-xs font-medium focus:outline-none"
                                      >
                                        {expandedRequestIds[item.session_id] ? '−' : '+'}
                                      </button>
                                    </div>
                                  ) : (
                                    requestId || '-'
                                  )}
                                </td>
                              <td className="py-2 px-4 border-r border-gray-100">{date}</td>
                              <td className="py-2 px-4 border-r border-gray-100">{time}</td>
                            <td className="py-2 px-4 border-r border-gray-100">
                              {item.execution_time !== undefined && item.execution_time !== null && item.execution_time > 0 ? `${Math.round(item.execution_time)}s` : '-'}
                            </td>
                            <td className="py-2 px-4 border-r border-gray-100">
                              {item.val_status !== undefined ? (
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  item.val_status === 'validated' ? 'bg-green-200 text-green-900' :
                                  item.val_status === 'reset' ? 'bg-orange-200 text-orange-900' :
                                  'bg-yellow-100 text-yellow-700'
                                  }`}>
                                  {item.val_status === 'validated' ? 'Validated' :
                                   item.val_status === 'reset' ? 'Reset' :
                                   'Reprocessed'}
                                </span>
                              ) : '-'}
                            </td>
                            <td className="py-2 px-4 border-r border-gray-100">
                              {item.val_mark !== undefined ? (
                                item.val_mark === '1' || item.val_mark === '0' ? (
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${item.val_mark === '1' ? 'bg-green-200 text-green-900' : 'bg-red-200 text-red-900'
                                    }`}>
                                    {item.val_mark === '1' ? 'Correct' : 'Incorrect'}
                                  </span>
                                ) : item.val_mark
                              ) : '-'}
                            </td>
                            <td className="py-2 px-4 border-r border-gray-100">
                              {(() => {
                                const featureVal = item.as_per_feature !== undefined && item.as_per_feature !== null && item.as_per_feature !== ''
                                  ? item.as_per_feature
                                  : (item.val_mark === '1' ? '1' : item.as_per_feature);
                                if (featureVal === undefined) return '-';
                                if (featureVal === '1' || featureVal === '0') {
                                  return (
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${featureVal === '1' ? 'bg-green-200 text-green-900' : 'bg-red-200 text-red-900'}`}>
                                      {featureVal === '1' ? 'Correct' : 'Incorrect'}
                                    </span>
                                  );
                                }
                                return featureVal || '-';
                              })()}
                            </td>
                            <td className="py-2 px-4 border-r border-gray-100">
                              {(() => {
                                const issues = parseIssueCodes(item.issue_codes);
                                if (issues.length === 0) return '-';
                                const labels = issues.map(ic => getIssueLabel(ic.code));
                                const isExpanded = expandedIssues[item.session_id];
                                const display = isExpanded ? labels : labels.slice(0, 3);
                                return (
                                  <span>
                                    {display.map(lbl => (
                                    <span key={lbl} className="inline-block max-w-full whitespace-normal break-words px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-800 text-xs font-medium mr-1 mb-1">
                                      {lbl}
                                    </span>
                                  ))}
                                    {labels.length > 3 && (
                                      <button onClick={() => toggleIssueExpansion(item.session_id)} className="ml-1 text-blue-600 hover:text-blue-800 text-xs font-medium focus:outline-none">
                                        {isExpanded ? 'Show Less' : `+${labels.length - 3}`}
                                      </button>
                                    )}
                                  </span>
                                );
                              })()}
                            </td>
                            <td className="py-2 px-4 border-r border-gray-100 w-80 break-words">
                              {item.val_comment && item.val_comment.length > 50 ? (
                                <div>
                                  <span>
                                    {expandedComments[item.session_id] 
                                      ? item.val_comment 
                                      : truncateComment(item.val_comment)
                                    }
                                  </span>
                                  <button
                                    onClick={() => toggleCommentExpansion(item.session_id)}
                                    className="ml-2 text-blue-600 hover:text-blue-800 text-sm font-medium focus:outline-none"
                                  >
                                    {expandedComments[item.session_id] ? 'Show Less' : 'Show More'}
                                  </button>
                                </div>
                              ) : (
                                item.val_comment || '-'
                              )}
                            </td>
                            <td className="py-2 px-4 text-center">
                              <button
                                onClick={() => handleViewResult(item.provider, requestId, item.session_id)}
                                className="text-blue-600 hover:text-blue-800"
                              >
                                ℹ️
                              </button>
                            </td>
                          </tr>
                          );
                        })
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                /* Analytics View */
                <ProcessedRequestAnalytics data={trackingData} isLoading={isLoadingTracking} />
              )}
            </div>

            {/* Pagination controls */}
            {processedViewMode === 'table' && paginatedTrackingData.length > 0 && (
              <div className="flex items-center justify-center space-x-4 py-4">
                <button
                  onClick={gotoPrev}
                  disabled={!canPrev}
                  className={`px-3 py-1 rounded-md text-sm font-medium ${canPrev ? 'bg-teal-600 text-white hover:bg-teal-700' : 'bg-gray-300 text-gray-600 cursor-not-allowed'}`}
                >
                  Previous
                </button>
                <span className="text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={gotoNext}
                  disabled={!canNext}
                  className={`px-3 py-1 rounded-md text-sm font-medium ${canNext ? 'bg-teal-600 text-white hover:bg-teal-700' : 'bg-gray-300 text-gray-600 cursor-not-allowed'}`}
                >
                  Next
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {showDownloadOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-sm w-full">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Select Download Period</h3>
            <div className="space-y-3">
              <button
                onClick={() => {
                  setNumDays(1);
                  downloadCSV();
                  setShowDownloadOptions(false);
                }}
                className="w-full px-4 py-2 bg-teal-500 text-white rounded-md hover:bg-teal-600"
              >
                Last 1 Day
              </button>
              <button
                onClick={() => {
                  setNumDays(7);
                  downloadCSV();
                  setShowDownloadOptions(false);
                }}
                className="w-full px-4 py-2 bg-teal-500 text-white rounded-md hover:bg-teal-600"
              >
                Last 7 Days
              </button>
              <button
                onClick={() => {
                  setNumDays(0);
                  downloadCSV();
                  setShowDownloadOptions(false);
                }}
                className="w-full px-4 py-2 bg-teal-500 text-white rounded-md hover:bg-teal-600"
              >
                All Data
              </button>
              <div className="flex items-center">
                <input
                  type="number"
                  min="1"
                  value={customDays}
                  onChange={(e) => setCustomDays(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter number of days"
                />
                <button
                  onClick={() => {
                    const days = parseInt(customDays, 10);
                    if (!isNaN(days) && days > 0) {
                      setNumDays(days);
                      downloadCSV();
                      setShowDownloadOptions(false);
                    }
                  }}
                  className="px-4 py-2 bg-green-500 text-white rounded-r-md hover:bg-green-600"
                >
                  Download
                </button>
              </div>
            </div>
            <button
              onClick={() => setShowDownloadOptions(false)}
              className="mt-4 w-full px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
      {selectedResult && (
        <ResultsViewModal
          open={resultModalOpen}
          onClose={handleCloseResultModal}
          provider={selectedResult.provider}
          requestId={selectedResult.requestId}
          sessionId={selectedResult.sessionId}
        />
      )}
    </div>
  );
}
