{"version": 3, "sources": ["../../../../../src/server/future/route-modules/app-route/module.ts"], "names": ["RouteModule", "RequestAsyncStorageWrapper", "StaticGenerationAsyncStorageWrapper", "handleBadRequestResponse", "handleInternalServerErrorResponse", "HTTP_METHODS", "isHTTPMethod", "addImplicitTags", "patchFetch", "getTracer", "AppRouteRouteHandlersSpan", "getPathnameFromAbsolutePath", "proxyRequest", "resolveHandlerError", "Log", "autoImplementMethods", "getNonStaticMethods", "appendMutableCookies", "parsedUrlQueryToParams", "serverHooks", "header<PERSON><PERSON>s", "staticGenerationBailout", "requestAsyncStorage", "staticGenerationAsyncStorage", "actionAsyncStorage", "sharedModules", "getIsServerAction", "AppRouteRouteModule", "constructor", "userland", "definition", "resolvedPagePath", "nextConfigOutput", "methods", "nonStaticMethods", "dynamic", "Error", "pathname", "process", "env", "NODE_ENV", "lowercased", "map", "method", "toLowerCase", "error", "toUpperCase", "some", "resolve", "execute", "request", "context", "handler", "requestContext", "req", "renderOpts", "previewProps", "prerenderManifest", "preview", "staticGenerationContext", "urlPathname", "nextUrl", "fetchCache", "response", "run", "isAppRoute", "isAction", "wrap", "staticGenerationStore", "join", "forceDynamic", "forceStatic", "dynamicShouldError", "revalidate", "wrappedRequest", "route", "getRootSpanAttributes", "set", "trace", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "attributes", "res", "params", "undefined", "Response", "fetchMetrics", "waitUntil", "Promise", "all", "Object", "values", "pendingRevalidates", "fetchTags", "tags", "requestStore", "getStore", "mutableCookies", "headers", "Headers", "body", "status", "statusText", "has", "get", "handle", "err"], "mappings": "AAMA,SACEA,WAAW,QAGN,kBAAiB;AACxB,SACEC,0BAA0B,QAErB,uDAAsD;AAC7D,SACEC,mCAAmC,QAE9B,iEAAgE;AACvE,SACEC,wBAAwB,EACxBC,iCAAiC,QAC5B,+BAA8B;AACrC,SAA2BC,YAAY,EAAEC,YAAY,QAAQ,oBAAmB;AAChF,SAASC,eAAe,EAAEC,UAAU,QAAQ,2BAA0B;AACtE,SAASC,SAAS,QAAQ,4BAA2B;AACrD,SAASC,yBAAyB,QAAQ,+BAA8B;AACxE,SAASC,2BAA2B,QAAQ,4CAA2C;AACvF,SAASC,YAAY,QAAQ,0BAAyB;AACtD,SAASC,mBAAmB,QAAQ,kCAAiC;AACrE,YAAYC,SAAS,+BAA8B;AACnD,SAASC,oBAAoB,QAAQ,mCAAkC;AACvE,SAASC,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,oBAAoB,QAAQ,uDAAsD;AAC3F,SAASC,sBAAsB,QAAQ,uCAAsC;AAE7E,YAAYC,iBAAiB,qDAAoD;AACjF,YAAYC,iBAAiB,wCAAuC;AACpE,SAASC,uBAAuB,QAAQ,0DAAyD;AAEjG,SAASC,mBAAmB,QAAQ,+DAA8D;AAClG,SAASC,4BAA4B,QAAQ,yEAAwE;AACrH,SAASC,kBAAkB,QAAQ,8DAA6D;AAChG,YAAYC,mBAAmB,mBAAkB;AACjD,SAASC,iBAAiB,QAAQ,0CAAyC;AAsE3E;;CAEC,GACD,OAAO,MAAMC,4BAA4B3B;qBAgChByB,gBAAgBA;IAevCG,YAAY,EACVC,QAAQ,EACRC,UAAU,EACVC,gBAAgB,EAChBC,gBAAgB,EACW,CAAE;QAC7B,KAAK,CAAC;YAAEH;YAAUC;QAAW;QAjD/B;;GAEC,QACeR,sBAAsBA;QAEtC;;GAEC,QACeC,+BAA+BA;QAE/C;;;GAGC,QACeJ,cAAcA;QAE9B;;;GAGC,QACeC,cAAcA;QAE9B;;;GAGC,QACeC,0BAA0BA;QAI1C;;;GAGC,QACeG,qBAAqBA;QAiBnC,IAAI,CAACO,gBAAgB,GAAGA;QACxB,IAAI,CAACC,gBAAgB,GAAGA;QAExB,yEAAyE;QACzE,mBAAmB;QACnB,IAAI,CAACC,OAAO,GAAGlB,qBAAqBc;QAEpC,6CAA6C;QAC7C,IAAI,CAACK,gBAAgB,GAAGlB,oBAAoBa;QAE5C,qDAAqD;QACrD,IAAI,CAACM,OAAO,GAAG,IAAI,CAACN,QAAQ,CAACM,OAAO;QACpC,IAAI,IAAI,CAACH,gBAAgB,KAAK,UAAU;YACtC,IAAI,CAAC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACA,OAAO,KAAK,QAAQ;gBAC5C,IAAI,CAACA,OAAO,GAAG;YACjB,OAAO,IAAI,IAAI,CAACA,OAAO,KAAK,iBAAiB;gBAC3C,MAAM,IAAIC,MACR,CAAC,gDAAgD,EAAEN,WAAWO,QAAQ,CAAC,wHAAwH,CAAC;YAEpM;QACF;QAEA,oEAAoE;QACpE,eAAe;QACf,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,6EAA6E;YAC7E,oCAAoC;YACpC,MAAMC,aAAapC,aAAaqC,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;YAClE,KAAK,MAAMD,UAAUF,WAAY;gBAC/B,IAAIE,UAAU,IAAI,CAACd,QAAQ,EAAE;oBAC3Bf,IAAI+B,KAAK,CACP,CAAC,2BAA2B,EAAEF,OAAO,MAAM,EACzC,IAAI,CAACZ,gBAAgB,CACtB,yBAAyB,EAAEY,OAAOG,WAAW,GAAG,gCAAgC,CAAC;gBAEtF;YACF;YAEA,2EAA2E;YAC3E,gCAAgC;YAChC,IAAI,aAAa,IAAI,CAACjB,QAAQ,EAAE;gBAC9Bf,IAAI+B,KAAK,CACP,CAAC,4BAA4B,EAAE,IAAI,CAACd,gBAAgB,CAAC,sDAAsD,CAAC;YAEhH;YAEA,0EAA0E;YAC1E,YAAY;YACZ,IAAI,CAAC1B,aAAa0C,IAAI,CAAC,CAACJ,SAAWA,UAAU,IAAI,CAACd,QAAQ,GAAG;gBAC3Df,IAAI+B,KAAK,CACP,CAAC,6BAA6B,EAAE,IAAI,CAACd,gBAAgB,CAAC,8CAA8C,CAAC;YAEzG;QACF;IACF;IAEA;;;;;GAKC,GACD,AAAQiB,QAAQL,MAAc,EAAqB;QACjD,yEAAyE;QACzE,IAAI,CAACrC,aAAaqC,SAAS,OAAOxC;QAElC,sBAAsB;QACtB,OAAO,IAAI,CAAC8B,OAAO,CAACU,OAAO;IAC7B;IAEA;;GAEC,GACD,MAAcM,QACZC,OAAoB,EACpBC,OAAoC,EACjB;QACnB,iDAAiD;QACjD,MAAMC,UAAU,IAAI,CAACJ,OAAO,CAACE,QAAQP,MAAM;QAE3C,mCAAmC;QACnC,MAAMU,iBAAiC;YACrCC,KAAKJ;QACP;QAGEG,eAAuBE,UAAU,GAAG;YACpCC,cAAcL,QAAQM,iBAAiB,CAACC,OAAO;QACjD;QAEA,6CAA6C;QAC7C,MAAMC,0BAAmD;YACvDC,aAAaV,QAAQW,OAAO,CAACxB,QAAQ;YACrCkB,YAAYJ,QAAQI,UAAU;QAChC;QAEA,+CAA+C;QAC/CI,wBAAwBJ,UAAU,CAACO,UAAU,GAAG,IAAI,CAACjC,QAAQ,CAACiC,UAAU;QAExE,0EAA0E;QAC1E,wEAAwE;QACxE,+CAA+C;QAC/C,MAAMC,WAAoB,MAAM,IAAI,CAACvC,kBAAkB,CAACwC,GAAG,CACzD;YACEC,YAAY;YACZC,UAAUxC,kBAAkBwB;QAC9B,GACA,IACEjD,2BAA2BkE,IAAI,CAC7B,IAAI,CAAC7C,mBAAmB,EACxB+B,gBACA,IACEnD,oCAAoCiE,IAAI,CACtC,IAAI,CAAC5C,4BAA4B,EACjCoC,yBACA,CAACS;wBAuDC3D;oBAtDA,mEAAmE;oBACnE,6BAA6B;oBAC7B,IAAI,IAAI,CAACyB,gBAAgB,EAAE;wBACzB,IAAI,CAACb,uBAAuB,CAC1B,CAAC,wBAAwB,EAAE,IAAI,CAACa,gBAAgB,CAACmC,IAAI,CACnD,MACA,CAAC;oBAEP;oBAEA,oEAAoE;oBACpE,OAAQ,IAAI,CAAClC,OAAO;wBAClB,KAAK;4BACH,6DAA6D;4BAC7D,gCAAgC;4BAChCiC,sBAAsBE,YAAY,GAAG;4BACrC,IAAI,CAACjD,uBAAuB,CAAC,CAAC,aAAa,CAAC,EAAE;gCAC5Cc,SAAS,IAAI,CAACA,OAAO;4BACvB;4BACA;wBACF,KAAK;4BACH,4DAA4D;4BAC5D,+BAA+B;4BAC/BiC,sBAAsBG,WAAW,GAAG;4BACpC;wBACF,KAAK;4BACH,8DAA8D;4BAC9D,mDAAmD;4BACnDH,sBAAsBI,kBAAkB,GAAG;4BAC3C;wBACF;4BACE;oBACJ;oBAEA,kEAAkE;oBAClE,oEAAoE;oBACpE,8BAA8B;oBAC9BJ,sBAAsBK,UAAU,KAC9B,IAAI,CAAC5C,QAAQ,CAAC4C,UAAU,IAAI;oBAE9B,mEAAmE;oBACnE,yDAAyD;oBACzD,MAAMC,iBAAiB9D,aACrBsC,SACA;wBAAEf,SAAS,IAAI,CAACA,OAAO;oBAAC,GACxB;wBACEf,aAAa,IAAI,CAACA,WAAW;wBAC7BD,aAAa,IAAI,CAACA,WAAW;wBAC7BE,yBAAyB,IAAI,CAACA,uBAAuB;oBACvD;oBAGF,mDAAmD;oBACnD,MAAMsD,QAAQhE,4BAA4B,IAAI,CAACoB,gBAAgB;qBAC/DtB,mCAAAA,YAAYmE,qBAAqB,uBAAjCnE,iCAAqCoE,GAAG,CAAC,cAAcF;oBACvD,OAAOlE,YAAYqE,KAAK,CACtBpE,0BAA0BqE,UAAU,EACpC;wBACEC,UAAU,CAAC,0BAA0B,EAAEL,MAAM,CAAC;wBAC9CM,YAAY;4BACV,cAAcN;wBAChB;oBACF,GACA;4BA4BIP;wBA3BF,0BAA0B;wBAC1B5D,WAAW;4BACTW,aAAa,IAAI,CAACA,WAAW;4BAC7BI,8BACE,IAAI,CAACA,4BAA4B;wBACrC;wBACA,MAAM2D,MAAM,MAAM9B,QAAQsB,gBAAgB;4BACxCS,QAAQhC,QAAQgC,MAAM,GAClBjE,uBAAuBiC,QAAQgC,MAAM,IACrCC;wBACN;wBACA,IAAI,CAAEF,CAAAA,eAAeG,QAAO,GAAI;4BAC9B,MAAM,IAAIjD,MACR,CAAC,4CAA4C,EAAE,IAAI,CAACL,gBAAgB,CAAC,0FAA0F,CAAC;wBAEpK;wBACEoB,QAAQI,UAAU,CAAS+B,YAAY,GACvClB,sBAAsBkB,YAAY;wBAEpCnC,QAAQI,UAAU,CAACgC,SAAS,GAAGC,QAAQC,GAAG,CACxCC,OAAOC,MAAM,CACXvB,sBAAsBwB,kBAAkB,IAAI,EAAE;wBAIlDrF,gBAAgB6D;wBACdjB,QAAQI,UAAU,CAASsC,SAAS,IACpCzB,8BAAAA,sBAAsB0B,IAAI,qBAA1B1B,4BAA4BC,IAAI,CAAC;wBAEnC,4DAA4D;wBAC5D,0DAA0D;wBAC1D,QAAQ;wBACR,MAAM0B,eAAe,IAAI,CAACzE,mBAAmB,CAAC0E,QAAQ;wBACtD,IAAID,gBAAgBA,aAAaE,cAAc,EAAE;4BAC/C,MAAMC,UAAU,IAAIC,QAAQjB,IAAIgB,OAAO;4BACvC,IACEjF,qBACEiF,SACAH,aAAaE,cAAc,GAE7B;gCACA,OAAO,IAAIZ,SAASH,IAAIkB,IAAI,EAAE;oCAC5BC,QAAQnB,IAAImB,MAAM;oCAClBC,YAAYpB,IAAIoB,UAAU;oCAC1BJ;gCACF;4BACF;wBACF;wBAEA,OAAOhB;oBACT;gBAEJ;QAKV,yEAAyE;QACzE,kBAAkB;QAClB,IAAI,CAAEnB,CAAAA,oBAAoBsB,QAAO,GAAI;YACnC,qEAAqE;YACrE,OAAOjF;QACT;QAEA,IAAI2D,SAASmC,OAAO,CAACK,GAAG,CAAC,yBAAyB;YAChD,oEAAoE;YACpE,6EAA6E;YAC7E,MAAM,IAAInE,MACR;QAGF,6EAA6E;QAC7E,iEAAiE;QAEjE,2EAA2E;QAC3E,6EAA6E;QAC7E,0EAA0E;QAC1E,mCAAmC;QACnC,sBAAsB;QACtB,8CAA8C;QAC9C,IAAI;QAEJ,yEAAyE;QACzE,gDAAgD;QAChD,oEAAoE;QACpE,kDAAkD;QAClD,qEAAqE;QACrE,yDAAyD;QAC3D;QAEA,IAAI2B,SAASmC,OAAO,CAACM,GAAG,CAAC,yBAAyB,KAAK;YACrD,iEAAiE;YACjE,MAAM,IAAIpE,MACR;QAEJ;QAEA,OAAO2B;IACT;IAEA,MAAa0C,OACXvD,OAAoB,EACpBC,OAAoC,EACjB;QACnB,IAAI;YACF,yCAAyC;YACzC,MAAMY,WAAW,MAAM,IAAI,CAACd,OAAO,CAACC,SAASC;YAE7C,uCAAuC;YACvC,OAAOY;QACT,EAAE,OAAO2C,KAAK;YACZ,+DAA+D;YAC/D,MAAM3C,WAAWlD,oBAAoB6F;YACrC,IAAI,CAAC3C,UAAU,MAAM2C;YAErB,wCAAwC;YACxC,OAAO3C;QACT;IACF;AACF;AAEA,eAAepC,oBAAmB"}