{"version": 3, "sources": ["../../../src/client/components/headers.ts"], "names": ["RequestCookiesAdapter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RequestCookies", "requestAsyncStorage", "actionAsyncStorage", "staticGenerationBailout", "DraftMode", "headers", "link", "seal", "Headers", "requestStore", "getStore", "Error", "cookies", "asyncActionStore", "isAction", "isAppRoute", "mutableCookies", "draftMode"], "mappings": "AAAA,SAEEA,qBAAqB,QAChB,2DAA0D;AACjE,SAASC,cAAc,QAAQ,mDAAkD;AACjF,SAASC,cAAc,QAAQ,0CAAyC;AACxE,SAASC,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,kBAAkB,QAAQ,kCAAiC;AACpE,SAASC,uBAAuB,QAAQ,8BAA6B;AACrE,SAASC,SAAS,QAAQ,eAAc;AAExC,OAAO,SAASC;IACd,IACEF,wBAAwB,WAAW;QACjCG,MAAM;IACR,IACA;QACA,OAAOP,eAAeQ,IAAI,CAAC,IAAIC,QAAQ,CAAC;IAC1C;IACA,MAAMC,eAAeR,oBAAoBS,QAAQ;IACjD,IAAI,CAACD,cAAc;QACjB,MAAM,IAAIE,MACP;IAEL;IAEA,OAAOF,aAAaJ,OAAO;AAC7B;AAEA,OAAO,SAASO;IACd,IACET,wBAAwB,WAAW;QACjCG,MAAM;IACR,IACA;QACA,OAAOR,sBAAsBS,IAAI,CAAC,IAAIP,eAAe,IAAIQ,QAAQ,CAAC;IACpE;IAEA,MAAMC,eAAeR,oBAAoBS,QAAQ;IACjD,IAAI,CAACD,cAAc;QACjB,MAAM,IAAIE,MACP;IAEL;IAEA,MAAME,mBAAmBX,mBAAmBQ,QAAQ;IACpD,IACEG,oBACCA,CAAAA,iBAAiBC,QAAQ,IAAID,iBAAiBE,UAAU,AAAD,GACxD;QACA,2EAA2E;QAC3E,+DAA+D;QAC/D,OAAON,aAAaO,cAAc;IACpC;IAEA,OAAOP,aAAaG,OAAO;AAC7B;AAEA,OAAO,SAASK;IACd,MAAMR,eAAeR,oBAAoBS,QAAQ;IACjD,IAAI,CAACD,cAAc;QACjB,MAAM,IAAIE,MACP;IAEL;IACA,OAAO,IAAIP,UAAUK,aAAaQ,SAAS;AAC7C"}