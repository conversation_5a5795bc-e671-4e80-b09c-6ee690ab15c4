{"name": "oceanmind-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/recharts": "^1.8.29", "chart.js": "^4.4.9", "gsap": "^3.13.0", "next": "14.0.4", "react": "^18", "react-chartjs-2": "^5.3.0", "react-dom": "^18", "react-icons": "^5.5.0", "recharts": "^2.15.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5"}}