@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #0077be;
  --secondary-color: #004488;
  --accent-color: #f26b38;
  --background-start: #f0f5ff;
  --background-end: #e6f0ff;
  --benefit-heading-color: rgba(0, 0, 0, 0.85);
  --benefit-text-color: rgb(30 58 138 / var(--tw-text-opacity, 0.8));
}

body {
  background: linear-gradient(135deg, var(--background-start) 0%, var(--background-end) 100%);
  color: #333;
  min-height: 100vh;
}

/* Custom button styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg flex items-center gap-2 transition-all duration-300 shadow-md;
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg flex items-center gap-2 transition-all duration-300 shadow-md;
}

.btn-accent {
  @apply bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-lg flex items-center gap-2 transition-all duration-300 shadow-md;
}

/* Section headers */
.section-header {
  @apply bg-gradient-to-r from-blue-600 to-blue-800 p-3 rounded-lg shadow-md mb-4 text-white text-xl font-semibold transition-all duration-300;
}

/* Card styling */
.card {
  @apply bg-white rounded-lg shadow-lg border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-xl;
}

.card-header {
  @apply bg-gradient-to-r from-blue-600 to-blue-800 p-4 text-white font-semibold;
}

.card-body {
  @apply p-5;
}

/* Form controls */
.form-input {
  @apply w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300;
}

.form-select {
  @apply w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300;
}

.form-checkbox {
  @apply h-5 w-5 text-blue-600 rounded focus:ring-blue-500 transition-all duration-300;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes slideIn {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

/* Progress bar animation */
@keyframes progress {
  0% { width: 0%; }
  100% { width: 100%; }
}

.animate-progress {
  animation: progress 30s linear forwards;
}

/* Aspect ratio utility for screenshots */
.aspect-w-16 {
  position: relative;
  padding-bottom: calc(9 / 16 * 100%);
}

.aspect-h-9 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

/* Navigation styles */
.nav-tab {
  @apply px-4 py-2 rounded-lg transition-all duration-300 font-medium;
}

.nav-tab-active {
  @apply bg-blue-600 text-white shadow-md;
}

.nav-tab-inactive {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700;
}

/* Glass effect for certain UI elements */
.glass-effect {
  @apply bg-white bg-opacity-30 backdrop-blur-sm border border-white border-opacity-30 rounded-lg shadow-lg;
}

/* Benefit item styles */
.benefit-heading {
  @apply text-[1.5rem] font-bold;
  color: var(--benefit-heading-color);
}

.benefit-text {
  @apply text-[1.2rem] font-semibold;
  color: var(--benefit-text-color);
}

.benefit-item {
  @apply px-3 py-2 rounded rounded-bl-2xl flex items-start;
}

.benefit-bullet {
  @apply text-teal-600 mr-2 text-xl;
}

/* Responsive utility for preventing overlaps */
.prevent-overlap {
  @apply mt-24 md:mt-0;
}

/* Responsive header adjustments */
@media (max-width: 768px) {
  .header-mobile {
    padding-bottom: 0.5rem;
  }
  
  .session-timer-mobile {
    top: 5rem;
    right: 0.5rem;
  }
}

/* Very small screens (minimize window scenarios) */
@media (max-width: 640px) {
  .header-compact {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
  
  .session-timer-compact {
    top: 6rem;
    right: 0.25rem;
    font-size: 0.7rem;
  }
}

/* Extra small screens (below 480px) */
@media (max-width: 480px) {
  .nav-item {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }
  
  .dashboard-button {
    font-size: 0.65rem;
    padding: 0.3rem 0.6rem;
  }
  
  .session-timer-mini {
    position: fixed;
    top: 7rem;
    right: 0.1rem;
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
  }
}

/* Responsive grid improvements */
@media (max-width: 1024px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .responsive-grid > * {
    margin-bottom: 1rem;
  }
}
