import os
from dotenv import load_dotenv
from loguru import logger
from Core.ui_logger import ui_log
import asyncio

from Core.browser_setup import AsyncBrowserSetup


class NCLLogin:

    @staticmethod
    def get_credentials():
        load_dotenv()
        return {
            "username": os.getenv("NCL_Username"), "password":
            os.getenv("NCL_Password"), "url": os.getenv("NCL_URL")
        }

    @staticmethod
    async def setup_driver(
        headless=True, session_id=None, cabin_id=None, video_auditing=False
    ):

        try:
            logger.info("Using shared NCL browser")
            
            from Core.browser_setup import browser_manager, AsyncBrowserSetup
            
            # Get the shared browser instance for NCL
            browser = await browser_manager.get_or_launch_browser('NCL')
            logger.info("Retrieved shared NCL browser")
            
            # Create browser setup instance for context creation
            browser_setup = AsyncBrowserSetup()

            context = await browser_setup.create_optimized_context(
                browser, logger, video_auditing=video_auditing, cabin_id=cabin_id,
                session_id=session_id
            )

            page = await context.new_page()
            page.set_default_timeout(30000)

            await browser_setup.setup_resource_blocking(page, logger, block=False)

            if video_auditing:
                logger.info(
                    "Video auditing enabled for NCL - maintaining default settings"
                )

            logger.info("Browser context setup complete")
            ui_log(
                "Resource allocation complete - taking input",
                session_id=session_id,
                cabin_id=cabin_id,
                module="NCL",
                step="browser_ready"
            )

            page._browser = browser
            page._context = context
            page._playwright = browser_manager.get_playwright('NCL')
            page._browser_setup = browser_setup

            return page

        except Exception as e:
            logger.error(f"Browser setup failed: {str(e)}")
            ui_log(
                "Browser setup failed — trying to recover",
                session_id=session_id,
                cabin_id=cabin_id,
                module="NCL",
                step="browser_setup_failed"
            )
            raise

    @staticmethod
    async def login(page, url, username, password, session_id=None, cabin_id=None):
        """Login to the NCL website using provided credentials"""
        try:
            logger.info(f"Navigating to {url}")
            await page.goto(url)

            try:
                if "/Security/login" not in page.url:
                    logger.info("Looking for login link on main page")
                    login_link = await page.wait_for_selector(
                        "a:text('Log In')", timeout=10000
                    )
                    logger.info("Clicking login link")
                    await login_link.click()
            except:
                logger.info("Either already on login page or no login link found")

            await page.wait_for_selector("#LoginForm_LoginForm", timeout=20000)

            logger.info(f"Entering username: {username}")
            username_field = page.locator("#LoginForm_LoginForm_Email")
            await username_field.fill("")
            await username_field.fill(username)

            logger.info("Entering password")
            password_field = page.locator("#LoginForm_LoginForm_Password")
            await password_field.fill("")
            await password_field.fill(password)

            logger.info("Clicking login button")
            try:
                async with page.expect_navigation(timeout=30000):
                    login_button = page.locator("#LoginForm_LoginForm_action_doLogin")
                    await login_button.click()
            except:
                try:
                    async with page.expect_navigation(timeout=30000):
                        login_button = page.locator(
                            "input[type='submit'][value='Log in']"
                        )
                        await login_button.click()
                except:
                    try:
                        async with page.expect_navigation(timeout=30000):
                            login_button = page.locator("input[value='GO']")
                            await login_button.click()
                    except:
                        async with page.expect_navigation(timeout=30000):
                            login_button = page.locator(
                                "*:text('Log in'), *:text('GO')"
                            )
                            await login_button.click()

            logger.info("Waiting for login to complete...")
            await page.wait_for_function(
                "() => !window.location.href.includes('/Security/login')",
                timeout=30000
            )

            logger.success(f"Login successful. Current URL: {page.url}")
            ui_log(
                "I am logged in successfully — ready to get started",
                session_id=session_id,
                cabin_id=cabin_id,
                module="NCL",
                step="login_success"
            )
            return True

        except Exception as e:
            logger.error(f"Login failed: {str(e)}")
            ui_log(
                "I couldn't log in — retrying now",
                session_id=session_id,
                cabin_id=cabin_id,
                module="NCL",
                step="login_failed"
            )
            return False