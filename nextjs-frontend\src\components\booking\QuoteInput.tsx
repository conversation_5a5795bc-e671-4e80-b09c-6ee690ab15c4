import React, { useState, useEffect } from 'react';
import { ProviderType } from './ProviderSelector';
import { getStoredInputText } from '../../services/api';
import { getProviderExamples, getExampleByIndex } from './ExampleData';

interface QuoteInputProps {
  provider: ProviderType;
  onSubmit: (data: QuoteInputData) => void;
  isLoading?: boolean;
}

export interface QuoteInputData {
  textInput: string;
  requestId: string;
  urlInput: string;
}

const QuoteInput: React.FC<QuoteInputProps> = ({ 
  provider, 
  onSubmit,
  isLoading = false
}) => {
  const [textInput, setTextInput] = useState<string>('');
  const [requestId, setRequestId] = useState<string>('');
  const [urlInput, setUrlInput] = useState<string>('');
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [apiError, setApiError] = useState<string | null>(null);
  const [isLoadingPrevious, setIsLoadingPrevious] = useState<boolean>(false);
  const [currentExampleIndex, setCurrentExampleIndex] = useState<number>(0);

  // Reset example index when provider changes
  useEffect(() => {
    setCurrentExampleIndex(0);
  }, [provider]);

  const validateInputs = (): boolean => {
    const newErrors: {[key: string]: string} = {};
    
    if (!textInput.trim()) {
      newErrors.textInput = 'Please enter the cruise details text';
    }
    
    if (!requestId.trim()) {
      newErrors.requestId = 'Please enter a request ID';
    }
    
    // URL is required only for Studio
    if (provider.startsWith('Studio') && !urlInput.trim()) {
      newErrors.urlInput = 'Please enter the booking URL';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const loadPreviousInput = async () => {
    try {
      setIsLoadingPrevious(true);
      setApiError(null);
      
      if (!requestId.trim()) {
        setErrors({ requestId: 'Please enter a request ID to load previous input' });
        return;
      }
      
      const storedData = await getStoredInputText(requestId);
      if (storedData) {
        setTextInput(storedData.text_input || '');
        if (storedData.url_input) {
          setUrlInput(storedData.url_input);
        }
        setApiError(null);
        setErrors({});
        // You could show a success message here if needed
      }
    } catch (error) {
      setApiError(error instanceof Error ? error.message : 'Failed to load previous input');
    } finally {
      setIsLoadingPrevious(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Clear any previous API errors
    setApiError(null);
    
    if (validateInputs()) {
      try {
        onSubmit({
          textInput,
          requestId,
          urlInput
        });
      } catch (error) {
        setApiError(error instanceof Error ? error.message : 'An unknown error occurred');
      }
    }
  };

  const getCurrentExample = () => {
    const example = getExampleByIndex(provider, currentExampleIndex);
    return example ? example.content : '';
  };

  const getCurrentExampleTitle = () => {
    return `Quote ${currentExampleIndex + 1}`;
  };

  const handleNextExample = () => {
    const examples = getProviderExamples(provider);
    if (examples.length > 0) {
      setCurrentExampleIndex((prev) => (prev + 1) % examples.length);
    }
  };

  const handlePrevExample = () => {
    const examples = getProviderExamples(provider);
    if (examples.length > 0) {
      setCurrentExampleIndex((prev) => (prev - 1 + examples.length) % examples.length);
    }
  };

  const handleUseExample = () => {
    const example = getExampleByIndex(provider, currentExampleIndex);
    if (example) {
      // Fill the cruise details text box for all providers
      setTextInput(example.content);
      
      // For Studio providers, also fill URL and Request ID
      if (isStudioProvider() && example.url) {
        setUrlInput(example.url);
      }
      
      // Set Request ID as "Quote_X" format
      setRequestId(`Quote_${currentExampleIndex + 1}`);
      
      // Clear any existing errors
      setErrors({});
      setApiError(null);
    }
  };

  // Helper function to check if the provider is a Studio variant
  const isStudioProvider = () => {
    return provider === 'Studio.Sales.CabinCloseOut' || provider === 'Studio.Res.CabinCloseOut';
  };

  return (
    <div>
      <div className="p-3">
        <h2 className="text-black text-xl font-bold">Quote Details Input</h2>
        <div className="mt-0 relative pb-2">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
        </div>
        <p className="mt-2 text-gray-800 text-s">Enter the quote details from respective portal below to extract the cruise details.</p>
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
      {/* <div className="p-6 bg-gray-400/10 rounded-b-lg border border-gray-800/30 shadow-inner "></div> */}
        {apiError && (
          <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-600 text-red-700 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="font-semibold">Error:</p>
                <p>{apiError}</p>
              </div>
            </div>
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1  gap-6">
            <div className="md:col-span-2">
              <div>
                <label className="block text-teal-800 text-md font-semibold mb-2">
                  Cruise Details Text
                </label>
                <div className="relative">
                  <textarea
                    className={`w-full h-72 p-4 border-2 rounded-lg transition-all ${
                      errors.textInput ? 'border-red-500' : 'border-gray-300 focus:border-teal-800 focus:ring-2 focus:ring-teal-700/30'
                    }`}
                    style={{ outline: 'none' }}
                    value={textInput}
                    onChange={(e) => setTextInput(e.target.value)}
                    placeholder="Paste cruise details here..."
                    disabled={isLoading}
                  />
                  {textInput && (
                    <button 
                      type="button"
                      className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                      onClick={() => setTextInput('')}
                    >
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </button>
                  )}
                </div>
                {errors.textInput && (
                  <p className="text-red-500 text-sm mt-1">{errors.textInput}</p>
                )}
                
                <div className="mt-2">
                  <details className="text-sm">
                    <summary className="cursor-pointer text-blue-600 hover:text-blue-800 font-medium">
                      See Example Format
                    </summary>
                    <div className="mt-2 bg-blue-50 p-4 border border-blue-100 rounded-md relative">
                      <h4 className="font-semibold text-blue-800 mb-2">
                        {getCurrentExampleTitle()}
                      </h4>
                      <pre className="whitespace-pre-wrap text-sm text-blue-700">
                        {getCurrentExample()}
                      </pre>
                      <div className="absolute bottom-2 right-2 flex gap-1 items-center">
                        <button 
                          type="button"
                          onClick={handleUseExample}
                          className="px-3 py-1 bg-green-100 hover:bg-green-200 text-green-700 text-xs font-medium rounded transition-colors duration-200"
                          title="Use This Example"
                        >
                          Autofill
                        </button>
                        <button 
                          type="button"
                          onClick={handlePrevExample}
                          className="p-1 rounded-full bg-blue-100 hover:bg-blue-200 transition-colors duration-200 flex items-center justify-center"
                          title="Previous Example"
                        >
                          <svg 
                            className="w-3 h-3 text-blue-600" 
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <path 
                              strokeLinecap="round" 
                              strokeLinejoin="round" 
                              strokeWidth={2} 
                              d="M15 19l-7-7 7-7" 
                            />
                          </svg>
                        </button>
                        <button 
                          type="button"
                          onClick={handleNextExample}
                          className="p-1 rounded-full bg-blue-100 hover:bg-blue-200 transition-colors duration-200 flex items-center justify-center"
                          title="Next Example"
                        >
                          <svg 
                            className="w-3 h-3 text-blue-600" 
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <path 
                              strokeLinecap="round" 
                              strokeLinejoin="round" 
                              strokeWidth={2} 
                              d="M9 5l7 7-7 7" 
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </details>
                </div>
              </div>
            </div>
            
            <div className=" grid grid-cols-2 gap-5">
              <div>
                <label className="block text-teal-800 text-md font-semibold mb-2">
                  Request ID
                </label>
                <div className="relative">
                  <input
                    type="text"
                    className={`w-full p-3 border-2 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none transition-all ${
                      errors.requestId ? 'border-red-500' : 'border-gray-300 focus:border-teal-800 focus:ring-2 focus:ring-teal-700/30'
                    }`}
                    value={requestId}
                    onChange={(e) => setRequestId(e.target.value)}
                    placeholder="e.g., badgerory123-4442491"
                    disabled={isLoading}
                  />
                  {requestId && (
                    <button 
                      type="button"
                      className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                      onClick={() => setRequestId('')}
                    >
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </button>
                  )}
                </div>
                {errors.requestId && (
                  <p className="text-red-500 text-sm mt-1">{errors.requestId}</p>
                )}
                
                <div className="mt-2">
                  <div className="flex items-center gap-2 mt-2">
                    <button
                      type="button"
                      onClick={loadPreviousInput}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={isLoading || isLoadingPrevious || !requestId.trim()}
                    >
                      {isLoadingPrevious ? (
                        <>
                          <svg className="animate-spin h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Loading...
                        </>
                      ) : (
                        <>
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                          </svg>
                          Load Previous Input
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
              
              {/* Show URL input only for Studio provider */}
              {isStudioProvider() && (
                <div>
                  <label className="block text-gray-700 text-md font-semibold mb-2">
                    Booking URL
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      className={`w-full p-3 border-2 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none transition-all ${
                        errors.urlInput ? 'border-red-500' : 'border-gray-300 focus:border-blue-500'
                      }`}
                      value={urlInput}
                      onChange={(e) => setUrlInput(e.target.value)}
                      placeholder="Paste booking URL here"
                      disabled={isLoading}
                    />
                    {urlInput && (
                      <button 
                        type="button"
                        className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                        onClick={() => setUrlInput('')}
                      >
                        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </button>
                    )}
                  </div>
                  {errors.urlInput && (
                    <p className="text-red-500 text-sm mt-1">{errors.urlInput}</p>
                  )}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex justify-end mt-4">
            <button
              type="submit"
              className={`
                px-2 py-2 rounded-lg font-semibold text-white
                transform transition-all duration-300
                ${isLoading
                  ? 'bg-gray-500 cursor-not-allowed'
                  : 'hover:scale-105 hover:shadow-xl'
                }
              `}
               style={{
                background: isLoading ? '#808080' : 'linear-gradient(to right,rgb(88, 116, 131),rgb(26, 78, 112))'
              }}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Extracting Details...
                </div>
              ) : (
                <div className="flex items-center">
                  Extract Cruise Details
                </div>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default QuoteInput; 