{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/index.ts"], "names": ["WellKnownErrorsPlugin", "NAME", "apply", "compiler", "hooks", "compilation", "tap", "afterSeal", "tapPromise", "warnings", "length", "Promise", "all", "map", "warn", "i", "name", "module", "context", "includes", "splice", "errors", "err", "moduleError", "getModuleBuildError", "e", "console", "log"], "mappings": ";;;;+BAKa<PERSON>;;;eAAAA;;;oCAHuB;AAEpC,MAAMC,OAAO;AACN,MAAMD;IACXE,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAACL,MAAM,CAACI;YACpCA,YAAYD,KAAK,CAACG,SAAS,CAACC,UAAU,CAACP,MAAM;oBACvCI,uBAaAA;gBAbJ,KAAIA,wBAAAA,YAAYI,QAAQ,qBAApBJ,sBAAsBK,MAAM,EAAE;oBAChC,MAAMC,QAAQC,GAAG,CACfP,YAAYI,QAAQ,CAACI,GAAG,CAAC,OAAOC,MAAMC;4BAGlCD;wBAFF,IACEA,KAAKE,IAAI,KAAK,+BACdF,uBAAAA,KAAKG,MAAM,CAACC,OAAO,qBAAnBJ,qBAAqBK,QAAQ,CAAC,kBAC9B;4BACAd,YAAYI,QAAQ,CAACW,MAAM,CAACL,GAAG;wBACjC;oBACF;gBAEJ;gBAEA,KAAIV,sBAAAA,YAAYgB,MAAM,qBAAlBhB,oBAAoBK,MAAM,EAAE;oBAC9B,MAAMC,QAAQC,GAAG,CACfP,YAAYgB,MAAM,CAACR,GAAG,CAAC,OAAOS,KAAKP;wBACjC,IAAI;4BACF,MAAMQ,cAAc,MAAMC,IAAAA,uCAAmB,EAC3CrB,UACAE,aACAiB;4BAEF,IAAIC,gBAAgB,OAAO;gCACzBlB,YAAYgB,MAAM,CAACN,EAAE,GAAGQ;4BAC1B;wBACF,EAAE,OAAOE,GAAG;4BACVC,QAAQC,GAAG,CAACF;wBACd;oBACF;gBAEJ;YACF;QACF;IACF;AACF"}