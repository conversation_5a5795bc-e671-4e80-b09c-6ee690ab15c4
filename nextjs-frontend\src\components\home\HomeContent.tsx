'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import ProcessingTimeChart from '../charts/ProcessingTimeChart';
import CruiseAgencyChart from '../charts/CruiseAgencyChart';
// import gsap from 'gsap';
// import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../context/AuthContext';
import LoginModal from '../LoginModal'; 

export default function HomeContent() {
  const [scrolled, setScrolled] = useState(false);
  const [text, setText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [loopNum, setLoopNum] = useState(0);
  const [typingSpeed, setTypingSpeed] = useState(150);

  const phrases = ['AI Processing', 'Human Supervision'];
  const typingRef = useRef(null);

  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const router = useRouter();
  const { isLoggedIn, user } = useAuth();

  // Refs for GSAP animations
  
  // Text typing animation effect
  useEffect(() => {
    const handleTyping = () => {
      const i = loopNum % phrases.length;
      const fullText = phrases[i];

      setText(
        isDeleting
          ? fullText.substring(0, text.length - 1)
          : fullText.substring(0, text.length + 1)
      );

      setTypingSpeed(isDeleting ? 75 : 150);

      if (!isDeleting && text === fullText) {
        setTimeout(() => setIsDeleting(true), 1000);
      } else if (isDeleting && text === '') {
        setIsDeleting(false);
        setLoopNum(loopNum + 1);
      }
    };

    const timer = setTimeout(handleTyping, typingSpeed);
    return () => clearTimeout(timer);
  }, [text, isDeleting, loopNum, phrases, typingSpeed]);

  return (
    <div className="min-h-screen relative font-['Times_New_Roman']">
      {/* Circular gradient background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0" style={{ backgroundColor: '#c8f5ed', backgroundImage: 'linear-gradient(45deg, #c8f5ed 0%, #c7c7c7 100%)' }}></div>
      </div>

      {/* Content with nav and main sections */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Use the shared Header component */}
        {/* <Header /> */}

        <main className="flex-grow container mx-auto px-6 py-2">
          {/* Main content in one-page layout */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-4">
            {/* Hero section - left aligned and more compact */}
            <div className="lg:col-span-5 flex flex-col justify-center mt-0 hero-content">
              <h1 className="text-4xl text-shadow-lg lg:text-8xl font-bold text-sky-900 mb-1">
                OceanMind<span className="block text-sky-800">HAI Agent</span>
              </h1>
              <div className="text-3xl text-sky-900 font-bold mb-2 h-20 flex items-center">
                <span className="inline-block border-r-2 border-green-500 pr-2 mr-2">→</span>
                <span
                  ref={typingRef}
                  className="text-sky-900 bg-gradient-to-r from-blue-900 to-sky-900 bg-clip-text text-transparent"
                >
                  {text}
                  <span className="animate-blink font-bold">|</span>
                </span>
              </div>
              <p className="text-[1.2rem] text-blue-900 max-w-xl mb-6 bg-transparent backdrop-blur-sm p-3 rounded-lg border-l-4 border-sky-900">
                Transforming cruise bookings with AI-powered efficiency and human expertise. Smart automation for better rates, faster turnaround, and exceptional customer experiences.
              </p>

            </div>

            {/* Combined data visualization section with consistent styling */}
            <div className="lg:col-span-7 bg-transparent p-2 rounded-lg charts-section">
              <div className="chart-container"><ProcessingTimeChart /></div>
              <div className="border-t border-sky-200 pt-2 mt-2">
                <div className="chart-container"><CruiseAgencyChart /></div>
              </div>
            </div>

            {/* Spacer div */}
            <div className="lg:col-span-12 h-16"></div>

            {/* Key Benefits - single container with internal partition */}
            <div
              className="lg:col-span-12 bg-transparent backdrop-blur-sm p-4 rounded-lg  mt-4 mb-6"
            >
              <h3 className="text-6xl lobster-regular font-bold text-sky-900 mb-12 text-shadow-lg ">Key Benefits</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-24">
                {/* Left side - Benefits for Travel Agencies */}
                <div>
                  <h4 className="text-3xl lobster-regular font-bold text-sky-900 mb-3 border-b border-sky-100 pb-2">For Travel Agencies</h4>
                  <ul className="space-y-4">
                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Faster Turnaround to Customers</h4>
                        <p className="benefit-text">3-4 minutes vs traditional methods that take 10+ minutes</p>
                      </div>
                    </li>

                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Finding Cheapest Price</h4>
                        <p className="benefit-text">Across multiple booking portals automatically with consistent discount application</p>
                      </div>
                    </li>

                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Enhanced Reporting & Analytics</h4>
                        <p className="benefit-text">Comprehensive insights into booking performance and customer patterns</p>
                      </div>
                    </li>

                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Competitive Edge</h4>
                        <p className="benefit-text">Become first to market with AI-powered bookings and responsive customer service</p>
                      </div>
                    </li>

                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Reduced Human Input</h4>
                        <p className="benefit-text">Less manual effort with human validation for accuracy and quality assurance</p>
                      </div>
                    </li>

                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Cost Efficiency</h4>
                        <p className="benefit-text">Reduce operational costs while increasing booking volume and accuracy</p>
                      </div>
                    </li>
                  </ul>
                </div>

                {/* Right side - Benefits for Agents */}
                <div>
                  <h4 className="text-3xl lobster-regular font-bold text-sky-900 mb-3 border-b border-sky-100 pb-2">For Agents</h4>
                  <ul className="space-y-4">
                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Single Portal Interaction</h4>
                        <p className="benefit-text">Work in one interface instead of juggling multiple booking systems</p>
                      </div>
                    </li>

                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Fewer Errors</h4>
                        <p className="benefit-text">Reduced mistakes in price calculation and bookings with automated validation</p>
                      </div>
                    </li>

                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Parallel Processing</h4>
                        <p className="benefit-text">Handle multiple cabins simultaneously while focussing on customer service</p>
                      </div>
                    </li>

                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Automated Documentation</h4>
                        <p className="benefit-text">Screenshots and records created automatically for compliance and reference</p>
                      </div>
                    </li>

                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Less Strain for optimal accuracy</h4>
                        <p className="benefit-text">Reduced cognitive load and fatigue, leading to better customer interactions</p>
                      </div>
                    </li>

                    <li>

                      <div>
                        <h4 className="benefit-heading"> <span className="benefit-bullet">•</span> Increased Productivity</h4>
                        <p className="benefit-text">Process more bookings in less time with consistent quality</p>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
      
    </div>
    
  );
}
