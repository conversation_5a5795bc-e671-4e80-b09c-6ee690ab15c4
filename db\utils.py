"""
Utility functions for database operations.
"""
import uuid
import logging
import time
import asyncio
import asyncpg
from datetime import datetime
from db_service import get_db_connection, execute_query, db_connection

# Configure logging
logger = logging.getLogger("database.utils")

def generate_session_id(request_id: str = None) -> str:
    """
    Generate a unique session ID for a booking process
    
    Args:
        request_id: Optional request ID to include in the session ID
    
    Returns:
        A unique session ID string
    """
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    
    if request_id:
        # Create a session ID that's just a combination of request_id and timestamp
        return f"{request_id}_{timestamp}"
    else:
        # If no request_id provided, still need to ensure uniqueness with UUID
        unique_id = str(uuid.uuid4())
        return f"{timestamp}_{unique_id[:8]}"

async def get_current_session_id(request_id: str, provider: str) -> str:
    """
    Get the most recent session ID for a request_id and provider
    
    Args:
        request_id: The booking request ID
        provider: The provider (Studio, NCL, or Cruising Power)
    
    Returns:
        The most recent session ID or None if no session exists
    """
    try:
        booking_table = f"{provider.lower().replace(' ', '_')}_bookings"
        
        query = f"""
            SELECT session_id FROM {booking_table}
            WHERE request_id = $1
            ORDER BY timestamp DESC
            LIMIT 1
        """
        
        result = await execute_query(query, [request_id], fetch_one=True)
        
        if result and result['session_id']:
            return result['session_id']
        return None
    except Exception as e:
        logger.error(f"Error getting current session ID: {e}")
        return None

async def execute_with_retry(query_func, max_retries=5, base_wait_time=0.5):
    """
    Execute a database operation with retry logic to handle database locks
    
    Args:
        query_func: A function that performs the database operations
        max_retries: Maximum number of retry attempts
        base_wait_time: Base time to wait between retries (will be multiplied exponentially)
        
    Returns:
        The result from the query_func if successful
    
    Raises:
        Exception: If all retries fail
    """
    retry_count = 0
    last_error = None
    
    while retry_count < max_retries:
        try:
            # Execute the provided database function
            return await query_func()
        except asyncpg.exceptions.PostgresError as e:
            # Check specific PostgreSQL error conditions for retry
            if "deadlock detected" in str(e).lower() or "duplicate key" in str(e).lower():
                retry_count += 1
                wait_time = base_wait_time * (2 ** retry_count)
                logger.warning(f"Database error, retry {retry_count}/{max_retries} after {wait_time}s: {e}")
                await asyncio.sleep(wait_time)
                last_error = e
            else:
                # Re-raise other Postgres errors
                logger.error(f"PostgreSQL error: {e}")
                raise
        except Exception as e:
            # For other exceptions, only retry if it might be due to concurrency
            err_msg = str(e).lower()
            if "unique constraint" in err_msg or "duplicate key" in err_msg:
                retry_count += 1
                wait_time = base_wait_time * (2 ** retry_count)
                logger.warning(f"Constraint error, retry {retry_count}/{max_retries} after {wait_time}s: {e}")
                await asyncio.sleep(wait_time)
                last_error = e
            else:
                logger.error(f"Error in database operation: {e}")
                raise
    
    # If we exhausted all retries
    logger.error(f"Failed after {max_retries} retries")
    if last_error:
        raise last_error
    raise Exception("Database operation failed after retries")

async def register_session(request_id, provider=None, attempt=1):
    """
    Register a new session for tracking purposes
    
    Args:
        request_id: The booking request ID
        provider: The provider name (optional)
        attempt: Internal parameter for tracking retry attempts
        
    Returns:
        A unique session ID
    """
    if attempt > 3:
        # Safety to prevent infinite recursion
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        unique_id = f"{request_id}_{timestamp}_{attempt}"
        logger.warning(f"Maximum registration attempts reached, using fallback ID: {unique_id}")
        return unique_id
    
    # First, check if there's already an active session for this request_id
    async def check_existing_session():
        async with db_connection() as conn:
            try:
                result = await conn.fetchrow('''
                SELECT session_id FROM session_tracking 
                WHERE request_id = $1 AND provider = $2 AND status = 'active'
                ORDER BY created_at DESC LIMIT 1
                ''', request_id, provider or 'unknown')

                if result:
                    return result['session_id']
                return None
            except Exception as e:
                logger.warning(f"Error checking for existing session: {e}")
                return None


    # Check for existing session first
    existing_session = await check_existing_session()
    if existing_session:
        logger.info(f"Using existing active session for {request_id}: {existing_session}")
        return existing_session
    
    # Create a new session ID if none exists
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    session_id = f"{request_id}_{timestamp}"
    
    # Wrap db operation in a function for the retry mechanism
    async def register_session_db():
        async with db_connection() as conn:
            try:
                now = datetime.now().isoformat()

                # Use a transaction for atomicity
                async with conn.transaction():
                    # First, check again if session exists (double-check to prevent race conditions)
                    existing = await conn.fetchrow('''
                    SELECT session_id FROM session_tracking 
                    WHERE session_id = $1
                    ''', session_id)

                    if existing:
                        logger.warning(f"Session {session_id} already exists (race condition), trying with a different ID")
                        # Add a small delay and try with a different timestamp
                        await asyncio.sleep(0.2)
                        return await register_session(request_id, provider, attempt + 1)

                    # Insert new session
                    result = await conn.fetchrow('''
                    INSERT INTO session_tracking 
                    (session_id, request_id, created_at, updated_at, status, provider, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    RETURNING session_id
                    ''', 
                        session_id,
                        request_id,
                        now,
                        now,
                        'active',
                        provider or 'unknown',
                        '{}'
                    )

                    session_id = result['session_id']
                    logger.info(f"Registered new session {session_id} for {provider or 'unknown'} request {request_id}")
                    return session_id
            except asyncpg.exceptions.UniqueViolationError:
                logger.warning(f"Session ID {session_id} already exists, retrying with a different ID")
                # Try again with a different timestamp (recursive)
                await asyncio.sleep(0.5)  # Ensure next timestamp will be different
                return await register_session(request_id, provider, attempt + 1)
            except Exception as e:
                logger.error(f"Error registering session: {e}")
                raise

    
    try:
        # Use our retry mechanism
        return await execute_with_retry(register_session_db)
    except Exception as e:
        logger.error(f"Failed to register session: {e}")
        # Return a session ID anyway, even if we couldn't register it
        return session_id

async def start_booking_session(request_id: str, provider: str) -> str:
    """
    Start a new booking session and register it in the database.
    This should be called at the beginning of any booking process.
    
    Args:
        request_id: The booking request ID
        provider: The provider name (studio, ncl, cruising_power)
        
    Returns:
        The new session ID
    """
    session_manager = SessionManager.get_instance()
    session_id = await session_manager.create_session(request_id, provider)
    logger.info(f"Started new booking session: {session_id} for {provider} request: {request_id}")
    return session_id

async def get_active_session(request_id: str, provider: str) -> str:
    """
    Get the active session for the current booking process
    
    Args:
        request_id: The booking request ID
        provider: The provider name
        
    Returns:
        The current session ID
    """
    session_manager = SessionManager.get_instance()
    return await session_manager.get_session(request_id, provider)

async def complete_booking_session(session_id: str, success: bool = True):
    """
    Mark a booking session as completed
    
    Args:
        session_id: The session ID to complete
        success: Whether the booking was successful
    """
    session_manager = SessionManager.get_instance()
    status = 'completed_success' if success else 'completed_failure'
    await session_manager.update_session_status(session_id, status)
    logger.info(f"Completed booking session: {session_id} with status: {status}")

async def list_all_entries(provider: str = None):
    """
    List all entries in the database, optionally filtered by provider
    
    Args:
        provider: Optional provider to filter by (Studio, NCL, or Cruising Power)
    
    Returns:
        Dictionary with summary of database entries
    """
    try:
        async with db_connection() as conn:
        
            results = {}

            # Define which providers to query
            providers = ["studio", "ncl", "cruising_power"]
            if provider:
                providers = [provider.lower().replace(' ', '_')]

            # Iterate through each provider
            for prov in providers:
                bookings_table = f"{prov}_bookings"
                cabins_table = f"{prov}_cabins"
                screenshots_query = f"SELECT COUNT(*) as count FROM centralized_screenshots WHERE provider = '{prov}'"

                # Count bookings
                booking_count = await conn.fetchval(f"SELECT COUNT(*) as count FROM {bookings_table}")

                # Count cabins
                cabin_count = await conn.fetchval(f"SELECT COUNT(*) as count FROM {cabins_table}")

                # Count screenshots
                screenshot_count = await conn.fetchval(screenshots_query)

                # Get all request_ids
                rows = await conn.fetch(f"SELECT request_id, id, timestamp FROM {bookings_table} ORDER BY timestamp DESC")
                bookings = [dict(row) for row in rows]

                # Store results for this provider
                results[prov] = {
                    'booking_count': booking_count,
                    'cabin_count': cabin_count,
                    'screenshot_count': screenshot_count,
                    'bookings': bookings
                }

            return results
    except Exception as e:
        logger.error(f"Error listing database entries: {e}")

        return {}

async def view_booking_details(booking_id: int, provider: str):
    """
    Get detailed information about a specific booking
    
    Args:
        booking_id: The booking ID
        provider: The provider (Studio, NCL, or Cruising Power)
    
    Returns:
        Dictionary with booking details
    """
    try:
        async with db_connection() as conn:
        
            provider = provider.lower().replace(' ', '_')
            bookings_table = f"{provider}_bookings"
            cabins_table = f"{provider}_cabins"

            # Get booking details
            booking = await conn.fetchrow(f"SELECT * FROM {bookings_table} WHERE id = $1", booking_id)
        
            if not booking:
                return {'error': f"No {provider} booking found with ID: {booking_id}"}

            booking_dict = dict(booking)

            # Get cabins for this booking
            cabins_rows = await conn.fetch(f"SELECT * FROM {cabins_table} WHERE booking_id = $1", booking_id)
            cabins = [dict(row) for row in cabins_rows]

            # Get screenshots for this booking (without image data to keep response size small)
            screenshots_rows = await conn.fetch(
                """SELECT id, booking_id, cabin_id, screenshot_type, timestamp, file_name, request_id 
                FROM centralized_screenshots 
                WHERE booking_id = $1 AND provider = $2""", 
                booking_id, provider
            )
            screenshots = [dict(row) for row in screenshots_rows]
        
            # Compile results
            result = {
                'booking': booking_dict,
                'cabins': cabins,
                'screenshots': screenshots
            }
        
            return result
    except Exception as e:
        logger.error(f"Error viewing booking details: {e}")

        return {'error': str(e)}

async def cleanup_database():
    """
    Clean up the database by removing orphaned records and fixing inconsistencies
    
    Returns:
        Dictionary with cleanup results
    """
    try:
        async with db_connection() as conn:
        
            results = {}
            providers = ["studio", "ncl", "cruising_power"]
        
            for provider in providers:
                results[provider] = {}

                # Update screenshots with NULL booking_id but valid request_id
                linked_count = await conn.execute(f"""
                    UPDATE centralized_screenshots
                    SET booking_id = (
                        SELECT b.id 
                        FROM {provider}_bookings b 
                        WHERE b.request_id = centralized_screenshots.request_id 
                        AND centralized_screenshots.provider = $1
                        LIMIT 1
                    ),
                    booking_table_id = (
                        SELECT b.id 
                        FROM {provider}_bookings b 
                        WHERE b.request_id = centralized_screenshots.request_id 
                        AND centralized_screenshots.provider = $1
                        LIMIT 1
                    )
                    WHERE booking_id IS NULL 
                    AND request_id IS NOT NULL
                    AND provider = $1
                """, provider)

                results[provider]['linked_screenshots'] = int(linked_count.split()[-1]) if linked_count else 0

            return results
    except Exception as e:
        logger.error(f"Error during database cleanup: {e}")

        return {'error': str(e)}

async def fix_cabin_booking_relationships():
    """
    Fix relationships between booking tables and their corresponding cabin tables.
    This addresses issues where cabins might have incorrect booking_id values.
    
    Returns:
        Dictionary with results of the fix operation
    """
    try:
        async with db_connection() as conn:
        
            results = {
                'ncl_fixed': 0,
                'cruising_power_fixed': 0,
                'studio_fixed': 0,
                'errors': []
            }

            # Begin transaction
            async with conn.transaction():
                try:
                    # 1. Fix NCL cabins
                    # First, find all records with potential issues
                    problematic_ncl_cabins = await conn.fetch("""
                    SELECT ncl_cabins.id, ncl_cabins.booking_id
                    FROM ncl_cabins
                    LEFT JOIN ncl_bookings ON ncl_cabins.booking_id = ncl_bookings.id
                    WHERE ncl_bookings.id IS NULL
                    """)

                    # Fix each problematic cabin
                    for cabin in problematic_ncl_cabins:
                        cabin_id = cabin['id']
                        current_booking_id = cabin['booking_id']

                        # Find the closest valid booking ID
                        new_booking_row = await conn.fetchrow("""
                        SELECT id FROM ncl_bookings
                        WHERE id < $1
                        ORDER BY id DESC LIMIT 1
                        """, current_booking_id)

                        if new_booking_row:
                            new_booking_id = new_booking_row['id']

                            # Update the cabin record
                            await conn.execute("""
                            UPDATE ncl_cabins
                            SET booking_id = $1
                            WHERE id = $2
                            """, new_booking_id, cabin_id)

                            results['ncl_fixed'] += 1
                            logger.info(f"Fixed NCL cabin {cabin_id}: booking_id changed from {current_booking_id} to {new_booking_id}")

                    # 2. Fix Cruising Power cabins
                    problematic_cp_cabins = await conn.fetch("""
                    SELECT cruising_power_cabins.id, cruising_power_cabins.booking_id
                    FROM cruising_power_cabins
                    LEFT JOIN cruising_power_bookings ON cruising_power_cabins.booking_id = cruising_power_bookings.id
                    WHERE cruising_power_bookings.id IS NULL
                    """)

                    # Fix each problematic cabin
                    for cabin in problematic_cp_cabins:
                        cabin_id = cabin['id']
                        current_booking_id = cabin['booking_id']

                        # Find the closest valid booking ID
                        new_booking_row = await conn.fetchrow("""
                        SELECT id FROM cruising_power_bookings
                        WHERE id < $1
                        ORDER BY id DESC LIMIT 1
                        """, current_booking_id)

                        if new_booking_row:
                            new_booking_id = new_booking_row['id']

                            # Update the cabin record
                            await conn.execute("""
                            UPDATE cruising_power_cabins
                            SET booking_id = $1
                            WHERE id = $2
                            """, new_booking_id, cabin_id)

                            results['cruising_power_fixed'] += 1
                            logger.info(f"Fixed Cruising Power cabin {cabin_id}: booking_id changed from {current_booking_id} to {new_booking_id}")

                    # 3. Fix Studio cabins
                    problematic_studio_cabins = await conn.fetch("""
                    SELECT studio_cabins.id, studio_cabins.booking_id
                    FROM studio_cabins
                    LEFT JOIN studio_bookings ON studio_cabins.booking_id = studio_bookings.id
                    WHERE studio_bookings.id IS NULL
                    """)

                    # Fix each problematic cabin
                    for cabin in problematic_studio_cabins:
                        cabin_id = cabin['id']
                        current_booking_id = cabin['booking_id']

                        # Find the closest valid booking ID
                        new_booking_row = await conn.fetchrow("""
                        SELECT id FROM studio_bookings
                        WHERE id < $1
                        ORDER BY id DESC LIMIT 1
                        """, current_booking_id)

                        if new_booking_row:
                            new_booking_id = new_booking_row['id']

                            # Update the cabin record
                            await conn.execute("""
                            UPDATE studio_cabins
                            SET booking_id = $1
                            WHERE id = $2
                            """, new_booking_id, cabin_id)

                            results['studio_fixed'] += 1
                            logger.info(f"Fixed Studio cabin {cabin_id}: booking_id changed from {current_booking_id} to {new_booking_id}")

                except Exception as e:
                    error_msg = f"Error fixing cabin-booking relationships: {str(e)}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
                    raise
                

            # Calculate total fixed
            results['total_fixed'] = results['ncl_fixed'] + results['cruising_power_fixed'] + results['studio_fixed']

            return results
    except Exception as e:
        logger.error(f"Error in fix_cabin_booking_relationships: {e}")
        import traceback
        logger.error(traceback.format_exc())

        return {
            'success': False,
            'error': str(e)
        }

# Import SessionManager at the end to avoid circular imports
from db.session_manager import SessionManager 