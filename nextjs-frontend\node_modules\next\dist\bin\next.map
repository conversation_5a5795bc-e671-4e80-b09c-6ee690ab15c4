{"version": 3, "sources": ["../../src/bin/next.ts"], "names": ["args", "arg", "Boolean", "permissive", "console", "log", "process", "env", "__NEXT_VERSION", "exit", "foundCommand", "commands", "_", "Object", "keys", "join", "semver", "lt", "versions", "node", "__NEXT_REQUIRED_NODE_VERSION", "error", "performance", "mark", "defaultCommand", "command", "includes", "push", "forwarded<PERSON>rgs", "slice", "Error", "defaultEnv", "standardEnv", "NODE_ENV", "isNotStandard", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warn", "NON_STANDARD_NODE_ENV", "NEXT_RUNTIME", "NEXT_MANUAL_SIG_HANDLE", "on", "main", "currentArgsSpec", "commandArgs", "validatedArgs", "getValidatedArgs", "dependency", "require", "resolve", "err", "then", "exec"], "mappings": ";;;;;QAEO;6DACc;8DACL;+DACG;2BACmB;0BACb;6BACG;kCACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,MAAMA,OAAOC,IAAAA,cAAG,EACd;IACE,QAAQ;IACR,aAAaC;IACb,UAAUA;IACV,aAAaA;IAEb,UAAU;IACV,MAAM;IACN,MAAM;AACR,GACA;IACEC,YAAY;AACd;AAGF,8DAA8D;AAC9D,IAAIH,IAAI,CAAC,YAAY,EAAE;IACrBI,QAAQC,GAAG,CAAC,CAAC,SAAS,EAAEC,QAAQC,GAAG,CAACC,cAAc,CAAC,CAAC;IACpDF,QAAQG,IAAI,CAAC;AACf;AAEA,wDAAwD;AACxD,MAAMC,eAAeR,QAAQS,kBAAQ,CAACX,KAAKY,CAAC,CAAC,EAAE,CAAC;AAEhD,+CAA+C;AAC/C,qDAAqD;AACrD,+DAA+D;AAC/D,IAAI,CAACF,gBAAgBV,IAAI,CAAC,SAAS,EAAE;IACnCI,QAAQC,GAAG,CAAC,CAAC;;;;;MAKT,EAAEQ,OAAOC,IAAI,CAACH,kBAAQ,EAAEI,IAAI,CAAC,MAAM;;;;;;;;EAQvC,CAAC;IACDT,QAAQG,IAAI,CAAC;AACf;AAEA,wCAAwC;AACxC,IACEO,eAAM,CAACC,EAAE,CAACX,QAAQY,QAAQ,CAACC,IAAI,EAAEb,QAAQC,GAAG,CAACa,4BAA4B,GACzE;IACAhB,QAAQiB,KAAK,CACX,CAAC,sBAAsB,EAAEf,QAAQY,QAAQ,CAACC,IAAI,CAAC,mCAAmC,EAAEb,QAAQC,GAAG,CAACa,4BAA4B,CAAC,aAAa,CAAC;IAE7Id,QAAQG,IAAI,CAAC;AACf;AAEA,+DAA+D;AAC/Da,YAAYC,IAAI,CAAC;AAEjB,MAAMC,iBAAiB;AACvB,MAAMC,UAAUf,eAAeV,KAAKY,CAAC,CAAC,EAAE,GAAGY;AAE3C,IAAI;IAAC;IAAwB;CAAwB,CAACE,QAAQ,CAACD,UAAU;IACvEzB,KAAKY,CAAC,CAACe,IAAI,CAAC,gBAAgBF;AAC9B;AAEA,MAAMG,gBAAgBlB,eAAeV,KAAKY,CAAC,CAACiB,KAAK,CAAC,KAAK7B,KAAKY,CAAC;AAE7D,IAAIZ,IAAI,CAAC,YAAY,EACnB,MAAM,IAAI8B,MACR,CAAC,mGAAmG,EAAEL,QAAQ,CAAC;AAGnH,2DAA2D;AAC3D,IAAIzB,IAAI,CAAC,SAAS,EAAE;IAClB4B,cAAcD,IAAI,CAAC;AACrB;AAEA,MAAMI,aAAaN,YAAY,QAAQ,gBAAgB;AAEvD,MAAMO,cAAc;IAAC;IAAc;IAAe;CAAO;AAEzD,IAAI1B,QAAQC,GAAG,CAAC0B,QAAQ,EAAE;IACxB,MAAMC,gBAAgB,CAACF,YAAYN,QAAQ,CAACpB,QAAQC,GAAG,CAAC0B,QAAQ;IAChE,MAAME,qBACJ7B,QAAQC,GAAG,CAAC0B,QAAQ,KAAK,gBACrB;QAAC;QAAS;KAAQ,GAClB3B,QAAQC,GAAG,CAAC0B,QAAQ,KAAK,eACzB;QAAC;KAAM,GACP,EAAE;IAER,IAAIC,iBAAiBC,mBAAmBT,QAAQ,CAACD,UAAU;QACzDpB,KAAI+B,IAAI,CAACC,gCAAqB;IAChC;AACF;AAEE/B,QAAQC,GAAG,CAAS0B,QAAQ,GAAG3B,QAAQC,GAAG,CAAC0B,QAAQ,IAAIF;AACvDzB,QAAQC,GAAG,CAAS+B,YAAY,GAAG;AAErC,+EAA+E;AAC/E,6DAA6D;AAC7D,IAAI,CAAChC,QAAQC,GAAG,CAACgC,sBAAsB,IAAId,YAAY,OAAO;IAC5DnB,QAAQkC,EAAE,CAAC,WAAW,IAAMlC,QAAQG,IAAI,CAAC;IACzCH,QAAQkC,EAAE,CAAC,UAAU,IAAMlC,QAAQG,IAAI,CAAC;AAC1C;AACA,eAAegC;IACb,MAAMC,kBAAkBC,wBAAW,CAAClB,QAAQ;IAC5C,MAAMmB,gBAAgBC,IAAAA,kCAAgB,EAACH,iBAAiBd;IAExD,KAAK,MAAMkB,cAAc;QAAC;QAAS;KAAY,CAAE;QAC/C,IAAI;YACF,yEAAyE;YACzEC,QAAQC,OAAO,CAACF;QAClB,EAAE,OAAOG,KAAK;YACZ7C,QAAQgC,IAAI,CACV,CAAC,YAAY,EAAEU,WAAW,4HAA4H,EAAEA,WAAW,CAAC,CAAC;QAEzK;IACF;IAEA,MAAMnC,kBAAQ,CAACc,QAAQ,GACpByB,IAAI,CAAC,CAACC,OAASA,KAAKP,gBACpBM,IAAI,CAAC;QACJ,IAAIzB,YAAY,WAAWA,YAAY,wBAAwB;YAC7D,yEAAyE;YACzE,8BAA8B;YAC9BnB,QAAQG,IAAI,CAAC;QACf;IACF;AACJ;AAEAgC"}