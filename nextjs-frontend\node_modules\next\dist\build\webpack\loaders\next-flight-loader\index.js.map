{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/index.ts"], "names": ["transformSource", "noopHeadPath", "require", "resolve", "MODULE_PROXY_PATH", "source", "sourceMap", "buildInfo", "Error", "getModuleBuildInfo", "_module", "rsc", "getRSCModuleInformation", "type", "RSC_MODULE_TYPES", "client", "sourceType", "parser", "detectedClientEntryType", "clientEntryType", "clientRefs", "assumedSourceType", "length", "includes", "callback", "esmSource", "resourcePath", "cnt", "ref", "warnOnce", "replace", "RSC_MOD_REF_PROXY_ALIAS"], "mappings": ";;;;+BAWA;;;eAAwBA;;;2BAXgB;4BACP;0BACR;mCACe;oCACL;AAEnC,MAAMC,eAAeC,QAAQC,OAAO,CAAC;AACrC,gEAAgE;AAChE,MAAMC,oBACJ;AAEa,SAASJ,gBAEtBK,MAAc,EACdC,SAAc;QAaVC,gBAiEAA;IA5EJ,8BAA8B;IAC9B,IAAI,OAAOF,WAAW,UAAU;QAC9B,MAAM,IAAIG,MAAM;IAClB;IAEA,gDAAgD;IAChD,mEAAmE;IACnE,MAAMD,YAAYE,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IACjDH,UAAUI,GAAG,GAAGC,IAAAA,0CAAuB,EAACP,QAAQ;IAEhD,qBAAqB;IACrB,IAAIE,EAAAA,iBAAAA,UAAUI,GAAG,qBAAbJ,eAAeM,IAAI,MAAKC,4BAAgB,CAACC,MAAM,EAAE;YAChC,sBAAA;QAAnB,MAAMC,cAAa,gBAAA,IAAI,CAACN,OAAO,sBAAZ,uBAAA,cAAcO,MAAM,qBAApB,qBAAsBD,UAAU;QACnD,MAAME,0BAA0BX,UAAUI,GAAG,CAACQ,eAAe;QAC7D,MAAMC,aAAab,UAAUI,GAAG,CAACS,UAAU;QAE3C,4EAA4E;QAC5E,6EAA6E;QAC7E,4DAA4D;QAC5D,IAAIC,oBAAoBL;QACxB,IAAIK,sBAAsB,UAAUH,4BAA4B,QAAQ;YACtE,IACEE,WAAWE,MAAM,KAAK,KACrBF,WAAWE,MAAM,KAAK,KAAKF,UAAU,CAAC,EAAE,KAAK,IAC9C;gBACA,uEAAuE;gBACvE,yEAAyE;gBACzE,oBAAoB;gBACpBC,oBAAoB;YACtB,OAAO,IAAI,CAACD,WAAWG,QAAQ,CAAC,MAAM;gBACpC,2CAA2C;gBAC3CF,oBAAoB;YACtB;QACF;QAEA,IAAIA,sBAAsB,UAAU;YAClC,IAAID,WAAWG,QAAQ,CAAC,MAAM;gBAC5B,IAAI,CAACC,QAAQ,CACX,IAAIhB,MACF,CAAC,oGAAoG,CAAC;gBAG1G;YACF;YAEA,IAAIiB,YAAY,CAAC;6BACM,EAAErB,kBAAkB;sCACX,EAAE,IAAI,CAACsB,YAAY,CAAC;;;;;;;;AAQ1D,CAAC;YACK,IAAIC,MAAM;YACV,KAAK,MAAMC,OAAOR,WAAY;gBAC5B,IAAIQ,QAAQ,IAAI;oBACdH,aAAa,CAAC,wCAAwC,EAAE,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC;gBAClF,OAAO,IAAIE,QAAQ,WAAW;oBAC5BH,aAAa,CAAC;;2BAEG,CAAC;gBACpB,OAAO;oBACLA,aAAa,CAAC;OACjB,EAAEE,IAAI,2BAA2B,EAAE,IAAI,CAACD,YAAY,CAAC,CAAC,EAAEE,IAAI;UACzD,EAAED,MAAM,IAAI,EAAEC,IAAI,GAAG,CAAC;gBACxB;YACF;YAEA,IAAI,CAACJ,QAAQ,CAAC,MAAMC,WAAWnB;YAC/B;QACF;IACF;IAEA,IAAIC,EAAAA,kBAAAA,UAAUI,GAAG,qBAAbJ,gBAAeM,IAAI,MAAKC,4BAAgB,CAACC,MAAM,EAAE;QACnD,IAAId,iBAAiB,IAAI,CAACyB,YAAY,EAAE;YACtCG,IAAAA,kBAAQ,EACN,CAAC,0OAA0O,CAAC;QAEhP;IACF;IAEA,IAAI,CAACL,QAAQ,CACX,MACAnB,OAAOyB,OAAO,CAACC,kCAAuB,EAAE3B,oBACxCE;AAEJ"}