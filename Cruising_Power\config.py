import json
from datetime import datetime
import sys
import logging
from loguru import logger

# Intercept all standard logging and redirect to loguru
class InterceptHandler(logging.Handler):
    def emit(self, record):
        # Get corresponding loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

# Dictionary to store overall processing results
processing_results = {
    "cabins": [],
    "timestamp": datetime.now().isoformat(),
    "overall_status": False
}


class LoggerSetup:
    """
    Class for setting up and configuring loguru logger
    """
    @staticmethod
    def setup_logger(log_dir=None):
        """
        Configure and return a logger instance
        
        Args:
            log_dir: Directory to save log files (parameter kept for backward compatibility)
            
        Returns:
            tuple: (logger instance, log_dir)
        """
        # Create timestamp for this run if not provided
        if log_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_dir = f"request_{timestamp}"  # Just use a name without creating directory
        
        # Remove all existing handlers
        logger.remove()
        
        # Add only one console handler with colors for better visibility
        logger.add(
            sys.stdout, 
            level="INFO", 
            format="<green>{time:YYYY-MM-DD HH:mm:ss,SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            colorize=True
        )
        
        # Intercept all standard logging
        logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
        
        # Replace all other handlers with our intercepting one
        for name in logging.root.manager.loggerDict.keys():
            logging.getLogger(name).handlers = []
            logging.getLogger(name).propagate = True
        
        return logger, log_dir
    
    @staticmethod
    def get_cabin_logger(log_dir, cabin_index, cabin_type):
        """
        Returns the main logger - no longer creates cabin-specific loggers
        
        Args:
            log_dir: Base log directory (parameter kept for backward compatibility)
            cabin_index: Index of the cabin (for backward compatibility)
            cabin_type: Type of the cabin (for backward compatibility)
            
        Returns:
            tuple: (logger, log_dir)
        """
        # Log cabin processing start for tracking in the single log file
        logger.info(f"Starting processing for cabin {cabin_index+1} ({cabin_type})")
        
        return logger, log_dir


class ResultProcessor:
    """
    Class for processing and managing result data
    """
    @staticmethod
    def save_results_json(filename="processing_results.json"):
        """
        Process the results data for database storage.
        Instead of saving to a JSON file, this now prepares the data for database storage.
        
        Returns:
            dict: Processing results data ready for database
        """
        # Update timestamp to current time
        processing_results["timestamp"] = datetime.now().isoformat()
        
        # Calculate overall status
        if processing_results["cabins"]:
            successful_cabins = sum(1 for cabin in processing_results["cabins"] if cabin.get("success", False))
            total_cabins = len(processing_results["cabins"])
            processing_results["overall_status"] = successful_cabins == total_cabins
        
        # Ensure all cabins have the same request_id as the main processing_results
        if "request_id" in processing_results:
            for cabin in processing_results["cabins"]:
                if "request_id" not in cabin or not cabin["request_id"]:
                    cabin["request_id"] = processing_results["request_id"]
        
        # Get session_id if available
        session_id = processing_results.get("session_id")
        
        # Try to save to database directly if we have a request_id - but now use CPResultsCache
        if "request_id" in processing_results and processing_results["request_id"]:
            try:
                # Import here to avoid circular imports
                try:
                    from db.cruising_power_provider import CPResultsCache, save_cruising_power_results
                    
                    # Only save if this session hasn't been processed yet
                    request_id = processing_results["request_id"]
                    if session_id and not CPResultsCache.is_processed(session_id):
                        save_cruising_power_results(processing_results, request_id, session_id)
                        logger.info(f"Results saved to database with request_id: {request_id}")
                    else:
                        logger.info(f"Skipping duplicate database save - already processed")
                except ImportError:
                    # Fall back to old method if import fails
                    from Core.database_helper import save_cruising_power_results
                    request_id = processing_results["request_id"]
                    save_cruising_power_results(processing_results, request_id, session_id)
                logger.info(f"Results saved to database with request_id: {request_id}")
            except Exception as e:
                logger.error(f"Error saving results to database: {e}")
                
        # Return the processed results
        return processing_results
    
    @staticmethod
    def add_cabin_result(cabin_info):
        """
        Add cabin processing result to the overall results
        
        Args:
            cabin_info: Dictionary with cabin processing information
        """
        # Ensure cabin has request_id if processing_results has it
        if "request_id" in processing_results and "request_id" not in cabin_info:
            cabin_info["request_id"] = processing_results["request_id"]
        
        # Ensure cabin has provider set to Cruising Power
        if "provider" not in cabin_info:
            cabin_info["provider"] = "Cruising Power"
            
        processing_results["cabins"].append(cabin_info)
    
    @staticmethod
    def reset_processing_results():
        """
        Reset the processing results before starting a new process
        """
        global processing_results
        processing_results = {
            "cabins": [],
            "timestamp": datetime.now().isoformat(),
            "overall_status": False,
            "provider": "Cruising Power",
            "request_id": None  # Will be set by main function
        }


# Maintain backward compatibility with the old function names
def setup_logger(log_dir=None):
    """
    Wrapper function for backward compatibility
    """
    return LoggerSetup.setup_logger(log_dir)

def get_cabin_logger(log_dir, cabin_index, cabin_type):
    """
    Wrapper function for backward compatibility
    """
    return LoggerSetup.get_cabin_logger(log_dir, cabin_index, cabin_type)

def save_results_json(filename="processing_results.json"):
    """
    Wrapper function for backward compatibility
    """
    return ResultProcessor.save_results_json(filename)

def add_cabin_result(cabin_info):
    """
    Wrapper function for backward compatibility
    """
    ResultProcessor.add_cabin_result(cabin_info)

def reset_processing_results():
    """
    Wrapper function for backward compatibility
    """
    ResultProcessor.reset_processing_results() 