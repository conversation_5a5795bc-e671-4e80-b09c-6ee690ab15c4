"""
Provider Manager Module

This module handles operations related to cruise providers in the database.
"""

import logging
from db_service import get_db_connection, db_connection

logger = logging.getLogger("database.provider_manager")

async def get_all_providers():
    """
    Get all providers from the database
    Returns a list of all available providers
    """
    try:
        async with db_connection() as conn:
        
            rows = await conn.fetch("SELECT provider_id, name, agency, description FROM providers")
            providers = []

            for row in rows:
                provider = {
                    "provider_id": row['provider_id'],
                    "name": row['name'],
                    "agency": row['agency'],
                    "description": row['description']
                }
                providers.append(provider)

            return providers

    except Exception as e:
        logger.error(f"Error fetching providers: {e}")
        return []

async def get_provider_by_name(name):
    """
    Get a provider by its name
    Returns the provider details or None if not found
    """
    try:
        async with db_connection() as conn:
        
            row = await conn.fetchrow("SELECT provider_id, name, agency, description FROM providers WHERE name = $1", name)

            if row:
                provider = {
                    "provider_id": row['provider_id'],
                    "name": row['name'],
                    "agency": row['agency'],
                    "description": row['description']
                }
                return provider
            else:
                return None

    except Exception as e:
        logger.error(f"Error fetching provider by name: {e}")
        return None

async def update_provider(provider_id, provider_data):
    """
    Update a provider's information
    provider_data should be a dict with keys: name, agency, description
    Returns True if successful, False otherwise
    """
    try:
        async with db_connection() as conn:
        
            # Build the update statement dynamically based on provided fields
            update_parts = []
            params = []
            param_idx = 1
        
            if provider_data.get('name'):
                update_parts.append(f"name = ${param_idx}")
                params.append(provider_data['name'])
                param_idx += 1

            if provider_data.get('agency'):
                update_parts.append(f"agency = ${param_idx}")
                params.append(provider_data['agency'])
                param_idx += 1

            if 'description' in provider_data:  # Allow empty string for description
                update_parts.append(f"description = ${param_idx}")
                params.append(provider_data['description'])
                param_idx += 1

            if not update_parts:
                return True  # Nothing to update

            # Add the provider_id to params
            params.append(provider_id)

            # Create and execute the update statement
            update_statement = f"UPDATE providers SET {', '.join(update_parts)} WHERE provider_id = ${param_idx}"
            await conn.execute(update_statement, *params)

            return True

    except Exception as e:
        logger.error(f"Error updating provider: {e}")
        return False


async def delete_provider(provider_id):
    """
    Delete a provider from the database
    Returns True if successful, False otherwise
    """
    try:
        async with db_connection() as conn:
        
        # Check if provider exists first
            provider_count = await conn.fetchval("SELECT COUNT(*) FROM providers WHERE provider_id = $1", provider_id)
            if provider_count == 0:
                logger.warning(f"Provider with ID {provider_id} not found")
                return False

            # Delete the provider
            result = await conn.execute("DELETE FROM providers WHERE provider_id = $1", provider_id)

            # Check if any rows were affected (asyncpg returns "DELETE X" where X is the count)
            success = result != "DELETE 0"
            if success:
                logger.info(f"Provider {provider_id} deleted successfully")

            return success

    except Exception as e:
        logger.error(f"Error deleting provider: {e}")
        return False