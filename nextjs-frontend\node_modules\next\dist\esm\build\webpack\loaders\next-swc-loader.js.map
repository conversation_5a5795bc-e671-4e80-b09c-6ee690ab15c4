{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "names": ["isWasm", "transform", "getLoaderSWCOptions", "path", "isAbsolute", "loaderTransform", "parentTrace", "source", "inputSourceMap", "nextConfig", "filename", "resourcePath", "loaderOptions", "getOptions", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "serverComponents", "isReactServerLayer", "esm", "isPageFile", "startsWith", "relativeFilePathFromRoot", "relative", "swcOptions", "development", "mode", "modularizeImports", "optimizePackageImports", "experimental", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "react", "Object", "prototype", "hasOwnProperty", "call", "swcSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "then", "output", "eliminatedPackages", "pkg", "parse", "add", "code", "map", "EXCLUDED_PATHS", "pitch", "callback", "async", "process", "versions", "pnp", "test", "loaders", "length", "loaderIndex", "loaderSpan", "currentTraceSpan", "addDependency", "r", "sw<PERSON><PERSON><PERSON><PERSON>", "inputSource", "transformedSource", "outputSourceMap", "err", "raw"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,GAGA,SAASA,MAAM,EAAEC,SAAS,QAAQ,YAAW;AAC7C,SAASC,mBAAmB,QAAQ,oBAAmB;AACvD,OAAOC,QAAQC,UAAU,QAAQ,OAAM;AAkBvC,eAAeC,gBAEbC,WAAgB,EAChBC,MAAe,EACfC,cAAoB;QAiCMC,0BACZA,2BAESA;IAlCvB,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,IAAIC,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAM,EACJC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfT,UAAU,EACVU,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,kBAAkB,EAClBC,GAAG,EACJ,GAAGZ;IACJ,MAAMa,aAAaf,SAASgB,UAAU,CAACV;IACvC,MAAMW,2BAA2BxB,KAAKyB,QAAQ,CAACb,SAASL;IAExD,MAAMmB,aAAa3B,oBAAoB;QACrCc;QACAC;QACAP;QACAI;QACAW;QACAK,aAAa,IAAI,CAACC,IAAI,KAAK;QAC3Bb;QACAc,iBAAiB,EAAEvB,8BAAAA,WAAYuB,iBAAiB;QAChDC,sBAAsB,EAAExB,+BAAAA,2BAAAA,WAAYyB,YAAY,qBAAxBzB,yBAA0BwB,sBAAsB;QACxEE,UAAU,EAAE1B,+BAAAA,4BAAAA,WAAYyB,YAAY,qBAAxBzB,0BAA0B0B,UAAU;QAChDC,eAAe,EAAE3B,8BAAAA,WAAY4B,QAAQ;QACrCC,mBAAmB,EAAE7B,+BAAAA,4BAAAA,WAAYyB,YAAY,qBAAxBzB,0BAA0B6B,mBAAmB;QAClEnB;QACAC;QACAC;QACAM;QACAL;QACAC;QACAC;IACF;IAEA,MAAMe,sBAAsB;QAC1B,GAAGV,UAAU;QACbnB;QACAF,gBAAgBA,iBAAiBgC,KAAKC,SAAS,CAACjC,kBAAkBkC;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBpC;IAClB;IAEA,IAAI,CAAC6B,oBAAoB/B,cAAc,EAAE;QACvC,OAAO+B,oBAAoB/B,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAACuB,IAAI,IACTQ,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAAC9C,SAAS,IACjCsC,oBAAoBQ,GAAG,CAAC9C,SAAS,CAAC+C,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCb,oBAAoBQ,GAAG,CAAC9C,SAAS,CAAC+C,KAAK,EACvC,gBAEF;QACAT,oBAAoBQ,GAAG,CAAC9C,SAAS,CAAC+C,KAAK,CAAClB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,MAAMsB,UAAU/C,YAAYgD,UAAU,CAAC;IACvC,OAAOD,QAAQE,YAAY,CAAC,IAC1BtD,UAAUM,QAAegC,qBAAqBiB,IAAI,CAAC,CAACC;YAClD,IAAIA,OAAOC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,EAAE;gBACxD,KAAK,MAAMC,OAAOnB,KAAKoB,KAAK,CAACH,OAAOC,kBAAkB,EAAG;oBACvD,IAAI,CAACA,kBAAkB,CAACG,GAAG,CAACF;gBAC9B;YACF;YACA,OAAO;gBAACF,OAAOK,IAAI;gBAAEL,OAAOM,GAAG,GAAGvB,KAAKoB,KAAK,CAACH,OAAOM,GAAG,IAAIrB;aAAU;QACvE;AAEJ;AAEA,MAAMsB,iBACJ;AAEF,OAAO,SAASC;IACd,MAAMC,WAAW,IAAI,CAACC,KAAK;IACzB,CAAA;QACA,IACE,kDAAkD;QAClD,CAACC,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACN,eAAeO,IAAI,CAAC,IAAI,CAAC5D,YAAY,KACtC,IAAI,CAAC6D,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5CtE,WAAW,IAAI,CAACO,YAAY,KAC5B,CAAE,MAAMX,UACR;YACA,MAAM2E,aAAa,IAAI,CAACC,gBAAgB,CAACtB,UAAU,CAAC;YACpD,IAAI,CAACuB,aAAa,CAAC,IAAI,CAAClE,YAAY;YACpC,OAAOgE,WAAWpB,YAAY,CAAC,IAC7BlD,gBAAgB+C,IAAI,CAAC,IAAI,EAAEuB;QAE/B;IACF,CAAA,IAAKnB,IAAI,CAAC,CAACsB;QACT,IAAIA,GAAG,OAAOZ,SAAS,SAASY;QAChCZ;IACF,GAAGA;AACL;AAEA,eAAe,SAASa,UAEtBC,WAAmB,EACnBxE,cAAmB;IAEnB,MAAMmE,aAAa,IAAI,CAACC,gBAAgB,CAACtB,UAAU,CAAC;IACpD,MAAMY,WAAW,IAAI,CAACC,KAAK;IAC3BQ,WACGpB,YAAY,CAAC,IACZlD,gBAAgB+C,IAAI,CAAC,IAAI,EAAEuB,YAAYK,aAAaxE,iBAErDgD,IAAI,CACH,CAAC,CAACyB,mBAAmBC,gBAAqB;QACxChB,SAAS,MAAMe,mBAAmBC,mBAAmB1E;IACvD,GACA,CAAC2E;QACCjB,SAASiB;IACX;AAEN;AAEA,oCAAoC;AACpC,OAAO,MAAMC,MAAM,KAAI"}