import React, { useState, useEffect } from 'react';
import BookingMetrics from './BookingMetrics';
import ScreenshotDisplay from './ScreenshotDisplay';
import { ProviderType } from './ProviderSelector';
import { submitValidation, getDiscountData, CabinDiscount, API_BASE_URL } from '../../services/api';
import { authFetch } from '../../services/auth';
import { ConfigState } from '../ui/Config';
import TextInputDisplay from './TextInputDisplay';
import VideosTab from './VideosTab';
import validationCategories from '../../data/validationCategories';

// New component for Discounts tab
const DiscountsDisplay: React.FC<{ provider: ProviderType; requestId: string; sessionId?: string }> = ({ provider, requestId, sessionId }) => {
  const [discountData, setDiscountData] = useState<CabinDiscount[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchDiscountData = async () => {
      try {
        setLoading(true);
        const data = await getDiscountData(requestId, sessionId);
        if (data.success) {
          setDiscountData(data.cabin_discounts || []);
        } else {
          setError(data.error || 'Failed to load discount data');
        }
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        setLoading(false);
      }
    };

    if (requestId) {
      fetchDiscountData();
    }
  }, [requestId, sessionId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 p-4">
        <p className="font-bold">Error loading discount data</p>
        <p>{error}</p>
      </div>
    );
  }

  if (discountData.length === 0) {
    return (
      <div className="p-2">
        <h4 className="text-md font-medium mb-2">Discount Information</h4>
        <div className="p-4 bg-gray-50 rounded-md border border-gray-200">
          <p className="text-sm text-gray-600">
            No discount information available for this booking.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-2">
      <h4 className="text-md font-medium mb-2">Discount Information</h4>
      <div className="space-y-4">
        {discountData.map((cabin, index) => (
          <div key={index} className="p-4 bg-gray-50 rounded-md border border-gray-200">
            <div className="font-semibold mb-2 text-blue-600">
              Cabin {cabin.cabin_number}
            </div>
            <div className="whitespace-pre-wrap text-sm">
              {cabin.discount_text}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// New component for Promotions tab
const PromotionsDisplay: React.FC<{ provider: ProviderType; requestId: string }> = ({ provider, requestId }) => {
  const [templates, setTemplates] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        
        // Make authenticated request to our backend API with provider filter
        const response = await authFetch(`${API_BASE_URL}/api/templates?provider=${encodeURIComponent(provider)}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch templates');
        }
        
        const data = await response.json();
        
        // Extract templates from the data property in the response
        if (data.success && Array.isArray(data.data)) {
          setTemplates(data.data);
        } else {
          throw new Error('Invalid response format');
        }
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        setLoading(false);
      }
    };

    fetchTemplates();
  }, [provider]); // Add provider as dependency so templates reload when provider changes

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 p-4">
        <p className="font-bold">Error loading templates</p>
        <p>{error}</p>
      </div>
    );
  }

  if (templates.length === 0) {
    return (
      <div className="p-4 bg-yellow-50 text-yellow-700 rounded-lg border border-yellow-200">
        <p className="font-bold">No templates available</p>
        <p>No promotion templates found for provider: {provider}</p>
      </div>
    );
  }

  // Find the selected template
  const selectedTemplateData = templates.find(template => template.id.toString() === selectedTemplate);

  return (
    <div className="p-2">
      <div className="mb-2">
        <p className="text-xs text-gray-600 mb-2">
          Provider: {provider}
        </p>
      </div>
      
      <div className="mb-4">
        <label htmlFor="template-select" className="block text-md font-semibold text-blue-900 mb-1">
          Select Template ID
        </label>
        <select
          id="template-select"
          value={selectedTemplate}
          onChange={(e) => setSelectedTemplate(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">--Select--</option>
          {templates.map((template) => (
            <option key={template.id} value={template.id}>
              {template.template_id}
            </option>
          ))}
        </select>
      </div>

      {selectedTemplateData && (
        <div className="">
          <h4 className="text-md font-medium mb-2">Template Content</h4>
          <div className="p-4 bg-gray-50 rounded-md border border-gray-200 whitespace-pre-wrap text-sm">
            {selectedTemplateData.content}
          </div>
          <div className="mt-2 text-xs text-gray-500">
            <p>Template ID: {selectedTemplateData.template_id}</p>
            <p>Provider: {selectedTemplateData.provider}</p>
          </div>
        </div>
      )}
    </div>
  );
};

interface ValidationModalProps {
  onValidationDone?: () => void;
  open: boolean;
  onClose: () => void;
  provider: ProviderType;
  requestId: string;
  sessionId: string;
  config?: ConfigState; // Add config prop
  extractedDetails?: any; // Add prop for extracted details
  onReprocess?: () => void; // Optional callback for reprocessing
  onResetBooking?: () => void; // Optional callback to reset booking process
  forceValidation?: boolean; // Optional prop to force validation
}
 

const ValidationModal: React.FC<ValidationModalProps> = ({
  open,
  onClose,
  provider,
  requestId,
  sessionId,
  config,
  extractedDetails,
  onReprocess,
  onResetBooking,
  onValidationDone,
  forceValidation = true // Default to true to force validation
}) => {
  const [validationStatus, setValidationStatus] = useState<'correct' | 'incorrect' | ''>('');
  const [featureStatus, setFeatureStatus] = useState<'correct' | 'incorrect' | ''>('');
  const [selectedIssues, setSelectedIssues] = useState<{category: string; code: string}[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isReprocessing, setIsReprocessing] = useState(false);
  const [formError, setFormError] = useState('');
  
  const [activeTab, setActiveTab] = useState<'screenshots' | 'videos' | 'textInput' | 'promotions' | 'discounts'>('screenshots');
  
  // Check if provider is Studio to show discounts tab
  const isStudioProvider = provider.toLowerCase().includes('studio');
  
  // Form validation state
  const [isFormValid, setIsFormValid] = useState(false);
  
  // Ensure activeTab is valid for the current provider
  useEffect(() => {
    if (!isStudioProvider && activeTab === 'discounts') {
      setActiveTab('screenshots'); // Fallback to screenshots tab
    }
  }, [isStudioProvider, activeTab]);
  
  // Prevent closing with Escape key when validation is forced
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && forceValidation) {
        event.preventDefault();
        setFormError('You must complete and submit the validation form before closing');
      }
    };

    if (open) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [open, forceValidation]);
  
  // Check form validity whenever form fields change
  useEffect(() => {
    const checkFormValidity = () => {
      // Both "Request Status as per Flow" and "Request Status as per Feature" are mandatory
      if (!validationStatus) {
        setIsFormValid(false);
        return;
      }
      
      // If "Request Status as per Flow" is correct, no need for feature status
      if (validationStatus === 'correct') {
        setIsFormValid(true);
        return;
      }
      
      // If "Request Status as per Flow" is incorrect, "Request Status as per Feature" is required
      if (validationStatus === 'incorrect' && !featureStatus) {
        setIsFormValid(false);
        return;
      }
      
      // If status is incorrect, at least one issue must be selected
      if (validationStatus === 'incorrect' && selectedIssues.length === 0) {
        setIsFormValid(false);
        return;
      }
      // If "Special Comment" issue selected, comment becomes mandatory
      const requiresComment = selectedIssues.some(i => i.code === 'SPECIAL_COMMENT');
      if (requiresComment && !comment.trim()) {
        setIsFormValid(false);
        return;
      }
      
      // All required fields are filled
      setIsFormValid(true);
    };
    
    checkFormValidity();
  }, [validationStatus, featureStatus, comment, selectedIssues]);
  
  // Auto-deselect "Request Status as per Feature" and clear issues/comments when "Flow" is correct
  useEffect(() => {
    if (validationStatus === 'correct') {
      setFeatureStatus('');
      setSelectedIssues([]);
      setComment('');
      setSelectedCategory('');
    }
  }, [validationStatus]);
  
  if (!open) return null;
 
  const handleSubmit = async () => {
    // Reset error state
    setFormError('');
   
    // Validate form
    if (!validationStatus) {
      setFormError('Please select a validation status');
      return;
    }
   
    if (validationStatus === 'incorrect') {
      if (!featureStatus) {
        setFormError('Please select a feature status');
        return;
      }
     
      if (selectedIssues.length === 0) {
        setFormError('Please choose at least one issue type');
        return;
      }
      const requiresComment = selectedIssues.some(i => i.code === 'SPECIAL_COMMENT');
      if (requiresComment && !comment.trim()) {
        setFormError('Comment is required for Special Comment');
        return;
      }
    }
   
    // Prepare payload and submit validation
    const payload = {
      session_id: sessionId,
      provider,
      request_id: requestId,
      val_status: 'validated',
      val_mark: validationStatus === 'correct' ? '1' : '0',
      as_per_feature: validationStatus === 'incorrect' ? (featureStatus === 'correct' ? '1' : '0') : '',
      val_comment: comment,
      issue_codes: validationStatus === 'incorrect' ? selectedIssues : [],
    };
    setIsSubmitting(true); //HELLO
    try {
      const result = await submitValidation(payload);
      alert(result.message || 'Validation submitted successfully');
      onClose();
      if (onValidationDone) {
        onValidationDone();
      }
    } catch (error) {
      setFormError(error instanceof Error ? error.message : 'Submission failed');
    } finally {
      setIsSubmitting(false);
    }
  };
 
  const handleReprocess = () => {
    // When validation is forced and form is not valid, prevent reprocessing
    // Removed the form validation check to always allow reprocessing
    
    // Set reprocessing state to true
    setIsReprocessing(true);
    
    // Call the provided reprocess callback function
    if (onReprocess) {
      onReprocess();
    } else {
      // Redirect to post-quote-extraction stage
      console.log('Redirecting to post-quote-extraction stage...');
      // TODO: Implement actual navigation logic
    }
    onClose();
  };

  // Issue helpers
  const handleAddIssue = (value: string) => {
    if (!value) return;
    const [cat, code] = value.split('|');
    if (selectedIssues.some(i => i.code === code)) return;
    setSelectedIssues(prev => [...prev, { category: cat, code }]);
  };
  const handleRemoveIssue = (code: string) => {
    setSelectedIssues(prev => prev.filter(i => i.code !== code));
  };
  const getIssueLabel = (code: string) => {
    for (const cat of validationCategories) {
      const found = cat.issues.find(i => i.code === code);
      if (found) return found.label;
    }
    return code;
  };

  // Handle close attempts when validation is forced
  const handleCloseAttempt = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    
    if (!forceValidation) {
      onClose();
      return;
    }
    // Display warning message if trying to close without submitting
    setFormError('You must complete and submit the validation form before closing');
  };
 
  return (
    <>
    <div 
      className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-20 p-2" 
      onClick={forceValidation ? (e) => e.stopPropagation() : onClose}
    >
      <div
        className="bg-white w-full max-w-[1650px] rounded-lg overflow-hidden max-h-[90vh] flex flex-col border-[4px] border-red-700 shadow-[0_0_15px_5px_rgba(255,0,0,0.1)]"
        onClick={e => e.stopPropagation()}
      >
        <div className="p-4 py-2 bg-gradient-to-r from-red-700 via-red-600 to-red-700 flex flex-col sm:flex-row justify-between items-center">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="text-2xl">⚠️</span>
              <h3 className="text-2xl font-bold text-white">Human Validation Required</h3>
            </div>
              <p className="text-sm text-white font-medium mt-2 bg-red-800 px-3 py-1 rounded-md inline-block">
                Critical Step: Please validate AI's work carefully before submitting to portal
              </p>        
          </div>
          <button
            onClick={handleCloseAttempt}
            className={`text-white/90 ml-auto ${forceValidation ? 'cursor-not-allowed opacity-50' : ''}`}
            title={forceValidation ? "Complete validation form to close" : "Close"}
          >
            ✕
          </button>
        </div>
        <div className="p-4 py-1 flex-1 overflow-auto">
          <div className="flex flex-col gap-2">
            {/* Process Metrics - Full Width Top */}
            <div className="w-full bg-white rounded-lg p-0">
              <BookingMetrics
                provider={provider}
                requestId={requestId}
                sessionId={sessionId}
                config={config}
                cruiseDetails={extractedDetails}
              />
            </div>
           
            {/* Screenshots and Validation Form Side by Side */}
            <div className="grid md:grid-cols-4 gap-2">
              {/* Screenshots/Extracted Info - 1/3 Width */}
              <div className="md:col-span-2">
                <div className="border border-teal-800 overflow-hidden rounded-lg">
                  {/* Tab Navigation */}
                  <div className="flex border-b border-gray-300">
                    <button
                      className={`flex-1 py-2 px-4 font-medium text-sm ${activeTab === 'screenshots' ? 'bg-blue-900 text-white border-blue-500' : 'text-gray-600 hover:bg-gray-50'}`}
                      onClick={() => setActiveTab('screenshots')}
                    >
                      <h3 className="text-lg">Screenshots</h3>
                    </button>
                    <button
                      className={`flex-1 py-2 px-4 font-medium text-sm ${activeTab === 'videos' ? 'bg-blue-900 text-white border-blue-500' : 'text-gray-600 hover:bg-gray-50'}`}
                      onClick={() => setActiveTab('videos')}
                    >
                      <h3 className="text-lg">Videos</h3>
                    </button>
                    <button
                      className={`flex-1 py-2 px-4 font-medium text-sm ${activeTab === 'textInput' ? 'bg-blue-900 text-white border-blue-500' : 'text-gray-600 hover:bg-gray-50'}`}
                      onClick={() => setActiveTab('textInput')}
                    >
                      <h3 className="text-lg">Quote Input</h3>
                    </button>
                    <button
                      className={`flex-1 py-2 px-4 font-medium text-sm ${activeTab === 'promotions' ? 'bg-blue-900 text-white border-blue-500' : 'text-gray-600 hover:bg-gray-50'}`}
                      onClick={() => setActiveTab('promotions')}
                    >
                      <h3 className="text-lg">Promotions</h3>
                    </button>
                    {isStudioProvider && (
                      <button
                        className={`flex-1 py-2 px-4 font-medium text-sm ${activeTab === 'discounts' ? 'bg-blue-900 text-white border-blue-500' : 'text-gray-600 hover:bg-gray-50'}`}
                        onClick={() => setActiveTab('discounts')}
                      >
                        <h3 className="text-lg">Discounts</h3>
                      </button>
                    )}
                    {/* <button
                      className={`flex-1 py-2 px-4 font-medium text-sm ${activeTab === 'text' ? 'bg-blue-900 text-white border-blue-500' : 'text-gray-600 hover:bg-gray-50'}`}
                      onClick={() => setActiveTab('text')}
                    >
                      <h3 className="text-lg">Text Input</h3>
                    </button> */}
                  </div>
                  
                  {/* Tab Content */}
                  <div className="p-1">
                    {/* Screenshot Display - Always mounted, visibility controlled */}
                    <div className={activeTab === 'screenshots' ? 'block' : 'hidden'}>
                      <ScreenshotDisplay
                        provider={provider}
                        requestId={requestId}
                        sessionId={sessionId}
                      />
                    </div>
                    
                    {/* Videos Display - Always mounted, visibility controlled */}
                    <div className={activeTab === 'videos' ? 'block' : 'hidden'}>
                      <VideosTab
                        provider={provider}
                        requestId={requestId}
                        sessionId={sessionId}
                      />
                    </div>
                    
                    {/* Text Input Display - Always mounted, visibility controlled */}
                    <div className={activeTab === 'textInput' ? 'block' : 'hidden'}>
                      <TextInputDisplay
                        provider={provider}
                        requestId={requestId}
                      />
                    </div>
                    
                    {/* Promotions Display - Always mounted, visibility controlled */}
                    <div className={activeTab === 'promotions' ? 'block' : 'hidden'}>
                      <PromotionsDisplay
                        provider={provider}
                        requestId={requestId}
                      />
                    </div>
                    
                    {/* Discounts Display - Only for Studio providers */}
                    {isStudioProvider && (
                      <div className={activeTab === 'discounts' ? 'block' : 'hidden'}>
                        <DiscountsDisplay
                          provider={provider}
                          requestId={requestId}
                          sessionId={sessionId}
                        />
                      </div>
                    )}
                    {/* <div className={activeTab === 'text' ? 'block' : 'hidden'}>
                    
                      <div>
                        input text from backend
                      </div>
                    </div> */}
                  </div>
                </div>
              </div>
             
              {/* Validation Form - 2/3 Width */}
              <div className="md:col-span-2 bg-gradient-to-b from-gray-50 to-white drop-shadow-md border border-blue-900 p-4 rounded-lg ">
                <div
                  className="p-3 rounded-lg -mt-4 -mx-4 mb-4 "
                  
                >
                  <h3 className="text-black text-2xl font-semibold">Validation Form</h3>
                  <p className="text-Blue-0900 text-sm px-1 py-0.5 drop-shadow-xl bg-green-200 mt-2 rounded-lg border border-blue-800 inline-block font-semibold">For Internal Beta Testing</p>
                </div>
                <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
                  {/* Request Status as per Flow */}
                  <div className="flex bg-white p-3 gap-5">
                    <label className="block text-md font-medium text-gray-700">
                      Request Status as per Flow :<span className="text-red-500 ml-1">*</span>
                    </label>
                    <div className="flex items-center space-x-10">
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          name="validationStatus"
                          checked={validationStatus === 'correct'}
                          onChange={() => setValidationStatus('correct')}
                          className="h-5 w-5 text-green-600 focus:ring-green-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm font-medium text-gray-700">Correct</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          name="validationStatus"
                          checked={validationStatus === 'incorrect'}
                          onChange={() => setValidationStatus('incorrect')}
                          className="h-5 w-5 text-red-600 focus:ring-red-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm font-medium text-gray-700">Incorrect</span>
                      </label>
                    </div>
                  </div>
                  {/* Request Status as per Feature */}
                  <div className={`bg-white p-3 flex gap-8 ${validationStatus === 'correct' ? 'opacity-50 pointer-events-none' : ''}`}>
                    <label className="block text-md font-medium text-gray-700">
                      Request Status as per Feature :<span className="text-red-500 ml-1">*</span>
                    </label>
                    <div className="flex items-center space-x-6">
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          name="featureStatus"
                          checked={featureStatus === 'correct'}
                          onChange={() => setFeatureStatus('correct')}
                          disabled={validationStatus === 'correct'}
                          className="h-5 w-5 text-green-600 focus:ring-green-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm font-medium text-gray-700">Correct</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          name="featureStatus"
                          checked={featureStatus === 'incorrect'}
                          onChange={() => setFeatureStatus('incorrect')}
                          disabled={validationStatus === 'correct'}
                          className="h-5 w-5 text-red-600 focus:ring-red-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm font-medium text-gray-700">Incorrect</span>
                      </label>
                    </div>
                  </div>
                  {/* Category Picker */}
                  {validationStatus === 'incorrect' && (
                    <div className="bg-white p-3 flex flex-col gap-2 mb-3">
                      <label className="block text-md font-medium text-gray-700">
                        Category <span className="text-red-500">*</span>
                      </label>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {validationCategories.map(cat => (
                          <button
                            key={cat.category}
                            type="button"
                            onClick={() => setSelectedCategory(cat.category)}
                            className={`px-3 py-1 rounded-full border text-sm ${selectedCategory === cat.category ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'}`}
                          >
                            {cat.category}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Issue Type */}
                  {validationStatus === 'incorrect' && (
                    <div className="bg-white p-3 flex flex-col gap-2 mb-3">
                      <label className="block text-md font-medium text-gray-700">
                        Issue Type <span className="text-red-500">*</span>
                      </label>
                      <select
                        value=""
                        disabled={!selectedCategory}
                        onChange={(e) => handleAddIssue(e.target.value)}
                        className={`w-full p-2 border rounded-md focus:outline-none transition-colors duration-150 
                          ${!selectedCategory ? 'bg-gray-100 text-gray-500 border-gray-300 cursor-not-allowed' : 'bg-white border-blue-600 focus:ring-2 focus:ring-blue-500'}`}
                      >
                        <option value="">--Select Issue--</option>
                        {(
                          validationCategories.find(cat => cat.category === selectedCategory)?.issues || []
                        ).map((issue) => (
                          <option key={issue.code} value={`${selectedCategory}|${issue.code}`}>
                            {issue.label}
                          </option>
                        ))}
                      </select>
                      {selectedIssues.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {selectedIssues.map((iss) => (
                            <span key={iss.code} className="flex items-center bg-blue-600/10 text-blue-900 px-2 py-0.5 rounded-full text-xs font-medium shadow-sm">
                              {getIssueLabel(iss.code)}
                              <button type="button" onClick={() => handleRemoveIssue(iss.code)} className="ml-1 text-red-600 hover:text-red-800">×</button>
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Comments */}
                  <div className="mb-5 bg-white p-2 rounded-lg border border-gray-400">
                    <label className="block text-md font-medium text-gray-700 mb-1 ">
                      Comments {(validationStatus === 'incorrect' && selectedIssues.some(i => i.code === 'SPECIAL_COMMENT')) && <span className="text-red-500">*</span>}
                    </label>
                    <textarea
                      value={comment}
                      onChange={(e) => setComment(e.target.value)}
                      rows={4}
                      className="w-full rounded-md text-md focus:outline-none"
                      placeholder="Add Your comment here, make sure to be onpoint and specific !!"
                    />
                  </div>
                  
                  {/* Error message */}
                  {formError && (
                    <div className="mb-4 p-2 bg-red-50 text-red-600 border border-red-200 rounded">
                      {formError}
                    </div>
                  )}
                  
                  {/* Submit Button */}
                  <div className="flex justify-end space-x-4">
                    <button
                      type="button"
                      onClick={handleReprocess}
                      // Removed disabled attribute to always keep button active
                      className="bg-blue-900 hover:bg-blue-600 text-white font-semibold py-1.5 px-3 rounded-lg flex items-center justify-center gap-2"
                    >
                      {isReprocessing ? (
                        <>
                          <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Reprocessing...
                        </>
                      ) : (
                        <>Reprocess</>
                      )}
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting || !isFormValid}
                      className="bg-blue-900 hover:bg-blue-600 text-white font-semibold py-1.5 px-3 rounded-lg flex items-center justify-center gap-2 disabled:opacity-70 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? 'Submitting...' : 'Submit Validation'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
};
 
export default ValidationModal;