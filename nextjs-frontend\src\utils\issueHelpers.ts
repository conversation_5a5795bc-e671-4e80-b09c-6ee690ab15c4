import validationCategories from '../data/validationCategories';

export interface IssueCodeEntry { category: string; code: string }

/**
 * Find the human-friendly label for an issue code.
 */
export const getIssueLabel = (code: string): string => {
  for (const cat of validationCategories) {
    const found = cat.issues.find((i) => i.code === code);
    if (found) return found.label;
  }
  return code;
};

/**
 * Parse whatever comes from the backend (array | JSON string | anything) into a
 * normalised array of {category, code}.
 */
export const parseIssueCodes = (raw: any): IssueCodeEntry[] => {
  if (!raw) return [];

  // Already an array of objects
  if (Array.isArray(raw)) {
    return raw.filter((e) => e && typeof e.code === 'string' && typeof e.category === 'string');
  }

  // JSON string?
  if (typeof raw === 'string') {
    try {
      const parsed = JSON.parse(raw);
      if (Array.isArray(parsed)) {
        return parsed.filter((e) => e && typeof e.code === 'string' && typeof e.category === 'string');
      }
    } catch (_) {
      /* swallow */
    }
  }

  return [];
};

/**
 * Join labels with semicolon for CSV.
 */
export const formatIssueLabels = (entries: IssueCodeEntry[]): string =>
  entries.map((e) => getIssueLabel(e.code)).join(';');
