import os
import io
import logging
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any, AsyncGenerator
from datetime import datetime, timedelta
from config.minio_config import minio_config

logger = logging.getLogger(__name__)

class MediaService:
    """Service for handling media storage operations with MinIO using aioboto3"""
    
    def __init__(self):
        self.config = minio_config
    
    async def upload_screenshot(self, image_data: bytes, provider: str, session_id: str, 
                         screenshot_type: str, cabin_id: Optional[int] = None,
                         file_name: Optional[str] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload screenshot to MinIO
        
        Args:
            image_data: Screenshot binary data
            provider: Provider name (ncl, cruising_power, studio)
            session_id: Session identifier
            screenshot_type: Type of screenshot (category_selection, rate_selection, etc.)
            cabin_id: Optional cabin identifier
            file_name: Optional custom filename
            
        Returns:
            Tuple of (success: bool, metadata: dict)
        """
        try:
            # Validate file size
            if len(image_data) > self.config.max_file_size['screenshot']:
                raise ValueError(f"Screenshot too large: {len(image_data)} bytes")
            
            # Generate object key
            object_key = self.config.generate_object_key(
                provider, session_id, 'screenshots', 'png', cabin_id, screenshot_type
            )
            
            # Get bucket name
            bucket_name = self.config.get_bucket_name('screenshots')
            
            # Prepare metadata
            metadata = {
                'provider': provider,
                'session_id': session_id,
                'screenshot_type': screenshot_type,
                'cabin_id': str(cabin_id) if cabin_id else '',
                'uploaded_at': datetime.now().isoformat()
            }
            
            # Upload to MinIO using the client pool
            async with self.config.client() as s3_client:
                await s3_client.put_object(
                    Bucket=bucket_name,
                    Key=object_key,
                    Body=image_data,
                    ContentType='image/png',
                    Metadata=metadata
                )
            
            # Generate public URL
            minio_url = f"{self.config.get_endpoint_url()}/{bucket_name}/{object_key}"
            
            # Generate metadata for database in expected format
            result_metadata = {
                'url': minio_url,
                'bucket': bucket_name,
                'object_key': object_key,
                'file_size_kb': len(image_data) // 1024
            }
            
            logger.info(f"Screenshot uploaded successfully: {object_key}")
            return True, result_metadata
            
        except Exception as e:
            logger.error(f"Failed to upload screenshot: {e}")
            return False, {'error': str(e)}
    
    async def upload_video(self, video_data: bytes, provider: str, session_id: str, video_type: str,
                    cabin_id: Optional[int] = None, file_name: Optional[str] = None, 
                    format: str = 'webm', duration: int = 0) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload video to MinIO
        
        Args:
            video_data: Video binary data
            provider: Provider name (ncl, cruising_power, studio)
            session_id: Session identifier
            video_type: Type of video (booking_process, verification, etc.)
            cabin_id: Optional cabin identifier
            file_name: Optional custom filename
            format: Video format (webm, mp4)
            duration: Video duration in seconds
            
        Returns:
            Tuple of (success: bool, metadata: dict)
        """
        try:
            # Validate file size
            if len(video_data) > self.config.max_file_size['video']:
                raise ValueError(f"Video too large: {len(video_data)} bytes")
            
            # Generate object key
            object_key = self.config.generate_object_key(
                provider, session_id, 'videos', format, cabin_id, video_type
            )
            
            # Get bucket name
            bucket_name = self.config.get_bucket_name('videos')
            
            # Prepare metadata
            metadata = {
                'provider': provider,
                'session_id': session_id,
                'video_type': video_type,
                'cabin_id': str(cabin_id) if cabin_id else '',
                'duration': str(duration),
                'uploaded_at': datetime.now().isoformat()
            }
            
            # Upload to MinIO using the client pool
            async with self.config.client() as s3_client:
                await s3_client.put_object(
                    Bucket=bucket_name,
                    Key=object_key,
                    Body=video_data,
                    ContentType=f'video/{format}',
                    Metadata=metadata
                )
            
            # Generate public URL
            minio_url = f"{self.config.get_endpoint_url()}/{bucket_name}/{object_key}"
            
            # Generate metadata for database in expected format
            result_metadata = {
                'url': minio_url,
                'bucket': bucket_name,
                'object_key': object_key,
                'file_size_kb': len(video_data) // 1024
            }
            
            logger.info(f"Video uploaded successfully: {object_key}")
            return True, result_metadata
            
        except Exception as e:
            logger.error(f"Failed to upload video: {e}")
            return False, {'error': str(e)}
    
    async def download_screenshot(self, bucket_name: str, object_key: str, range_header: Optional[str] = None) -> Tuple[Optional[bytes], Dict[str, Any]]:
        """
        Download screenshot from MinIO with support for range requests
        
        Args:
            bucket_name: MinIO bucket name
            object_key: Object key in the bucket
            range_header: HTTP Range header (e.g., "bytes=0-1023")
            
        Returns:
            Tuple of (image binary data or None if failed, response_metadata)
        """
        try:
            get_object_args = {
                'Bucket': bucket_name,
                'Key': object_key
            }
            
            # Process range header if provided
            start_byte = 0
            end_byte = None
            content_length = 0
            content_range = None
            status_code = 200
            
            # Get object metadata first to know total size
            async with self.config.client() as s3_client:
                head_response = await s3_client.head_object(Bucket=bucket_name, Key=object_key)
                total_size = head_response['ContentLength']
                
                if range_header:
                    status_code = 206  # Partial content
                    range_val = range_header.replace("bytes=", "").split("-")
                    start_byte = int(range_val[0] or 0)
                    
                    if len(range_val) > 1 and range_val[1]:
                        end_byte = min(int(range_val[1]), total_size - 1)
                    else:
                        # Default chunk size: 1MB
                        chunk_size = 1024 * 1024
                        end_byte = min(start_byte + chunk_size - 1, total_size - 1)
                    
                    if start_byte >= total_size:
                        raise ValueError(f"Start byte ({start_byte}) exceeds file size ({total_size})")
                    
                    get_object_args['Range'] = f"bytes={start_byte}-{end_byte}"
                    content_length = end_byte - start_byte + 1
                    content_range = f"bytes {start_byte}-{end_byte}/{total_size}"
                
                # Now get the actual data
                response = await s3_client.get_object(**get_object_args)
                
                async with response['Body'] as stream:
                    image_data = await stream.read()
            
            logger.info(f"Downloaded screenshot from MinIO: {bucket_name}/{object_key} ({len(image_data)} bytes)")
            
            response_metadata = {
                'status_code': status_code,
                'content_type': response.get('ContentType', 'image/png'),
                'headers': {
                    'Accept-Ranges': 'bytes',
                }
            }
            
            if content_range:
                response_metadata['headers']['Content-Range'] = content_range
            
            return image_data, response_metadata
            
        except Exception as e:
            logger.error(f"Failed to download screenshot from MinIO {bucket_name}/{object_key}: {e}")
            return None, {'status_code': 500}
    
    async def download_video(self, bucket_name: str, object_key: str, range_header: Optional[str] = None) -> Tuple[Optional[bytes], Dict[str, Any]]:
        """
        Download video from MinIO with support for range requests
        
        Args:
            bucket_name: MinIO bucket name
            object_key: Object key in the bucket
            range_header: HTTP Range header (e.g., "bytes=0-1023")
            
        Returns:
            Tuple of (video binary data or None if failed, response_metadata)
        """
        try:
            get_object_args = {
                'Bucket': bucket_name,
                'Key': object_key
            }
            
            # Process range header if provided
            start_byte = 0
            end_byte = None
            content_length = 0
            content_range = None
            status_code = 200
            
            # Get object metadata first to know total size
            async with self.config.client() as s3_client:
                head_response = await s3_client.head_object(Bucket=bucket_name, Key=object_key)
                total_size = head_response['ContentLength']
                content_type = head_response.get('ContentType', 'video/mp4')
                
                if range_header:
                    status_code = 206  # Partial content
                    range_val = range_header.replace("bytes=", "").split("-")
                    start_byte = int(range_val[0] or 0)
                    
                    if len(range_val) > 1 and range_val[1]:
                        end_byte = min(int(range_val[1]), total_size - 1)
                    else:
                        # Default chunk size: 2MB for videos
                        chunk_size = 2 * 1024 * 1024
                        end_byte = min(start_byte + chunk_size - 1, total_size - 1)
                    
                    if start_byte >= total_size:
                        raise ValueError(f"Start byte ({start_byte}) exceeds file size ({total_size})")
                    
                    get_object_args['Range'] = f"bytes={start_byte}-{end_byte}"
                    content_length = end_byte - start_byte + 1
                    content_range = f"bytes {start_byte}-{end_byte}/{total_size}"
                
                # Now get the actual data
                response = await s3_client.get_object(**get_object_args)
                
                async with response['Body'] as stream:
                    video_data = await stream.read()
            
            logger.info(f"Downloaded video chunk from MinIO: {bucket_name}/{object_key} ({len(video_data)} bytes, range: {get_object_args.get('Range', 'full')})")
            
            # Build response metadata
            response_metadata = {
                'status_code': status_code,
                'content_type': content_type,
                'headers': {
                    'Accept-Ranges': 'bytes',
                }
            }
            
            if content_range:
                response_metadata['headers']['Content-Range'] = content_range
            
            return video_data, response_metadata
            
        except Exception as e:
            logger.error(f"Failed to download video from MinIO {bucket_name}/{object_key}: {e}")
            return None, {'status_code': 500}
    
    async def get_presigned_url(self, bucket_name: str, object_key: str, 
                         expires: timedelta = timedelta(hours=1)) -> Optional[str]:
        """Generate presigned URL for direct access"""
        return await self.config.get_presigned_url(bucket_name, object_key, expires)
    
    async def delete_screenshot(self, bucket_name: str, object_key: str) -> bool:
        """Delete screenshot from MinIO"""
        return await self.delete_media(bucket_name, object_key)
    
    async def delete_video(self, bucket_name: str, object_key: str) -> bool:
        """Delete video from MinIO"""
        return await self.delete_media(bucket_name, object_key)
    
    async def delete_media(self, bucket_name: str, object_key: str) -> bool:
        """Delete media from MinIO"""
        try:
            async with self.config.client() as s3_client:
                await s3_client.delete_object(
                    Bucket=bucket_name,
                    Key=object_key
                )
            
            logger.info(f"Deleted media: {object_key}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to delete media {object_key}: {e}")
            return False
    
    async def list_session_media(self, session_id: str, provider: str) -> Dict[str, list]:
        """List all media for a session"""
        try:
            screenshots = []
            videos = []
            
            # List screenshots
            screenshot_bucket = self.config.get_bucket_name('screenshots')
            screenshot_prefix = f"{provider.lower()}/{session_id}/screenshots/"
            

            async with self.config.client() as s3_client:
                # List screenshots
                screenshot_paginator = s3_client.get_paginator('list_objects_v2')
                screenshot_pages = screenshot_paginator.paginate(
                    Bucket=screenshot_bucket,
                    Prefix=screenshot_prefix
                )
                
                async for page in screenshot_pages:
                    for obj in page.get('Contents', []):
                        screenshots.append({
                            'object_key': obj['Key'],
                            'size': obj['Size'],
                            'last_modified': obj['LastModified'],
                            'bucket': screenshot_bucket
                        })
                
                # List videos
                video_bucket = self.config.get_bucket_name('videos')
                video_prefix = f"{provider.lower()}/{session_id}/videos/"
                
                video_paginator = s3_client.get_paginator('list_objects_v2')
                video_pages = video_paginator.paginate(
                    Bucket=video_bucket,
                    Prefix=video_prefix
                )
                
                async for page in video_pages:
                    for obj in page.get('Contents', []):
                        videos.append({
                            'object_key': obj['Key'],
                            'size': obj['Size'],
                            'last_modified': obj['LastModified'],
                            'bucket': video_bucket
                        })
            
            return {'screenshots': screenshots, 'videos': videos}
            
        except Exception as e:
            logger.error(f"Failed to list session media: {e}")
            return {'screenshots': [], 'videos': []}
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        try:
            stats = {}
            
            async with self.config.client() as s3_client:
                for media_type, bucket_name in self.config.buckets.items():
                    total_size = 0
                    object_count = 0
                    
                    paginator = s3_client.get_paginator('list_objects_v2')
                    pages = paginator.paginate(
                        Bucket=bucket_name
                    )
                    
                    async for page in pages:
                        for obj in page.get('Contents', []):
                            total_size += obj['Size']
                            object_count += 1
                    
                    stats[media_type] = {
                        'bucket': bucket_name,
                        'total_size_mb': round(total_size / (1024 * 1024), 2),
                        'object_count': object_count
                    }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {e}")
            return {}
    
    async def stream_video(self, bucket_name: str, object_key: str, range_header: Optional[str] = None) -> Tuple[AsyncGenerator[bytes, None], Dict[str, Any]]:
        """
        Stream video from MinIO in small chunks using an async generator
        
        Args:
            bucket_name: MinIO bucket name
            object_key: Object key in the bucket
            range_header: HTTP Range header (e.g., "bytes=0-1023")
            
        Returns:
            Tuple of (async generator yielding video chunks, response_metadata)
        """
        try:
            # Default chunk size for streaming (64KB)
            chunk_size = 64 * 1024
            
            # Process range header if provided
            start_byte = 0
            end_byte = None
            content_range = None
            status_code = 200
            
            # Get object metadata first to know total size
            async with self.config.client() as s3_client:
                head_response = await s3_client.head_object(Bucket=bucket_name, Key=object_key)
                total_size = head_response['ContentLength']
                content_type = head_response.get('ContentType', 'video/mp4')
                
                if range_header:
                    status_code = 206  # Partial content
                    range_val = range_header.replace("bytes=", "").split("-")
                    start_byte = int(range_val[0] or 0)
                    
                    if len(range_val) > 1 and range_val[1]:
                        end_byte = min(int(range_val[1]), total_size - 1)
                    else:
                        end_byte = total_size - 1
                    
                    if start_byte >= total_size:
                        raise ValueError(f"Start byte ({start_byte}) exceeds file size ({total_size})")
                    
                    content_range = f"bytes {start_byte}-{end_byte}/{total_size}"
                else:
                    end_byte = total_size - 1
                    
            # Build response metadata
            response_metadata = {
                'status_code': status_code,
                'content_type': content_type,
                'content_length': end_byte - start_byte + 1,
                'headers': {
                    'Accept-Ranges': 'bytes',
                }
            }
            
            if content_range:
                response_metadata['headers']['Content-Range'] = content_range
            
            # Create async generator to stream the data
            async def video_streamer():
                nonlocal start_byte, end_byte, chunk_size
                current_position = start_byte
                
                # Create a single client outside the loop for connection reuse
                async with self.config.client() as s3_client:
                    while current_position <= end_byte:
                        try:
                            # Calculate chunk end position
                            chunk_end = min(current_position + chunk_size - 1, end_byte)
                            
                            # Get chunk range
                            chunk_range = f"bytes={current_position}-{chunk_end}"
                            
                            # Get chunk from S3 using the existing client
                            response = await s3_client.get_object(
                                Bucket=bucket_name,
                                Key=object_key,
                                Range=chunk_range
                            )
                            
                            async with response['Body'] as stream:
                                chunk = await stream.read()
                                
                            # Yield the chunk
                            yield chunk
                            
                            # Move to next chunk
                            current_position = chunk_end + 1
                            
                            # Log progress every ~5MB
                            if current_position % (5 * 1024 * 1024) < chunk_size:
                                logger.debug(f"Streaming progress: {current_position}/{end_byte} bytes ({current_position * 100 // (end_byte+1)}%)")
                                
                        except Exception as e:
                            logger.error(f"Error streaming chunk {current_position}-{chunk_end}: {e}")
                            # Re-raise to stop the stream if there's an error
                            raise
            
            return video_streamer(), response_metadata
            
        except Exception as e:
            logger.error(f"Failed to stream video from MinIO {bucket_name}/{object_key}: {e}")
            # Return an empty generator and error metadata
            async def empty_generator():
                yield b""
            return empty_generator(), {'status_code': 500, 'error': str(e)}

# Global media service instance
media_service = MediaService() 