import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useUILogger } from "../hooks/useUILogger";
import { getToken } from '@/services/auth';

// Define the structure of a UI log
export interface UILog {
  timestamp: string;
  message: string;
  session_id?: string;
  cabin_id?: number | string;
  category?: string;
  step?: string;
  module?: string;
  connected?: boolean;
}

// Define the context structure
export interface LogContextType {
  logs: UILog[];
  connected: boolean;
  clearLogs: () => void;
  activeCabins: (string | number)[];
  filteredLogs: (module?: string, cabinId?: string | number) => UILog[];
  getCabinLogs: (cabinId: string | number, module?: string) => UILog[];
  setActiveSessionId: (sessionId: string | null) => void;
}

// Create the context with default values
const LogContext = createContext<LogContextType>({
  logs: [],
  connected: false,
  clearLogs: () => {},
  activeCabins: [],
  filteredLogs: () => [],
  getCabinLogs: () => [],
  setActiveSessionId: () => {}
});

// Maximum number of logs to keep in memory
const MAX_LOGS = 1000;

// Create context provider component
export function LogProvider({ children, apiBaseUrl = '' }: { children: ReactNode, apiBaseUrl?: string }) {
  // State for logs and filtering
  const [logs, setLogs] = useState<UILog[]>([]);
  const [activeCabins, setActiveCabins] = useState<(string | number)[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  
  // Get authentication token
  const authToken = getToken();
  
  // Persist a single SSE connection; attach session when it becomes available
  const { connected: isConnected } = useUILogger(authToken, activeSessionId, {
    onMessage: (newLog: UILog) => {
      // Skip initial connection message
      if ((newLog as any).connected) return;
      
      // Only process logs that have a module name
      // Exclude logs without module names
      if (!newLog.module) return;
      
      // Only keep logs from our supported modules
      const supportedModules = ['Studio', 'NCL', 'Cruising_Power', 'OneSource'];
      if (!supportedModules.includes(newLog.module)) return;
      
      // Prepend new log (most recent first) and cap length
      setLogs(prev => {
                 // Enhanced duplicate prevention logic
         const filteredPrev = prev.filter(log => {
           // For logs with cabin_id, check exact match including cabin_id
           if (log.cabin_id !== undefined && log.cabin_id !== null && 
               newLog.cabin_id !== undefined && newLog.cabin_id !== null) {
             return !(log.message === newLog.message && 
                     log.cabin_id === newLog.cabin_id && 
                     log.step === newLog.step);
           }
           
           // For logs without cabin_id, check only message and step to prevent duplicates
           if ((log.cabin_id === undefined || log.cabin_id === null) && 
               (newLog.cabin_id === undefined || newLog.cabin_id === null)) {
             return !(log.message === newLog.message && 
                     log.step === newLog.step);
           }
           
           // Different cabin_id types, keep both
           return true;
         });
        
        return [newLog, ...filteredPrev].slice(0, MAX_LOGS);
      });
      
      // Track unique cabin IDs
      if (newLog.cabin_id != null) {
        setActiveCabins(prevCabins =>
          prevCabins.includes(newLog.cabin_id!) ? prevCabins : [...prevCabins, newLog.cabin_id!]
        );
      }
    },
  });

  // Clear logs when session changes (but keep SSE alive)
  useEffect(() => {
    console.log(`Active session ID updated: ${activeSessionId || 'none'}`);
    setLogs([]);
    setActiveCabins([]);
  }, [activeSessionId]);

  // Clear all logs
  const clearLogs = () => {
    setLogs([]);
    setActiveCabins([]);
  };

  // Helper function to remove duplicate logs without cabin_id
  const deduplicateGeneralLogs = (logs: UILog[]): UILog[] => {
    const uniqueLogs: UILog[] = [];
    const seenGeneralLogs = new Set<string>();

    for (const log of logs) {
      if (log.cabin_id === undefined || log.cabin_id === null) {
        // For logs without cabin_id, create a unique key based on message and step only
        const logKey = `${log.message}-${log.step || ''}`;
        
        if (!seenGeneralLogs.has(logKey)) {
          seenGeneralLogs.add(logKey);
          uniqueLogs.push(log);
        }
        // Skip duplicate general logs
      } else {
        // For logs with cabin_id, always include
        uniqueLogs.push(log);
      }
    }

    return uniqueLogs;
  };

  // Filter logs by module and optionally cabin ID
  const filteredLogs = (module?: string, cabinId?: string | number) => {
    // Step 1: Filter by module if specified
    let moduleFilteredLogs = logs.filter(log => {
      // Must have a module name
      if (!log.module) return false;
      
      // Filter by module if specified
      if (module && log.module !== module) return false;
      
      return true;
    });

    // Step 2: If no cabin ID specified, return all logs for the module with deduplication
    if (cabinId === undefined || cabinId === null) {
      return deduplicateGeneralLogs(moduleFilteredLogs);
    }

    // Step 3: If cabin ID is specified, return logs for that cabin + logs without cabin_id
    const cabinFilteredLogs = moduleFilteredLogs.filter(log => {
      // Include logs with the specific cabin_id
      if (log.cabin_id === cabinId) return true;
      
      // Include logs without cabin_id (these should appear in all cabin windows)
      if (log.cabin_id === undefined || log.cabin_id === null) return true;
      
      return false;
    });

    // Apply deduplication for logs without cabin_id
    return deduplicateGeneralLogs(cabinFilteredLogs);
  };

  // Get logs for a specific cabin ID, optionally filtered by module
  const getCabinLogs = (cabinId: string | number, module?: string) => {
    // First filter by module and cabin criteria
    const filteredLogs = logs.filter(log => {
      // Must have a module name
      if (!log.module) return false;
      
      // Filter by module if specified
      if (module && log.module !== module) return false;
      
      // Include logs with the specific cabin_id (use loose equality to handle type differences)
      if (log.cabin_id == cabinId) return true;
      
      // Include logs without cabin_id (these should appear in all cabin windows)
      if (log.cabin_id === undefined || log.cabin_id === null) return true;
      
      return false;
    });

    // Remove duplicates for logs without cabin_id based on message and timestamp
    // This prevents the same general log from appearing multiple times in each cabin window
    return deduplicateGeneralLogs(filteredLogs);
  };

  return (
    <LogContext.Provider 
      value={{
        logs: logs.filter(log => log.module), // Only return logs with module names
        connected: isConnected,
        clearLogs,
        activeCabins,
        filteredLogs,
        getCabinLogs,
        setActiveSessionId
      }}
    >
      {children}
    </LogContext.Provider>
  );
}

// Custom hook to use the log context
export const useLogContext = () => useContext(LogContext); 