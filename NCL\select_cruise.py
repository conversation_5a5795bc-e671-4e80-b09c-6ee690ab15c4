from loguru import logger
import asyncio
from NCL.utils import NCLUtils
from Core.ui_logger import ui_log


class CruiseSelector:

    @staticmethod
    async def display_available_cruises(page, session_id=None, cabin_id=None):
        try:
            logger.info("Waiting for cruise list to load")
            await asyncio.sleep(2)
            cruise_grid = await page.wait_for_selector(
                "#SWXMLForm_SelectVoyage_voyage", timeout=15000
            )

            cruise_rows = await page.locator(".slick-row").all()

            if not cruise_rows:
                logger.error("No cruises found")
                return None

            logger.info(f"Found {len(cruise_rows)} cruises")

            logger.info("\n==== AVAILABLE CRUISES ====")
            for i, row in enumerate(cruise_rows):
                try:
                    ship = await row.locator(".l1.r1").text_content()
                    sail_date = await row.locator(".l2.r2").text_content()
                    duration = await row.locator(".l3.r3").text_content()
                    itinerary = await row.locator(".l4.r4").text_content()
                    inside_price = await row.locator(".l8.r8").text_content()

                    cruise_info = f"{ship} - {sail_date} - {duration} - {itinerary} - Inside: {inside_price}"
                    logger.info(f"{i+1}. {cruise_info}")
                except Exception as e:
                    logger.error(f"Error parsing cruise row {i+1}: {str(e)}")
                    logger.info(f"{i+1}. [Error parsing cruise information]")

            logger.info("Automatically selecting the first cruise")
            return 0

        except Exception as e:
            logger.error(f"Error displaying cruises: {str(e)}")
            return None

    @staticmethod
    async def select_cruise(page, cruise_index, session_id=None, cabin_id=None):
        try:
            logger.info(f"Selecting cruise at index {cruise_index}")
            for attempt in range(2):
                logger.info(f"Attempt {attempt+1} to select cruise {cruise_index+1}")

                await asyncio.sleep(2)

                try:
                    cruise_rows = await page.locator(".slick-row").all()

                    if cruise_index < len(cruise_rows):
                        await asyncio.sleep(1)

                        selectors = ["a[data-link-action='select']"]

                        for selector in selectors:
                            try:
                                select_links = await cruise_rows[cruise_index].locator(
                                    selector
                                ).all()
                                if select_links and len(select_links) > 0:
                                    await select_links[0].click()
                                    logger.info(
                                        f"Clicked select link with selector: {selector}"
                                    )
                                    await asyncio.sleep(2)

                                    if await page.locator("#SWXMLForm_SelectCategory_category"
                                                    ).count() > 0:
                                        logger.success(
                                            "Successfully selected cruise after clicking select link"
                                        )
                                        ui_log(
                                            "Cruise selected successfully — all set",
                                            session_id=session_id,
                                            cabin_id=cabin_id,
                                            module="NCL",
                                            step="cruise_selection"
                                        )
                                        return True

                                    break
                            except Exception as select_err:
                                logger.debug(
                                    f"Selection with {selector} failed: {str(select_err)}"
                                )
                                continue
                except Exception as click_error:
                    logger.warning(f"Direct click approach failed: {str(click_error)}")

                try:
                    await page.wait_for_selector(
                        "#SWXMLForm_SelectCategory_category", timeout=5000
                    )
                    logger.success("Successfully selected cruise")
                    ui_log(
                        "Cruise selected successfully — all set",
                        session_id=session_id,
                        cabin_id=cabin_id,
                        module="NCL",
                        step="cruise_selection"
                    )
                    return True
                except Exception as nav_error:
                    logger.warning(
                        f"Attempt {attempt+1} failed to navigate to category page: {str(nav_error)}"
                    )

                    if attempt == 1:
                        try:
                            continue_buttons = await page.locator(
                                "[id*='action_DoSelect'], [id*='action_select'], [id*='action_continue'], [id*='action_DoContinue']"
                            ).all()

                            if continue_buttons and len(continue_buttons) > 0:
                                logger.info(
                                    f"Trying generic continue button approach, found {len(continue_buttons)} buttons"
                                )
                                for btn in continue_buttons:
                                    if await btn.is_visible():
                                        try:
                                            btn_id = await btn.get_attribute(
                                                'id'
                                            ) or "unknown"
                                            logger.info(
                                                f"Clicking button with ID: {btn_id}"
                                            )
                                            await btn.click()
                                            await asyncio.sleep(2)

                                            try:
                                                await page.wait_for_selector(
                                                    "#SWXMLForm_SelectCategory_category",
                                                    timeout=5000
                                                )
                                                logger.success(
                                                    "Successfully navigated to category page"
                                                )
                                                ui_log(
                                                    "Cruise selected successfully — all set",
                                                    session_id=session_id,
                                                    cabin_id=cabin_id,
                                                    module="NCL",
                                                    step="cruise_selection"
                                                )
                                                return True
                                            except:
                                                pass
                                        except Exception as btn_err:
                                            logger.warning(
                                                f"Button click failed: {str(btn_err)}"
                                            )
                        except Exception as final_error:
                            logger.warning(
                                f"Generic button approach failed: {str(final_error)}"
                            )

            try:
                category_page_element = await page.locator(
                    "#SWXMLForm_SelectCategory_category"
                ).count()
                if category_page_element > 0:
                    logger.success("Already on category selection page")
                    return True

                select_buttons = await page.locator(
                    "a:text('Select'), button:text('Select'), input[value='Select']"
                ).all()
                if select_buttons and len(select_buttons) > 0:
                    for btn in select_buttons:
                        if await btn.is_visible():
                            logger.info("Clicking general Select button")
                            try:
                                async with page.expect_navigation(timeout=10000):
                                    await btn.click()
                                logger.success(
                                    "Successfully navigated after clicking general Select button"
                                )
                                ui_log(
                                    "Cruise selected successfully — all set",
                                    session_id=session_id,
                                    cabin_id=cabin_id,
                                    module="NCL",
                                    step="cruise_selection"
                                )
                                return True
                            except Exception as nav_err:
                                logger.warning(
                                    f"Navigation failed after general select button: {str(nav_err)}"
                                )

                                if await page.locator("#SWXMLForm_SelectCategory_category"
                                                ).count() > 0:
                                    logger.success(
                                        "Successfully navigated to category page"
                                    )
                                    ui_log(
                                        "Cruise selected successfully — all set",
                                        session_id=session_id,
                                        cabin_id=cabin_id,
                                        module="NCL",
                                        step="cruise_selection"
                                    )
                                    return True
            except Exception as general_btn_err:
                logger.warning(
                    f"General select button click failed: {str(general_btn_err)}"
                )

            try:
                if page.url.find("category") != -1 or await page.locator(
                        "#SWXMLForm_SelectCategory_category").count() > 0:
                    logger.success("Successfully selected cruise by URL verification")
                    ui_log(
                        "Cruise selected successfully — all set",
                        session_id=session_id,
                        cabin_id=cabin_id,
                        module="NCL",
                        step="cruise_selection"
                    )
                    return True
            except:
                pass

            logger.error("All attempts to select cruise failed")
            return False

        except Exception as e:
            logger.error(f"Error selecting cruise: {str(e)}")
            return False