"""
Cancellation Service for handling booking cancellations.

This service provides functionality to cancel active booking sessions,
including stopping tasks and closing browser contexts.
"""

import asyncio
import logging
from typing import Dict, Any

from services.task_registry import task_registry
from Core.browser_setup import browser_manager
from db.session_manager import SessionManager
from Core.ui_logger import ui_log

logger = logging.getLogger("services.cancellation_service")


class CancellationService:
    """Service for handling booking cancellations."""
    
    @staticmethod
    async def cancel_booking(session_id: str, user_id: str = None) -> Dict[str, Any]:
        """
        Cancel a booking session.
        
        This method:
        1. Cancels the active booking task
        2. Closes all browser contexts for the session
        3. Updates session status to 'cancelled'
        4. Logs the cancellation
        
        Args:
            session_id: Session identifier to cancel
            user_id: Optional user ID for logging
            
        Returns:
            Dictionary with cancellation results
        """
        logger.info(f"Starting cancellation for session {session_id}")
        
        result = {
            'session_id': session_id,
            'success': False,
            'task_cancelled': False,
            'contexts_closed': 0,
            'session_updated': False,
            'message': ''
        }
        
        try:
            # Step 1: Cancel the booking task
            task_cancelled = await task_registry.cancel_task(session_id, timeout=5.0)
            result['task_cancelled'] = task_cancelled

            if not task_cancelled:
                # Task not found in this worker – broadcast cancellation so that the worker that owns the task can cancel it
                try:
                    await task_registry.broadcast_cancel(session_id)
                    logger.info(f"Broadcasted cross-worker cancellation for session {session_id}")
                except Exception as e:
                    logger.error(f"Failed to broadcast cancellation for session {session_id}: {e}")

            if task_cancelled:
                logger.info(f"Successfully cancelled task for session {session_id}")
            else:
                logger.warning(f"No active task found locally for session {session_id}")
            
            # Step 2: Close browser contexts
            contexts_closed = await browser_manager.close_session_contexts(session_id)
            result['contexts_closed'] = contexts_closed
            
            if contexts_closed > 0:
                logger.info(f"Closed {contexts_closed} browser contexts for session {session_id}")
            
            # Step 3: Update session status
            session_manager = SessionManager.get_instance()
            try:
                await session_manager.update_session_status(
                    session_id, 
                    'cancelled', 
                    {'cancelled_by': user_id or 'unknown', 'reason': 'user_reset'}
                )
                result['session_updated'] = True
                logger.info(f"Updated session {session_id} status to cancelled")
            except Exception as e:
                logger.error(f"Failed to update session status for {session_id}: {e}")
            
            # Step 4: Log to UI
            try:
                ui_log(
                    "Booking cancelled by user - cleaning up resources",
                    session_id=session_id,
                    user_id=user_id,
                    step="booking_cancelled",
                    module="System"
                )
            except Exception as e:
                logger.error(f"Failed to send UI log for session {session_id}: {e}")
            
            # Determine overall success
            result['success'] = True  # We consider it successful even if task wasn't found
            result['message'] = f"Booking cancelled successfully. Task cancelled: {task_cancelled}, Contexts closed: {contexts_closed}"
            
            logger.info(f"Cancellation completed for session {session_id}: {result['message']}")
            
        except Exception as e:
            error_msg = f"Error during cancellation for session {session_id}: {str(e)}"
            logger.error(error_msg)
            result['message'] = error_msg
            
            # Still try to close contexts and update session as cleanup
            try:
                await browser_manager.close_session_contexts(session_id)
                session_manager = SessionManager.get_instance()
                await session_manager.update_session_status(
                    session_id, 
                    'cancelled', 
                    {'cancelled_by': user_id or 'unknown', 'reason': 'user_reset', 'error': str(e)}
                )
            except:
                pass  # Don't let cleanup errors mask the original error
        
        return result
    
    @staticmethod
    async def get_cancellation_status(session_id: str) -> Dict[str, Any]:
        """
        Get the current cancellation status for a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Dictionary with status information
        """
        task = task_registry.get_task(session_id)
        task_metadata = task_registry.get_task_metadata(session_id)
        context_count = browser_manager.get_session_context_count(session_id)
        
        return {
            'session_id': session_id,
            'has_active_task': task is not None and not task.done() if task else False,
            'task_metadata': task_metadata,
            'active_contexts': context_count,
            'can_cancel': (task is not None and not task.done()) or context_count > 0
        }