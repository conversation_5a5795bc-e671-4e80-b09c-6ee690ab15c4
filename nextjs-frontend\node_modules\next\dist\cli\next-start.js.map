{"version": 3, "sources": ["../../src/cli/next-start.ts"], "names": ["nextStart", "args", "console", "log", "process", "exit", "dir", "getProjectDir", "_", "host", "port", "getPort", "isPortIsReserved", "printAndExit", "getReservedPortExplanation", "isExperimentalTestProxy", "keepAliveTimeoutArg", "Number", "isNaN", "isFinite", "keepAliveTimeout", "Math", "ceil", "undefined", "startServer", "isDev", "hostname"], "mappings": ";;;;;+BAsESA;;;eAAAA;;;QArEF;6BACqB;uBACU;+BACR;iCAKvB;AAEP,MAAMA,YAAwB,OAAOC;IACnC,IAAIA,IAAI,CAAC,SAAS,EAAE;QAClBC,QAAQC,GAAG,CAAC,CAAC;;;;;;;;;;;;;;;;IAgBb,CAAC;QACDC,QAAQC,IAAI,CAAC;IACf;IAEA,MAAMC,MAAMC,IAAAA,4BAAa,EAACN,KAAKO,CAAC,CAAC,EAAE;IACnC,MAAMC,OAAOR,IAAI,CAAC,aAAa;IAC/B,MAAMS,OAAOC,IAAAA,cAAO,EAACV;IAErB,IAAIW,IAAAA,iCAAgB,EAACF,OAAO;QAC1BG,IAAAA,mBAAY,EAACC,IAAAA,2CAA0B,EAACJ,OAAO;IACjD;IAEA,MAAMK,0BAA0Bd,IAAI,CAAC,4BAA4B;IAEjE,MAAMe,sBAA0Cf,IAAI,CAAC,qBAAqB;IAC1E,IACE,OAAOe,wBAAwB,eAC9BC,CAAAA,OAAOC,KAAK,CAACF,wBACZ,CAACC,OAAOE,QAAQ,CAACH,wBACjBA,sBAAsB,CAAA,GACxB;QACAH,IAAAA,mBAAY,EACV,CAAC,yEAAyE,EAAEG,oBAAoB,CAAC,CAAC,EAClG;IAEJ;IAEA,MAAMI,mBAAmBJ,sBACrBK,KAAKC,IAAI,CAACN,uBACVO;IAEJ,MAAMC,IAAAA,wBAAW,EAAC;QAChBlB;QACAmB,OAAO;QACPV;QACAW,UAAUjB;QACVC;QACAU;IACF;AACF"}