import re
from loguru import logger

class CruiseInfoExtractor:
    """
    A class for extracting and normalizing cruise information from text.
    """
    
    def __init__(self):
        """Initialize the CruiseInfoExtractor"""
        pass
        
    def normalize_cabin_category(self, cabin_type):
        """
        Normalize cabin category names to match website options
        
        Args:
            cabin_type: Original cabin type description
            
        Returns:
            Normalized cabin type matching the website's options or the cabin code if it's a direct match
        """
        if not cabin_type:
            logger.debug("Empty cabin type provided, returning 'All Categories'")
            return "All Categories"
            
        original_cabin_type = cabin_type.strip()
        cabin_type = cabin_type.lower().strip()
        logger.debug(f"Normalizing cabin type: '{cabin_type}'")
        
        # For 1-2 character codes, check if it's a direct cabin code
        if len(original_cabin_type) <= 2:
            logger.debug(f"Detected potential cabin code: '{original_cabin_type}'")
            # Return the original case-preserved code for direct matching
            # This will be handled specially in the category selection logic
            return f"CABIN_CODE:{original_cabin_type.upper()}"
        
        # Use exact matching for predefined categories to prevent "Royal Suite" from being treated as "Suite"
        import re
        
        # Suite variations - exact match only
        if any(re.search(rf'\b{re.escape(term)}\b', cabin_type, re.IGNORECASE) for term in ["suite", "jr suite", "deluxe"]):
            # Check if it's an exact match for just "suite", "jr suite", or "deluxe"
            if cabin_type in ["suite", "jr suite", "deluxe"]:
                logger.debug(f"Identified cabin type '{cabin_type}' as 'Suite'")
                return "Suite"
            else:
                logger.debug(f"Found suite variant in '{cabin_type}' but not exact match, returning original")
                return cabin_type.title()  # Return original with title case
        
        # Balcony/Veranda variations - exact match only
        elif any(re.search(rf'\b{re.escape(term)}\b', cabin_type, re.IGNORECASE) for term in ["balcony", "veranda", "balcony stateroom"]):
            if cabin_type in ["balcony", "veranda", "balcony stateroom"]:
                logger.debug(f"Identified cabin type '{cabin_type}' as 'Veranda'")
                return "Veranda"
            else:
                logger.debug(f"Found balcony/veranda variant in '{cabin_type}' but not exact match, returning original")
                return cabin_type.title()
        
        # Outside/Oceanview variations - exact match only
        elif any(re.search(rf'\b{re.escape(term)}\b', cabin_type, re.IGNORECASE) for term in ["outside", "oceanview", "ocean view", "window"]):
            if cabin_type in ["outside", "oceanview", "ocean view", "window"]:
                logger.debug(f"Identified cabin type '{cabin_type}' as 'Outside'")
                return "Outside"
            else:
                logger.debug(f"Found outside/oceanview variant in '{cabin_type}' but not exact match, returning original")
                return cabin_type.title()
        
        # Interior/Inside variations - exact match only
        elif any(re.search(rf'\b{re.escape(term)}\b', cabin_type, re.IGNORECASE) for term in ["inside", "interior", "inner"]):
            if cabin_type in ["inside", "interior", "inner"]:
                logger.debug(f"Identified cabin type '{cabin_type}' as 'Interior'")
                return "Interior"
            else:
                logger.debug(f"Found interior/inside variant in '{cabin_type}' but not exact match, returning original")
                return cabin_type.title()
        else:
            # Return original cabin type with title case instead of defaulting to "All Categories"
            logger.debug(f"Could not identify specific cabin type for '{cabin_type}', returning original")
            return cabin_type.title()

    def extract_cruise_info(self, text):
        logger.info("Extracting cruise information from text input")
        # Extract ship info
        ship_match = re.search(r'Ship:\s+(.*?),\s+(Royal Caribbean|Celebrity\s*Cruises?)', text, re.IGNORECASE)
        ship_name = ship_match.group(1).strip() if ship_match else "Not found"
        ship_brand = "Celebrity" if ship_match and "Celebrity" in ship_match.group(2) else "Royal Caribbean" if ship_match else "Not found"
        
        # Extract date and format it
        date_match = re.search(r'Sails:\s+(\w+)\s+(\d+)(?:st|nd|rd|th),\s+(\d{4})', text)
        if date_match:
            month, day, year = date_match.groups()
            # Convert to dd-Mon-yyyy format
            month_short = month[:3]
            formatted_date = f"{day}-{month_short}-{year}"
        else:
            formatted_date = "Not found"
        
        # Extract passenger info - improved pattern
        passengers_match = re.search(r'Passengers:\s+(\d+)\s+total\s+\(\s*(\d+)\s+(adult|adults),\s+(\d+)\s+(senior|seniors)\s*\)', text, re.IGNORECASE)
        
        if passengers_match:
            total = int(passengers_match.group(1))
            adults = int(passengers_match.group(2))
            seniors = int(passengers_match.group(4))
            children = total - (adults + seniors)  # Calculate children as the difference
            
            # Log the values for debugging
            logger.debug(f"Extracted passengers - Total: {total}, Adults: {adults}, Seniors: {seniors}, Calculated Children: {children}")
            
            # Verify the sum matches the total
            if adults + seniors + children != total:
                logger.warning(f"Passenger count mismatch: {adults} adults + {seniors} seniors + {children} children != {total} total")
                # Force the total to be correct
                children = total - (adults + seniors)
                logger.debug(f"Corrected children count to: {children}")
        else:
            # Fallback to separate patterns
            total_match = re.search(r'Passengers:\s+(\d+)\s+total', text)
            total = int(total_match.group(1)) if total_match else 0
            
            adults_match = re.search(r'(\d+)\s+adult', text)
            adults = int(adults_match.group(1)) if adults_match else 0
            
            children_match = re.search(r'(\d+)\s+child', text)
            children = int(children_match.group(1)) if children_match else 0
            
            seniors_match = re.search(r'(\d+)\s+senior', text)
            seniors = int(seniors_match.group(1)) if seniors_match else 0
            
            # If we have adults and seniors but no children, validate total
            if adults > 0 and total > 0:
                expected_children = total - (adults + seniors)
                if children != expected_children:
                    logger.warning(f"Children count mismatch: found {children}, expected {expected_children} based on total")
                    children = expected_children
                    logger.debug(f"Corrected children count to: {children}")
            
            # Log the final values
            logger.debug(f"Final passenger counts - Total: {total}, Adults: {adults}, Seniors: {seniors}, Children: {children}")
        
        # Extract residency - improved to get full state name
        home_match = re.search(r'Home:\s+(.*?)(?:,|(?=\s+Airport:))', text)
        residency = home_match.group(1).strip() if home_match else "Not found"
        
        # Extract cabin information with multiple pattern attempts
        cabin_categories = []
        normalized_cabin_type = "All Categories"  # Default
        
        # First, check for the "Cabins: X total ( Y )" pattern with multiple cabins
        multi_cabins_match = re.search(r'Cabins:\s+(\d+)\s+total\s*\(\s*(.*?)\s*\)', text, re.IGNORECASE)
        if multi_cabins_match:
            total_cabins = int(multi_cabins_match.group(1))
            cabin_details = multi_cabins_match.group(2).strip()
            
            # First look for individual cabin entries like "1 Inside, 1 Balcony"
            cabin_types = re.findall(r'(\d+)\s+([\w\s]+?)(?:,|\s*$)', cabin_details)
            
            if not cabin_types:
                # Try alternative pattern if the first one doesn't find matches
                cabin_types = re.findall(r'(\d+)\s+([\w\s]+)', cabin_details)
                
            # If still no cabin types found, check if it's just a cabin code without count
            if not cabin_types and cabin_details:
                # Handle case like "A2" without count prefix
                logger.debug(f"No count prefix found in cabin details: '{cabin_details}', treating as single cabin")
                cabin_types = [(str(total_cabins), cabin_details)]
                
            # If still no individual entries found, check if it's a single entry with "or"
            if not cabin_types and " or " in cabin_details.lower():
                # Handle the case like "Outside or Balcony"
                cabin_count = 1  # Default to 1 cabin
                cabin_options = [option.strip() for option in cabin_details.split(" or ")]
                
                # Create a cabin entry for each option
                for option in cabin_options:
                    normalized_type = self.normalize_cabin_category(option)
                    cabin_categories.append({
                        "count": 1,
                        "type": option,
                        "normalized": normalized_type,
                        "is_or_option": True  # Flag to identify this was part of an "or" option
                    })
            else:
                # Process regular cabin entries (without "or")
                for count, cabin_type in cabin_types:
                    count = int(count)
                    
                    # Check if this cabin type contains "or"
                    if " or " in cabin_type.lower():
                        # Split the cabin type by "or" and create entries for each option
                        cabin_options = [option.strip() for option in cabin_type.split(" or ")]
                        
                        for option in cabin_options:
                            normalized_type = self.normalize_cabin_category(option)
                            # Add an entry for each option
                            cabin_categories.append({
                                "count": count,
                                "type": option,
                                "normalized": normalized_type,
                                "is_or_option": True  # Flag to identify this was part of an "or" option
                            })
                    else:
                        # Regular cabin type without "or"
                        normalized_type = self.normalize_cabin_category(cabin_type.strip())
                        # Add each cabin individually
                        for i in range(count):
                            cabin_categories.append({
                                "count": 1,  # Each entry represents one cabin
                                "type": cabin_type.strip(),
                                "normalized": normalized_type,
                                "is_or_option": False
                            })
            
            # For "or" cases, verify we have the right number of cabin types
            or_options = [cabin for cabin in cabin_categories if cabin.get("is_or_option", False)]
            regular_cabins = [cabin for cabin in cabin_categories if not cabin.get("is_or_option", False)]
            
            # If we have "or" options and the total cabins is less than our found cabins,
            # we'll treat the "or" options as alternatives rather than separate cabins
            if or_options and len(regular_cabins) + 1 != total_cabins:
                # Remove the "or" options and add a single placeholder
                for cabin in or_options:
                    cabin_categories.remove(cabin)
                
                # Add a special cabin entry that represents all the "or" options
                or_types = [cabin["type"] for cabin in or_options]
                combined_type = " or ".join(or_types)
                cabin_categories.append({
                    "count": 1,
                    "type": combined_type,
                    "normalized": "Multiple Options",
                    "is_or_option": True,
                    "or_options": or_options  # Store all the individual options
                })
        
        # If still no cabins found, try more flexible patterns
        if not cabin_categories:
            # Search for direct mentions of cabin types
            cabin_types_to_check = ["balcony", "veranda", "suite", "jr suite", "inside", "interior", "oceanview", "outside"]
            for cabin_type in cabin_types_to_check:
                if re.search(rf'\b{re.escape(cabin_type)}\b', text, re.IGNORECASE):
                    normalized_type = self.normalize_cabin_category(cabin_type)
                    cabin_categories.append({
                        "count": 1,
                        "type": cabin_type,
                        "normalized": normalized_type,
                        "is_or_option": False
                    })
                    break
                    
            # If still not found, try looser patterns
            if not cabin_categories:
                alt_cabins_patterns = [
                    r'Cabin(?:s)?:.*?(\d+)\s+([\w\s]+)',  # Cabins: 1 Balcony
                    r'(\d+)\s+(balcony|suite|interior|inside|outside|oceanview)',  # 1 Balcony
                    r'cabin type:?\s*([\w\s]+)',  # Cabin type: Balcony
                ]
                
                for pattern in alt_cabins_patterns:
                    alt_match = re.search(pattern, text, re.IGNORECASE)
                    if alt_match:
                        if len(alt_match.groups()) == 2:
                            count = alt_match.group(1)
                            cabin_type = alt_match.group(2).strip()
                        else:
                            count = 1  # Assume 1 if no count specified
                            cabin_type = alt_match.group(1).strip()
                        
                        # Check if this cabin type contains "or"
                        if " or " in cabin_type.lower():
                            # Split the cabin type by "or" and create entries for each option
                            cabin_options = [option.strip() for option in cabin_type.split(" or ")]
                            
                            for option in cabin_options:
                                normalized_type = self.normalize_cabin_category(option)
                                cabin_categories.append({
                                    "count": int(count) if count.isdigit() else 1,
                                    "type": option,
                                    "normalized": normalized_type,
                                    "is_or_option": True
                                })
                        else:
                            # Regular cabin type without "or"
                            normalized_type = self.normalize_cabin_category(cabin_type)
                            cabin_categories.append({
                                "count": int(count) if count.isdigit() else 1,
                                "type": cabin_type,
                                "normalized": normalized_type,
                                "is_or_option": False
                            })
                        break
        
        # Set default category if none found
        if not cabin_categories:
            cabin_categories.append({
                "count": 1,
                "type": "All Categories",
                "normalized": "All Categories",
                "is_or_option": False
            })
        
        # Use first cabin type as the primary one
        if cabin_categories:
            normalized_cabin_type = cabin_categories[0]["normalized"]
        
        # Check for special statuses
        has_seniors = "Yes" if seniors > 0 else "No"
        has_military = "Yes" if re.search(r'Military:\s+(Active|Retired)', text, re.IGNORECASE) else "No"
        has_fire = "Yes" if re.search(r'Fire\s+Department:\s+Active', text) else "No"
        has_law = "Yes" if re.search(r'Law\s+Enforcement:\s+Active', text) else "No"
        
        # Special handling for "or" options in cabins
        final_cabin_categories = []
        or_options_cabin = None
        
        for cabin in cabin_categories:
            if cabin.get("is_or_option", False) and "or_options" in cabin:
                # This is a special cabin that has multiple "or" options
                or_options_cabin = cabin
            else:
                final_cabin_categories.append(cabin)
        
        # If we have a special "or options" cabin, expand it
        if or_options_cabin:
            # Extract the individual options
            individual_options = or_options_cabin.get("or_options", [])
            
            # If this is a valid "or" case (has multiple options), use those instead
            if individual_options:
                for option in individual_options:
                    # For "or" options, each option gets the full passenger count
                    # (not distributing passengers)
                    final_cabin_categories.append(option)
        
        # Distribute passengers among cabins
        cabins_with_passengers = self.distribute_passengers(final_cabin_categories, adults, children, seniors)
        
        # Create structured data dictionary
        info_dict = {
            "ship_brand": ship_brand,
            "ship_name": ship_name,
            "departure_date": formatted_date,
            "total_passengers": total,
            "adults": adults,
            "children": children,
            "seniors": seniors,
            "residency": residency,
            "cabin_categories": final_cabin_categories,
            "cabin_type": normalized_cabin_type,
            "has_senior": has_seniors,
            "has_military": has_military,
            "has_fire_department": has_fire,
            "has_law_enforcement": has_law,
            "cabins_with_passengers": cabins_with_passengers,
            "total_cabins": len(final_cabin_categories)
        }
        
        # Format output text
        result = (f"Ship Brand: {ship_brand}\n"
                 f"Ship Name: {ship_name}\n"
                 f"Departure Date: {formatted_date}\n"
                 f"Passengers: {total} ({adults} adults, {children} children, {seniors} seniors)\n"
                 f"Residency: {residency}\n"
                 f"Total Cabins: {len(cabins_with_passengers)}\n")
                 
        # Add each cabin's info
        for i, cabin in enumerate(cabins_with_passengers):
            result += f"Cabin {i+1}: {cabin['type']} - {cabin['assigned_adults']} adults, {cabin['assigned_children']} children, {cabin['assigned_seniors']} seniors\n"
        
        result += (f"Senior: {has_seniors}\n"
                  f"Fire Department: {has_fire}\n"
                  f"Law Enforcement: {has_law}\n"
                  f"Military: {has_military}")
        
        logger.info("Successfully extracted cruise information")
        return result, info_dict

    def distribute_passengers(self, cabin_categories, total_adults, total_children, total_seniors):
        """
        Distributes passengers evenly among cabins
        
        Args:
            cabin_categories: List of cabin category dictionaries
            total_adults: Total number of adults
            total_children: Total number of children
            total_seniors: Total number of seniors
            
        Returns:
            List of cabins with assigned passengers
        """
        logger.debug(f"Distributing passengers among {len(cabin_categories)} cabins: {total_adults} adults, {total_children} children, {total_seniors} seniors")
        
        # Copy cabins to avoid modifying originals
        cabins = []
        for cabin in cabin_categories:
            new_cabin = cabin.copy()
            new_cabin["assigned_adults"] = 0
            new_cabin["assigned_children"] = 0
            new_cabin["assigned_seniors"] = 0
            cabins.append(new_cabin)
        
        # Count total cabins
        num_cabins = len(cabins)
        if num_cabins == 0:
            logger.warning("No cabins found for passenger distribution")
            return []
        
        # Check if we have "or" options that need to get full passenger counts
        has_or_options = any(cabin.get("is_or_option", False) for cabin in cabins)
        
        # If we have "or" options, each cabin gets the full passenger count
        if has_or_options:
            logger.debug("'OR' options detected - assigning full passenger count to each cabin")
            for cabin in cabins:
                cabin["assigned_adults"] = total_adults
                cabin["assigned_children"] = total_children
                cabin["assigned_seniors"] = total_seniors
            return cabins
        
        # For normal cabins without "or", distribute passengers evenly
        # Initialize remaining passengers
        remaining_adults = total_adults
        remaining_children = total_children
        remaining_seniors = total_seniors
        
        # First pass: distribute passengers evenly
        for i in range(num_cabins):
            # Distribute adults
            adults_per_cabin = remaining_adults // (num_cabins - i)
            cabins[i]["assigned_adults"] = adults_per_cabin
            remaining_adults -= adults_per_cabin
            
            # Distribute children
            children_per_cabin = remaining_children // (num_cabins - i)
            cabins[i]["assigned_children"] = children_per_cabin
            remaining_children -= children_per_cabin
            
            # Distribute seniors
            seniors_per_cabin = remaining_seniors // (num_cabins - i)
            cabins[i]["assigned_seniors"] = seniors_per_cabin
            remaining_seniors -= seniors_per_cabin
        
        logger.debug("Passenger distribution complete")
        return cabins


# Wrapper functions for backward compatibility
def normalize_cabin_category(cabin_type):
    """Backward compatibility wrapper for normalize_cabin_category"""
    logger.debug(f"Normalizing cabin category: {cabin_type}")
    extractor = CruiseInfoExtractor()
    return extractor.normalize_cabin_category(cabin_type)

def extract_cruise_info(text):
    """Backward compatibility wrapper for extract_cruise_info"""
    logger.info("Extracting cruise information via compatibility wrapper")
    extractor = CruiseInfoExtractor()
    return extractor.extract_cruise_info(text)

def distribute_passengers(cabin_categories, total_adults, total_children, total_seniors):
    """Backward compatibility wrapper for distribute_passengers"""
    logger.debug(f"Distributing {total_adults} adults, {total_children} children, {total_seniors} seniors among {len(cabin_categories)} cabins")
    extractor = CruiseInfoExtractor()
    return extractor.distribute_passengers(cabin_categories, total_adults, total_children, total_seniors)

def main():
    logger.info("Enter cruise information (press Enter when finished):")
    lines = []
    
    # Read input until the user enters a blank line
    try:
        while True:
            line = input()
            if line.strip() == "":
                # If the line is blank, break the loop
                break
            lines.append(line)
    except EOFError:
        # Handle EOF (Ctrl+D on Unix, Ctrl+Z on Windows)
        pass
    
    text = "\n".join(lines)
    extractor = CruiseInfoExtractor()
    result, info_dict = extractor.extract_cruise_info(text)
    logger.info("\nExtracted Information:")
    logger.info(result)

if __name__ == "__main__":
    main()