from datetime import datetime, timedelta
import logging
from db_service import get_db_connection, db_connection
from .utils import execute_with_retry

logger = logging.getLogger("database.input_text_storage")

async def store_input_text(request_id, text_input, url_input=None, retention_days=90):
    """Store input text for future reference"""
    async def do_storage():
        try:
            async with db_connection() as conn:
            
                # Check if record already exists
                result = await conn.fetchrow("""
                    SELECT id FROM input_text_storage
                    WHERE request_id = $1
                """, request_id)
                
                if result:
                    # Update existing record
                    await conn.execute("""
                        UPDATE input_text_storage
                        SET text_input = $1, url_input = $2, retention_days = $3, created_at = $4
                        WHERE request_id = $5
                    """, 
                        text_input,
                        url_input or "",
                        retention_days,
                        datetime.now().isoformat(),
                        request_id
                    )
                    logger.info(f"Updated input text for request_id: {request_id}")
                else:
                    # Insert new record
                    await conn.execute("""
                        INSERT INTO input_text_storage
                        (request_id, text_input, url_input, created_at, retention_days)
                        VALUES ($1, $2, $3, $4, $5)
                    """, 
                        request_id,
                        text_input,
                        url_input or "",
                        datetime.now().isoformat(),
                        retention_days
                    )
                    logger.info(f"Stored new input text for request_id: {request_id}")
                
                return True
                
        except Exception as e:
            logger.error(f"Error storing input text: {e}")
            return False
    
    return await execute_with_retry(do_storage, max_retries=3)

async def retrieve_input_text(request_id):
    """Retrieve stored input text by request_id"""
    try:
        async with db_connection() as conn:
            
            result = await conn.fetchrow("""
                SELECT * FROM input_text_storage
                WHERE request_id = $1
            """, request_id)

            if result:
                return dict(result)
            return None

    except Exception as e:
        logger.error(f"Error retrieving input text: {e}")
        return None

async def delete_stored_input_text(request_id):
    """Delete stored input text"""
    try:
        async with db_connection() as conn:
        
            result = await conn.execute("""
                DELETE FROM input_text_storage
                WHERE request_id = $1
            """, request_id)

            deleted = result != "DELETE 0"

            if deleted:
                logger.info(f"Deleted input text for request_id: {request_id}")

            return deleted

    except Exception as e:
        logger.error(f"Error deleting input text: {e}")
        return False

async def cleanup_expired_text_storage():
    """Delete records older than their retention period"""
    try:
        async with db_connection() as conn:
        
            # PostgreSQL syntax for date operations is different from SQLite
            result = await conn.execute("""
                DELETE FROM input_text_storage
                WHERE created_at::timestamp < (NOW() - (retention_days * INTERVAL '1 day'))
            """)

            # Get the number of deleted rows from the result string format "DELETE X"
            deleted_count = int(result.split()[1]) if result else 0

            if deleted_count > 0:
                logger.info(f"Cleaned up {deleted_count} expired input text records")

            return deleted_count

    except Exception as e:
        logger.error(f"Error cleaning up expired text storage: {e}")

        return 0

async def list_stored_inputs(limit=100):
    """List all stored inputs (for admin purposes)"""
    try:
        async with db_connection() as conn:
        
            results = await conn.fetch("""
                SELECT request_id, created_at, retention_days,
                       LENGTH(text_input) as text_length,
                       CASE WHEN url_input IS NOT NULL AND url_input != '' THEN 1 ELSE 0 END as has_url
                FROM input_text_storage
                ORDER BY created_at DESC
                LIMIT $1
            """, limit)


            return [dict(row) for row in results]

    except Exception as e:
        logger.error(f"Error listing stored inputs: {e}")

        return [] 