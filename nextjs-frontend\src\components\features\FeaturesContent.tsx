'use client';

import React, { useState, useEffect, useRef } from 'react';
// import gsap from 'gsap';
// import { ScrollTrigger } from 'gsap/ScrollTrigger';

// NavLink component for sidebar navigation
type NavLinkProps = {
  href: string;
  active: boolean;
  children: React.ReactNode;
};

const NavLink = ({ href, active, children }: NavLinkProps) => {
  return (
    <a
      href={href}
      className={`block px-4 py-2 text-lg transition-all duration-300 rounded-bl-xl rounded-tr-lg mb-3 ${
        active ? 'font-bold' : 'font-semibold'
      }`}
      style={{
        borderWidth: '0px',
        borderStyle: 'solid',
        borderColor: '#0c6066',
        color: '#036868',
        boxShadow: '0 3px 6px rgba(0, 0, 0, 0.3)',
        background: active
          ? 'linear-gradient(0deg, #dcdcdc 0%, #89aeae  98%)'
          : 'white',
        transition: 'background 0.3s ease'
      }}
      onMouseOver={(e) => {
        if (!active) {
          e.currentTarget.style.background = 'linear-gradient(0deg, #dcdcdc 0%, #89aeae  98%)';
        }
      }}
      onMouseOut={(e) => {
        if (!active) {
          e.currentTarget.style.background = 'white';
        }
      }}
    >
      {children}
    </a>
  );
};

export default function FeaturesContent() {
  const [scrolled, setScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState('key-features');
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const sliderRef = useRef<HTMLDivElement>(null);
  const autoSlideTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Feature cards data
  const featureCards = [
    {
      title: "Automated Extraction",
      description: "Automatically extract cruise details from quote text, eliminating manual data entry and reducing errors."
    },
    {
      title: "Multi-Provider Support",
      description: "Support for multiple cruise providers including Studio, NCL, and Royal Caribbean's Cruising Power."
    },
    {
      title: "Parallel Processing",
      description: "Handle multiple booking requests simultaneously for increased efficiency."
    },
    {
      title: "Browser Automation",
      description: "Sophisticated browser automation that mimics human interaction with booking systems."
    },
    {
      title: "Data Validation",
      description: "Comprehensive validation of extracted data to ensure accuracy before processing."
    },
    {
      title: "Secure Handling",
      description: "Secure processing of sensitive booking information with proper data protection measures."
    }
  ];

  const totalSlides = featureCards.length - 3; // Total number of possible starting positions

  const handleCardHover = (e: React.MouseEvent<HTMLDivElement>) => {
    const index = parseInt(e.currentTarget.getAttribute('data-index') || '0', 10);
    setHoveredCard(index);
  };

  const handleCardHoverEnd = () => {
    setHoveredCard(null);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  // Auto slide functionality - faster interval
  useEffect(() => {
    // Start auto-sliding
    autoSlideTimerRef.current = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % totalSlides);
    }, 5000); // Change slide every 5 seconds (reduced from 7 seconds)

    // Clean up timer on component unmount
    return () => {
      if (autoSlideTimerRef.current) {
        clearInterval(autoSlideTimerRef.current);
      }
    };
  }, []);



  // Handle scroll event to track when page is scrolled
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);

      // Update active section based on scroll position
      const sections = document.querySelectorAll('section[id]');
      let currentSectionId = 'key-features';

      sections.forEach(section => {
        const sectionTop = section.getBoundingClientRect().top;
        if (sectionTop < 100) {
          currentSectionId = section.id;
        }
      });

      setActiveSection(currentSectionId);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Scroll to top on page load/refresh
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo({ top: 0, behavior: 'auto' });
  }, []);

  return (
    <div className="min-h-screen relative font-['Times_New_Roman']">
      {/* Circular gradient background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0" style={{ backgroundColor: '#c0ffee', backgroundImage: 'linear-gradient(45deg, #c0ffee 0%, #d4d4d4 100%)' }}></div>
      </div>

      {/* Content with nav and main sections */}
      <div className="relative z-10 min-h-screen flex flex-col">
        <main className="flex-grow container mx-auto px-6 py-16 flex">
          {/* Sidebar Navigation */}
          <div className="hidden lg:block w-80 flex-shrink-0 pr-10">
            <div className="fixed w-72 space-y-5 rounded-lg top-48 p-6">
              <h3 className="text-3xl font-bold mb-6" style={{ color: '#036868' }}>Features</h3>
              <NavLink href="#key-features" active={activeSection === 'key-features'}>
                Features
              </NavLink>
              <NavLink href="#performance-statistics" active={activeSection === 'performance-statistics'}>
                Performance Statistics
              </NavLink>
              <NavLink href="#technical-capabilities" active={activeSection === 'technical-capabilities'}>
                Technical Capabilities
              </NavLink>
              <NavLink href="#supported-cruise-lines" active={activeSection === 'supported-cruise-lines'}>
                Supported Cruise Lines
              </NavLink>
            </div>
          </div>

          {/* Visual divider */}
          <div className="hidden lg:block w-px h-full bg-sky-100/50 mx-4"></div>

          <div className="flex-grow pl-4">
            {/* Page Title */}
            <div className="mb-16 mt-8">
              <h1 className="text-6xl text-shadow-xl font-bold text-sky-900 mb-3">Features</h1>
              <p className="text-xl text-sky-900 max-w-4xl">
                Discover how Oceanmind transforms the cruise booking experience with advanced AI technology
                and intelligent automation, saving you valuable time and resources.
              </p>
            </div>

            {/* Features Section */}
            <section id="key-features" className="mb-16 scroll-mt-24">

              <div className="flex flex-col pb-4" style={{ boxShadow: 'none' }}>
                {/* Feature cards slider */}
                <div className="relative pb-16 w-full" style={{ boxShadow: 'none' }}>
                  {/* Slider container */}
                  <div
                    ref={sliderRef}
                    className="flex flex-row flex-nowrap mb-6 relative overflow-hidden"
                    style={{ width: '100%', maxWidth: '1200px', margin: '0 auto', boxShadow: 'none' }}
                  >
                    <div
                      className="flex transition-transform duration-300 ease-in-out space-x-5"
                      style={{
                        transform: `translateX(-${currentSlide * 300}px)`,
                        boxShadow: 'none'
                      }}
                    >
                      {featureCards.map((card, index) => (
                        <div
                          key={index}
                          onMouseEnter={() => handleCardHover({ currentTarget: { getAttribute: () => index.toString() } } as any)}
                          onMouseLeave={handleCardHoverEnd}
                          data-index={index}
                          style={{
                            position: 'relative',
                            zIndex: hoveredCard === index ? 10 : 1,
                            boxShadow: 'none'
                          }}
                        >
                          <FeatureArticleCard
                            title={card.title}
                            description={card.description}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Navigation arrows */}
                  <button
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 text-white rounded-full p-2 shadow-md z-20 transition-colors duration-300"
                    style={{ marginLeft: '-45px', marginTop: '-20px', backgroundColor: '#064364' }}
                    onClick={prevSlide}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <button
                    className="absolute right-0 top-1/2 transform -translate-y-1/2 text-white rounded-full p-2 shadow-md z-20 transition-colors duration-300"
                    style={{ marginRight: '-30px', marginTop: '-20px', backgroundColor: '#064364' }}
                    onClick={nextSlide}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>

                  {/* Dots navigation */}
                  <div className="flex justify-center mt-4">
                    {Array.from({ length: totalSlides }).map((_, index) => (
                      <button
                        key={index}
                        className={`h-2 w-2 mx-1 rounded-full transition-colors ${currentSlide === index ? '' : 'bg-gray-300 hover:bg-blue-400'
                          }`}
                        style={{ backgroundColor: currentSlide === index ? '#064364' : '' }}
                        onClick={() => goToSlide(index)}
                        aria-label={`Go to slide ${index + 1}`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </section>

            {/* Performance Statistics */}
            <section id="performance-statistics" className="mb-16 scroll-mt-24">
              <h2 className="text-6xl text-shadow-xl font-extrabold text-sky-900 mb-6 border-b border-sky-200 pb-2">Performance Statistics</h2>

              <div className="bg-transparent rounded-lg p-6 mb-6">
                <h3 className="text-2xl font-bold text-sky-900 mb-4">Time Savings by Cruise Line</h3>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <StatCard name="Royal Caribbean" percentage="58%" />
                  <StatCard name="Celebrity" percentage="51%" />
                  <StatCard name="Princess" percentage="45%" />
                  <StatCard name="Carnival" percentage="42%" />
                </div>

                <h3 className="text-2xl font-bold text-sky-900 mb-4">Cabin Type Performance</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="bg-transparent p-4 rounded-lg">
                    <h4 className="text-xl font-bold text-sky-900 mb-2">Single Cabin</h4>
                    <div className="flex items-center">
                      <div className="w-24 h-24 relative mr-4">
                        <svg viewBox="0 0 36 36" className="w-full h-full">
                          <circle cx="18" cy="18" r="15.915" fill="none" stroke="#e5e7eb" strokeWidth="3"></circle>
                          <circle cx="18" cy="18" r="15.915" fill="none" stroke="green" strokeWidth="3" strokeDasharray="56, 100" strokeDashoffset="25" className="transform -rotate-90 origin-center"></circle>
                        </svg>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <span className="text-3xl font-extrabold text-sky-900">56%</span>
                        </div>
                      </div>
                      <div>
                        <p className="text-xl text-sky-900 font-semibold">Time reduction for single cabin bookings</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-transparent p-4 rounded-lg">
                    <h4 className="text-xl font-bold text-sky-900 mb-2">Multiple Cabins</h4>
                    <div className="flex items-center">
                      <div className="w-24 h-24 relative mr-4">
                        <svg viewBox="0 0 36 36" className="w-full h-full">
                          <circle cx="18" cy="18" r="15.915" fill="none" stroke="#e5e7eb" strokeWidth="3"></circle>
                          <circle cx="18" cy="18" r="15.915" fill="none" stroke="green" strokeWidth="3" strokeDasharray="49, 100" strokeDashoffset="25" className="transform -rotate-90 origin-center"></circle>
                        </svg>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <span className="text-3xl font-extrabold text-sky-900">49%</span>
                        </div>
                      </div>
                      <div>
                        <p className="text-xl text-sky-900 font-semibold">Time reduction for multiple cabin bookings</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-transparent p-4 rounded-lg">
                  <h4 className="text-2xl font-bold text-sky-900 mb-2">Overall System Performance</h4>
                  <div className="h-5 w-full bg-gray-100 rounded-full mb-2 overflow-hidden">
                    <div className="h-5 bg-green-700 flex items-center justify-end pr-3 text-white font-bold text-sm rounded-full" style={{ width: '55%' }}>
                      55% Faster
                    </div>
                  </div>
                  <p className="text-xl text-sky-900 font-semibold">Average time reduction across all cruise lines and cabin types</p>
                </div>
              </div>
            </section>

            {/* Technical Capabilities */}
            <section id="technical-capabilities" className="mb-16 mt-40 scroll-mt-24">
              <h2 className="text-6xl text-shadow-xl font-bold text-sky-900 mb-6 border-b border-sky-200 pb-2">Technical Capabilities</h2>

              <div className="bg-transparent rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <TechCapability
                    title="AI-Powered Text Processing"
                    description="Advanced natural language processing to accurately extract booking details from unstructured text."
                    icon="🧠"
                  />

                  <TechCapability
                    title="Parallel Processing"
                    description="Handle multiple booking requests simultaneously for increased efficiency."
                    icon="⚡"
                  />

                  <TechCapability
                    title="Browser Automation"
                    description="Sophisticated browser automation that mimics human interaction with booking systems."
                    icon="🖥️"
                  />

                  <TechCapability
                    title="Data Validation"
                    description="Comprehensive validation of extracted data to ensure accuracy before processing."
                    icon="✓"
                  />

                  <TechCapability
                    title="Secure Handling"
                    description="Secure processing of sensitive booking information with proper data protection measures."
                    icon="🔒"
                  />

                  <TechCapability
                    title="Adaptive Learning"
                    description="System improves over time by learning from successful bookings and user corrections."
                    icon="📈"
                  />
                </div>
              </div>
            </section>

            {/* Supported Cruise Lines */}
            <section id="supported-cruise-lines" className="mb-16 mt-40 scroll-mt-24">
              <h2 className="text-6xl font-bold text-sky-900 mb-12 border-b text-shadow-xl border-sky-200 pb-2">Supported Cruise Lines</h2>

              <div className="bg-transparent rounded-lg p-6">
                {/* First row of cruise line cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                  <CruiseLineCard
                    name="Royal Cruise Line"
                    description="Complete support with 58% average time savings"
                    count="111 quotes processed"
                  />

                  <CruiseLineCard
                    name="Celebrity Cruise Line"
                    description="Full integration with 51% time reduction"
                    count="100 quotes processed"
                  />

                  <CruiseLineCard
                    name="Princess Cruise Line"
                    description="Comprehensive support with verification"
                    count="100 quotes processed"
                  />
                </div>

                {/* Second row of cruise line cards with increased gap from first row */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <CruiseLineCard
                    name="Carnival Cruise Line"
                    description="Full booking automation support"
                    count="64 quotes processed"
                  />

                  <CruiseLineCard
                    name="Norwegian Cruise Line"
                    description="Complete CNM booking support"
                    count="120 quotes processed"
                  />

                  <CruiseLineCard
                    name="Studio (Working Cruise Line)"
                    description="Advanced automation with 45% time savings"
                    count="311 quotes processed"
                  />
                </div>
              </div>
            </section>
          </div>
        </main>
      </div>
    </div>
  );
}

const FeatureArticleCard = ({ title, description }: { title: string; description: string }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="rounded-md overflow-hidden transition-all duration-300 hover:translate-y-[-5px] focus:outline-none relative cursor-pointer"
      style={{
        width: '280px',
        height: '380px',
        border: 'none',
        outline: 'none',
        boxShadow: isHovered
          ? '0 8px 16px rgba(4, 63, 90, 0.5), 0 4px 8px rgba(0, 0, 0, 0.2)'
          : '0 4px 8px rgba(0, 0, 0, 0.1)'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background gradient div */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundColor: '#e8e8e8',
          backgroundImage: 'linear-gradient(180deg, #e8e8e8 1%, #03627b 100%)'
        }}
      ></div>

      <div className="h-full p-6 flex flex-col justify-between relative z-10">
        <div>
          <h3 className="text-3xl font-bold mb-4" style={{ color: '#043F5A' }}>{title}</h3>
        </div>
        <div>
          <p className="text-white text-xl">{description}</p>
        </div>
      </div>
    </div>
  );
};

const StatCard = ({ name, percentage }: { name: string; percentage: string }) => (
  <div className="bg-transparent p-4 rounded-lg">
    <h4 className="text-xl font-bold text-sky-900 mb-2">{name}</h4>
    <div className="h-5 w-full bg-gray-100 rounded-full mb-2">
      <div
        className="h-5 bg-green-700 rounded-full"
        style={{ width: percentage }}
      ></div>
    </div>
    <div className="text-right text-xl font-extrabold text-sky-900">{percentage}</div>
  </div>
);

const TechCapability = ({ title, description, icon }: { title: string; description: string; icon: string }) => (
  <div className="backdrop-blur-sm p-6 rounded-lg flex items-start cursor-pointer hover:shadow-[0_8px_16px_rgba(0,0,0,0.2)] transition-all duration-300">
    <div className="text-3xl mr-4">{icon}</div>
    <div>
      <h3 className="text-xl font-bold text-sky-900 mb-2">{title}</h3>
      <p className="text-lg text-sky-900">{description}</p>
    </div>
  </div>
);

const CruiseLineCard = ({ name, description, count }: { name: string; description: string; count: string }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-xl cursor-pointer overflow-hidden"
      style={{
        backgroundColor: '#c4e2df',
        backgroundImage: 'linear-gradient(180deg, #c4e2df 0%, #1a5c64 100%)',
        transform: isHovered ? 'translateY(-5px)' : 'translateY(0)',
        boxShadow: isHovered ? '0 10px 20px rgba(0, 0, 0, 0.2)' : '0 4px 8px rgba(0, 0, 0, 0.1)'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative z-10">
        <h3 className="text-3xl font-bold mb-8" style={{ color: '#013e5f' }}>{name}</h3>
        <p className="text-xl text-white mb-2">{description}</p>
        <p className="text-lg text-white font-medium">{count}</p>
      </div>

      {/* Optional decorative element */}
      <div
        className="absolute top-0 right-0 w-16 h-16 rounded-full bg-white/10 transition-transform duration-300"
        style={{
          transform: isHovered ? 'scale(1.2)' : 'scale(1)',
          opacity: 0.2,
          right: '-20px',
          top: '-20px'
        }}
      ></div>
    </div>
  );
};