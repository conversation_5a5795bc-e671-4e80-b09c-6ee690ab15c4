"""
Database operations for the OneSource provider.
"""
import logging
import re
from datetime import datetime
from typing import Dict, List, Optional
from db_service import get_db_connection, db_connection
from db.utils import register_session

# Configure logging
logger = logging.getLogger("database.onesource_provider")

def extract_category_info(category_name: str) -> tuple[str, str, str]:
    """
    Extract category type, category code, and rate code from category name.
    
    Args:
        category_name: Category name like "D44 Balcony" or "MF4 Suites"
        
    Returns:
        Tuple of (category_type, category_code, rate_code)
    """
    if not category_name:
        return "Unknown", "", ""
    
    # Extract category type
    category_lower = category_name.lower()
    if 'suite' in category_lower:
        category_type = "Suite"
    elif 'balcony' in category_lower or 'veranda' in category_lower:
        category_type = "Balcony"
    elif 'inside' in category_lower or 'interior' in category_lower:
        category_type = "Inside"
    elif 'outside' in category_lower or 'oceanview' in category_lower or 'obstructed' in category_lower:
        category_type = "Outside"
    else:
        category_type = "Unknown"
    
    # Extract category code (up to first two non-space characters)
    code_match = re.match(r"\s*(\S{1,2})", category_name)
    category_code = code_match.group(1).upper() if code_match else ""
    
    # For now, rate_code will be extracted separately from promo info
    rate_code = ""
    
    return category_type, category_code, rate_code

async def save_onesource_results(results: Dict, request_id: str, session_id: str = None):
    """Save OneSource booking results to the database"""
    try:
        # If no session_id provided, create and register one
        if not session_id:
            session_id = await register_session(request_id, 'OneSource')
            logger.info(f"Created new session ID for OneSource booking {request_id}: {session_id}")
        
        # Log the detailed results for debugging
        logger.debug(f"OneSource results to save: {results}")
        
        # Extract booking-level information
        total_cabins = results.get('total_cabins', 0)
        grand_total = results.get('total_price', 0)
        execution_time = results.get('execution_time', 0)
        overall_status = 1 if results.get('overall_status', False) else 0
        is_or_case = results.get('is_or_case', False)
        
        # Calculate grand_total from cabin results if not provided
        if grand_total == 0:
            cabin_results = results.get('cabins', [])
            for cabin in cabin_results:
                if cabin.get('success', False):
                    cabin_total = cabin.get('cabin_total', 0)
                    if isinstance(cabin_total, (int, float)) and cabin_total > 0:
                        grand_total += cabin_total
        
        logger.info(f"Saving OneSource booking with request_id: {request_id}, cabins: {total_cabins}, grand total: {grand_total}, execution time: {execution_time}s")
        
        # Insert into onesource_bookings
        async with db_connection() as conn:
            # Insert booking record
            booking_row = await conn.fetchrow('''
            INSERT INTO onesource_bookings 
            (request_id, timestamp, total_cabins, grand_total, execution_time, overall_status, session_id, is_or_case)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id
            ''', 
                request_id,
                datetime.now().isoformat(),
                total_cabins,
                grand_total,
                execution_time,
                overall_status,
                session_id,
                is_or_case
            )
            
            booking_id = booking_row['id']
            
            # Insert cabin entries into onesource_cabins
            cabin_results = results.get('cabins', [])
            for cabin in cabin_results:
                try:
                    # Extract cabin information
                    cabin_number = cabin.get('cabin_number', 0)
                    success = cabin.get('success', False)
                    status = 'success' if success else 'failed'
                    error = cabin.get('error', '')
                    
                    # Extract passenger count
                    passenger_count = cabin.get('passengers', 0)
                    
                    # Extract price information
                    commission = cabin.get('commission', 0)
                    gross_fare = cabin.get('gross_fare', 0)
                    onboard_credit = cabin.get('onboard_credit', 0)
                    cabin_total = cabin.get('cabin_total', 0)
                    
                    # Extract category information
                    category_type = "Unknown"
                    category_code = ""
                    selected_rate_code = ""
                    
                    # Get category info from selected category
                    if 'selected_category_name' in cabin:
                        selected_category_name = cabin['selected_category_name']
                        category_type, category_code, _ = extract_category_info(selected_category_name)
                        selected_rate_code = cabin.get('selected_rate_code', '')
                    elif 'cabin_type' in cabin:
                        # Fallback to cabin_type if no selected category
                        cabin_type = cabin['cabin_type']
                        category_type, category_code, _ = extract_category_info(cabin_type)
                    
                    # Insert cabin record
                    await conn.execute('''
                    INSERT INTO onesource_cabins 
                    (booking_id, cabin_number, category_type, category_code, selected_rate_code, 
                     passenger_count, commission, gross_fare, onboard_credit, cabin_total, status, error)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    ''',
                        booking_id,
                        cabin_number,
                        category_type,
                        category_code,
                        selected_rate_code,
                        passenger_count,
                        commission,
                        gross_fare,
                        onboard_credit,
                        cabin_total,
                        status,
                        error
                    )
                    
                except Exception as e:
                    logger.error(f"Error saving cabin {cabin.get('cabin_number', 'unknown')}: {e}")
                    continue
            
            logger.info(f"Successfully saved OneSource booking {request_id} with {len(cabin_results)} cabins")
            return True
            
    except Exception as e:
        logger.error(f"Error saving OneSource results for {request_id}: {e}")
        return False

async def get_onesource_results(request_id: str, session_id: str = None) -> tuple[Optional[Dict], Optional[List[Dict]]]:
    """Retrieve OneSource booking results from the database"""
    try:
        async with db_connection() as conn:
            # Get booking record
            if session_id:
                booking_query = '''
                SELECT * FROM onesource_bookings 
                WHERE request_id = $1 AND session_id = $2
                ORDER BY timestamp DESC LIMIT 1
                '''
                booking_row = await conn.fetchrow(booking_query, request_id, session_id)
            else:
                booking_query = '''
                SELECT * FROM onesource_bookings 
                WHERE request_id = $1
                ORDER BY timestamp DESC LIMIT 1
                '''
                booking_row = await conn.fetchrow(booking_query, request_id)
            
            if not booking_row:
                logger.warning(f"No OneSource booking found for request_id: {request_id}")
                return None, None
            
            booking_data = dict(booking_row)
            booking_id = booking_data['id']
            
            # Get cabin records
            cabin_query = '''
            SELECT * FROM onesource_cabins 
            WHERE booking_id = $1
            ORDER BY cabin_number
            '''
            cabin_rows = await conn.fetch(cabin_query, booking_id)
            cabin_data = [dict(row) for row in cabin_rows]
            
            logger.info(f"Retrieved OneSource booking {request_id} with {len(cabin_data)} cabins")
            return booking_data, cabin_data
            
    except Exception as e:
        logger.error(f"Error retrieving OneSource results for {request_id}: {e}")
        return None, None

async def update_cabin_category_info(booking_id: int, cabin_number: int, 
                                   category_name: str, rate_code: str = ""):
    """
    Update category information for a specific cabin after category selection.
    
    Args:
        booking_id: The booking ID
        cabin_number: The cabin number
        category_name: Selected category name (e.g., "D44 Balcony")
        rate_code: Selected rate code/promo (e.g., "NCP")
    """
    try:
        category_type, category_code, _ = extract_category_info(category_name)
        
        async with db_connection() as conn:
            await conn.execute('''
            UPDATE onesource_cabins 
            SET category_type = $1, category_code = $2, selected_rate_code = $3
            WHERE booking_id = $4 AND cabin_number = $5
            ''',
                category_type,
                category_code,
                rate_code,
                booking_id,
                cabin_number
            )
            
        logger.info(f"Updated cabin {cabin_number} category info: {category_type}, {category_code}, {rate_code}")
        
    except Exception as e:
        logger.error(f"Error updating cabin category info: {e}")

async def update_cabin_price_info(booking_id: int, cabin_number: int, 
                                commission: float, gross_fare: float, cabin_total: float):
    """
    Update price information for a specific cabin after price extraction.
    
    Args:
        booking_id: The booking ID
        cabin_number: The cabin number
        commission: Commission amount
        gross_fare: Gross fare amount
        cabin_total: Final calculated cabin total
    """
    try:
        async with db_connection() as conn:
            await conn.execute('''
            UPDATE onesource_cabins 
            SET commission = $1, gross_fare = $2, cabin_total = $3
            WHERE booking_id = $4 AND cabin_number = $5
            ''',
                commission,
                gross_fare,
                cabin_total,
                booking_id,
                cabin_number
            )
            
        logger.info(f"Updated cabin {cabin_number} price info: commission={commission}, gross_fare={gross_fare}, total={cabin_total}")
        
    except Exception as e:
        logger.error(f"Error updating cabin price info: {e}") 