'use client';

import React, { useState } from 'react';
import { registerUser } from '../services/auth';

// CSS for hover gradient
const hoverStyles = `
  .hover-gradient:hover {
    background-color: #394349 !important;
    background-image: linear-gradient(180deg, #394349 0%, #7cbbb5 100%) !important;
  }
`;

interface RegisterModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const RegisterModal: React.FC<RegisterModalProps> = ({ isOpen, onClose }) => {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  if (!isOpen) return null;

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    setError(null);
    
    // Validate password match
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }
    
    try {
    
      // Role and agency will be assigned by admin later
      const result = await registerUser({
        username,
        password,
        email,
        full_name: fullName,
        // Default values will be overridden by admin
        role: 'user', 
        agency: 'STUDIO',
        portal_access: []
      });
      
      // Show success message
      setShowSuccess(true);
      
      // Close the modal after a delay
      setTimeout(() => {
        onClose();
        // Reset the success state after closing
        setTimeout(() => setShowSuccess(false), 300);
      }, 3000);
    } catch (error) {
      console.error('Registration error:', error);
      setError(error instanceof Error ? error.message : 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = (e: React.MouseEvent) => {
    e.preventDefault();
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-55">
      <style dangerouslySetInnerHTML={{ __html: hoverStyles }} />

      {showSuccess ? (
        <div className="rounded-lg shadow-xl p-6 max-w-md w-full"
             style={{
               background: 'linear-gradient(180deg, #55d4e0 5%, #00262b 100%)'
             }}>
          <div className="flex justify-end mb-2">
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div className="flex flex-col items-center">
            <div className="bg-white/20 rounded-full p-4 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Registration Request Submitted!</h3>
            <p className="text-white text-center">
              Your registration request has been submitted. An administrator will review your request and assign appropriate access roles.
              You will receive an email notification once your account is approved.
            </p>
          </div>
        </div>
      ) : (
        <div className="relative flex w-full max-w-3xl overflow-hidden rounded-lg shadow-xl" style={{ maxHeight: '97vh', overflowY: 'auto' }}>
          {/* Left side - Teal gradient with illustration */}
          <div className="hidden md:flex md:w-1/3 p-6 text-white flex-col justify-between"
               style={{
                 background: 'linear-gradient(180deg, #55d4e0 5%, #00262b 100%)'
               }}>
            <div className="flex-grow">
              <h2 className="text-3xl font-bold ">OceanMind-o</h2>
              <h2 className='text-2xl font-bold'>HAI Agent</h2>
            </div>

            <div className="mb-4">
              {/* Login Icon */}
              <div className="flex justify-center items-center mb-3">
                <div className="bg-white/20 p-3 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                </div>
              </div>
              <p className="text-sm opacity-80 text-center">Secure registration with enhanced protection</p>
            </div>
          </div>

          {/* Right side - Registration form */}
          <div className="w-full md:w-2/3 bg-white p-10 flex flex-col">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-3xl font-bold text-[#013e5f]">Create your Account</h2>
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="flex-grow">
              {error && (
                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {error}
                </div>
              )}
              
              <form onSubmit={handleRegister} className="mb-8">
                <div className="mb-6">
                  <label htmlFor="fullName" className="block text-base font-medium text-[#0F6095] mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    className="w-full px-4 py-3 text-base bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Enter your full name"
                    required
                    disabled={isLoading}
                  />
                </div>

                <div className="mb-6">
                  <label htmlFor="username" className="block text-base font-medium text-[#0F6095] mb-2">
                    Username
                  </label>
                  <input
                    type="text"
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="w-full px-4 py-3 text-base bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Choose a username"
                    required
                    disabled={isLoading}
                  />
                </div>

                <div className="mb-6">
                  <label htmlFor="email" className="block text-base font-medium text-[#0F6095] mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-4 py-3 text-base bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Enter your email"
                    required
                    disabled={isLoading}
                  />
                </div>

                <div className="mb-6">
                  <label htmlFor="password" className="block text-base font-medium text-[#0F6095] mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 text-base bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Create a password"
                    required
                    disabled={isLoading}
                  />
                </div>

                <div className="mb-6">
                  <label htmlFor="confirmPassword" className="block text-base font-medium text-[#0F6095] mb-2">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="w-full px-4 py-3 text-base bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Confirm your password"
                    required
                    disabled={isLoading}
                  />
                </div>

                <div className="mb-6">
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-md text-blue-700">
                    <p className="text-sm">
                      <strong>Important:</strong> After registration, an administrator will review your request and 
                      assign appropriate agency access and permissions. You will receive an email notification once your account is approved.
                    </p>
                  </div>
                </div>

                <div className="mb-6 text-xs text-gray-600">
                  By continuing, you agree to Oceanmind's <a href="#" className="text-[#0F6095]">Terms of Use</a> and <a href="#" className="text-[#0F6095]">Privacy Policy</a>.
                </div>

                <div className="mt-4">
                  <button
                    type="submit"
                    className="w-full py-2 px-4 text-lg text-white font-semibold rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 hover-gradient"
                    style={{
                      backgroundColor: '#394349',
                      backgroundImage: 'linear-gradient(0deg, #394349 0%, #7cbbb5 100%)',
                      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                    }}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Submitting...' : 'Request Account'}
                  </button>
                </div>
              </form>

              <div className="text-center mt-2">
                <a href="#" onClick={handleLogin} className="text-lg text-[#0F6095] hover:text-[#013e5f]">
                  Already have an account? Login
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RegisterModal;
