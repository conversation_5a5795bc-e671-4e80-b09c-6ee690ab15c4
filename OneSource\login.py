import os
import async<PERSON>
import random
from dotenv import load_dotenv
from loguru import logger
from Core.ui_logger import ui_log
from Core.browser_setup import AsyncBrowserSetup, browser_manager

# Load environment variables from .env file
load_dotenv()

# Get credentials from environment variables
onesource_username = os.getenv("ONESOURCE_USERNAME")
onesource_password = os.getenv("ONESOURCE_PASSWORD")
onesource_login_url = os.getenv("ONESOURCE_LOGIN_URL")


class OneSourceLogin:
    """
    Class for handling login functionality to OneSource website
    """
    
    def __init__(self, username=None, password=None, login_url=None, headless=True, 
                 existing_page=None, video_auditing=False, session_id=None, cabin_id=None):
        """
        Initialize the OneSource login handler.
        
        Args:
            username: Username for login (defaults to env variable)
            password: Password for login (defaults to env variable)
            login_url: Login URL (defaults to env variable)
            headless: Whether to run the browser in headless mode (defaults to True)
            existing_page: Existing Playwright Page instance (optional)
            video_auditing: Whether to enable video recording for audit purposes (defaults to False)
            session_id: Session identifier for UI log scoping
            cabin_id: Cabin identifier for video recording and logging
        """
        self.username = username or onesource_username
        self.password = password or onesource_password
        self.login_url = login_url or onesource_login_url
        self.headless = headless
        self.existing_page = existing_page
        self.video_auditing = video_auditing
        self.session_id = session_id
        self.cabin_id = cabin_id
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
        self.browser_setup = None
        
        # Validate credentials
        if not self.username or not self.password or not self.login_url:
            raise ValueError("OneSource credentials not found in environment variables")
    
    async def login(self):
        """
        Perform login to OneSource website
        
        Returns:
            Tuple (page, success) with Playwright Page and boolean success indicator
        """
        try:
            # Use existing page if provided, otherwise create a new one
            if self.existing_page:
                logger.info("Using existing Playwright page for OneSource login")
                self.page = self.existing_page
            else:
                # Use shared browser for OneSource
                logger.info("Using shared OneSource browser")
                ui_log("Allocating resources - just a moment", 
                      session_id=self.session_id, 
                      cabin_id=self.cabin_id,
                      step="starting_browser", 
                      module="OneSource")
                
                # Get the shared browser instance for OneSource
                browser = await browser_manager.get_or_launch_browser('OneSource')
                logger.info("Retrieved shared OneSource browser")
                
                # Create browser setup instance for context creation
                self.browser_setup = AsyncBrowserSetup()
                
                # Create an optimized context
                self.context = await self.browser_setup.create_optimized_context(
                    browser,
                    logger=logger,
                    video_auditing=self.video_auditing,
                    cabin_id=self.cabin_id,
                    session_id=self.session_id
                )
                
                # Create a new page and configure it
                self.page = await self.context.new_page()
                
                # Set default timeout for page operations to 30 seconds
                self.page.set_default_timeout(30000)
                self.playwright = browser_manager.get_playwright('OneSource')
                
                # Apply stealth mode for anti-detection
                await self._apply_stealth_mode(self.page)
                
                # Setup resource blocking for faster loading (block images/fonts/videos)
                await self.browser_setup.setup_resource_blocking(self.page, logger, block=True)
                
                logger.info("Browser context setup complete with anti-detection")
                ui_log("Resource allocation complete - taking input", 
                      session_id=self.session_id, 
                      cabin_id=self.cabin_id,
                      step="browser_ready", 
                      module="OneSource")
            
            # Navigate to the login page
            logger.info(f"Navigating to OneSource login page: {self.login_url}")
            ui_log("Navigating to login page", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_id,
                  step="loading_login_page", 
                  module="OneSource")
            
            await self.page.goto(self.login_url)
            
            # Handle cookie consent popup if present
            await self._handle_cookie_popup()
            
            # Add human-like delay
            await self._random_delay(0.2, 0.5)
            
            # Find and fill in the username field
            logger.info("Entering username...")
            ui_log("Entering credentials", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_id,
                  step="submitting_credentials", 
                  module="OneSource")
            
            username_field = await self.page.wait_for_selector("#userName", timeout=15000)
            await self._human_like_typing(username_field, self.username)
            
            # Add human-like delay before typing password
            await self._random_delay(0.2, 0.4)
            
            # Find and fill in the password field
            logger.info("Entering password...")
            password_field = await self.page.wait_for_selector("#password", timeout=5000)
            await self._human_like_typing(password_field, self.password)
            
            # Add delay before clicking sign-in
            await self._random_delay(0.1, 0.25)
            
            # Click the sign-in button
            logger.info("Clicking sign in button...")
            sign_in_button = await self.page.wait_for_selector(
                "#__next > div > div > div > div > div.content-wrapper > div > section > div > div > div.col-lg-5.lhs.login-form > div > div.col-md-6.col-lg-10.login-form-wrp > form > div > div.w-100 > div:nth-child(3) > button",
                timeout=5000
            )
            
            # Alternative selector for the sign-in button
            if not sign_in_button:
                sign_in_button = await self.page.wait_for_selector(
                    'button[type="submit"].btn.fill.col-12.bg-warning:has-text("Sign In")',
                    timeout=5000
                )
            
            # Click and wait for navigation
            try:
                async with self.page.expect_navigation(timeout=30000):
                    await sign_in_button.click()
            except:
                # If navigation doesn't happen, just click the button
                await sign_in_button.click()
            
            
            # Check for common error indicators
            error_indicators = [
                ".error-message",
                ".alert-danger",
                ".login-error",
                "[class*='error']",
                "[class*='invalid']"
            ]
            
            login_failed = False
            for indicator in error_indicators:
                try:
                    error_element = await self.page.query_selector(indicator)
                    if error_element:
                        error_text = await error_element.text_content()
                        if error_text and error_text.strip():
                            logger.warning(f"Login error detected: {error_text}")
                            login_failed = True
                            break
                except:
                    continue
            
            # Check if we're still on the login page (another indicator of failure)
            current_url = self.page.url
            if "login" in current_url.lower() and not login_failed:
                # Additional check - look for username field still present
                try:
                    username_still_present = await self.page.query_selector("#userName")
                    if username_still_present:
                        login_failed = True
                        logger.warning("Login may have failed - still on login page")
                except:
                    pass
            
            if login_failed:
                logger.error("Login failed for OneSource")
                ui_log("Login failed - retrying now", 
                      session_id=self.session_id, 
                      cabin_id=self.cabin_id,
                      step="retrying_login", 
                      module="OneSource")
                return self.page, False
            else:
                logger.success("Login successful for OneSource!")
                
                # Removed image blocking for full resource loading
                
                ui_log("Login successful - ready to proceed", 
                      session_id=self.session_id, 
                      cabin_id=self.cabin_id,
                      step="logged_in", 
                      module="OneSource")
                return self.page, True
                
        except Exception as e:
            logger.error(f"An error occurred during OneSource login: {e}")
            ui_log("There was an error during login - retrying now", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_id,
                  step="login_error", 
                  module="OneSource")
            return self.page, False
    
    async def _apply_stealth_mode(self, page):
        """Apply stealth mode to avoid headless detection"""
        try:
            await page.add_init_script("""
            () => {
                // Override the 'navigator.webdriver' property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                    configurable: true
                });
                
                // Override the language property
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                    configurable: true
                });
                
                // Override the plugins property
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                    configurable: true
                });
                
                // Override Chrome property
                Object.defineProperty(window, 'chrome', {
                    get: () => ({
                        runtime: {},
                        app: {},
                    }),
                    configurable: true
                });
                
                // Override permissions
                Object.defineProperty(navigator, 'permissions', {
                    get: () => ({
                        query: () => Promise.resolve({ state: 'granted' })
                    }),
                    configurable: true
                });
                
                // Pass the user-agent check
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
                );
            }
            """)
            
            logger.info("Applied stealth mode for OneSource")
        except Exception as e:
            logger.error(f"Error applying stealth mode: {e}")
    
    async def _random_delay(self, min_seconds=0.5, max_seconds=2.0):
        """Add a random delay to simulate human behavior"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def _human_like_typing(self, element, text, min_delay=0.05, max_delay=0.15):
        """Type text in a human-like manner with varying delays between keystrokes"""
        await element.click()
        # Clear any existing text using Playwright's fill method with empty string
        await element.fill("")
        # Add a small delay after clearing
        await asyncio.sleep(0.1)
        # Fill the full text at once (sequential) to avoid position errors
        await element.fill(text)
        # Simulate typing time with random delay
        await asyncio.sleep(random.uniform(min_delay * len(text), max_delay * len(text)))
    
    async def stop_video_recording(self, request_id=None, provider="OneSource"):
        """Stop video recording and save the file"""
        if self.browser_setup and hasattr(self.browser_setup, 'video_path'):
            try:
                return await self.browser_setup.stop_video_recording(
                    self.page,
                    logger=logger,
                    request_id=request_id,
                    provider=provider,
                    session_id=self.session_id,
                    cabin_id=self.cabin_id
                )
            except Exception as e:
                logger.error(f"Error stopping video recording: {e}")
                return None
        return None
    
    async def _handle_cookie_popup(self):
        """Handle cookie consent popup if it appears"""
        try:
            # Common cookie popup selectors
            cookie_selectors = [
                # Generic "Accept" or "Agree" buttons
                'button:has-text("Accept")',
                'button:has-text("Agree")',
                'button:has-text("Accept All")',
                'button:has-text("Accept all")',
                'button:has-text("I Agree")',
                'button:has-text("OK")',
                'button:has-text("Got it")',
                
                # Class-based selectors for common cookie popup frameworks
                '.cookie-accept',
                '.cookie-agree',
                '.accept-cookies',
                '.accept-all',
                '.btn-accept',
                '.btn-agree',
                
                # ID-based selectors
                '#cookie-accept',
                '#accept-cookies',
                '#acceptCookies',
                '#cookieAccept',
                
                # Attribute-based selectors
                '[data-accept="cookies"]',
                '[data-cookie="accept"]',
                '[data-action="accept"]',
                
                # OneSource specific selectors (if any)
                '.capsule_btn:has-text("Agree")',
                'a:has-text("Agree")',
                
                # Generic close buttons for cookie banners
                '.cookie-banner .close',
                '.cookie-notice .close',
                '[aria-label="Close cookie banner"]'
            ]
            
            # Wait briefly for the page to load and popups to appear
            await asyncio.sleep(1)
            
            # Try each selector with a short timeout
            for selector in cookie_selectors:
                try:
                    # Check if element exists with a short timeout
                    element = await self.page.wait_for_selector(selector, timeout=1000)
                    if element:
                        # Check if element is visible
                        is_visible = await element.is_visible()
                        if is_visible:
                            await element.click()
                            logger.info(f"Cookie popup dismissed using selector: {selector}")
                            ui_log("Cookie consent handled", 
                                  session_id=self.session_id, 
                                  cabin_id=self.cabin_id,
                                  step="cookie_handled", 
                                  module="OneSource")
                            # Wait a moment for the popup to disappear
                            await asyncio.sleep(0.5)
                            return
                except:
                    # Continue to next selector if this one fails
                    continue
            
            # If no cookie popup found, that's fine - just continue
            logger.debug("No cookie popup detected or handled")
            
        except Exception as e:
            logger.warning(f"Error handling cookie popup: {e}")
            # Don't fail the login process if cookie handling fails
    
    async def cleanup(self):
        """Clean up resources"""
        try:
            if self.context:
                await self.context.close()
                logger.info("OneSource context closed")
            # Note: Don't close browser or stop playwright as they are shared
        except Exception as e:
            logger.error(f"Error during OneSource cleanup: {e}")


# Function to maintain backward compatibility
async def login_to_onesource(username=None, password=None, login_url=None, headless=True, 
                           existing_page=None, video_auditing=False, session_id=None, cabin_id=None):
    """
    Wrapper function to maintain backward compatibility
    
    Args:
        username: Username for login (defaults to env variable)
        password: Password for login (defaults to env variable)
        login_url: Login URL (defaults to env variable)
        headless: Whether to run the browser in headless mode (defaults to True)
        existing_page: Existing Playwright Page instance (optional)
        video_auditing: Whether to enable video recording for audit purposes (defaults to False)
        session_id: Session identifier for UI log scoping
        cabin_id: Cabin identifier for video recording and logging
        
    Returns:
        Tuple (page, success) with Playwright Page and boolean success indicator
    """
    login_handler = OneSourceLogin(
        username=username,
        password=password,
        login_url=login_url,
        headless=headless,
        existing_page=existing_page,
        video_auditing=video_auditing,
        session_id=session_id,
        cabin_id=cabin_id
    )
    return await login_handler.login()
 