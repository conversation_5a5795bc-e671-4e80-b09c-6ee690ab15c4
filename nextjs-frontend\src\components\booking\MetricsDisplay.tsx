import React from 'react';

interface MetricsDisplayProps {
  totalCabins: number;
  successfulCabins: number;
  grandTotal: number;
  executionTime?: number | string;
  formatPrice: (price: number) => string;
  showGrandTotalAsNA?: boolean;
}

const MetricsDisplay: React.FC<MetricsDisplayProps> = ({
  totalCabins,
  successfulCabins,
  grandTotal,
  executionTime,
  formatPrice,
  showGrandTotalAsNA = false
}) => {
  return (
    <div className="col-span-1">
      <div className="bg-gradient-to-br from-blue-100 mb-1 to-gray-400 p-2 rounded-lg border border-gray-200 h-12 flex items-center inline-block justify-left">
        <p className="text-md font-medium text-gray-600">Total Requested Cabins:</p>
        <p className="text-2xl font-bold text-gray-800 ml-2">{totalCabins}</p>
      </div>
      <div className="bg-gradient-to-br from-blue-100 mb-1 to-gray-400 p-2 rounded-lg border border-gray-200 h-12 flex items-center inline-block justify-left">
        <p className="text-md font-medium text-gray-600">Successful Cabins:</p>
        <p className="text-2xl font-bold text-green-600 ml-2">{successfulCabins}</p>
      </div>
      <div className="bg-gradient-to-br from-blue-100 mb-1 to-gray-400 p-2 rounded-lg border border-gray-200 h-12 flex items-center inline-block justify-left">
        <p className="text-md font-medium text-gray-600">Failed Cabins:</p>
        <p className="text-2xl font-bold text-red-600 ml-2">{totalCabins - successfulCabins}</p>
      </div>
      <div className="bg-gradient-to-br from-blue-100 mb-1 to-gray-400 p-2 rounded-lg border border-gray-300 h-12 flex items-center inline-block justify-left">
        <p className="text-md font-medium text-gray-600">Grand Total:</p>
        <p className="text-2xl font-bold text-blue-700 ml-2">
          {showGrandTotalAsNA ? "NA" : formatPrice(grandTotal)}
        </p>
      </div>
      {executionTime && (
        <div className="bg-gradient-to-br from-blue-100 mb-1 to-gray-400 p-2 rounded-lg border border-gray-300 h-12 flex items-center  justify-left">
          <p className="text-md font-medium text-gray-600">Execution Time:</p>
          <p className="text-2xl font-bold text-teal-700 ml-2">
            {typeof executionTime === 'number'
              ? (() => {
                const min = Math.floor((executionTime as number) / 60);
                const sec = Math.floor((executionTime as number) % 60).toString().padStart(2,'0');
                return `${min} min ${sec} sec`;
              })()
              : executionTime}
          </p>
        </div>
      )}
    </div>
  );
};

export default MetricsDisplay;