import re
from datetime import datetime
from loguru import logger
import math

class OneSourceExtractor:
    """
    Extractor for OneSource cruise details with structured input format
    """
    
    def __init__(self):
        self.logger = logger
        
    def extract_cruise_details(self, text_input):
        """
        Extract cruise details from OneSource structured text input
        
        Special handling for "or" cases:
        - "Cabins: 1 total ( Suite or Balcony )" → Creates 2 cabins (Suite, Balcony)
        - "Cabins: 1 total ( Suite or Balcony or Inside )" → Creates 3 cabins (Suite, Balcony, Inside)
        - Each "or" cabin gets the full passenger count (no distribution)
        
        Args:
            text_input (str): Raw cruise details text
            
        Returns:
            dict: Extracted cruise details
        """
        self.logger.info("Starting OneSource cruise details extraction")
        
        try:
            # First extract cabin types to check for or case
            cabin_types = self._extract_cabin_types(text_input)
            is_or_case = self._detect_or_case_from_text(text_input)
            
            details = {
                'cruise_line': self._extract_cruise_line(text_input),
                'ship_name': self._extract_ship_name(text_input),
                'date': self._extract_date(text_input),
                'nights': self._extract_nights(text_input),
                'airport_code': self._extract_airport_code(text_input),
                'cabin_count': self._extract_cabin_count(text_input),
                'passengers': self._extract_passengers(text_input),
                'cabin_types': cabin_types,
                'past_passenger_number': self._extract_past_passenger_number(text_input),
                'has_senior': self._extract_has_senior(text_input),
                'has_child': self._extract_has_child(text_input),
                'is_or_case': is_or_case
            }
            
            self.logger.info("OneSource extraction completed successfully")
            self.logger.info(f"Passenger flags - has_senior: {details['has_senior']}, has_child: {details['has_child']}")
            return details
            
        except Exception as e:
            self.logger.error(f"Error extracting OneSource details: {str(e)}")
            return {}
    
    def _extract_cruise_line(self, text):
        """Extract cruise line from Ship section"""
        pattern = r'Ship:\s*[^,]+,\s*(.+)'
        match = re.search(pattern, text)
        if match:
            return match.group(1).strip()
        return None
    
    def _extract_ship_name(self, text):
        """Extract ship name from Ship section"""
        pattern = r'Ship:\s*([^,]+)'
        match = re.search(pattern, text)
        if match:
            return match.group(1).strip()
        return None
    
    def _extract_date(self, text):
        """Extract and format date from Sails section"""
        pattern = r'Sails:\s*([A-Za-z]+ \d{1,2}[a-z]{2}, \d{4})'
        match = re.search(pattern, text)
        if match:
            date_str = match.group(1)
            try:
                # Parse date like "January 14th, 2026"
                # Remove ordinal suffix (st, nd, rd, th)
                clean_date = re.sub(r'(\d+)[a-z]{2}', r'\1', date_str)
                parsed_date = datetime.strptime(clean_date, '%B %d, %Y')
                # Format as DDMmmYY (e.g., 14Jan26)
                return parsed_date.strftime('%d%b%y')
            except ValueError as e:
                self.logger.error(f"Error parsing date: {e}")
                return None
        return None
    
    def _extract_nights(self, text):
        """Extract number of nights from Sails section"""
        pattern = r'for (\d+) nights'
        match = re.search(pattern, text)
        if match:
            return int(match.group(1))
        return None
    
    def _extract_airport_code(self, text):
        """Extract airport code from Home section"""
        pattern = r'Airport:\s*[^(]+\(([A-Z]{3})\)'
        match = re.search(pattern, text)
        if match:
            return match.group(1)
        return None
    
    def _extract_cabin_count(self, text):
        """Extract total number of cabins from Cabins section"""
        pattern = r'Cabins:\s*(\d+) total'
        match = re.search(pattern, text)
        if match:
            original_count = int(match.group(1))
            
            # Check if this is an "or" case that should expand the cabin count
            cabin_types_pattern = r'Cabins:\s*\d+ total \( ([^)]+) \)'
            types_match = re.search(cabin_types_pattern, text)
            
            if types_match:
                cabin_types_text = types_match.group(1).strip()
                # If it contains " or ", the actual cabin count is the number of options
                if self._is_or_case(cabin_types_text):
                    or_types = self._parse_or_types(cabin_types_text)
                    actual_count = len(or_types)
                    logger.info(f"Original cabin count: {original_count}, 'or' case detected, actual processing count: {actual_count}")
                    return actual_count
            
            return original_count
        return None
    
    def _extract_passengers(self, text):
        """Extract total number of passengers from Passengers section"""
        pattern = r'Passengers:\s*(\d+) total'
        match = re.search(pattern, text)
        if match:
            return int(match.group(1))
        return None
    
    def _extract_cabin_types(self, text):
        """Extract cabin types and distribute passengers equally"""
        cabin_count = self._extract_cabin_count(text)
        total_passengers = self._extract_passengers(text)
        
        if not cabin_count or not total_passengers:
            return []
        
        # Extract cabin types from Cabins section
        cabin_types_pattern = r'Cabins:\s*\d+ total \( ([^)]+) \)'
        match = re.search(cabin_types_pattern, text)
        
        if not match:
            return []
        
        cabin_types_text = match.group(1).strip()
        
        # Parse different cabin type formats
        cabin_types_list = []
        
        # Check if it's an "or" case like "Suite or Balcony" or "Suite or Balcony or Inside"
        if self._is_or_case(cabin_types_text):
            logger.info(f"Detected 'or' case in cabin types: {cabin_types_text}")
            # Split by " or " to get individual cabin types
            or_types = self._parse_or_types(cabin_types_text)
            logger.info(f"Creating {len(or_types)} cabins from 'or' case: {or_types}")
            
            # Create separate cabins for each "or" type with full passenger count
            cabin_types = []
            for i, cabin_type in enumerate(or_types):
                cabin_types.append({
                    'cabin_number': i + 1,
                    'cabin_type': cabin_type,
                    'passengers': total_passengers  # Each cabin gets full passenger count
                })
            
            logger.info(f"Created {len(cabin_types)} cabins for 'or' case processing")
            return cabin_types
        
        # Check if it's a mixed format like "1 Balcony, 1 Inside"
        elif ',' in cabin_types_text:
            # Split by comma and parse each cabin type with count
            parts = [part.strip() for part in cabin_types_text.split(',')]
            for part in parts:
                # Extract count and type from patterns like "1 Balcony" or "2 Inside"
                count_type_match = re.match(r'(\d+)\s+(.+)', part)
                if count_type_match:
                    count = int(count_type_match.group(1))
                    cabin_type = count_type_match.group(2).strip()
                    for _ in range(count):
                        cabin_types_list.append(cabin_type)
        else:
            # Single cabin type format like "2 Balcony"
            count_type_match = re.match(r'(\d+)\s+(.+)', cabin_types_text)
            if count_type_match:
                count = int(count_type_match.group(1))
                cabin_type = count_type_match.group(2).strip()
                for _ in range(count):
                    cabin_types_list.append(cabin_type)
            else:
                # Fallback: just the cabin type without count
                cabin_types_list = [cabin_types_text] * cabin_count
        
        # For non-"or" cases, continue with normal processing
        # Ensure we have the right number of cabin types
        while len(cabin_types_list) < cabin_count:
            cabin_types_list.append("Unknown")
        
        # Trim if we have too many
        cabin_types_list = cabin_types_list[:cabin_count]
        
        # Distribute passengers equally across cabins
        base_passengers = total_passengers // cabin_count
        extra_passengers = total_passengers % cabin_count
        
        cabin_types = []
        for i in range(cabin_count):
            # First 'extra_passengers' cabins get one extra passenger
            passengers_in_cabin = base_passengers + (1 if i < extra_passengers else 0)
            cabin_types.append({
                'cabin_number': i + 1,
                'cabin_type': cabin_types_list[i],
                'passengers': passengers_in_cabin
            })
        
        return cabin_types

    def _extract_past_passenger_number(self, text):
        """Extract optional past passenger number"""
        pattern = r'Past Passenger Number:\s*(\d+)'
        match = re.search(pattern, text)
        if match:
            return match.group(1)
        return None

    def _extract_has_senior(self, text):
        """
        Extract whether there are seniors in the passenger details
        
        Args:
            text (str): Raw cruise details text
            
        Returns:
            bool: True if seniors are mentioned, False otherwise
        """
        # Look for "senior" or "seniors" in the passengers section
        pattern = r'Passengers:\s*\d+ total \([^)]*\bseniors?\b[^)]*\)'
        match = re.search(pattern, text, re.IGNORECASE)
        return bool(match)
    
    def _extract_has_child(self, text):
        """
        Extract whether there are children in the passenger details
        
        Args:
            text (str): Raw cruise details text
            
        Returns:
            bool: True if children are mentioned, False otherwise
        """
        # Look for "child" or "children" in the passengers section
        pattern = r'Passengers:\s*\d+ total \([^)]*\bchild(?:ren)?\b[^)]*\)'
        match = re.search(pattern, text, re.IGNORECASE)
        return bool(match)

    def _is_or_case(self, cabin_types_text):
        """
        Check if cabin types contain "or" case
        
        Args:
            cabin_types_text (str): The cabin types text from extraction
            
        Returns:
            bool: True if this is an "or" case, False otherwise
        """
        return ' or ' in cabin_types_text
    
    def _parse_or_types(self, cabin_types_text):
        """
        Parse "or" case cabin types into individual types
        
        Args:
            cabin_types_text (str): Text like "Suite or Balcony or Inside"
            
        Returns:
            list: List of individual cabin types
        """
        if not self._is_or_case(cabin_types_text):
            return []
        
        or_types = [cabin_type.strip() for cabin_type in cabin_types_text.split(' or ')]
        # Remove empty strings and validate
        or_types = [t for t in or_types if t.strip()]
        
        logger.info(f"Parsed 'or' types: {or_types}")
        return or_types
    
    def _detect_or_case_from_text(self, text):
        """
        Detect if the original text input contains an "or" case in cabin types
        
        Args:
            text (str): The complete input text
            
        Returns:
            bool: True if this booking has "or" case cabin types, False otherwise
        """
        # Extract cabin types from Cabins section
        cabin_types_pattern = r'Cabins:\s*\d+ total \( ([^)]+) \)'
        match = re.search(cabin_types_pattern, text)
        
        if match:
            cabin_types_text = match.group(1).strip()
            return self._is_or_case(cabin_types_text)
        
        return False
