import os
from loguru import logger
from datetime import datetime
import copy
import sys
import asyncio

__package__ = "NCL.utils"

parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)


class NCLUtils:
    """Utility class for NCL bookings"""

    _processed_bookings = set()

    @staticmethod
    async def save_screenshot(
        page_or_driver, filename, cabin_id=None, request_id=None, session_id=None
    ):

        try:

            screenshot_type = "general"
            fname = filename.lower()
            if "search_form" in fname:
                screenshot_type = "Form Filled"
            elif "available_cruises" in fname:
                screenshot_type = "Available Cruises"
            elif "available_categories" in fname:
                screenshot_type = "Available Categories"
            elif "available_staterooms" in fname:
                screenshot_type = "Available Staterooms"
            elif "price_programs" in fname:
                screenshot_type = "Promotion"
            elif "final_pricing" in fname:
                screenshot_type = "Final Pricing"
            elif "login" in fname:
                screenshot_type = "Login"
            elif "cruise_select" in filename.lower(
            ) or "cruise_selection" in filename.lower():
                screenshot_type = "Cruise Selection"
            elif "category" in filename.lower():
                screenshot_type = "Category Selection"
            elif "stateroom" in filename.lower() or "cabin" in filename.lower():
                screenshot_type = "Cabin Selection"
            elif "pricing" in filename.lower() or "price" in filename.lower():
                screenshot_type = "Pricing Details"
            elif "promotion" in filename.lower():
                screenshot_type = "Promotion Selection"
            png_data = None

            if hasattr(page_or_driver, 'screenshot'):
                png_data = await page_or_driver.screenshot()
            elif isinstance(page_or_driver, tuple) and len(page_or_driver) == 4:
                _, _, _, page = page_or_driver
                png_data = await page.screenshot()
            else:
                try:
                    png_data = page_or_driver.get_screenshot_as_png()
                except Exception as selenium_err:
                    logger.error(
                        f"Error taking screenshot with Selenium: {str(selenium_err)}"
                    )
                    png_data = b"Mock screenshot data"

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if cabin_id is not None:
                file_name = f"{filename}_cabin_{cabin_id}_{timestamp}.png"
            else:
                file_name = f"{filename}_{timestamp}.png"

            if request_id:
                try:
                    from db.screenshot_manager import save_screenshot_to_db as save_to_db

                    await save_to_db(
                        png_data, request_id, "NCL", screenshot_type, file_name,
                        cabin_id, session_id
                    )
                    logger.info(f"Screenshot saved to database: {file_name}")
                except Exception as db_err:
                    logger.error(f"Error saving screenshot to database: {str(db_err)}")

        except Exception as e:
            logger.error(f"Error in save_screenshot: {str(e)}")
            return None

    @staticmethod
    async def save_reservation_data(
        data, cabin_id=None, is_parallel=False, request_id=None, session_id=None
    ):
        """Save individual cabin reservation data to the database"""
        if is_parallel and cabin_id is not None and cabin_id > 0:
            logger.info(
                f"Skipping individual save for cabin {cabin_id} in parallel mode - will be saved in combined data"
            )
            return data

        save_data = copy.deepcopy(data)

        if "detailed_pricing" in save_data:
            del save_data["detailed_pricing"]

        if "current_promos" not in save_data:
            save_data["current_promos"] = []

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        save_data['timestamp'] = timestamp
        save_data['cabin_id'] = cabin_id
        save_data['is_parallel'] = is_parallel
        if request_id:
            save_data['request_id'] = request_id

        if 'passengers' not in save_data:
            save_data['passengers'] = {
                'total': 0, 'adults': 0, 'children': 0, 'infants': 0
            }
        elif not save_data['passengers'].get('total'):
            save_data['passengers']['total'] = (
                save_data['passengers'].get('adults', 0) +
                save_data['passengers'].get('children', 0) +
                save_data['passengers'].get('infants', 0)
            )

        if 'cruise_details' not in save_data:
            save_data['cruise_details'] = {}

        try:
            from Core.database_helper import save_ncl_results

            booking_key = f"{request_id}_{session_id}"
            if booking_key in NCLUtils._processed_bookings:
                logger.info(
                    f"Skipping duplicate save for {booking_key} - already processed"
                )
                return data

            combined_data = {
                "request_id": request_id, "timestamp": timestamp, "booking_mode":
                "parallel" if is_parallel else "standard", "total_cabins": 1, "cabins":
                [save_data]
            }

            total_passengers = save_data.get("passengers", {}).get("total", 0)

            total_final_price = 0.0
            if "final_price" in save_data:
                total_final_price = save_data["final_price"]
            elif "reservation_total" in save_data:
                total_final_price = save_data["reservation_total"]

            for field in ['reservation_total_raw', 'non_comm_fare_raw', 'savings_raw',
                          'insurance_raw', 'dining_package_raw', 'beverage_package_raw',
                          'soda_package_raw', 'govt_tax_raw', 'final_price_raw']:
                if field in save_data:
                    del save_data[field]

            combined_data["summary"] = {
                "total_passengers":
                total_passengers, "total_cost":
                f"${total_final_price:.2f}"
                if isinstance(total_final_price, (int, float)) else total_final_price,
                "total_final_price":
                total_final_price
            }

            logger.info(f"Saving data to database: {combined_data}")

            await save_ncl_results(request_id, combined_data)
            NCLUtils._processed_bookings.add(booking_key)

            return data
        except Exception as e:
            logger.error(f"Error saving reservation data: {str(e)}")
            return data

    @staticmethod
    async def combine_reservation_data(
        individual_data_list,
        is_parallel=False,
        execution_time=None,
        request_id=None,
        session_id=None
    ):
        """Combine and save reservation data for multiple cabins"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if request_id:
            request_id = request_id.strip()
            logger.info(
                f"Combining reservation data for cleaned request_id: {request_id}"
            )

        if not individual_data_list:
            logger.error("No cabin data to combine - individual_data_list is empty")
            return {
                "error": "No cabin data to save", "request_id": request_id, "timestamp":
                timestamp, "cabins": []
            }

        combined_data = {
            "timestamp": timestamp, "booking_mode":
            "parallel" if is_parallel else "standard", "total_cabins":
            len(individual_data_list), "cabins": individual_data_list
        }

        if request_id:
            combined_data["request_id"] = request_id

        if execution_time is not None:
            combined_data["execution_time"] = execution_time

        total_passengers = 0
        total_final_price = 0.0

        for cabin_index, cabin_data in enumerate(individual_data_list):
            try:
                if request_id:
                    cabin_data["request_id"] = request_id

                if "cabin_id" not in cabin_data or not cabin_data["cabin_id"]:
                    cabin_data["cabin_id"] = cabin_index + 1
                    logger.info(f"Setting missing cabin_id to {cabin_index + 1}")

                if "passengers" not in cabin_data:
                    cabin_data["passengers"] = {
                        "total": 0, "adults": 0, "children": 0, "infants": 0
                    }
                    logger.warning(
                        f"Added missing passengers structure to cabin {cabin_data.get('cabin_id', cabin_index + 1)}"
                    )
                elif not isinstance(cabin_data["passengers"], dict):
                    cabin_data["passengers"] = {
                        "total": 0, "adults": 0, "children": 0, "infants": 0
                    }
                    logger.warning(
                        f"Fixed invalid passengers structure in cabin {cabin_data.get('cabin_id', cabin_index + 1)}"
                    )

                if "total" not in cabin_data["passengers"]:
                    cabin_data["passengers"]["total"] = (
                        cabin_data["passengers"].get("adults", 0) +
                        cabin_data["passengers"].get("children", 0) +
                        cabin_data["passengers"].get("infants", 0)
                    )

                total_passengers += cabin_data.get("passengers", {}).get("total", 0)

                if "final_price" in cabin_data:
                    total_final_price += cabin_data["final_price"]
                elif "total_cost" in cabin_data:
                    cost_str = cabin_data["total_cost"]
                    if isinstance(cost_str, str):
                        cost_str = cost_str.replace("$", "").replace(",", "")
                        try:
                            cost = float(cost_str)
                            total_final_price += cost
                        except ValueError:
                            logger.warning(f"Could not parse cost string: {cost_str}")
                    elif isinstance(cost_str, (int, float)):
                        total_final_price += float(cost_str)
                elif "reservation_total" in cabin_data:
                    total_final_price += cabin_data["reservation_total"]
            except Exception as e:
                logger.error(f"Error calculating totals: {str(e)}")

        combined_data["summary"] = {
            "total_passengers": total_passengers, "total_cost":
            f"${total_final_price:.2f}", "total_final_price": total_final_price
        }

        if not combined_data["cabins"]:
            logger.error("No cabin data in combined_data['cabins']")

        for cabin in combined_data["cabins"]:
            if "detailed_pricing" in cabin:
                del cabin["detailed_pricing"]

            if "cruise_details" not in cabin:
                cabin["cruise_details"] = {}

            if "current_promos" not in cabin:
                cabin["current_promos"] = []

            for field in ['reservation_total_raw', 'non_comm_fare_raw', 'savings_raw',
                          'insurance_raw', 'dining_package_raw', 'beverage_package_raw',
                          'soda_package_raw', 'govt_tax_raw', 'final_price_raw']:
                if field in cabin:
                    del cabin[field]

        try:
            from Core.database_helper import save_ncl_results

            booking_key = f"{request_id}_{session_id}"
            if booking_key in NCLUtils._processed_bookings:
                logger.info(
                    f"Skipping duplicate save for {booking_key} - already processed"
                )
                return combined_data

            logger.info(
                f"Saving combined data to database with request_id: {request_id}, session_id: {session_id}"
            )
            logger.info(
                f"Combined data summary: {len(combined_data['cabins'])} cabins, "
                f"{combined_data['summary']['total_passengers']} passengers, "
                f"{combined_data['summary']['total_cost']} total cost"
            )

            save_result = await save_ncl_results(combined_data, request_id, session_id)
            NCLUtils._processed_bookings.add(booking_key)

            if save_result:
                logger.info(
                    f"Successfully saved combined reservation data to database with request_id: {request_id}"
                )
                combined_data["_saved_to_database"] = True
            else:
                logger.error(
                    f"Failed to save combined reservation data to database with request_id: {request_id}"
                )
        except Exception as e:
            logger.error(
                f"Error saving combined reservation data to database: {str(e)}"
            )
            import traceback
            logger.error(f"Error traceback: {traceback.format_exc()}")

        logger.info(f"Combined reservation data processed")
        return combined_data

    @staticmethod
    def reset_processed_bookings():
        """
        Reset the processed bookings tracking set.
        
        This should be called at the beginning of a new booking process to ensure
        that the tracking mechanism starts fresh and doesn't retain state from
        previous booking operations.
        """
        NCLUtils._processed_bookings.clear()
        logger.info(
            "Reset processed bookings tracking - preventing duplicate database saves"
        )

    @staticmethod
    def identify_similar_cabins(cruise_details):
        """
        Identify groups of similar cabins (same category type and passenger allocation)
        Similar to Cruising Power's cabin optimization logic
        
        Args:
            cruise_details: Dictionary containing cruise booking details
            
        Returns:
            tuple: (similar_cabin_groups, cabin_to_group_mapping)
        """
        try:
            similar_cabin_groups = {}
            cabin_to_group_mapping = {}

            num_cabins = cruise_details.get('cabins', 1)
            cabin_categories = cruise_details.get('cabin_categories', [])
            cabin_passengers_dict = cruise_details.get('cabin_passengers_dict', {})

            is_or_cabin_scenario = (
                cruise_details.get('cabins', 1) > 1
                and len(cabin_categories) == cruise_details.get('cabins', 1)
                and cruise_details.get('cabins', 1)
                != int(cruise_details.get('original_cabin_count', '1'))
            )

            total_adults = cruise_details.get('adults', 0)
            total_children = cruise_details.get('children', 0)
            total_infants = cruise_details.get('infants', 0)

            logger.info(f"Analyzing {num_cabins} cabins for similarity optimization...")

            for cabin_id in range(1, num_cabins + 1):
                if cabin_id <= len(cabin_categories):
                    cabin_category = cabin_categories[cabin_id - 1]
                else:
                    cabin_category = cabin_categories[
                        -1] if cabin_categories else "inside"

                try:
                    from NCL.cabin_categories import CabinCategories
                    normalized_category = CabinCategories.normalize_category(
                        cabin_category
                    )
                except:
                    normalized_category = cabin_category.lower()

                if cruise_details.get('is_edited',
                                      False) and str(cabin_id) in cabin_passengers_dict:
                    cabin_data = cabin_passengers_dict[str(cabin_id)]
                    cabin_adults = cabin_data['adults']
                    cabin_children = cabin_data['children']
                    cabin_infants = cabin_data.get('infants', 0)
                else:
                    if num_cabins == 1:
                        cabin_adults = total_adults
                        cabin_children = total_children
                        cabin_infants = total_infants
                    elif is_or_cabin_scenario:
                        cabin_adults = total_adults
                        cabin_children = total_children
                        cabin_infants = total_infants
                    else:
                        cabin_adults = total_adults // num_cabins
                        extras = total_adults % num_cabins
                        if cabin_id <= extras:
                            cabin_adults += 1

                        cabin_children = total_children // num_cabins
                        child_extras = total_children % num_cabins
                        if cabin_id <= child_extras:
                            cabin_children += 1

                        cabin_infants = total_infants // num_cabins
                        infant_extras = total_infants % num_cabins
                        if cabin_id <= infant_extras:
                            cabin_infants += 1

                signature = (
                    normalized_category, cabin_adults, cabin_children, cabin_infants
                )

                if signature not in similar_cabin_groups:
                    similar_cabin_groups[signature] = []

                similar_cabin_groups[signature].append(cabin_id)
                cabin_to_group_mapping[cabin_id] = signature

                logger.debug(
                    f"Cabin {cabin_id}: {normalized_category} with {cabin_adults} adults, {cabin_children} children, {cabin_infants} infants"
                )

            unique_groups = 0
            for signature, cabin_indices in similar_cabin_groups.items():
                if len(cabin_indices) > 1:
                    cabin_type, adults, children, infants = signature
                    logger.info(
                        f"Found {len(cabin_indices)} similar cabins of type '{cabin_type}' with {adults} adults, {children} children, {infants} infants"
                    )
                    logger.info(f"  Cabin numbers: {cabin_indices}")
                unique_groups += 1

            logger.info(
                f"Optimization summary: {unique_groups} unique cabin types out of {num_cabins} total cabins"
            )

            return similar_cabin_groups, cabin_to_group_mapping

        except Exception as e:
            logger.error(f"Error identifying similar cabins: {e}")
            return {}, {}

    @staticmethod
    def replicate_cabin_result(
        source_cabin_id, target_cabin_id, source_result, cruise_details
    ):
        """
        Replicates results from a processed cabin to a similar cabin
        Similar to Cruising Power's result replication logic
        
        Args:
            source_cabin_id: ID of the source cabin (already processed)
            target_cabin_id: ID of the target cabin (to replicate results to)
            source_result: Result data from the source cabin
            cruise_details: Original cruise details
            
        Returns:
            dict: Replicated result for target cabin
        """
        try:
            logger.info(
                f"Replicating results from cabin {source_cabin_id} to cabin {target_cabin_id}"
            )

            target_result = copy.deepcopy(source_result)

            if 'reservation_data' in target_result:
                target_result['reservation_data']['cabin_id'] = target_cabin_id
                target_result['reservation_data']['timestamp'] = datetime.now(
                ).strftime("%Y-%m-%d %H:%M:%S")
                target_result['reservation_data']['replicated_from'] = source_cabin_id

                if 'cabin_details' in target_result['reservation_data']:
                    target_result['reservation_data']['cabin_details'][
                        'cabin_number'] = target_cabin_id

            target_result['cabin_id'] = target_cabin_id
            if 'success' in target_result:
                target_result['replicated'] = True
                target_result['source_cabin'] = source_cabin_id

            logger.info(
                f"Successfully replicated results from cabin {source_cabin_id} to cabin {target_cabin_id}"
            )
            return target_result

        except Exception as e:
            logger.error(
                f"Error replicating results from cabin {source_cabin_id} to cabin {target_cabin_id}: {e}"
            )
            return {
                'success': False, 'cabin_id': target_cabin_id, 'error':
                f'Result replication failed: {str(e)}', 'reservation_data': {
                    'cabin_id': target_cabin_id, 'timestamp':
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 'replication_error':
                    str(e)
                }
            }