import logging
import sys
import asyncio
import os
from datetime import datetime
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontex<PERSON>
from typing import Dict, List
from db.video_manager import save_video_to_db
from db.screenshot_manager import save_screenshot_to_db

# Set Windows event loop policy at module level if on Windows
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

BASE_CHROMIUM_ARGS = [
    "--disable-gpu",
    "--disable-gl-drawing-for-tests",
    
    "--disable-backgrounding-occluded-windows",
    "--disable-background-timer-throttling",
    "--disable-background-networking",
    
    "--disable-features=IsolateOrigins,site-per-process",
    "--disable-features=TranslateUI",
    "--disable-features=BlinkGenPropertyTrees",
    "--disable-features=CalculateNativeWinOcclusion",
    "--disable-features=MediaRouter",
    "--disable-features=BackForwardCache",
    "--disable-features=DialMediaRouteProvider",
    "--disable-features=PaintHolding",
    "--disable-features=AcceptCHFrame",
    "--disable-features=OptimizationHints",
    "--disable-features=AutofillServerCommunication",
    "--disable-features=CertificateTransparencyComponentUpdater",
    "--disable-features=PrivacySandboxSettings4",
    "--disable-features=ImprovedCookieControls",
    "--disable-features=GlobalMediaControls",
    
    "--disable-ipc-flooding-protection",
    "--disable-hang-monitor",
    "--disable-partial-raster",
    "--disable-extensions",
    "--disable-component-extensions-with-background-pages",
    "--disable-default-apps",
    "--disable-client-side-phishing-detection",
    "--disable-popup-blocking",
    "--disable-notifications",
    "--disable-web-security",
    "--disable-dev-shm-usage",
    "--disable-breakpad",
    "--disable-domain-reliability",
    "--disable-component-update",
    "--disable-speech-api",
    "--disable-sync",
    "--disable-print-preview",
    "--disable-prompt-on-repost",
    "--disable-search-engine-choice-screen",
    "--disable-setuid-sandbox",
    
    "--js-flags=--random-seed=1157259157",
    "--mute-audio",
    "--hide-scrollbars",
    "--no-default-browser-check",
    "--no-first-run",
    "--metrics-recording-only",
    "--no-pings",
    
    "--in-process-gpu",
    "--disable-skia-runtime-opts",
    "--disable-threaded-animation",
    "--disable-threaded-scrolling",
    "--disable-checker-imaging",
    "--disable-image-animation-resync",
    "--run-all-compositor-stages-before-draw",
    
    "--autoplay-policy=user-gesture-required",
    "--force-color-profile=srgb",
    "--avoid-unnecessary-beforeunload-check",
    
    # Anti-detection for headless mode
    "--window-size=1920,1080",
    "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
    "--enable-automation=false",
    "--use-angle=default"
]

TRACKER_DOMAINS = [
    "google-analytics", "analytics", "tracking", 
    "stats", "pixel", "advertisement", "banner",
    "facebook", "doubleclick", "optimizely",
    "hotjar", "clicktale", "pingdom", "mixpanel",
    "mouseflow", "gtm", "googletagmanager",
    "fontawesome", "cloudfront", "googlesyndication",
    "adservice", "chartbeat", "kissmetrics"
]

def get_chromium_args(Load_images=False):
    """Get the appropriate Chromium args based on whether images should be enabled.
    
    Args:
        enable_images (bool): Flag to enable/disable images. Default is False.
        
    Returns:
        list: List of Chromium arguments with or without image blocking.
    """
    ARGS = BASE_CHROMIUM_ARGS.copy()
    
    if not Load_images:
        ARGS.append("--blink-settings=imagesEnabled=false")
        
    return ARGS

def setup_logging():
    """Common logging setup for both classes"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger('browser_setup')
    logger.propagate = False  
    return logger

def ensure_video_directory(cabin_id=None):
    """
    Ensure the video directory exists at the root level with optional cabin subdirectory.
    
    Args:
        cabin_id: Optional cabin ID to create a subdirectory for
        
    Returns:
        str: Path to the video directory
    """
    # Get the root directory (parent of Core)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    root_dir = os.path.dirname(current_dir)
    video_dir = os.path.join(root_dir, "video")
    
    # Create main video directory if it doesn't exist
    os.makedirs(video_dir, exist_ok=True)
    
    # Create cabin-specific subdirectory if cabin_id is provided and valid
    if cabin_id is not None and str(cabin_id).strip():
        # Sanitize cabin_id to ensure it's valid for a directory name
        cabin_id_str = str(cabin_id).strip().replace('/', '_').replace('\\', '_')
        cabin_dir = os.path.join(video_dir, f"cabin_{cabin_id_str}")
        os.makedirs(cabin_dir, exist_ok=True)
        return cabin_dir
    
    return video_dir

class AsyncBrowserSetup:
   
    def __init__(self):
        self.logger = setup_logging()
        self.playwright = None
        self.video_path = None
        self.cabin_id = None  # Store cabin_id for later use
         
    async def create_chromium_driver(self, headless=True, logger=None, Load_images=False, custom_args=None, video_auditing=False):
        try:
            log = logger or self.logger
            log.info("Setting up Chromium browser with Playwright")
            
            chromium_args = get_chromium_args(Load_images)
            
            # Add any custom arguments if provided
            if custom_args and isinstance(custom_args, list):
                chromium_args.extend(custom_args)
                log.info(f"Added {len(custom_args)} custom browser arguments")

            if Load_images:
                log.info("Images are ENABLED for this session")
            else:
                log.info("Images are DISABLED for this session")
                
            if video_auditing:
                log.info("Video auditing is ENABLED for this session")
                
            self.playwright = await async_playwright().start()
            
            browser = await self.playwright.chromium.launch(
                headless=headless,
                args=chromium_args,
                ignore_default_args=["--enable-automation"],
                channel="chromium",
                proxy=None
            )
            
            log.info("Chromium browser launched successfully")
            return browser
            
        except Exception as e:
            log = logger or self.logger
            log.error(f"Failed to create Chromium browser: {str(e)}")
            raise

    async def create_optimized_context(self, browser, logger=None, video_auditing=False, cabin_id=None, session_id=None):
        log = logger or self.logger
        log.info("Creating optimized browser context")
        
        # Store cabin_id for later use
        self.cabin_id = cabin_id
        
        try:
            context_options = {
                "java_script_enabled": True,
                "bypass_csp": True,
                "ignore_https_errors": True,
                "service_workers": "block",
                "is_mobile": False,
                "has_touch": False,
                "offline": False,
                "geolocation": None,
                "permissions": [],
                "reduced_motion": "reduce",
                "extra_http_headers": {},
                "http_credentials": None,
                "locale": "en-US",
                "timezone_id": "UTC",
                "color_scheme": "no-preference",
                "forced_colors": "none",
                "storage_state": None,
                "viewport": {"width": 1920, "height": 1080}
            }
            
            # Add video recording if enabled
            if video_auditing:
                video_dir = ensure_video_directory(cabin_id)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                self.video_path = os.path.join(video_dir, f"video_{timestamp}.webm")
                context_options["record_video_dir"] = video_dir
                context_options["record_video_size"] = {"width": 1920, "height": 1080}
                log.info(f"Video recording enabled for cabin_id {cabin_id}: {self.video_path}")
            
            # Serialize context creation per browser to avoid potential deadlocks
            lock = getattr(browser, "_context_lock", None)
            if lock is None:
                lock = asyncio.Lock()
                setattr(browser, "_context_lock", lock)

            async with lock:
                context = await browser.new_context(**context_options)
            
            # Register context with session_id if provided
            if session_id:
                from Core.browser_setup import browser_manager
                browser_manager.register_context(session_id, context)
            
            # Execute client side JavaScript to mask headless mode
            for page in context.pages:
                await self._apply_stealth_mode(page)
            
            log.info("Browser context created with optimized settings")
            return context
            
        except Exception as e:
            log.error(f"Failed to create optimized context: {str(e)}")
            raise

    async def stop_video_recording(self, page, logger=None, request_id=None, provider=None, session_id=None, cabin_id=None):
        """Stop video recording and save the file"""
        if self.video_path:
            log = logger or self.logger
            try:
                # Use provided cabin_id or fall back to stored cabin_id
                cabin_id = cabin_id or self.cabin_id
                
                # Close the page to finalize the video recording
                await page.close()
                
                # Get the actual video file that Playwright created (hash-based name)
                video_dir = os.path.dirname(self.video_path)
                actual_video_path = None
                
                # Find the most recently created video file
                latest_video = None
                latest_time = 0
                for file in os.listdir(video_dir):
                    if file.endswith('.webm'):
                        file_path = os.path.join(video_dir, file)
                        file_time = os.path.getmtime(file_path)
                        if file_time > latest_time:
                            latest_time = file_time
                            latest_video = file_path
                
                if latest_video:
                    actual_video_path = latest_video
                else:
                    log.warning(f"No video file found in directory: {video_dir}")
                    actual_video_path = self.video_path
                
                log.info(f"Video recording saved to: {actual_video_path}")
                
                # Save to database if request_id and provider are provided
                if request_id and provider and actual_video_path:
                    try:
                        success = await save_video_to_db(
                            actual_video_path,  # Use the actual video path
                            request_id,
                            provider,
                            "booking_process",
                            None,  # Use default filename
                            cabin_id,  # Use cabin_id if available
                            session_id,
                            "webm",
                            True,   # Compress
                            True    # Delete source file after storing
                        )
                        if success:
                            log.info(f"Video saved to database for {provider} request {request_id}, cabin {cabin_id or 'N/A'}")
                        else:
                            log.error(f"Failed to save video to database for {provider} request {request_id}")
                    except Exception as e:
                        log.error(f"Error saving video to database: {e}")
                
                return actual_video_path
            except Exception as e:
                log.error(f"Error stopping video recording: {str(e)}")
                return None
        return None
            
    async def setup_resource_blocking(self, page, logger=None, block=True):
        log = logger or self.logger
        
        if not block:
            log.info("Resource blocking disabled")
            return
            
        try:
            await page.route("**/*.{png,jpg,jpeg,gif,webp,svg,ico,ttf,otf,woff,woff2,mp4,webm,ogg,mp3,wav,avi,mov}", 
                       lambda route: route.abort())
            
            log.info("Resource file blocking configured")
            
            await page.route("**/*", lambda route, request: self._route_handler(route, request, log))
            
            log.info("Network request interception configured")
            
        except Exception as e:
            log.error(f"Failed to set up resource blocking: {str(e)}")
            
    async def _route_handler(self, route, request, log):
        try:
            try:
                resource_type = request.resource_type()
            except TypeError:
                resource_type = request.resource_type
            
            if resource_type == "document":
                await route.continue_()
                return
                
            if resource_type in ["image", "media", "font"]:
                await route.abort()
                return
                
            if resource_type in ["stylesheet", "script", "xhr", "fetch"]:
                await route.continue_()
                return
                
            url = request.url
            if any(tracker in url for tracker in TRACKER_DOMAINS):
                await route.abort()
                return
                
            await route.continue_()
            
        except Exception as e:
            log.error(f"Error in route handler: {str(e)}")
            await route.continue_()

    async def _apply_stealth_mode(self, page):
        """Apply stealth mode to avoid headless detection"""
        await page.add_init_script("""
        () => {
            // Override the 'navigator.webdriver' property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false
            });
            
            // Override the language property
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en']
            });
            
            // Override the plugins property
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });
            
            // Override Chrome property
            window.chrome = {
                runtime: {}
            };
            
            // Pass the user-agent check
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
            );
        }
        """)

class BrowserManager:
    """
    Manages shared browser instances for each module (Studio, Cruising Power, NCL).
    Each module gets a dedicated browser that stays alive for the application lifecycle.
    Also tracks browser contexts by session_id for cancellation support.
    """
    
    def __init__(self):
        self.browsers = {}  # type: Dict[str, Dict[asyncio.AbstractEventLoop, Browser]]
        self.playwrights = {}  # type: Dict[str, Dict[asyncio.AbstractEventLoop, any]]
        self.session_contexts = {}  # type: Dict[str, List[BrowserContext]]
        self.logger = setup_logging()
        
    async def start_browsers(self):
        """
        Start dedicated browsers for each module.
        """
        modules = ['Studio', 'Cruising_Power', 'NCL', 'OneSource']
        
        try:
            self.logger.info("Starting dedicated browsers for all modules...")
            
            for module in modules:
                self.logger.info(f"Initializing browser for {module} module")
                
                # Create browser setup instance
                browser_setup = AsyncBrowserSetup()
                
                # Module-specific configurations
                if module == 'Cruising_Power':
                    # Custom args specifically for Cruising Power to bypass bot detection
                    custom_args = [
                        "--disable-blink-features=AutomationControlled",
                        "--disable-features=AutomationControlled",
                        "--start-maximized",
                        "--new-window",
                        "--window-position=0,0"
                    ]
                    load_images = True  # Enable images to help with bot detection
                elif module == 'Studio':
                    custom_args = None
                    load_images = False  # Default for Studio
                elif module == 'OneSource':
                    custom_args = None
                    load_images = True  # Enable images for OneSource
                else:  # NCL
                    custom_args = None
                    load_images = True
                
                # Create the browser instance
                browser = await browser_setup.create_chromium_driver(
                    headless=True,
                    logger=self.logger,
                    Load_images=load_images,
                    custom_args=custom_args,
                    video_auditing=False  # Video auditing is handled per context
                )
                
                # Flag this browser as the root shared instance so that
                # close_current_loop_browsers (used by background tasks)
                # will NOT close it while cleanup on shutdown will.
                setattr(browser, "_shared_root", True)
                
                # Store browser and playwright instances
                self.browsers.setdefault(module, {})[asyncio.get_running_loop()] = browser
                self.playwrights.setdefault(module, {})[asyncio.get_running_loop()] = browser_setup.playwright

                
                self.logger.info(f"Browser for {module} module started successfully")
            
            self.logger.info("All module browsers started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start browsers: {str(e)}")
            # Clean up any successfully started browsers
            await self.stop_browsers()
            raise
    
    async def stop_browsers(self):
        """
        Stop all dedicated browsers.
        """
        try:
            self.logger.info("Stopping all module browsers...")
            
            # Close all browsers
            for module, loop_map in list(self.browsers.items()):
                for loop, browser in list(loop_map.items()):
                    if browser:
                        try:
                            await browser.close()
                            self.logger.info(f"Closed browser for module {module} (loop {id(loop)})")
                        except Exception as e:
                            self.logger.error(f"Error closing browser for module {module}: {e}")
                    # Remove references regardless of success
                    self.browsers[module].pop(loop, None)
            
            # Stop all playwright instances
            for module, loop_map in list(self.playwrights.items()):
                for loop, playwright in list(loop_map.items()):
                    if playwright:
                        try:
                            await playwright.stop()
                        except:
                            pass
                    # remove reference
                    self.playwrights[module].pop(loop, None)
            
            # Clear the dictionaries
            self.browsers.clear()
            self.playwrights.clear()
            
            self.logger.info("All module browsers stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error during browser cleanup: {str(e)}")
    
    async def _launch_browser_for_module(self, module, loop):
        """Internal helper to launch a browser for a module on the given event loop"""
        # This helper assumes it is already running within the target loop
        browser_setup = AsyncBrowserSetup()
        if module == 'Cruising_Power':
            custom_args = [
                "--disable-blink-features=AutomationControlled",
                "--disable-features=AutomationControlled",
                "--start-maximized",
                "--new-window",
                "--window-position=0,0"
            ]
            load_images = True
        elif module == 'Studio':
            custom_args = None
            load_images = False
        elif module == 'OneSource':
            custom_args = None
            load_images = True
        else:
            custom_args = None
            load_images = True
        browser = await browser_setup.create_chromium_driver(
            headless=True,
            logger=self.logger,
            Load_images=load_images,
            custom_args=custom_args,
            video_auditing=False
        )
        setattr(browser, "_shared_root", False)
        # Store
        self.browsers.setdefault(module, {})[loop] = browser
        self.playwrights.setdefault(module, {})[loop] = browser_setup.playwright
        return browser

    def _get_loop(self):
        # Helper to get current running loop (handles cases where none)
        try:
            return asyncio.get_running_loop()
        except RuntimeError:
            return None

    async def get_or_launch_browser(self, module_name):
        """Get browser for current event loop, launching if necessary"""
        loop = self._get_loop()
        if loop is None:
            raise RuntimeError("get_or_launch_browser must be called from within an event loop")
        module_map = self.browsers.get(module_name, {})
        if loop in module_map:
            return module_map[loop]
        # Launch new browser for this loop
        return await self._launch_browser_for_module(module_name, loop)

    def get_browser(self, module_name):
        """Public accessor used by modules. If browser for this loop not present, raise to remind caller to await async getter"""
        loop = self._get_loop()
        if loop is None:
            raise RuntimeError("Must be called within an event loop")
        module_map = self.browsers.get(module_name, {})
        if loop not in module_map:
            raise RuntimeError(f"Browser for module {module_name} not initialized for this event loop")
        return module_map[loop]

    def get_playwright(self, module_name):
        loop = self._get_loop()
        if loop is None:
            raise RuntimeError("Must be called within an event loop")
        module_map = self.playwrights.get(module_name, {})
        if loop not in module_map:
            raise RuntimeError(f"Playwright for module {module_name} not initialized for this event loop")
        return module_map[loop]

    async def close_current_loop_browsers(self):
        """Close all module browsers associated with current event loop (used by background tasks)"""
        loop = self._get_loop()
        if loop is None:
            return
        for module in list(self.browsers.keys()):
            loop_map = self.browsers.get(module, {})
            browser = loop_map.get(loop)
            if not browser:
                continue
            # Only close if NOT shared root
            if not getattr(browser, "_shared_root", False):
                # Remove from mapping and close
                loop_map.pop(loop, None)
                try:
                    await browser.close()
                    self.logger.info(f"Closed background browser for module {module} (loop {id(loop)})")
                except Exception as e:
                    self.logger.error(f"Error closing browser for module {module}: {e}")
            # If shared root, leave it in place
            if not getattr(browser, "_shared_root", False):
                playwright = self.playwrights[module].pop(loop, None)
                if playwright:
                    try:
                        await playwright.stop()
                    except:
                        pass
        # Clean empty dicts
        self.browsers = {m: mp for m, mp in self.browsers.items() if mp}
        self.playwrights = {m: mp for m, mp in self.playwrights.items() if mp}

    def register_context(self, session_id: str, context) -> None:
        """
        Register a browser context for a session.
        
        Args:
            session_id: Unique session identifier
            context: Browser context to register
        """
        if session_id not in self.session_contexts:
            self.session_contexts[session_id] = []
        self.session_contexts[session_id].append(context)
        self.logger.info(f"Registered context for session {session_id} (total: {len(self.session_contexts[session_id])})")

    def _context_is_closed(self, context) -> bool:
        """Best-effort check whether a Playwright BrowserContext is already closed."""
        # Newer Playwright versions expose context.is_closed(); older ones don't.
        try:
            if hasattr(context, "is_closed"):
                return bool(context.is_closed())
            # Fallback to private flag used internally by Playwright (<1.36)
            return bool(getattr(context, "_closed", False))
        except Exception:
            # On any unexpected error assume closed so we don't leak it
            return True

    async def close_session_contexts(self, session_id: str) -> int:
        """
        Close all browser contexts for a given session.
        Returns the number of contexts that were successfully closed.
        """
        contexts = self.session_contexts.get(session_id, [])
        if not contexts:
            self.logger.info(f"No contexts found for session {session_id}")
            return 0

        closed_count = 0
        for context in contexts:
            try:
                if not self._context_is_closed(context):
                    await context.close()
                    closed_count += 1
                    self.logger.info(f"Closed context for session {session_id}")
            except Exception as e:
                # Ignore cases where the context is already closed; log others
                if "closed" not in str(e).lower():
                    self.logger.error(f"Error closing context for session {session_id}: {e}")
        # Remove the session record regardless of outcome
        self.session_contexts.pop(session_id, None)
        if closed_count:
            self.logger.info(f"Closed {closed_count} contexts for session {session_id}")
        return closed_count

    def get_session_context_count(self, session_id: str) -> int:
        """Return how many contexts are still open for a session."""
        contexts = self.session_contexts.get(session_id, [])
        active_count = 0
        for context in contexts:
            try:
                if not self._context_is_closed(context):
                    active_count += 1
            except:
                # If any error arises, assume the context is closed
                pass
        return active_count

    def cleanup_closed_contexts(self) -> int:
        """
        Clean up closed contexts from the registry.
        
        Returns:
            Number of contexts cleaned up
        """
        cleaned_count = 0
        for session_id in list(self.session_contexts.keys()):
            contexts = self.session_contexts[session_id]
            active_contexts = []
            
            for context in contexts:
                try:
                    if not self._context_is_closed(context):
                        active_contexts.append(context)
                    else:
                        cleaned_count += 1
                except:
                    cleaned_count += 1
            
            if active_contexts:
                self.session_contexts[session_id] = active_contexts
            else:
                self.session_contexts.pop(session_id, None)
        
        if cleaned_count > 0:
            self.logger.info(f"Cleaned up {cleaned_count} closed contexts")
        
        return cleaned_count

# Global browser manager instance
browser_manager = BrowserManager()