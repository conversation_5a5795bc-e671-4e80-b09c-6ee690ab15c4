{"version": 3, "sources": ["../../../src/server/base-http/index.ts"], "names": ["RedirectStatusCode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseNextRequest", "constructor", "method", "url", "body", "cookies", "_cookies", "headers", "BaseNextResponse", "destination", "redirect", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "PermanentRedirect"], "mappings": "AAGA,SAASA,kBAAkB,QAAQ,+CAA8C;AAEjF,SAASC,eAAe,QAAQ,iCAAgC;AAmBhE,OAAO,MAAeC;IAIpBC,YAAmBC,QAAuBC,KAAoBC,KAAY;sBAAvDF;mBAAuBC;oBAAoBC;IAAa;IAE3E,qDAAqD;IAErD,IAAWC,UAAU;QACnB,IAAI,IAAI,CAACC,QAAQ,EAAE,OAAO,IAAI,CAACA,QAAQ;QACvC,OAAQ,IAAI,CAACA,QAAQ,GAAGP,gBAAgB,IAAI,CAACQ,OAAO;IACtD;AACF;AAEA,OAAO,MAAeC;IAKpBP,YAAmBQ,YAA0B;2BAA1BA;IAA2B;IAmC9C,qDAAqD;IAErDC,SAASD,WAAmB,EAAEE,UAAkB,EAAE;QAChD,IAAI,CAACC,SAAS,CAAC,YAAYH;QAC3B,IAAI,CAACE,UAAU,GAAGA;QAElB,0DAA0D;QAC1D,qCAAqC;QACrC,IAAIA,eAAeb,mBAAmBe,iBAAiB,EAAE;YACvD,IAAI,CAACD,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEH,YAAY,CAAC;QAClD;QACA,OAAO,IAAI;IACb;AACF"}