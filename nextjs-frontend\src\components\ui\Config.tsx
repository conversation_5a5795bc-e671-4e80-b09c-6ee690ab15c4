import React, { useState, useEffect } from 'react';
import { ProviderType } from '../booking/ProviderSelector';
import { useAuth } from '../../context/AuthContext';

interface ConfigProps {
  onConfigChange: (config: ConfigState) => void;
  provider: ProviderType;
}

export interface ConfigState {
  browser_type: string;
  headless: boolean;
  take_screenshots: boolean;
  debug_mode: boolean;
  video_auditing_ncl: boolean;
  video_auditing_studio: boolean;
  video_auditing_cruising_power: boolean;
  cruising_power_onboard_percentage: number;
  ncl_category_percentages: {
    inside: number;
    outside: number;
    balcony: number;
    junior_suite: number;
    suite: number;
  };
  rate_selection_strategy: string;
  ncl_rate_codes: {
    ALL4CHO: boolean;
    CHOALL42: boolean;
    FITOBC: boolean;
    STAROBC: boolean;
    NATOBC: boolean;
  };
  onesource_commission_percentages: {
    holland: number;
    princess: number;
  };
  onesource_perk: string;
  provider?: string;
}

const Config: React.FC<ConfigProps> = ({ onConfigChange, provider }) => {
  const { isAdmin } = useAuth();
  
  // Set initial state with headless true for all providers
  const [configState, setConfigState] = useState<ConfigState>({
    browser_type: 'chrome',
    headless: false, 
    take_screenshots: true,
    debug_mode: false,
    video_auditing_ncl: false,
    video_auditing_studio: false,
    video_auditing_cruising_power: false,
    cruising_power_onboard_percentage: 12,
    ncl_category_percentages: {
      inside: 12,
      outside: 12,
      balcony: 10,
      junior_suite: 10,
      suite: 10,
    },
    rate_selection_strategy: 'Cheapest',
    ncl_rate_codes: {
      ALL4CHO: true,
      CHOALL42: true,
      FITOBC: false,
      STAROBC: false,
      NATOBC: false,
    },
    onesource_commission_percentages: {
      holland: 12,
      princess: 11,
    },
    onesource_perk: 'None'
  });

  // No longer need to adjust headless based on provider
  // Just pass config changes to parent
  const handleChange = (field: keyof ConfigState | string, value: string | boolean | number) => {
    let newState = { ...configState };
    
    // Handle nested property updates (e.g., 'ncl_category_percentages.inside')
    if (field.includes('.')) {
      const [parentKey, childKey] = field.split('.');
      if (parentKey === 'ncl_category_percentages') {
        newState = {
          ...configState,
          ncl_category_percentages: {
            ...configState.ncl_category_percentages,
            [childKey]: value
          }
        };
      } else if (parentKey === 'onesource_commission_percentages') {
        newState = {
          ...configState,
          onesource_commission_percentages: {
            ...configState.onesource_commission_percentages,
            [childKey]: value
          }
        };
      }
    } else {
      // Handle simple property updates
      newState = { ...configState, [field as keyof ConfigState]: value };
    }
    
    setConfigState(newState);
    onConfigChange(newState);
  };

  // Handle rate code checkbox changes
  const handleRateCodeChange = (rateCode: keyof ConfigState['ncl_rate_codes'], checked: boolean) => {
    const newRateCodes = { ...configState.ncl_rate_codes, [rateCode]: checked };
    const newState = { ...configState, ncl_rate_codes: newRateCodes };
    setConfigState(newState);
    onConfigChange(newState);
  };

  // Get the appropriate video auditing toggle for the current provider
  const getVideoAuditingValue = () => {
    if (provider === 'NCL') {
      return configState.video_auditing_ncl;
    } else if (provider === 'Cruising Power') {
      return configState.video_auditing_cruising_power;
    } else if (provider.startsWith('Studio')) {
      // Always return false for Studio
      return false;
    }
    return false;
  };

  // Get the appropriate video auditing field name for the current provider
  const getVideoAuditingField = () => {
    if (provider === 'NCL') {
      return 'video_auditing_ncl';
    } else if (provider === 'Cruising Power') {
      return 'video_auditing_cruising_power';
    } else if (provider.startsWith('Studio')) {
      return 'video_auditing_studio';
    }
    return '';
  };
  
  // Check if video auditing toggle should be disabled
  const isVideoAuditingDisabled = () => {
    return !isAdmin || provider.startsWith('Studio');
  };
  
  // Handle video auditing change
  const handleVideoAuditingChange = (checked: boolean) => {
    // For Studio, always set to false regardless of toggle state
    if (provider.startsWith('Studio')) {
      handleChange('video_auditing_studio', false);
      return;
    }
    
    // For other providers, use normal handling
    handleChange(getVideoAuditingField(), checked);
  };

  return (
    <div className="">
      <div className="p-3">
        <h2 className="text-black text-xl font-semibold flex items-center">
          Travel Agency Customization </h2>
        <div className="mt-0 relative pb-2">
          <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
          <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
        </div>
        <p className="mt-2 text-gray-800 text-sm">Choose the options below to customize the booking process as per travel agency.</p>
      </div>

      <div className="bg-white p-6 px-2 rounded-lg shadow-md">
        <div className="flex flex-col items-center">
          <div className="w-full max-w-md px-4">

            {/* Video Auditing Toggle - At the top */}
            <div className="mb-6 p-4 rounded-lg border-2 border-purple-200 bg-purple-50">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <label className="block text-purple-800 text-[15px] font-semibold mb-1">
                    Video Auditing
                  </label>
                  <p className="text-sm text-purple-600">
                    {isAdmin 
                      ? (provider.startsWith('Studio') 
                        ? "Video recording is disabled for Studio" 
                        : "Enable video recording during booking process for audit purposes") 
                      : "Video recording for audit purposes (Admin only)"}
                  </p>
                </div>
                <div className="ml-4">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only peer"
                      checked={getVideoAuditingValue()}
                      onChange={(e) => isAdmin && handleVideoAuditingChange(e.target.checked)}
                      disabled={isVideoAuditingDisabled()}
                    />
                    <div className={`relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600 ${isVideoAuditingDisabled() ? 'opacity-50 cursor-not-allowed' : ''}`}></div>
                  </label>
                </div>
              </div>
            </div>

            {/* Cruising Power Onboard Credit - Only show for Cruising Power */}
            {provider === 'Cruising Power' && (
              <div className="">
                <label className="block text-cyan-800 text-[15px] font-semibold mb-2">
                  Cruising Power Onboard Credit %
                </label>
                <input
                  type="number"
                  className="w-full p-3 rounded-lg bg-gray-200"
                  min="1"
                  max="100"
                  value={configState.cruising_power_onboard_percentage}
                  onChange={(e) => handleChange('cruising_power_onboard_percentage', parseInt(e.target.value) || 1)}
                />
                <p className="text-sm text-gray-500 mt-2">Percentage used to calculate onboard credit for Cruising Power</p>
              </div>
            )}

            {/* NCL Category-Specific Onboard Credit - Only show for NCL */}
            {provider === 'NCL' && (
              <div className="mb-4 rounded-lg">
                <label className="block text-cyan-800 text-[15px] font-semibold mb-4">
                  NCL Category-Specific Onboard Credit %
                </label>
                <p className="text-sm text-gray-500 mb-4">Set different onboard credit percentages for each cabin category</p>
                
                {/* First line: Inside and Outside Cabins */}
                <div className="mb-3 grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Inside Cabins</label>
                    <input
                      type="number"
                      className="w-full p-2 rounded-lg bg-gray-200 text-sm"
                      min="1"
                      max="100"
                      value={configState.ncl_category_percentages.inside}
                      onChange={(e) => handleChange('ncl_category_percentages.inside', parseInt(e.target.value) || 1)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Outside Cabins</label>
                    <input
                      type="number"
                      className="w-full p-2 rounded-lg bg-gray-200 text-sm"
                      min="1"
                      max="100"
                      value={configState.ncl_category_percentages.outside}
                      onChange={(e) => handleChange('ncl_category_percentages.outside', parseInt(e.target.value) || 1)}
                    />
                  </div>
                </div>

                {/* Second line: Balcony and Junior Suite Cabins */}
                <div className="mb-3 grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Balcony Cabins</label>
                    <input
                      type="number"
                      className="w-full p-2 rounded-lg bg-gray-200 text-sm"
                      min="1"
                      max="100"
                      value={configState.ncl_category_percentages.balcony}
                      onChange={(e) => handleChange('ncl_category_percentages.balcony', parseInt(e.target.value) || 1)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Junior Suite Cabins</label>
                    <input
                      type="number"
                      className="w-full p-2 rounded-lg bg-gray-200 text-sm"
                      min="1"
                      max="100"
                      value={configState.ncl_category_percentages.junior_suite}
                      onChange={(e) => handleChange('ncl_category_percentages.junior_suite', parseInt(e.target.value) || 1)}
                    />
                  </div>
                </div>

                {/* Third line: Suite Cabins (full width) */}
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Suite Cabins</label>
                  <input
                    type="number"
                    className="w-full p-2 rounded-lg bg-gray-200 text-sm"
                    min="1"
                    max="100"
                    value={configState.ncl_category_percentages.suite}
                    onChange={(e) => handleChange('ncl_category_percentages.suite', parseInt(e.target.value) || 1)}
                  />
                </div>
              </div>
            )}

             {/* Rate Selection Strategy - Only show for Studio providers */}
             {provider.startsWith('Studio') && (
               <div className="mt-6 mb-4 rounded-lg relative">
                <label className="block text-cyan-800 text-[15px] font-semibold mb-2">
                  Rate Selection Strategy
                </label>
                <div className="relative">
                  <select
                    className="w-full p-2 rounded-lg bg-gray-200 appearance-none pr-8"
                    value={configState.rate_selection_strategy}
                    onChange={(e) => handleChange('rate_selection_strategy', e.target.value)}
                  >
                    <option value="Cheapest">Cheapest</option>
                    <option value="USail">USail</option>
                    <option value="USail (Refundable)">USail (Refundable)</option>
                    <option value="USail (Perk)">USail (Perk)</option>
                    <option value="Value Sails">Value Sails</option>
                    <option value="Value Sails (Refundable)">Value Sails (Refundable)</option>
                    <option value="Value Sails (Perk)">Value Sails (Perk)</option>
                    
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4 text-gray-700">
                    <svg className="fill-current h-6 w-6" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <path d="M7 10l5 5 5-5z"/>
                    </svg>
                  </div>
                </div>
                <p className="text-sm text-gray-500 mt-2">Strategy for selecting rates (only affects Studio bookings)</p>
                <p className="text-sm text-gray-500 mt-2">It will select as per cruise line preference</p>
                
                <li>Cheapest: Select the cheapest</li>
                <p> --------------------------------------- </p>
                <li>USail :-</li>
                <div className="text-sm text-gray-500 mt-1 ml-2">
                  <li>USail (Cunard)</li>
                  <li>USail (Princess)</li>
                  <li>USail + Free Grats</li>
                  <li>USail W/ Grats Non-Refundable RCL</li>
                </div>
                <p> --------------------------------------- </p>
                <li>USail (Perk) :-</li>
                <div className="text-sm text-gray-500 mt-1 ml-2">
                  <li>USail All In (Celebrity)</li>
                  <li>USail PPLUS (Princess)</li>
                  <li>Enjoy It All (Cunard)</li>
                </div>
                <p> --------------------------------------- </p>
                <li>USail Refundable :-</li>
                <div className="text-sm text-gray-500 mt-1 ml-2">
                  <li>USail W/ Grats Refundable RCL</li>
                </div>
                <p> --------------------------------------- </p>
                <li>Value Sails</li>
                <li>Value Sails (Refundable)</li>
                <li>Value Sails (Perk)</li>
              </div>
             )}

            {/* NCL Rate Code Selection - Only show for NCL */}
            {provider === 'NCL' && (
              <div className="mt-6 mb-4 rounded-lg">
                <label className="block text-cyan-800 text-[15px] font-semibold mb-2">
                  NCL Rate Code Selection
                </label>
                <p className="text-sm text-gray-500 mb-3">Select which rate codes to include for NCL bookings</p>
                
                {/* Primary Rate Codes */}
                <div className="mb-3">
                  <p className="text-sm font-medium text-gray-700 mb-2">Primary Rate Codes:</p>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="ALL4CHO"
                        className="h-4 w-4 text-teal-600 rounded focus:ring-teal-500"
                        checked={configState.ncl_rate_codes.ALL4CHO}
                        onChange={(e) => handleRateCodeChange('ALL4CHO', e.target.checked)}
                      />
                      <label htmlFor="ALL4CHO" className="text-sm text-gray-700">
                        ALL4CHO
                      </label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="CHOALL42"
                        className="h-4 w-4 text-teal-600 rounded focus:ring-teal-500"
                        checked={configState.ncl_rate_codes.CHOALL42}
                        onChange={(e) => handleRateCodeChange('CHOALL42', e.target.checked)}
                      />
                      <label htmlFor="CHOALL42" className="text-sm text-gray-700">
                        CHOALL42
                      </label>
                    </div>
                  </div>
                </div>

                {/* Secondary Rate Codes */}
                <div className="mb-3">
                  <p className="text-sm font-medium text-gray-700 mb-2">Secondary Rate Codes:</p>
                  <div className="space-y-2">
                    {/* First line: FITOBC and STAROBC */}
                    <div className="grid grid-cols-2 gap-3">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          id="FITOBC"
                          className="h-4 w-4 text-teal-600 rounded focus:ring-teal-500"
                          checked={configState.ncl_rate_codes.FITOBC}
                          onChange={(e) => handleRateCodeChange('FITOBC', e.target.checked)}
                        />
                        <label htmlFor="FITOBC" className="text-sm text-gray-700">
                          FITOBC
                        </label>
                      </div>
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          id="STAROBC"
                          className="h-4 w-4 text-teal-600 rounded focus:ring-teal-500"
                          checked={configState.ncl_rate_codes.STAROBC}
                          onChange={(e) => handleRateCodeChange('STAROBC', e.target.checked)}
                        />
                        <label htmlFor="STAROBC" className="text-sm text-gray-700">
                          STAROBC
                        </label>
                      </div>
                    </div>
                    {/* Second line: NATOBC */}
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="NATOBC"
                        className="h-4 w-4 text-teal-600 rounded focus:ring-teal-500"
                        checked={configState.ncl_rate_codes.NATOBC}
                        onChange={(e) => handleRateCodeChange('NATOBC', e.target.checked)}
                      />
                      <label htmlFor="NATOBC" className="text-sm text-gray-700">
                        NATOBC
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* OneSource Commission Percentages - Only show for OneSource */}
            {provider === 'OneSource' && (
              <div className="mt-6 mb-4 rounded-lg">
                <label className="block text-cyan-800 text-[15px] font-semibold mb-2">
                  OneSource Commission Percentages
                </label>
                <p className="text-sm text-gray-500 mb-4">Set commission percentages for different cruise lines</p>
                
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Holland Cruise Line (%)</label>
                    <input
                      type="number"
                      className="w-full p-2 rounded-lg bg-gray-200 text-sm"
                      min="1"
                      max="100"
                      value={configState.onesource_commission_percentages.holland}
                      onChange={(e) => handleChange('onesource_commission_percentages.holland', parseInt(e.target.value) || 1)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Princess Cruise Line (%)</label>
                    <input
                      type="number"
                      className="w-full p-2 rounded-lg bg-gray-200 text-sm"
                      min="1"
                      max="100"
                      value={configState.onesource_commission_percentages.princess}
                      onChange={(e) => handleChange('onesource_commission_percentages.princess', parseInt(e.target.value) || 1)}
                    />
                  </div>
                </div>
                
                <p className="text-sm text-gray-500 mt-2">
                  Default: Holland 12%, Princess 11%.
                </p>
                
                {/* Perk Selector */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Perk Selection</label>
                  <select
                    className="w-full p-2 rounded-lg bg-gray-200 text-sm"
                    value={configState.onesource_perk}
                    onChange={(e) => handleChange('onesource_perk', e.target.value)}
                  >
                    <option value="No">No</option>
                    <option value="Yes">Yes</option>
                  </select>
                  <p className="text-sm text-gray-500 mt-1">
                    Select whether to include perk-based promotions in fare comparison.
                  </p>
                </div>
              </div>
            )}
          </div>        
        </div>
      </div>
    </div>
  );
};

export default Config;