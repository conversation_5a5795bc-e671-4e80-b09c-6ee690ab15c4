{"version": 3, "sources": ["../../../src/shared/lib/match-remote-pattern.ts"], "names": ["matchRemotePattern", "hasMatch", "pattern", "url", "protocol", "undefined", "actualProto", "slice", "port", "hostname", "Error", "JSON", "stringify", "makeRe", "test", "pathname", "domains", "remotePatterns", "some", "domain", "p"], "mappings": ";;;;;;;;;;;;;;;IAGgBA,kBAAkB;eAAlBA;;IA8BAC,QAAQ;eAARA;;;4BAhCO;AAEhB,SAASD,mBAAmBE,OAAsB,EAAEC,GAAQ;IACjE,IAAID,QAAQE,QAAQ,KAAKC,WAAW;QAClC,MAAMC,cAAcH,IAAIC,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC;QAC3C,IAAIL,QAAQE,QAAQ,KAAKE,aAAa;YACpC,OAAO;QACT;IACF;IACA,IAAIJ,QAAQM,IAAI,KAAKH,WAAW;QAC9B,IAAIH,QAAQM,IAAI,KAAKL,IAAIK,IAAI,EAAE;YAC7B,OAAO;QACT;IACF;IAEA,IAAIN,QAAQO,QAAQ,KAAKJ,WAAW;QAClC,MAAM,IAAIK,MACR,AAAC,+CAA4CC,KAAKC,SAAS,CAACV;IAEhE,OAAO;QACL,IAAI,CAACW,IAAAA,kBAAM,EAACX,QAAQO,QAAQ,EAAEK,IAAI,CAACX,IAAIM,QAAQ,GAAG;YAChD,OAAO;QACT;IACF;QAEYP;IAAZ,IAAI,CAACW,IAAAA,kBAAM,EAACX,CAAAA,oBAAAA,QAAQa,QAAQ,YAAhBb,oBAAoB,MAAMY,IAAI,CAACX,IAAIY,QAAQ,GAAG;QACxD,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAASd,SACde,OAAiB,EACjBC,cAA+B,EAC/Bd,GAAQ;IAER,OACEa,QAAQE,IAAI,CAAC,CAACC,SAAWhB,IAAIM,QAAQ,KAAKU,WAC1CF,eAAeC,IAAI,CAAC,CAACE,IAAMpB,mBAAmBoB,GAAGjB;AAErD"}