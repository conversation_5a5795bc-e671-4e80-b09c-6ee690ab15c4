import os
import logging
from contextlib import asynccontextmanager
from dotenv import load_dotenv
import asyncio
import asyncpg
load_dotenv()
logger = logging.getLogger('db_service')
logging.basicConfig(level=logging.INFO)

# Environment-based database configuration
ENVIRONMENT = os.getenv('ENVIRONMENT')  # 'production' or 'development'

# Production Database Configuration
PROD_PG_HOST = os.getenv('PG_HOST')
PROD_PG_PORT = os.getenv('PG_PORT')
PROD_PG_DATABASE = os.getenv('PG_DATABASE')
PROD_PG_USER = os.getenv('PG_USER')
PROD_PG_PASSWORD = os.getenv('PG_PASSWORD')

# development Database Configuration
TEST_PG_HOST = os.getenv('PG_HOST')
TEST_PG_PORT = os.getenv('PG_PORT')
TEST_PG_DATABASE = os.getenv('TEST_PG_DATABASE')
TEST_PG_USER = os.getenv('TEST_PG_USER')
TEST_PG_PASSWORD = os.getenv('TEST_PG_PASSWORD')

# Connection pool settings
MIN_CONN = int(os.getenv('DB_POOL_MIN_CONN', 1))
MAX_CONN = int(os.getenv('DB_POOL_MAX_CONN', 20))

# Select configuration based on environment
if ENVIRONMENT == 'development':
    PG_HOST = TEST_PG_HOST
    PG_PORT = TEST_PG_PORT
    PG_DATABASE = TEST_PG_DATABASE
    PG_USER = TEST_PG_USER
    PG_PASSWORD = TEST_PG_PASSWORD
    logger.info(f"Using development database configuration: {PG_DATABASE}")
else:
    PG_HOST = PROD_PG_HOST
    PG_PORT = PROD_PG_PORT
    PG_DATABASE = PROD_PG_DATABASE
    PG_USER = PROD_PG_USER
    PG_PASSWORD = PROD_PG_PASSWORD
    logger.info(f"Using PRODUCTION database configuration: {PG_DATABASE}")

# Global variable for the connection pool
_pool = None

# Initialize the connection pool
async def init_db_pool():
    global _pool
    try:
        if _pool is None:
            _pool = await asyncpg.create_pool(
                min_size=MIN_CONN,
                max_size=MAX_CONN,
                host=PG_HOST,
                port=PG_PORT,
                database=PG_DATABASE,
                user=PG_USER,
                password=PG_PASSWORD
            )
            logger.info(f"Initialized DB pool: host={PG_HOST} port={PG_PORT} db={PG_DATABASE} min={MIN_CONN} max={MAX_CONN}")
    except Exception as e:
        logger.error(f"Failed to create DB connection pool: {e}")
        raise

# Helper to get a connection from the pool
async def get_db_connection():
    global _pool
    if _pool is None:
        await init_db_pool()
    return await _pool.acquire()

# Context manager for pooled connections
@asynccontextmanager
async def db_connection():
    conn = await get_db_connection()
    try:
        yield conn
    finally:
        await _pool.release(conn)

# Execute a query with optional fetch semantics
async def execute_query(query, params=None, fetch_one=False, fetch_all=False):
    conn = await get_db_connection()
    try:
        if fetch_one:
            result = await conn.fetchrow(query, *(params or []))
            # Convert to dict to maintain compatibility
            if result:
                result = dict(result)
        elif fetch_all:
            rows = await conn.fetch(query, *(params or []))
            # Convert to list of dicts to maintain compatibility
            result = [dict(row) for row in rows]
        else:
            result = await conn.execute(query, *(params or []))
        return result
    except Exception as e:
        logger.error(f"DB query error: {e}")
        raise
    finally:
        await _pool.release(conn)

async def close_db_pool():
    """Close the database connection pool."""
    global _pool
    if _pool is not None:
        logging.info("Closing database connection pool")
        await _pool.close()
        _pool = None
        logging.info("Database connection pool closed")
    else:
        logging.info("No database connection pool to close")
 