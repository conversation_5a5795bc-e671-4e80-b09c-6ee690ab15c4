"""
Module for handling Celebrity cruise promotions based on LLM analysis
"""
from playwright.async_api import Page, expect
import asyncio
from loguru import logger
import os
from Cruising_Power.screenshot_utils import take_scrolling_screenshot

class PromotionHandler:
    """
    Class for handling promotions on the Celebrity website based on LLM analysis
    """
    
    def __init__(self, page, promotion_info, request_id=None, cabin_id=None, log_dir=None):
        """
        Initialize the promotion handler
        
        Args:
            page: Playwright Page instance
            promotion_info: Dictionary with promotion details from LLM
            request_id: Request ID for database storage
            cabin_id: Cabin ID for database storage
            log_dir: Kept for backward compatibility but not used
        """
        self.page = page
        self.promotion_info = promotion_info
        self.request_id = request_id
        self.cabin_id = cabin_id
        # We don't store log_dir as we don't use it
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """Set up and configure logger"""
        # Using loguru logger directly - no file handlers needed, only console
        return logger
    
    async def handle_promotions(self):
        """
        Handle promotions on the Celebrity website based on LLM analysis.
        
        Returns:
            Boolean indicating success or failure
        """
        try:
            if not self.promotion_info.get("needs_promotion", False):
                self.logger.info("No promotions needed based on LLM analysis")
                return True
                
            self.logger.info(f"LLM indicates promotions are needed: {self.promotion_info.get('promotion_type')} - {self.promotion_info.get('reason')}")
            
            # Store selected rate codes for JSON output
            selected_rate_codes = []
            
            # Click on View Promotions button
            try:
                self.logger.info("Clicking on View Promotions button...")
                promo_button = await self.page.wait_for_selector("#cruiseOnlyAllocation > header > div.formItem.filterResultsAuto.promoButton > a", 
                                                          state="visible", 
                                                          timeout=10000)
                if promo_button:
                    # Scroll to view and click
                    await promo_button.scroll_into_view_if_needed()
                    await promo_button.click()
                    await asyncio.sleep(1)  # Allow time for promotions to load
                    
                    # Take screenshot
                    # await take_scrolling_screenshot(self.page, "5_promotions_view", self.request_id, self.cabin_id)
                else:
                    self.logger.error("View Promotions button not found")
                    return False
            except Exception as e:
                self.logger.error(f"Could not find or click on View Promotions button: {e}")
                return False
                
            # First, uncheck any already selected rate codes (especially Best Rate)
            try:
                await asyncio.sleep(2)
                self.logger.info("Unchecking any pre-selected rate codes...")
                
                # Try with JavaScript to find and uncheck any selected checkboxes
                result = await self.page.evaluate("""() => {
                    const allCheckboxes = document.querySelectorAll('#promoFareTableFIT input[type="checkbox"]');
                    let uncheckedCount = 0;
                    for (let checkbox of allCheckboxes) {
                        if (checkbox.checked) {
                            checkbox.click();
                            uncheckedCount++;
                        }
                    }
                    return uncheckedCount;
                }""")
                
                self.logger.info(f"Unchecked {result} pre-selected rate codes")
                await asyncio.sleep(1)  # Give time for UI to update
                
            except Exception as e:
                self.logger.warning(f"Error when trying to uncheck pre-selected rate codes: {e}")
                # Continue anyway as this is not critical
            
            # Now select the two specific rate codes by value or text content
            try:
                self.logger.info("Selecting rate codes...")
                
                # Find and select ALL INC 2PK
                try:
                    self.logger.info("Trying to select ALL INC 2PK rate code...")
                    # Try to find by value attribute containing ALL INC 2PK
                    result = await self.page.evaluate("""() => {
                        const allCheckboxes = document.querySelectorAll('#promoFareTableFIT input[type="checkbox"]');
                        for (let checkbox of allCheckboxes) {
                            if (checkbox.value && checkbox.value.includes('ALL INC 2PK')) {
                                if (!checkbox.checked) {
                                    checkbox.click();
                                    return {success: true, value: checkbox.value};
                                } else {
                                    return {success: true, value: checkbox.value, alreadyChecked: true};
                                }
                            }
                        }
                        
                        // If not found by value, try to find by row text content
                        const rows = document.querySelectorAll('#promoFareTableFIT > tbody > tr');
                        for (let row of rows) {
                            if (row.textContent && row.textContent.includes('ALL INC 2PK')) {
                                const checkbox = row.querySelector('input[type="checkbox"]');
                                if (checkbox && !checkbox.checked) {
                                    checkbox.click();
                                    return {success: true, value: checkbox.value || 'ALL INC 2PK'};
                                } else if (checkbox) {
                                    return {success: true, value: checkbox.value || 'ALL INC 2PK', alreadyChecked: true};
                                }
                            }
                        }
                        
                        return {success: false};
                    }""")
                    
                    if result and result.get('success'):
                        if result.get('alreadyChecked'):
                            self.logger.info(f"ALL INC 2PK rate code was already selected: {result.get('value')}")
                        else:
                            self.logger.info(f"Selected ALL INC 2PK rate code: {result.get('value')}")
                        selected_rate_codes.append(result.get('value', 'ALL INC 2PK'))
                        await asyncio.sleep(1)  # Allow UI to update
                    else:
                        self.logger.warning("Could not find or select ALL INC 2PK rate code")
                        
                except Exception as e:
                    self.logger.warning(f"Error selecting ALL INC 2PK rate code: {e}")
                
                # Find and select RETREAT NRD
                try:
                    self.logger.info("Trying to select RETREAT NRD rate code...")
                    # Try to find by value attribute containing RETREAT NRD
                    result = await self.page.evaluate("""() => {
                        const allCheckboxes = document.querySelectorAll('#promoFareTableFIT input[type="checkbox"]');
                        for (let checkbox of allCheckboxes) {
                            if (checkbox.value && checkbox.value.includes('RETREAT NRD')) {
                                if (!checkbox.checked) {
                                    checkbox.click();
                                    return {success: true, value: checkbox.value};
                                } else {
                                    return {success: true, value: checkbox.value, alreadyChecked: true};
                                }
                            }
                        }
                        
                        // If not found by value, try to find by row text content
                        const rows = document.querySelectorAll('#promoFareTableFIT > tbody > tr');
                        for (let row of rows) {
                            if (row.textContent && row.textContent.includes('RETREAT NRD')) {
                                const checkbox = row.querySelector('input[type="checkbox"]');
                                if (checkbox && !checkbox.checked) {
                                    checkbox.click();
                                    return {success: true, value: checkbox.value || 'RETREAT NRD'};
                                } else if (checkbox) {
                                    return {success: true, value: checkbox.value || 'RETREAT NRD', alreadyChecked: true};
                                }
                            }
                        }
                        
                        return {success: false};
                    }""")
                    
                    if result and result.get('success'):
                        if result.get('alreadyChecked'):
                            self.logger.info(f"RETREAT NRD rate code was already selected: {result.get('value')}")
                        else:
                            self.logger.info(f"Selected RETREAT NRD rate code: {result.get('value')}")
                        selected_rate_codes.append(result.get('value', 'RETREAT NRD'))
                        await asyncio.sleep(1)  # Allow UI to update
                    else:
                        self.logger.warning("Could not find or select RETREAT NRD rate code")
                        
                except Exception as e:
                    self.logger.warning(f"Error selecting RETREAT NRD rate code: {e}")
                
                # Take screenshot after selecting rate codes
                await take_scrolling_screenshot(self.page, "6_rate_codes_selected", self.request_id, self.cabin_id)
                    
                # Click the Compare button
                try:
                    compare_button = await self.page.wait_for_selector("#promoFare10", 
                                                              state="visible",
                                                              timeout=10000)
                    if compare_button:
                        await compare_button.click()
                        self.logger.info("Clicked on Compare button")
                        await asyncio.sleep(2)  # Allow time for comparison to load
                        
                        # Take screenshot of comparison page
                        await take_scrolling_screenshot(self.page, "7_rate_comparison", self.request_id, self.cabin_id)
                    else:
                        self.logger.error("Compare button not found")
                        return False
                except Exception as e:
                    self.logger.error(f"Could not find or click on Compare button: {e}")
                    return False
                
                # Return the selected rate codes for JSON output
                return {"success": True, "selected_rate_codes": selected_rate_codes}
                    
            except Exception as e:
                self.logger.error(f"Error selecting rate codes: {e}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error handling promotions: {e}")
            return False


# Function to maintain backward compatibility
async def handle_promotions(page, promotion_info, request_id=None, cabin_id=None, log_dir=None):
    """
    Convenience function for handling promotions
    
    Args:
        page: Playwright Page instance
        promotion_info: Dictionary with promotion details from LLM
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        log_dir: Kept for backward compatibility but not used
        
    Returns:
        Dictionary or Boolean indicating success or failure with selected rate codes
    """
    handler = PromotionHandler(page, promotion_info, request_id, cabin_id, None)
    return await handler.handle_promotions() 