import React, { useState, useEffect } from 'react';
import { getAgencies, calculateTimeSavings, timeToMinutes } from '../../data/cruiseData';

interface ProcessingTimeChartProps {
  className?: string;
}

type ViewMode = 'absolute' | 'percentage';
type FilterPortal = 'all' | 'portalA' | 'portalB';
type FilterCabin = 'all' | 'single' | 'multiple';

const ProcessingTimeChart: React.FC<ProcessingTimeChartProps> = ({ className }) => {
  const [hoveredBar, setHoveredBar] = useState<string | null>(null);
  const [animate, setAnimate] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('percentage');
  const [portalFilter, setPortalFilter] = useState<FilterPortal>('all');
  const [cabinFilter, setCabinFilter] = useState<FilterCabin>('all');
  const [showDrillDown, setShowDrillDown] = useState(false);
  const [drillDownData, setDrillDownData] = useState<{
    portal: string;
    cabinType: string;
    manual: string;
    oceanmind: string;
    quotes: number;
    savingsPercent: number;
  } | null>(null);

  // Get the agencies data
  const agencies = getAgencies();
  const portalA = agencies[0];
  const portalB = agencies[1];

  // Calculate all time savings percentages for scaling
  const portalASingleSavings = calculateTimeSavings(
    portalA.processingTimes.singleCabin.manual,
    portalA.processingTimes.singleCabin.oceanmind
  );
  const portalAMultipleSavings = calculateTimeSavings(
    portalA.processingTimes.multipleCabin.manual,
    portalA.processingTimes.multipleCabin.oceanmind
  );
  const portalBSingleSavings = calculateTimeSavings(
    portalB.processingTimes.singleCabin.manual,
    portalB.processingTimes.singleCabin.oceanmind
  );
  const portalBMultipleSavings = calculateTimeSavings(
    portalB.processingTimes.multipleCabin.manual,
    portalB.processingTimes.multipleCabin.oceanmind
  );

  // Find the maximum savings percentage to use as reference (100%)
  const maxSavingsPercent = Math.max(
    portalASingleSavings,
    portalAMultipleSavings,
    portalBSingleSavings,
    portalBMultipleSavings
  );

  // Scale the percentages relative to the maximum value
  const getScaledHeight = (savingsPercent: number) => {
    return (savingsPercent / maxSavingsPercent) * 100;
  };

  // Calculate time values in minutes for visualization heights
  const timeValues = {
    portalA: {
      singleManual: timeToMinutes(portalA.processingTimes.singleCabin.manual),
      singleOceanmind: timeToMinutes(portalA.processingTimes.singleCabin.oceanmind),
      multipleManual: timeToMinutes(portalA.processingTimes.multipleCabin.manual),
      multipleOceanmind: timeToMinutes(portalA.processingTimes.multipleCabin.oceanmind)
    },
    portalB: {
      singleManual: timeToMinutes(portalB.processingTimes.singleCabin.manual),
      singleOceanmind: timeToMinutes(portalB.processingTimes.singleCabin.oceanmind),
      multipleManual: timeToMinutes(portalB.processingTimes.multipleCabin.manual),
      multipleOceanmind: timeToMinutes(portalB.processingTimes.multipleCabin.oceanmind)
    }
  };

  // Calculate max time to scale the chart
  const maxTime = Math.max(
    timeValues.portalA.singleManual,
    timeValues.portalA.multipleManual,
    timeValues.portalB.singleManual,
    timeValues.portalB.multipleManual
  );

  // Scale the heights to percentages based on the absolute max times
  const getHeightPercentage = (time: number) => {
    return Math.min((time / maxTime) * 100, 100); // Ensure it doesn't exceed 100%
  };

  // Calculate height in pixels for the absolute view based on available space
  const getBarHeightStyle = (time: number, viewHeight = 180) => {
    const maxPixels = viewHeight * 0.85; // Use 85% of the container height as maximum
    return {
      height: `${(time / maxTime) * maxPixels}px`,
      maxHeight: `${maxPixels}px`
    };
  };

  // Get percentage savings for visualization when in percentage mode
  const getSavingsPercentage = (manual: number, oceanmind: number) => {
    if (manual === 0) return 0;
    return (manual - oceanmind) / manual * 100;
  };

  // Determine if a portal/cabin combination should be displayed based on filters
  const shouldShowBar = (portal: 'portalA' | 'portalB', cabin: 'single' | 'multiple') => {
    const showPortal = portalFilter === 'all' || portalFilter === portal;
    const showCabin = cabinFilter === 'all' || (cabinFilter === 'single' && cabin === 'single') ||
      (cabinFilter === 'multiple' && cabin === 'multiple');
    return showPortal && showCabin;
  };

  // Handle drill down click
  const handleDrillDown = (portal: string, cabinType: string, manual: string, oceanmind: string, quotes: number) => {
    setDrillDownData({
      portal,
      cabinType,
      manual,
      oceanmind,
      quotes,
      savingsPercent: calculateTimeSavings(manual, oceanmind)
    });
    setShowDrillDown(true);
  };

  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setAnimate(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`${className || ''}`}>
      <div className="text-center mb-4 ">
        <h2 className="text-4xl font-semibold text-sky-900">Processing Time Comparison</h2>
        {/* View Mode Toggle - Now on the right side */}
      </div>

      <div className="flex items-center space-x-4 mb-3 justify-end">
        <div className="flex items-center bg-white/50 rounded-lg p-1">
          <button
            className={`px-3 py-1 text-xl font-medium rounded-md transition-all ${viewMode === 'absolute'
                ? 'text-white'
                : 'text-sky-900 hover:text-white hover:shadow-md'
              }`}
            style={{
              backgroundImage: viewMode === 'absolute' ? 'linear-gradient(135deg,rgb(154, 147, 192) 0%,rgb(76, 129, 228) 100%)' : 'none',
              backgroundColor: viewMode === 'absolute' ? '#B5A6FF' : 'transparent',
              transition: 'all 0.3s ease-in-out',
            }}
            onMouseOver={(e) => {
              if (viewMode !== 'absolute') {
                e.currentTarget.style.backgroundImage = 'linear-gradient(135deg,rgb(154, 147, 192) 0%,rgb(76, 129, 228) 100%)';
                e.currentTarget.style.opacity = '0.8';
              }
            }}
            onMouseOut={(e) => {
              if (viewMode !== 'absolute') {
                e.currentTarget.style.backgroundImage = 'none';
                e.currentTarget.style.opacity = '1';
              }
            }}
            onClick={() => setViewMode('absolute')}
          >
            Absolute Time
          </button>
          <button
            className={`px-3 py-1 text-xl font-medium rounded-md transition-all ${viewMode === 'percentage'
                ? 'text-white'
                : 'text-sky-900 hover:text-white hover:shadow-md'
              }`}
            style={{
              backgroundImage: viewMode === 'percentage' ? 'linear-gradient(135deg,rgb(154, 147, 192) 0%,rgb(76, 129, 228) 100%)' : 'none',
              backgroundColor: viewMode === 'percentage' ? '#B5A6FF' : 'transparent',
              transition: 'all 0.3s ease-in-out',
            }}
            onMouseOver={(e) => {
              if (viewMode !== 'percentage') {
                e.currentTarget.style.backgroundImage = 'linear-gradient(135deg,rgb(154, 147, 192) 0%,rgb(76, 129, 228) 100%)';
                e.currentTarget.style.opacity = '0.8';
              }
            }}
            onMouseOut={(e) => {
              if (viewMode !== 'percentage') {
                e.currentTarget.style.backgroundImage = 'none';
                e.currentTarget.style.opacity = '1';
              }
            }}
            onClick={() => setViewMode('percentage')}
          >
            Time Savings %
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap items-center justify-between mb-4 gap-2">
        <div className="flex flex-wrap items-center gap-2">
          <div className="text-lg text-sky-900 font-medium">Filters:</div>

          {/* Portal Filter */}
          <div className="flex items-center bg-white/50 rounded-lg p-1">
            <button
              className={`px-2 py-1 text-lg font-medium rounded-md transition-colors ${portalFilter === 'all' ? 'bg-sky-900 text-white' : 'text-sky-900'}`}
              onClick={() => setPortalFilter('all')}
            >
              All Portals
            </button>
            <button
              className={`px-2 py-1 text-lg font-medium rounded-md transition-colors ${portalFilter === 'portalA' ? 'bg-sky-900 text-white' : 'text-sky-900'}`}
              onClick={() => setPortalFilter('portalA')}
            >
              Portal A
            </button>
            <button
              className={`px-2 py-1 text-lg font-medium rounded-md transition-colors ${portalFilter === 'portalB' ? 'bg-sky-900 text-white' : 'text-sky-900'}`}
              onClick={() => setPortalFilter('portalB')}
            >
              Portal B
            </button>
          </div>

          {/* Cabin Type Filter */}
          <div className="flex items-center bg-white/50 rounded-lg p-1">
            <button
              className={`px-2 py-1 text-lg font-medium rounded-md transition-colors ${cabinFilter === 'all' ? 'bg-sky-900 text-white' : 'text-sky-900'}`}
              onClick={() => setCabinFilter('all')}
            >
              All Cabins
            </button>
            <button
              className={`px-2 py-1 text-lg font-medium rounded-md transition-colors ${cabinFilter === 'single' ? 'bg-sky-900 text-white' : 'text-sky-900'}`}
              onClick={() => setCabinFilter('single')}
            >
              Single
            </button>
            <button
              className={`px-2 py-1 text-lg font-medium rounded-md transition-colors ${cabinFilter === 'multiple' ? 'bg-sky-900 text-white' : 'text-sky-900'}`}
              onClick={() => setCabinFilter('multiple')}
            >
              Multiple
            </button>
          </div>
        </div>

        {/* Reset Filters - Now on the right side */}
        {(portalFilter !== 'all' || cabinFilter !== 'all') && (
          <button
            className="px-3 py-1 text-lg text-white rounded-md font-medium transition-all ml-auto"
            style={{
              backgroundImage: 'linear-gradient(135deg, #FF9671 0%, #FF6B5E 100%)',
              backgroundColor: '#FF6B5E',
              transition: 'all 0.3s ease-in-out',
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.opacity = '0.9';
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.opacity = '1';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
            }}
            onClick={() => {
              setPortalFilter('all');
              setCabinFilter('all');
            }}
          >
            Reset Filters
          </button>
        )}
      </div>

      {/* Show drill-down view if active */}
      {showDrillDown && drillDownData ? (
        <div className="mb-6 bg-transparent rounded-lg p-4 backdrop-blur-sm">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-bold text-sky-900">Detailed Analysis: {drillDownData.portal} - {drillDownData.cabinType} Cabin</h3>
            <button
              className="text-lg text-green-800 px-3 py-1 rounded-md transition-all"
              style={{
                backgroundImage: 'linear-gradient(45deg, #8fecb2 0%, #d4d1c8 100%)',
                backgroundColor: '#8fecb2',
                transition: 'all 0.3s ease-in-out',
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.opacity = '0.9';
                e.currentTarget.style.transform = 'translateY(-1px)';
                e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.opacity = '1';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
              onClick={() => setShowDrillDown(false)}
            >
              Back to Overview
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/60 rounded-lg p-3">
              <h4 className="text-xl font-medium text-sky-900 mb-1">Processing Times</h4>
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-orange-100 rounded-md p-2">
                  <div className="text-lg text-orange-800">Manual</div>
                  <div className="text-lg font-bold text-orange-900">{drillDownData.manual}</div>
                </div>
                <div className="bg-green-100 rounded-md p-2">
                  <div className="text-lg text-green-800">Oceanmind</div>
                  <div className="text-lg font-bold text-green-900">{drillDownData.oceanmind}</div>
                </div>
              </div>
            </div>

            <div className="bg-white/60 rounded-lg p-3">
              <h4 className="text-xl font-medium text-sky-900 mb-1">Time Savings</h4>
              <div className="flex flex-col items-center justify-center h-full">
                <div className="text-3xl font-bold text-green-600">{drillDownData.savingsPercent}%</div>
                <div className="text-lg text-sky-900">faster with Oceanmind</div>
              </div>
            </div>

            <div className="bg-white/60 rounded-lg p-3">
              <h4 className="text-xl font-medium text-sky-900 mb-1">Quote Volume</h4>
              <div className="flex items-end">
                <div className="text-3xl font-bold text-blue-700">{drillDownData.quotes}</div>
                <div className="text-lg text-sky-900 ml-2 mb-1">quotes processed</div>
              </div>
              <div className="w-full bg-blue-100 h-2 rounded-full mt-2">
                <div
                  className="bg-sky-900 h-full rounded-full"
                  style={{ width: `${Math.min(drillDownData.quotes / 5, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>




        </div>
      ) : (
        <div className="h-64 flex items-end space-x-6  mb-2 px-2">
          {/* Portal A - Single Cabin */}
          {shouldShowBar('portalA', 'single') && (
            <div className="flex-1 flex flex-col items-center">
              <div className="w-full h-52">
                <div className="h-full w-full bg-white/50 rounded-t-lg relative overflow-hidden cursor-pointer"
                  onMouseEnter={() => setHoveredBar('portalA-single')}
                  onMouseLeave={() => setHoveredBar(null)}
                  onClick={() => handleDrillDown(
                    portalA.name,
                    'Single',
                    portalA.processingTimes.singleCabin.manual,
                    portalA.processingTimes.singleCabin.oceanmind,
                    portalA.processingTimes.singleCabin.quotes
                  )}
                >
                  {viewMode === 'absolute' ? (
                    <div className="h-full w-full flex items-start justify-around p-1">
                      <div className="relative h-full flex flex-col items-center" style={{ width: '45%' }}>
                        <div
                          className="w-full bg-orange-400/70 rounded-sm transition-all duration-1000 ease-out mt-auto relative"
                          style={{
                            ...getBarHeightStyle(timeValues.portalA.singleManual),
                            transitionProperty: 'height'
                          }}
                        >
                          <div className="absolute -top-5 w-full text-center text-xs font-bold text-sky-800">
                            {portalA.processingTimes.singleCabin.manual}
                          </div>
                        </div>
                        <div className="mt-2 text-lg text-sky-800 font-medium">Manual</div>
                      </div>
                      <div className="relative h-full flex flex-col items-center" style={{ width: '45%' }}>
                        <div
                          className="w-full bg-green-500/70 rounded-sm transition-all duration-1000 ease-out mt-auto relative"
                          style={{
                            ...getBarHeightStyle(timeValues.portalA.singleOceanmind),
                            transitionProperty: 'height',
                            transitionDelay: '300ms'
                          }}
                        >
                          <div className="absolute -top-5 w-full text-center text-xs font-bold text-sky-900">
                            {portalA.processingTimes.singleCabin.oceanmind}
                          </div>
                        </div>
                        <div className="mt-2 text-lg text-sky-800 font-medium">Oceanmind</div>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="absolute top-0 inset-x-0 pt-3 flex flex-col items-center justify-start z-10">
                        <div className="text-3xl font-bold text-green-600">
                          {portalASingleSavings}%
                        </div>
                        <div className="text-lg text-sky-900">time savings</div>
                      </div>
                      <div className="absolute bottom-0 w-full bg-green-500/70 transition-all duration-1000 ease-out"
                        style={{
                          height: animate ? `${portalASingleSavings}%` : '0%',
                        }}>
                      </div>
                    </>
                  )}

                  {/* Enhanced tooltip with detailed information */}

                </div>
              </div>
              <p className="text-xs mt-2 text-center font-medium text-sky-900">{portalA.name}<br />Single</p>
            </div>
          )}

          {/* Portal A - Multiple Cabin */}
          {shouldShowBar('portalA', 'multiple') && (
            <div className="flex-1 flex flex-col items-center">
              <div className="w-full h-52">
                <div className="h-full w-full bg-white/50 rounded-t-lg relative overflow-hidden cursor-pointer"
                  onMouseEnter={() => setHoveredBar('portalA-multiple')}
                  onMouseLeave={() => setHoveredBar(null)}
                  onClick={() => handleDrillDown(
                    portalA.name,
                    'Multiple',
                    portalA.processingTimes.multipleCabin.manual,
                    portalA.processingTimes.multipleCabin.oceanmind,
                    portalA.processingTimes.multipleCabin.quotes
                  )}
                >
                  {viewMode === 'absolute' ? (
                    <div className="h-full w-full flex items-start justify-around p-1">
                      <div className="relative h-full flex flex-col items-center" style={{ width: '45%' }}>
                        <div
                          className="w-full bg-orange-400/70 rounded-sm transition-all duration-1000 ease-out mt-auto relative"
                          style={{
                            ...getBarHeightStyle(timeValues.portalA.multipleManual),
                            transitionProperty: 'height',
                            transitionDelay: '150ms'
                          }}
                        >
                          <div className="absolute -top-5 w-full text-center text-xs font-bold text-sky-800">
                            {portalA.processingTimes.multipleCabin.manual}
                          </div>
                        </div>
                        <div className="mt-2 text-xs text-sky-800 font-medium">Manual</div>
                      </div>
                      <div className="relative h-full flex flex-col items-center" style={{ width: '45%' }}>
                        <div
                          className="w-full bg-green-500/70 rounded-sm transition-all duration-1000 ease-out mt-auto relative"
                          style={{
                            ...getBarHeightStyle(timeValues.portalA.multipleOceanmind),
                            transitionProperty: 'height',
                            transitionDelay: '450ms'
                          }}
                        >
                          <div className="absolute -top-5 w-full text-center text-xs font-bold text-sky-900">
                            {portalA.processingTimes.multipleCabin.oceanmind}
                          </div>
                        </div>
                        <div className="mt-2 text-xs text-sky-800 font-medium">Oceanmind</div>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="absolute top-0 inset-x-0 pt-3 flex flex-col items-center justify-start z-10">
                        <div className="text-3xl font-bold text-green-600">
                          {portalAMultipleSavings}%
                        </div>
                        <div className="text-lg text-sky-900">time savings</div>
                      </div>
                      <div className="absolute bottom-0 w-full bg-green-500/70 transition-all duration-1000 ease-out"
                        style={{
                          height: animate ? `${portalAMultipleSavings}%` : '0%',
                        }}>
                      </div>
                    </>
                  )}

                  {/* Enhanced tooltip with detailed information */}

                </div>
              </div>
              <p className="text-xs mt-2 text-center font-medium text-sky-900">{portalA.name}<br />Multiple</p>
            </div>
          )}

          {/* Portal B - Single Cabin */}
          {shouldShowBar('portalB', 'single') && (
            <div className="flex-1 flex flex-col items-center">
              <div className="w-full h-52">
                <div className="h-full w-full bg-white/50 rounded-t-lg relative overflow-hidden cursor-pointer"
                  onMouseEnter={() => setHoveredBar('portalB-single')}
                  onMouseLeave={() => setHoveredBar(null)}
                  onClick={() => handleDrillDown(
                    portalB.name,
                    'Single',
                    portalB.processingTimes.singleCabin.manual,
                    portalB.processingTimes.singleCabin.oceanmind,
                    portalB.processingTimes.singleCabin.quotes
                  )}
                >
                  {viewMode === 'absolute' ? (
                    <div className="h-full w-full flex items-start justify-around p-1">
                      <div className="relative h-full flex flex-col items-center" style={{ width: '45%' }}>
                        <div
                          className="w-full bg-orange-400/70 rounded-sm transition-all duration-1000 ease-out mt-auto relative"
                          style={{
                            ...getBarHeightStyle(timeValues.portalB.singleManual),
                            transitionProperty: 'height',
                            transitionDelay: '200ms'
                          }}
                        >
                          <div className="absolute -top-5 w-full text-center text-xs font-bold text-sky-800">
                            {portalB.processingTimes.singleCabin.manual}
                          </div>
                        </div>
                        <div className="mt-2 text-xs text-sky-800 font-medium">Manual</div>
                      </div>
                      <div className="relative h-full flex flex-col items-center" style={{ width: '45%' }}>
                        <div
                          className="w-full bg-green-500/70 rounded-sm transition-all duration-1000 ease-out mt-auto relative"
                          style={{
                            ...getBarHeightStyle(timeValues.portalB.singleOceanmind),
                            transitionProperty: 'height',
                            transitionDelay: '500ms'
                          }}
                        >
                          <div className="absolute -top-5 w-full text-center text-xs font-bold text-sky-900">
                            {portalB.processingTimes.singleCabin.oceanmind}
                          </div>
                        </div>
                        <div className="mt-2 text-xs text-sky-800 font-medium">Oceanmind</div>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="absolute top-0 inset-x-0 pt-3 flex flex-col items-center justify-start z-10">
                        <div className="text-3xl font-bold text-green-600">
                          {portalBSingleSavings}%
                        </div>
                        <div className="text-lg text-sky-900">time savings</div>
                      </div>
                      <div className="absolute bottom-0 w-full bg-green-500/70 transition-all duration-1000 ease-out"
                        style={{
                          height: animate ? `${portalBSingleSavings}%` : '0%',
                        }}>
                      </div>
                    </>
                  )}

                  {/* Enhanced tooltip with detailed information */}

                </div>
              </div>
              <p className="text-xs mt-2 text-center font-medium text-sky-900">{portalB.name}<br />Single</p>
            </div>
          )}

          {/* Portal B - Multiple Cabin */}
          {shouldShowBar('portalB', 'multiple') && (
            <div className="flex-1 flex flex-col items-center">
              <div className="w-full h-52">
                <div className="h-full w-full bg-white/50 rounded-t-lg relative overflow-hidden cursor-pointer"
                  onMouseEnter={() => setHoveredBar('portalB-multiple')}
                  onMouseLeave={() => setHoveredBar(null)}
                  onClick={() => handleDrillDown(
                    portalB.name,
                    'Multiple',
                    portalB.processingTimes.multipleCabin.manual,
                    portalB.processingTimes.multipleCabin.oceanmind,
                    portalB.processingTimes.multipleCabin.quotes
                  )}
                >
                  {viewMode === 'absolute' ? (
                    <div className="h-full w-full flex items-start justify-around p-1">
                      <div className="relative h-full flex flex-col items-center" style={{ width: '45%' }}>
                        <div
                          className="w-full bg-orange-400/70 rounded-sm transition-all duration-1000 ease-out mt-auto relative"
                          style={{
                            ...getBarHeightStyle(timeValues.portalB.multipleManual),
                            transitionProperty: 'height',
                            transitionDelay: '250ms'
                          }}
                        >
                          <div className="absolute -top-5 w-full text-center text-xs font-bold text-sky-800">
                            {portalB.processingTimes.multipleCabin.manual}
                          </div>
                        </div>
                        <div className="mt-2 text-xs text-sky-800 font-medium">Manual</div>
                      </div>
                      <div className="relative h-full flex flex-col items-center" style={{ width: '45%' }}>
                        <div
                          className="w-full bg-green-500/70 rounded-sm transition-all duration-1000 ease-out mt-auto relative"
                          style={{
                            ...getBarHeightStyle(timeValues.portalB.multipleOceanmind),
                            transitionProperty: 'height',
                            transitionDelay: '550ms'
                          }}
                        >
                          <div className="absolute -top-5 w-full text-center text-xs font-bold text-sky-900">
                            {portalB.processingTimes.multipleCabin.oceanmind}
                          </div>
                        </div>
                        <div className="mt-2 text-xs text-sky-800 font-medium">Oceanmind</div>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="absolute top-0 inset-x-0 pt-3 flex flex-col items-center justify-start z-10">
                        <div className="text-3xl font-bold text-green-600">
                          {portalBMultipleSavings}%
                        </div>
                        <div className="text-lg text-sky-900">time savings</div>
                      </div>
                      <div className="absolute bottom-0 w-full bg-green-500/70 transition-all duration-1000 ease-out"
                        style={{
                          height: animate ? `${portalBMultipleSavings}%` : '0%',
                        }}>
                      </div>
                    </>
                  )}

                  {/* Enhanced tooltip with detailed information */}

                </div>
              </div>
              <p className="text-xs mt-2 text-center font-medium text-sky-900">{portalB.name}<br />Multiple</p>
            </div>
          )}
        </div>
      )}

      {/* Legend */}
      <div className="flex justify-center items-center mt-2 space-x-6">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-orange-400/70 rounded-sm mr-2"></div>
          <span className="text-xl text-sky-900">Manual</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-green-500/70 rounded-sm mr-2"></div>
          <span className="text-xl text-sky-900">Oceanmind</span>
        </div>
      </div>

      {/* Overall time savings summary */}
      <div className="mt-4 pt-3 border-t border-sky-100">
        <div className="text-xl font-semibold text-sky-900">Overall Time Savings</div>
        <div className="flex items-center mt-2">
          <div className="flex-1 h-2 bg-white/50 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full"
              style={{
                width: animate ? `${(
                  calculateTimeSavings(portalA.processingTimes.singleCabin.manual, portalA.processingTimes.singleCabin.oceanmind) +
                  calculateTimeSavings(portalA.processingTimes.multipleCabin.manual, portalA.processingTimes.multipleCabin.oceanmind) +
                  calculateTimeSavings(portalB.processingTimes.singleCabin.manual, portalB.processingTimes.singleCabin.oceanmind) +
                  calculateTimeSavings(portalB.processingTimes.multipleCabin.manual, portalB.processingTimes.multipleCabin.oceanmind)
                ) / 4}%` : '0%',
                transition: 'width 1s ease-out',
                transitionDelay: '600ms'
              }}
            ></div>
          </div>
          <span className="ml-2 text-lg font-semibold text-sky-900">
            {Math.round((
              calculateTimeSavings(portalA.processingTimes.singleCabin.manual, portalA.processingTimes.singleCabin.oceanmind) +
              calculateTimeSavings(portalA.processingTimes.multipleCabin.manual, portalA.processingTimes.multipleCabin.oceanmind) +
              calculateTimeSavings(portalB.processingTimes.singleCabin.manual, portalB.processingTimes.singleCabin.oceanmind) +
              calculateTimeSavings(portalB.processingTimes.multipleCabin.manual, portalB.processingTimes.multipleCabin.oceanmind)
            ) / 4)}%
          </span>
        </div>
      </div>
    </div>
  );
};

export default ProcessingTimeChart;