import logging
import asyncio
from typing import Dict, Any, Optional
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError
from OneSource.screenshot_utils import OneSourceScreenshotManager

logger = logging.getLogger(__name__)

class OneSourcePriceDetails:
    """Handle passenger-level price detail options (Air, Vacation Protection, Transfer) and age selection."""

    def __init__(self, page: Page):
        self.page = page
        self.screenshot_manager = None
        self.request_id = None
        self.cabin_id = None
        self.session_id = None

    def set_screenshot_config(self, request_id: str, cabin_id: int = None, session_id: str = None):
        """Set screenshot configuration"""
        self.request_id = request_id
        self.cabin_id = cabin_id
        self.session_id = session_id
        self.screenshot_manager = OneSourceScreenshotManager(
            self.page, request_id, cabin_id, session_id
        )

    async def apply_price_preferences(self, passenger_count: int = 2, has_senior: bool = False, has_child: bool = False) -> bool:
        """
        Set Air=N, Vacation Protection=None, Transfer=N for all passenger rows.
        Handle age selection for seniors and children when passenger_count > 2.
        
        Args:
            passenger_count: Total number of passengers
            has_senior: Whether there are seniors in the group
            has_child: Whether there are children in the group
        """
        try:
            # Wait a moment for the price details page to load
            await self.page.wait_for_load_state('domcontentloaded', timeout=10000)
            await asyncio.sleep(1)

            # First handle age selection for passengers 3+ if needed
            if passenger_count > 2 and (has_senior or has_child):
                await self._handle_age_selection(passenger_count, has_senior, has_child)

            # Then iterate over all selects and set preferences (Air, Vacation Protection, Transfer)
            all_selects = await self.page.query_selector_all("select")
            for sel in all_selects:
                try:
                    # Skip age selection dropdowns (they contain numerical values)
                    name_attr = await sel.get_attribute("name")
                    if name_attr and "DFH" in name_attr:
                        # Check if this is an age dropdown by looking at options
                        options = await sel.query_selector_all("option")
                        option_values = [await opt.get_attribute("value") for opt in options]
                        # If it has numerical age values, skip it (already handled in age selection)
                        if any(val and val.strip().isdigit() for val in option_values if val):
                            continue

                    # Get all option values and labels for preference selects
                    options = await sel.query_selector_all("option")
                    values = [await opt.get_attribute("value") for opt in options]
                    labels = [await opt.inner_text() for opt in options]

                    # Air/Transfer: if has 'Y'/'N' or 'Yes'/'No', set to 'N' or 'No'
                    if ('Y' in values and 'N' in values) or any('Yes' in lbl for lbl in labels) and any('No' in lbl for lbl in labels):
                        try:
                            await sel.select_option(value="N")
                        except:
                            await sel.select_option(label="No")
                        continue

                    # Vacation Protection: if has 'N'/'F' or 'None'/'Standard', set to 'N' or 'None'
                    if ('N' in values and 'F' in values) or any('None' in lbl for lbl in labels) and any('Standard' or 'Platinum' in lbl for lbl in labels):
                        try:
                            await sel.select_option(value="N")
                        except:
                            await sel.select_option(label="None")
                        continue

                except Exception as e:
                    logger.debug(f"Error setting select: {e}")

            logger.info("Price detail preferences applied (Air=No, Protection=None, Transfer=No)")
            # Pause to allow visual verification
            await asyncio.sleep(3)

            # Click Submit Changes button
            submit_selectors = [
                'a.capsule_btn.gray666:has-text("Submit Changes")',
                '#CMRDLEP > table > tbody > tr:nth-child(2) > td > table > tbody > tr:nth-child(7) > td > table > tbody > tr > td > table > tbody > tr:nth-child(8) > td > table > tbody > tr > td:nth-child(1) > a',
                'xpath=//*[@id="CMRDLEP"]/table/tbody/tr[2]/td/table/tbody/tr[4]/td/table/tbody/tr/td/table/tbody/tr[6]/td/table/tbody/tr/td[1]/a',
                'a[href="javascript:doSimpleSubmit(document.CMRDLEP, \'DFH_ENTER\')"]'
            ]

            for selector in submit_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        await element.click()
                        logger.info("Successfully clicked Submit Changes button")
                        await self.page.wait_for_load_state('domcontentloaded', timeout=10000)
                        await asyncio.sleep(2)
                        
                        # Take screenshot after submit changes button click
                        if self.screenshot_manager:
                            await self.screenshot_manager.take_price_details_screenshot()
                        
                        return True
                except Exception as e:
                    logger.debug(f"Failed to click with selector {selector}: {e}")
                    continue

            logger.error("Failed to find and click Submit Changes button")
            return False

        except PlaywrightTimeoutError as te:
            logger.error(f"Timeout on price details page: {te}")
            return False
        except Exception as e:
            logger.error(f"Error applying price preferences: {e}")
            return False 

    async def _handle_age_selection(self, passenger_count: int, has_senior: bool, has_child: bool):
        """
        Handle age selection for passengers 3 and onwards.
        Set seniors to age 60 and children to age 10.
        
        Args:
            passenger_count: Total number of passengers
            has_senior: Whether there are seniors in the group
            has_child: Whether there are children in the group
        """
        try:
            logger.info(f"Handling age selection for passengers 3+ (total: {passenger_count}, seniors: {has_senior}, children: {has_child})")

            # Find all age selection dropdowns
            age_selectors = await self.page.query_selector_all("select[name*='DFH']")
            
            # We need to handle passengers 3 onwards (passengers 1 and 2 are adults by default)
            passengers_to_set = passenger_count - 2
            age_dropdowns_set = 0

            for sel in age_selectors:
                if age_dropdowns_set >= passengers_to_set:
                    break

                try:
                    # Check if this is an age dropdown by examining options
                    options = await sel.query_selector_all("option")
                    option_values = [await opt.get_attribute("value") for opt in options]
                    
                    # Verify this is an age dropdown (has numerical values)
                    has_age_options = any(val and val.strip().isdigit() for val in option_values if val)
                    if not has_age_options:
                        continue

                    # Determine what age to set based on priority:
                    # If both seniors and children are present, alternate or use business logic
                    # For simplicity, set seniors first, then children
                    age_to_set = None
                    
                    if has_senior and has_child:
                        # Alternate: odd passengers get senior age, even get child age
                        if age_dropdowns_set % 2 == 0:
                            age_to_set = "60"  # Senior
                        else:
                            age_to_set = "10"  # Child
                    elif has_senior:
                        age_to_set = "60"  # Senior
                    elif has_child:
                        age_to_set = "10"  # Child

                    if age_to_set:
                        # Try to select the age
                        try:
                            await sel.select_option(value=age_to_set)
                            logger.info(f"Set passenger {age_dropdowns_set + 3} age to {age_to_set}")
                            age_dropdowns_set += 1
                        except Exception as e:
                            logger.debug(f"Failed to set age {age_to_set}: {e}")

                except Exception as e:
                    logger.debug(f"Error processing age dropdown: {e}")
                    continue

            logger.info(f"Completed age selection for {age_dropdowns_set} passengers")

        except Exception as e:
            logger.error(f"Error in age selection: {e}") 