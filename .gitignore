# Python virtual environments
.venv/
venv/
monitoring
# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Data and logs
logs/

# Database files
oceanmind.db

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

services/__pycache__/
video/

# Python virtual environments
.venv/
venv/

# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Data and logs
logs/

# Database files
oceanmind.db

userinput.py

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

services/__pycache__/
video
browser_monitor.sh
capacity_analysis.sh
nohup.out
database.md

.ipynb_checkpoints/
**/.ipynb_checkpoints/
*/.ipynb_checkpoints/*
