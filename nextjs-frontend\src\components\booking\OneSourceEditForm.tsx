import React, { useState, useEffect } from 'react';

interface OneSourceCabin {
  cabin_number: number;
  cabin_type: string;
  passengers: number;
}

interface OneSourceDetails {
  cruise_line?: string;
  ship_name?: string;
  date?: string;
  nights?: number;
  airport_code?: string;
  cabin_count?: number;
  past_passenger_number?: string;
  cabin_types?: OneSourceCabin[];
  request_id?: string;
}

interface OneSourceEditFormProps {
  details: OneSourceDetails;
  requestId: string;
  onSave: (updatedDetails: OneSourceDetails) => void;
  onCancel: () => void;
}

const OneSourceEditForm: React.FC<OneSourceEditFormProps> = ({
  details,
  requestId,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState<OneSourceDetails>({
    cruise_line: '',
    ship_name: '',
    date: '',
    nights: 0,
    airport_code: '',
    cabin_count: 1,
    past_passenger_number: '',
    cabin_types: [{ cabin_number: 1, cabin_type: 'INSIDE', passengers: 2 }],
    request_id: ''
  });

  useEffect(() => {
    if (details) {
      const initialFormData = { ...details };
      initialFormData.request_id = requestId;

      if (!initialFormData.cabin_types || initialFormData.cabin_types.length === 0) {
        initialFormData.cabin_types = Array.from({ length: initialFormData.cabin_count || 1 }, (_, index) => ({
          cabin_number: index + 1,
          cabin_type: 'INSIDE',
          passengers: 2
        }));
      }

      // Normalize cabin types
      initialFormData.cabin_types = initialFormData.cabin_types.map((cabin) => ({
        ...cabin,
        cabin_type: (cabin.cabin_type || 'INSIDE').replace(/\s+/g, '').toUpperCase()
      }));

      setFormData(initialFormData);
    }
  }, [details, requestId]);

  const handleChange = (field: keyof OneSourceDetails, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCabinCountChange = (newCount: number) => {
    const currentCabins = formData.cabin_types || [];
    let newCabins = [...currentCabins];

    if (newCount > currentCabins.length) {
      for (let i = currentCabins.length; i < newCount; i++) {
        newCabins.push({
          cabin_number: i + 1,
          cabin_type: 'INSIDE',
          passengers: 2
        });
      }
    } else {
      newCabins = newCabins.slice(0, newCount);
    }

    setFormData(prev => ({
      ...prev,
      cabin_count: newCount,
      cabin_types: newCabins
    }));
  };

  const handleCabinChange = (index: number, field: keyof OneSourceCabin, value: any) => {
    const newCabins = [...(formData.cabin_types || [])];
    newCabins[index] = { ...newCabins[index], [field]: value };
    setFormData(prev => ({ ...prev, cabin_types: newCabins }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Edit OneSource Details</h2>
        <div className="space-x-2">
          <button onClick={handleSubmit} className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Save</button>
          <button onClick={onCancel} className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">Cancel</button>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Cruise Line</label>
            <input
              type="text"
              value={formData.cruise_line || ''}
              onChange={(e) => handleChange('cruise_line', e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Ship Name</label>
            <input
              type="text"
              value={formData.ship_name || ''}
              onChange={(e) => handleChange('ship_name', e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sailing Date</label>
            <input
              type="text"
              value={formData.date || ''}
              onChange={(e) => handleChange('date', e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Duration (nights)</label>
            <input
              type="number"
              value={formData.nights || 0}
              onChange={(e) => handleChange('nights', parseInt(e.target.value))}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Airport Code</label>
            <input
              type="text"
              value={formData.airport_code || ''}
              onChange={(e) => handleChange('airport_code', e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Number of Cabins</label>
            <input
              type="number"
              value={formData.cabin_count || 1}
              onChange={(e) => handleCabinCountChange(parseInt(e.target.value))}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Past Passenger Number (optional)</label>
            <input
              type="text"
              value={formData.past_passenger_number || ''}
              onChange={(e) => handleChange('past_passenger_number', e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
        </div>

        <h3 className="text-lg font-semibold mb-4 text-gray-700">Cabin Details</h3>
        {formData.cabin_types?.map((cabin, index) => (
          <div key={index} className="mb-4 p-4 border rounded bg-gray-50">
            <h4 className="font-medium mb-2">Cabin {index + 1}</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cabin Number</label>
                <input
                  type="number"
                  value={cabin.cabin_number}
                  onChange={(e) => handleCabinChange(index, 'cabin_number', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border rounded"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cabin Type</label>
                <select
                  value={cabin.cabin_type}
                  onChange={(e) => handleCabinChange(index, 'cabin_type', e.target.value)}
                  className="w-full px-3 py-2 border rounded"
                >
                  <option value="INSIDE">INSIDE</option>
                  <option value="OUTSIDE">OUTSIDE</option>
                  <option value="BALCONY">BALCONY</option>
                  <option value="SUITE">SUITE</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Passengers</label>
                <input
                  type="number"
                  value={cabin.passengers}
                  onChange={(e) => handleCabinChange(index, 'passengers', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border rounded"
                />
              </div>
            </div>
          </div>
        ))}
      </form>
    </div>
  );
};

export default OneSourceEditForm; 