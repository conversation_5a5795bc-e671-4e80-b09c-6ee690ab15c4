"""
Base Scraper module for browser automation.

This module provides the foundation for all web scraping operations in the 
cruise booking system. It manages browser initialization, logging, page navigation,
and various utility functions for reliable web automation.
"""

import os
import asyncio
import sys
from datetime import datetime

from loguru import logger
from playwright.async_api import TimeoutError

from Core.browser_setup import AsyncBrowserSetup
from db.screenshot_manager import save_screenshot_to_db
from Core.ui_logger import ui_log


class BaseScraper:
    """
    Base class for all scraper operations providing core browser automation functionality.
    
    This class handles browser initialization, logging setup, page navigation,
    screenshot capture, and provides utility methods for reliable web interaction.
    It serves as the foundation for specialized scrapers in the cruise booking system.
    
    Attributes:
        browser_type (str): Type of browser being used (e.g., 'chromium')
        session_id (str): Unique identifier for the current session
        request_id (str): Unique identifier for the current request
        browser: Playwright browser instance
        context: Playwright browser context
        page: Playwright page instance for interaction
    """

    def __init__(self, request_id, browser_type, session_id, headless=False, provider='Studio.Sales.CabinCloseOut'):
        """
        Initialize the BaseScraper with configuration and browser setup.
        
        Args:
            request_id (str): Unique identifier for the current request
            browser_type (str): Type of browser to use (e.g., 'chromium')
            session_id (str): Unique identifier for this browser session
            headless (bool, optional): Whether to run browser in headless mode. Defaults to False.
            provider (str, optional): The provider for determining URL. Defaults to 'Studio.Sales.CabinCloseOut'.
        """
        self.browser_type = browser_type
        self.session_id = session_id
        self.request_id = request_id
        self.provider = provider
        self.setup_logging()
        self.setup_directories()
        # Setup driver is now async, so it will be called separately

    def setup_logging(self):
        """
        Configure logging for this scraper instance.
        
        Sets up console logging with appropriate formatters. Uses a unique logger name 
        based on browser type and session ID to prevent log message duplication.
        """
        # Configure unique logger name for this session
        logger_id = logger.add(
            sink=sys.stderr,
            format="{time:YYYY-MM-DD HH:mm:ss} - {name} - {level} - {message}",
            level="INFO",
            filter=lambda record: record["extra"].get("session_id") == self.session_id
        )
        
        # Store the logger ID for potential removal later
        self.logger_id = logger_id
        
        # Configure logger context for this session
        logger.configure(extra={"session_id": self.session_id})

    def setup_directories(self):
        """
        Configure directories for storing data like screenshots.
        
        Currently sets screenshots_dir to None as screenshot storage is handled
        by the screenshot_handler module.
        """
        self.screenshots_dir = None

    async def setup_driver(self, headless=False, video_auditing=False, cabin_id=None):
        """
        Set up the browser driver with optimized settings for web scraping.
        
        Uses the shared browser instance for Studio module and creates a new context
        for this specific scraping session.
        
        Args:
            headless (bool): Whether to run browser in headless mode (ignored for shared browser)
            video_auditing (bool): Whether to enable video recording for audit purposes
            cabin_id (str/int): Optional cabin ID for organizing videos
            
        Raises:
            Exception: If browser setup fails
        """
        try:
            ui_log("Getting ready to roll - initializing the resource allocation", session_id=self.session_id, step="browser_setup", module="Studio")
            logger.info("Using shared Studio browser")
            
            from Core.browser_setup import browser_manager, AsyncBrowserSetup
            
            # Get the shared browser instance for Studio
            self.browser = await browser_manager.get_or_launch_browser('Studio')
            logger.info("Retrieved shared Studio browser")
            
            # Create browser setup instance for context creation
            browser_setup = AsyncBrowserSetup()
            
            # Create an optimized browser context using the shared browser
            self.context = await browser_setup.create_optimized_context(
                self.browser, 
                logger,
                video_auditing=video_auditing,
                cabin_id=cabin_id,
                session_id=self.session_id
            )
            
            # Create a new page and configure it
            #ui_log("Opening new browser page", session_id=self.session_id, step="page_setup", module="Studio")
            self.page = await self.context.new_page()
            # Set default timeout for page operations to 50 seconds
            self.page.set_default_timeout(50000)  
            self.playwright = browser_manager.get_playwright('Studio')
            self.browser_setup = browser_setup  # Store reference for video recording
            
            # Configure resource blocking to improve performance
            # For Studio: when video auditing is enabled, disable resource blocking for better video quality
            block_resources = not video_auditing
            #ui_log("Optimizing browser performance", session_id=self.session_id, step="performance_setup", module="Studio")
            await browser_setup.setup_resource_blocking(self.page, logger, block=block_resources)
            
            if video_auditing:
                #ui_log("Video auditing enabled - images loaded, resource blocking disabled", session_id=self.session_id, step="video_audit_setup", module="Studio")
                logger.info("Video auditing enabled for Studio - optimized for video quality")
            
            ui_log("Resource allocation complete - taking input", session_id=self.session_id, step="browser_ready", module="Studio")
            logger.info("Browser context setup complete")

        except Exception as e:
            error_msg = f"Browser setup failed: {str(e)}"
            ui_log("Browser setup encountered an issue", session_id=self.session_id, step="browser_error", module="Studio")
            logger.error(error_msg)
            raise

    async def cleanup(self):
        """
        Clean up browser resources when done.
        
        Closes the browser context and page, but keeps the shared browser alive.
        Uses hasattr checks to handle cases where initialization may have failed.
        """
        # Stop video recording if it was enabled
        if hasattr(self, 'browser_setup') and hasattr(self, 'page'):
            try:
                # Get cabin_id from the instance or use request_id as fallback
                cabin_id = getattr(self, 'cabin_id', None)
                if not cabin_id and hasattr(self, 'page') and hasattr(self.page, 'cabin_id'):
                    cabin_id = self.page.cabin_id
                
                # Use the session_id that was passed to the scraper for video saving
                # This ensures all videos are saved with the same session ID as used in the bookings table
                session_id = self.session_id
                
                # Log the session ID being used for video recording
                logger.info(f"Saving video recording with session ID: {session_id}, cabin ID: {cabin_id}")
                
                await self.browser_setup.stop_video_recording(
                    self.page, 
                    logger, 
                    request_id=self.request_id, 
                    provider="Studio",
                    session_id=session_id,
                    cabin_id=cabin_id
                )
            except Exception as e:
                logger.error(f"Error stopping video recording: {str(e)}")
        
        # Close browser context and page (but keep shared browser alive)
        if hasattr(self, 'context'):
            await self.context.close()
            logger.info("Browser context closed")
        
        # Note: Don't close browser or stop playwright as they are shared resources

    async def load_url(self, url):
        """
        Navigate to a URL and wait for it to load completely.
        
        Handles page navigation with appropriate waiting strategies and error handling.
        Also checks for cruise-specific content to verify correct page loading.
        
        Args:
            url (str): The URL to navigate to
            
        Returns:
            bool: True if page loaded successfully, False otherwise
        """
        try:
            short_url = url[:60] + "..." if len(url) > 60 else url
            logger.info(f"Loading URL: {url}")
            
            # Navigate to URL and wait for network activity to settle
            await self.page.goto(url, wait_until="networkidle", timeout=60000)
            
            # Additional wait time to ensure dynamic content loads
            ui_log("Fetching page content, hang tight", session_id=self.session_id, step="page_loading", module="Studio")
            await asyncio.sleep(25)

            # Use wait_for_page_load method to ensure page is fully loaded
            if not await self.wait_for_page_load(timeout=40):
                error_msg = "Website is taking longer than expected to load"
                ui_log("The website is loading a bit slower than usual - Thanks for your patience", session_id=self.session_id, step="page_load_error", module="Studio")
                logger.error(error_msg)
                return False
            
            await asyncio.sleep(5)
            # Check for cruise-specific content to verify proper page loading
            content_selector = ".item-summary-wrapper, .item-summary-content, .item-summary-ports-container"
            try:
                await self.page.wait_for_selector(content_selector, state="visible", timeout=30000)
                ui_log("All set! The page has loaded successfully", session_id=self.session_id, step="page_loaded", module="Studio")
                logger.info("Page loaded successfully with cruise content")
            except TimeoutError:
                # Page loaded but cruise content may not be present (could be error page)
                warning_msg = "Page is up, but the content we're looking for didn't load"
                ui_log(warning_msg, session_id=self.session_id, step="content_warning", module="Studio")
                logger.warning("Cruise content not found - page may be showing an error")
            
            return True
            
        except Exception as e:
            error_msg = f"Page load failed: {str(e)}"
            ui_log("Page failed to load - trying to recover - please stand by", session_id=self.session_id, step="load_error", module="Studio")
            logger.error(error_msg)
            return False

    async def wait_for_page_load(self, timeout=30):
        """
        Wait for a page to be fully loaded and ready for interaction.
        
        Uses multiple wait strategies to ensure the page is completely loaded:
        1. DOM content loaded
        2. Network activity settled
        3. Body element visible
        4. Loading indicators hidden
        
        Args:
            timeout (int, optional): Maximum time to wait in seconds. Defaults to 30.
            
        Returns:
            bool: True if page loaded within timeout, False otherwise
        """
        try:

            ui_log("Waiting for the page to load - almost there", session_id=self.session_id, step="Loading", module="Studio")

            logger.info("Waiting for page to load...")
            
            # Wait for initial DOM content to load
            await self.page.wait_for_load_state("domcontentloaded", timeout=timeout * 1000)
            #ui_log("DOM content loaded", session_id=self.session_id, step="dom_loaded", module="Studio")
            
            # Wait for network activity to settle
            await self.page.wait_for_load_state("networkidle", timeout=timeout * 1000)
            #ui_log("Network activity settled", session_id=self.session_id, step="network_idle", module="Studio")
            
            # Wait for body to be visible (page structure available)
            await self.page.wait_for_selector("body", state="visible", timeout=timeout * 1000)
            
            # Wait for loading indicators to disappear (double timeout for this step)
            loading_selector = ".loader-box-overlay, .loader-box, [data-loading-splash-title], [data-loading-splash-message]"
            await self.page.wait_for_selector(loading_selector, state="hidden", timeout=timeout * 1000 * 2)
            # Scroll down and back up to trigger any lazy-loaded content
            await self.page.evaluate("""
                window.scrollTo(0, document.body.scrollHeight/3);
                window.scrollTo(0, 0);
            """)
            
            return True
            
        except Exception as e:
            error_msg = f"Page load failed: {str(e)}"
            ui_log("Page failed to load, but recovery is in progress — please stand by", session_id=self.session_id, step="wait_error", module="Studio")
            logger.error(error_msg)
            return False

    def construct_base_url(self, sailing_date, duration, ship_id):
        """
        Construct a URL for cruise search with the given parameters.
        
        Creates a properly formatted URL for the cabin closeout store search
        with parameters for sailing date, duration, and ship ID. The base URL
        is determined by the provider type.
        
        Args:
            sailing_date (str): Date of sailing in format MM-DD-YYYY
            duration (str): Cruise duration in nights
            ship_id (str): Ship identifier
            
        Returns:
            str: Fully constructed URL for searching cruises
        """
        # Provider-specific URL mapping
        provider_urls = {
            "Studio.Sales.CabinCloseOut": "https://sales.cabincloseoutstore.com/app/0/cruise/0/search_cruises_quick.html?",
            "Studio.Res.CabinCloseOut": "https://res.cabincloseoutstore.com/app/0/cruise/0/search_cruises_quick.html?"
        }
        
        # Get the base URL for the current provider (fallback to Sales for backward compatibility)
        base_url = provider_urls.get(self.provider, provider_urls["Studio.Sales.CabinCloseOut"])
        
        return (
            base_url +
            "clear=all&search[search_type]=cruise_only"
            f"&search[sailing_date]={sailing_date}"
            f"&search[duration]={duration}"
            f"&search[ship_id]={ship_id}")

    async def take_screenshot(self, element, name, cabin_id):
        """
        Capture and save a screenshot of a specific page element.
        
        Takes a screenshot of the specified element, determines appropriate
        screenshot type and cabin ID based on filename, and saves it using
        the screenshot_handler module.
        
        Args:
            element: Playwright element to capture
            name (str): Base name for the screenshot file
            
        Returns:
            str: Filename of saved screenshot, or None if capture failed
        """
        try:
            # Capture the element as PNG data
            png_data = await element.screenshot()

            # Determine screenshot type based on filename
            screenshot_type = "general"
            if "category_selection" in name.lower():
                screenshot_type = "Category Selection"
            elif "rate_selection" in name.lower():
                screenshot_type = "Rate Selection"
            elif "cabin_selection" in name.lower():
                screenshot_type = "Cabin Selection"
            elif "pricing_details" in name.lower():
                screenshot_type = "Pricing Details"

            # Extract cabin ID from filename if present
            # cabin_id = None
            # if "cabin_" in name.lower():
            #     try:
            #         cabin_parts = name.lower().split("cabin_")
            #         if len(cabin_parts) > 1:
            #             cabin_id_str = cabin_parts[1].split("_")[0]
            #             if cabin_id_str.isdigit():
            #                 cabin_id = int(cabin_id_str)
            #     except:
            #         pass

            file_name = f"{cabin_id}_cabin_{name}.png"

            # Save screenshot using external handler
            try:
                saved = await save_screenshot_to_db(png_data, self.request_id, "Studio",
                                        screenshot_type, file_name, cabin_id)
                if saved:
                    logger.info(f"Screenshot saved: {file_name}")
                else:
                    logger.error(
                        f"Failed to save screenshot: {file_name}")
            except Exception as e:
                logger.error(f"Screenshot save error: {e}")

            return file_name
        except Exception as e:
            logger.error(f"Screenshot capture failed: {e}")
            return None

    async def optimize_screenshot(self, element, filename, cabin_id, quality=50):
        """
        Take an optimized screenshot with error handling and fallback.
        
        Wrapper around take_screenshot that handles errors and attempts
        to take a fallback screenshot if the primary attempt fails.
        
        Args:
            element: Playwright element to capture
            filename (str): Name for the screenshot file
            quality (int, optional): Image quality (0-100). Defaults to 50.
            
        Returns:
            str: Base filename of saved screenshot, or None if all attempts failed
        """
        try:
            # Extract base filename without extension
            base_filename = os.path.splitext(filename)[0]

            # Take the screenshot using the base method
            await self.take_screenshot(element, base_filename, cabin_id)
            logger.info(f"Screenshot captured: {base_filename}")
            return base_filename
        except Exception as e:
            logger.error(f"Screenshot failed: {e}")
            # Try to take a fallback screenshot if primary attempt fails
            try:
                return await self.take_screenshot(element,
                                            f"fallback_{base_filename}", cabin_id)
            except:
                return None
            
    @staticmethod
    async def retry_with_delay(func, max_retries=3, delay=5, increasing_delay=True):
        """
        Execute a function with retry logic and exponential backoff.
        
        Attempts to execute the provided function multiple times with delay
        between attempts. Can use increasing delay (exponential backoff) 
        for more robust retry strategy.
        
        Args:
            func (callable): Function to execute
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.
            delay (int, optional): Base delay between retries in seconds. Defaults to 5.
            increasing_delay (bool, optional): Whether to increase delay with each retry. Defaults to True.
            
        Returns:
            Any: Result from the function if successful, None otherwise
            
        Raises:
            Exception: Re-raises any exception from the final retry attempt
        """
        # Create a dedicated logger for retry operations
        retry_logger = logger.bind(name='retry')

        # Try the function up to max_retries times
        for attempt in range(max_retries):
            try:
                # Execute the function
                result = await func()
                # If function returns a truthy result, return it
                if result:
                    return result

                # Calculate delay for next attempt (linear or exponential)
                current_delay = delay * (attempt + 1) if increasing_delay else delay
                retry_logger.info(
                    f"Retry {attempt + 1}/{max_retries} in {current_delay}s")
                # Wait before next attempt
                await asyncio.sleep(current_delay)
            except Exception as e:
                retry_logger.warning(
                    f"Attempt {attempt + 1}/{max_retries} failed: {e}")
                # If this is the last attempt, re-raise the exception
                if attempt == max_retries - 1:
                    raise
                # Calculate delay for next attempt
                current_delay = delay * (attempt + 1) if increasing_delay else delay
                await asyncio.sleep(current_delay)
        return None