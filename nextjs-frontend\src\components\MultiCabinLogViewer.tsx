import React from 'react';
import { useLogContext } from '../contexts/LogContext';
import LogViewer from './LogViewer';

interface MultiCabinLogViewerProps {
  maxHeight?: string;
  module?: string;
}

const MultiCabinLogViewer: React.FC<MultiCabinLogViewerProps> = ({ maxHeight = '500px', module }) => {
  const { activeCabins } = useLogContext();

  // If no active cabins, show the default "All Logs" view
  if (activeCabins.length === 0) {
    return (
      <div className="space-y-4">
        <div>
          <LogViewer
            title="All Logs" 
            maxHeight={maxHeight}
            className="w-full"
            module={module}
          />
        </div>
      </div>
    );
  }

  // If there are active cabins, show separate cabin log windows
  return (
    <div className="space-y-4">
      {activeCabins.map((cabinId) => (
        <div key={cabinId}>
          <LogViewer
            title={`Cabin ${cabinId}`}
            cabinId={cabinId}
            maxHeight={maxHeight}
            className="w-full"
            module={module}
          />
        </div>
      ))}
    </div>
  );
};

export default MultiCabinLogViewer; 