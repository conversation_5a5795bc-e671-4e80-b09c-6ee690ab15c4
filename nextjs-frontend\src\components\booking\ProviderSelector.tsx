'use client';
import React, { useMemo, useEffect, useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { fetchProviders } from '../../services/api';

// Define the valid provider types - now dynamic based on database
// Keep some common types for backwards compatibility but allow any string
const COMMON_PROVIDERS = [
  'Studio.Sales.CabinCloseOut', 
  'Studio.Res.CabinCloseOut', 
  'NCL', 
  'Cruising Power', 
  'OneSource'
] as const;

// Make ProviderType more flexible to accept any string from database
export type ProviderType = string;

// Type guard to check if a string is a valid ProviderType
// Now accepts any non-empty string since providers come from database
const isValidProviderType = (provider: string): provider is ProviderType => {
  return typeof provider === 'string' && provider.trim().length > 0;
};

interface ProviderSelectorProps {
  selectedProvider: ProviderType;
  onProviderChange: (provider: ProviderType) => void;
}

// Define provider interface - now supports any agency from database
interface Provider {
  provider_id: number; // Changed to number to match database INTEGER type
  name: string;
  agency: string; // Changed from literal types to string to support any agency
  description: string;
  status: 'active' | 'coming_soon' | 'maintenance';
}

const ProviderSelector: React.FC<ProviderSelectorProps> = ({
  selectedProvider,
  onProviderChange
}) => {
  const { user, hasProviderAccess, isAdmin } = useAuth();
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch providers from API
  useEffect(() => {
    const getProviders = async () => {
      try {
        setLoading(true);
        const fetchedProviders = await fetchProviders();
        
        setProviders(fetchedProviders);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch providers:', err);
        setError('Failed to load providers');
        // Removed hardcoded fallback – rely on API data only
        setProviders([]);
      } finally {
        setLoading(false);
      }
    };

    getProviders();
  }, []);

  // Since the backend now filters providers based on user access,
  // we can directly use the providers returned from the API
  const allowedProviders = useMemo(() => {
    if (!user || providers.length === 0) {
      return [];
    }

    // Backend already filtered providers based on user's provider_access
    // All providers returned should be valid since they come from database
    return providers.filter(p => isValidProviderType(p.name));

  }, [user, providers]);

  // When component mounts or user/allowedProviders changes, verify the selected provider
  // is one of the allowed providers. If not, select the first allowed provider.
  useEffect(() => {
    if (allowedProviders.length > 0) {
      // If the current selection is not in allowed providers or is invalid
      const isCurrentProviderAllowed = allowedProviders.some(p => p.name === selectedProvider && p.status === 'active');
      
      if (!isCurrentProviderAllowed) {
        // Select the first active provider (not coming soon or maintenance)
        const firstValidProvider = allowedProviders.find(p => p.status === 'active');
        if (firstValidProvider && isValidProviderType(firstValidProvider.name)) {
          onProviderChange(firstValidProvider.name);
        }
      }
    }
  }, [allowedProviders, selectedProvider, onProviderChange]);

  const handleSelection = (provider: string, status: string) => {
    if (status === 'coming_soon' || status === 'maintenance') {
      return; // Don't do anything for "coming soon" or "maintenance" providers
    }
    console.log("Selected provider:", provider);
    // Check if the provider name is a valid ProviderType
    if (isValidProviderType(provider)) {
      onProviderChange(provider);
    }
  };

  if (loading) {
    return (
      <div className="mb-0 p-4">
        <div className="text-center">Loading providers...</div>
      </div>
    );
  }

  return (
    <div className="mb-0">
      <div className="p-3 rounded-t-lg ">
        <h2 className="text-black text-xl font-semibold">Price Provider Selection</h2>
        <div className="mt-0 relative pb-2">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
        </div>
      </div>

      {error && (
        <div className="w-[95%] ml-3 px-2 py-2 mb-2 bg-red-100 border-l-4 border-red-500 text-sm text-red-900">
          {error}
        </div>
      )}

      {allowedProviders.length > 0 && (
        <div className=" w-[95%] ml-3 px-2 py-2 mb-2 bg-teal-400/20 border-l-4 border-teal-500 text-sm text-green-900 font-semibold">
          {allowedProviders.length > 1 
            ? `You have access to ${allowedProviders.length} providers.` 
            : 'You have access to 1 provider.'}
        </div>
      )}

      <div className="flex flex-col gap-3 p-2">
        {allowedProviders.map(({ name, description, status }) => (
          <ProviderOption
            key={name}
            name={name}
            description={description}
            isSelected={selectedProvider === name}
            onClick={() => handleSelection(name, status)}
            status={status}
          />
        ))}
        {allowedProviders.length === 0 && (
          <div className="w-full text-center p-4 text-gray-500">
            No providers available for your account. Please contact an administrator.
          </div>
        )}
      </div>
      <div className="ml-2.5 mt-2 text-sm font-medium text-black/90">
        Currently selected: <span className="font-bold">{selectedProvider}</span>
      </div>
    </div>
  );
};

interface ProviderOptionProps {
  name: string;
  description: string;
  isSelected: boolean;
  onClick: () => void;
  status: 'active' | 'coming_soon' | 'maintenance';
}

const ProviderOption: React.FC<ProviderOptionProps> = ({
  name,
  description,
  isSelected,
  onClick,
  status
}) => {
  const isDisabled = status === 'coming_soon' || status === 'maintenance';
  
  return (
    <div
      className={`
        p-4 rounded-lg w-full flex flex-row items-center justify-between relative h-[100px]
        transition-all duration-300 ease-in-out 
        ${isSelected ? 'border-2 border-[#3D73E9] bg-[#F0F9FF] shadow-md shadow-gray-300' : 'border border-gray-300'}
        ${isDisabled ? 'cursor-not-allowed' : 'cursor-pointer hover:shadow-lg transform hover:-translate-y-1 hover:border-blue-400'}
      `}
      onClick={onClick}
    >
      <div className="flex flex-row items-center gap-4">
        <div className="flex flex-col text-left">
          <div className="font-semibold text-lg text-gray-700">
            {name}
          </div>
          <div className="text-sm text-gray-600">{description}</div>
        </div>
      </div>
      
      {isSelected && status === 'active' && (
        <div className="absolute top-2 right-2 text-sm text-green-700 font-medium flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        </div>
      )}
      
      {status === 'coming_soon' && (
        <>
          <div className="absolute inset-0 bg-white opacity-80 rounded-lg" />
          <div className="absolute top-1 right-1 z-5">
            <div className="text-blue-700 font-semibold text-sm">
              Coming Soon
            </div>
          </div>
        </>
      )}
      
      {status === 'maintenance' && (
        <>
          <div className="absolute inset-0 bg-white opacity-80 rounded-lg" />
          <div className="absolute top-1 right-1 z-5">
            <div className="text-red-700 font-semibold text-sm">
              Maintenance
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ProviderSelector;