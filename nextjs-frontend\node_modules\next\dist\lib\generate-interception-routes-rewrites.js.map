{"version": 3, "sources": ["../../src/lib/generate-interception-routes-rewrites.ts"], "names": ["generateInterceptionRoutesRewrites", "toPathToRegexpPath", "path", "replace", "_", "capture", "startsWith", "slice", "voidParamsBeforeInterceptionMarker", "newPath", "foundInterceptionMarker", "segment", "split", "INTERCEPTION_ROUTE_MARKERS", "find", "marker", "push", "join", "appPaths", "rewrites", "appPath", "isInterceptionRouteAppPath", "interceptingRoute", "interceptedRoute", "extractInterceptionRouteInformation", "normalizedInterceptingRoute", "normalizedInterceptedRoute", "normalizedAppPath", "interceptingRouteRegex", "pathToRegexp", "toString", "source", "destination", "has", "type", "key", "NEXT_URL", "value"], "mappings": ";;;;+BA6CgBA;;;eAAAA;;;8BA7Ca;kCACJ;oCAKlB;AAGP,iIAAiI;AACjI,SAASC,mBAAmBC,IAAY;IACtC,OAAOA,KAAKC,OAAO,CAAC,uBAAuB,CAACC,GAAGC;QAC7C,4EAA4E;QAC5E,IAAIA,QAAQC,UAAU,CAAC,QAAQ;YAC7B,OAAO,CAAC,CAAC,EAAED,QAAQE,KAAK,CAAC,GAAG,CAAC,CAAC;QAChC;QACA,OAAO,MAAMF;IACf;AACF;AAEA,gFAAgF;AAChF,yEAAyE;AACzE,2EAA2E;AAC3E,0EAA0E;AAC1E,SAASG,mCAAmCN,IAAY;IACtD,IAAIO,UAAU,EAAE;IAEhB,IAAIC,0BAA0B;IAC9B,KAAK,MAAMC,WAAWT,KAAKU,KAAK,CAAC,KAAM;QACrC,IACEC,8CAA0B,CAACC,IAAI,CAAC,CAACC,SAAWJ,QAAQL,UAAU,CAACS,UAC/D;YACAL,0BAA0B;QAC5B;QAEA,IAAIC,QAAQL,UAAU,CAAC,QAAQ,CAACI,yBAAyB;YACvDD,QAAQO,IAAI,CAAC;QACf,OAAO;YACLP,QAAQO,IAAI,CAACL;QACf;IACF;IAEA,OAAOF,QAAQQ,IAAI,CAAC;AACtB;AAEO,SAASjB,mCACdkB,QAAkB;IAElB,MAAMC,WAAsB,EAAE;IAE9B,KAAK,MAAMC,WAAWF,SAAU;QAC9B,IAAIG,IAAAA,8CAA0B,EAACD,UAAU;YACvC,MAAM,EAAEE,iBAAiB,EAAEC,gBAAgB,EAAE,GAC3CC,IAAAA,uDAAmC,EAACJ;YAEtC,MAAMK,8BAA8B,CAAC,EACnCH,sBAAsB,MAAMrB,mBAAmBqB,qBAAqB,GACrE,MAAM,CAAC;YAER,MAAMI,6BAA6BzB,mBAAmBsB;YACtD,MAAMI,oBAAoBnB,mCACxBP,mBAAmBmB;YAGrB,qEAAqE;YACrE,4DAA4D;YAC5D,4CAA4C;YAC5C,IAAIQ,yBAAyBC,IAAAA,0BAAY,EAACJ,6BACvCK,QAAQ,GACRvB,KAAK,CAAC,GAAG,CAAC;YAEbY,SAASH,IAAI,CAAC;gBACZe,QAAQL;gBACRM,aAAaL;gBACbM,KAAK;oBACH;wBACEC,MAAM;wBACNC,KAAKC,0BAAQ;wBACbC,OAAOT;oBACT;iBACD;YACH;QACF;IACF;IAEA,OAAOT;AACT"}