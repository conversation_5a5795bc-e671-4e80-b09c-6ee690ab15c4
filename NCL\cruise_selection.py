import asyncio
import os
import sys
from datetime import datetime
from loguru import logger
import re
from NCL.utils import NCLUtils
import copy
from Core.ui_logger import ui_log
from NCL.cabin_categories import CabinCategories

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from NCL.login import NCLLogin

NCL_Utils = NCLUtils()

from NCL.select_cruise import CruiseSelector
from NCL.select_category import CategorySelector
from NCL.select_stateroom import StateRoomSelector

is_parallel = 'parallel_booking' in sys.modules


class CruiseSelection:

    @staticmethod
    async def click_continue(page, session_id=None, cabin_id=None):
        try:
            logger.info("Looking for Continue button")

            for selector in ["[id*='action_DoContinue']", "[id*='action_continue']",
                             "input[value='Continue']",
                             "input[type='submit'][value='Continue']"]:
                try:
                    continue_button = await page.wait_for_selector(
                        selector, state="visible", timeout=5000
                    )
                    if continue_button:
                        logger.info(f"Found continue button with selector: {selector}")

                        async with page.expect_navigation(timeout=15000):
                            await continue_button.click()

                        logger.success("Clicked continue button, navigation completed")
                        return True
                except Exception as e:
                    logger.debug(f"Button not found with selector {selector}: {str(e)}")

            logger.warning("Continue button not found with standard selectors")

            try:
                submit_buttons = await page.locator(
                    "input[type='submit'], button[type='submit']"
                ).all()
                for button in submit_buttons:
                    if await button.is_visible():
                        logger.info("Using generic submit button as fallback")
                        async with page.expect_navigation(timeout=15000):
                            await button.click()
                        logger.success("Navigation completed with fallback button")
                        return True
            except Exception as e:
                logger.error(f"Fallback button click failed: {str(e)}")

            return False

        except Exception as e:
            logger.error(f"Error clicking continue: {str(e)}")
            return False

    @staticmethod
    async def handle_price_programs(
        page, cabin_id=None, request_id=None, session_id=None, config=None
    ):
        try:
            logger.info("Handling price programs")
            price_programs_table = await page.wait_for_selector(
                "#SWXMLForm_PricePrograms_priceProgramsTable",
                state="visible",
                timeout=30000
            )
            if not price_programs_table:
                logger.error("Price programs table not found")
                return False

            promo_rows = await page.locator(
                "#SWXMLForm_PricePrograms_priceProgramsTable .slick-row"
            ).all()

            if not promo_rows or len(promo_rows) == 0:
                logger.error("No price programs found")
                return False

            logger.info(f"Found {len(promo_rows)} price programs")

            all_headers = await page.locator(".slick-header-column").all()
            header_texts = []
            for h in all_headers:
                text = await h.text_content()
                header_texts.append(text.strip())
            logger.info(f"All column headers: {header_texts}")

            guest_columns = []
            for i, header in enumerate(header_texts):
                if "Guest" in header:
                    try:
                        guest_num = int(''.join(filter(str.isdigit, header)))
                        guest_columns.append((guest_num, i))
                        logger.info(f"Found Guest {guest_num} column at index {i}")
                    except ValueError:
                        logger.warning(f"Could not parse guest number from '{header}'")

            guest_columns.sort()
            total_guests = len(guest_columns)

            if total_guests == 0:
                logger.warning("No guest columns found in the UI")
                logger.info("Trying alternative method to detect guest columns...")
                for i, header in enumerate(header_texts):
                    if any(f"Guest {num}" in header for num in range(1, 10)):
                        guest_num = int(''.join(filter(str.isdigit, header)))
                        guest_columns.append((guest_num, i))
                        logger.info(
                            f"Found Guest {guest_num} column using alternative method"
                        )

                if not guest_columns:
                    logger.error("Could not detect any guest columns")
                    return False

                guest_columns.sort()
                total_guests = len(guest_columns)

            logger.info(f"Final detected guest count: {total_guests}")

            logger.info("\n==== AVAILABLE PRICE PROGRAMS ====")
            for i, row in enumerate(promo_rows):
                try:
                    cells = await row.locator("[class*='l'][class*='r']").all()
                    promo_code = await cells[0].text_content() if cells else "Unknown"
                    name = await cells[1].text_content() if len(cells) > 1 else "Unknown"
                    guest_statuses = []

                    for guest_num, col_index in guest_columns:
                        if col_index < len(cells):
                            checkbox = cells[col_index].locator(".checkbox").first
                            if await checkbox.count() > 0:
                                class_attr = await checkbox.get_attribute("class")
                                is_selected = "yes" in class_attr.lower()
                                guest_statuses.append(
                                    f"Guest {guest_num}: {'Selected' if is_selected else 'Not Selected'}"
                                )

                    logger.info(
                        f"{i+1}. {promo_code} - {name} - {' - '.join(guest_statuses)}"
                    )
                except Exception as e:
                    logger.error(f"Error parsing row {i+1}: {str(e)}")
                    logger.info(f"{i+1}. [Error parsing program information]")

            rate_codes_config = {}
            if config and 'ncl_rate_codes' in config:
                rate_codes_config = config['ncl_rate_codes']
                logger.info(
                    f"Using rate code configuration from frontend: {rate_codes_config}"
                )
            else:
                rate_codes_config = {
                    'ALL4CHO': True,
                    'CHOALL42': True,
                    'FITOBC': False,
                    'STAROBC': False,
                    'NATOBC': False,
                }
                logger.info("Using default rate code configuration")

            logger.info(
                "Step 1: Looking for primary promotions based on selected checkboxes"
            )
            primary_promotion_found = False

            if rate_codes_config.get('ALL4CHO', False):
                for row in promo_rows:
                    try:
                        promo_code_cell = row.locator(".l0.r0").first
                        if await promo_code_cell.count() > 0:
                            promo_code = await promo_code_cell.text_content()
                            promo_code = promo_code.strip()
                            if "ALL4CHO" in promo_code:
                                primary_promotion_found = True
                                logger.info(
                                    f"Found selected primary promotion: {promo_code}"
                                )

                                await CruiseSelection._select_promotion_for_all_guests(
                                    row, guest_columns, promo_code, session_id, cabin_id
                                )
                                break
                    except Exception as row_err:
                        logger.warning(
                            f"Error processing ALL4CHO promo row: {str(row_err)}"
                        )

            if not primary_promotion_found and rate_codes_config.get('CHOALL42', False):
                for row in promo_rows:
                    try:
                        promo_code_cell = row.locator(".l0.r0").first
                        if await promo_code_cell.count() > 0:
                            promo_code = await promo_code_cell.text_content()
                            promo_code = promo_code.strip()
                            if "CHOALL42" in promo_code:
                                primary_promotion_found = True
                                logger.info(
                                    f"Found selected alternative primary promotion: {promo_code}"
                                )

                                await CruiseSelection._select_promotion_for_all_guests(
                                    row, guest_columns, promo_code, session_id, cabin_id
                                )
                                break
                    except Exception as row_err:
                        logger.warning(
                            f"Error processing CHOALL42 promo row: {str(row_err)}"
                        )

            if not primary_promotion_found:
                logger.info("No primary promotions were selected or found")

            logger.info(
                "Step 2: Looking for secondary promotions based on selected checkboxes"
            )
            secondary_promo_codes = []

            if rate_codes_config.get('FITOBC', False):
                secondary_promo_codes.append('FITOBC')
            if rate_codes_config.get('STAROBC', False):
                secondary_promo_codes.append('STAROBC')
            if rate_codes_config.get('NATOBC', False):
                secondary_promo_codes.append('NATOBC')

            secondary_promotion_found = False

            for target_promo in secondary_promo_codes:
                if secondary_promotion_found:
                    break

                for row in promo_rows:
                    try:
                        promo_code_cell = row.locator(".l0.r0").first
                        if await promo_code_cell.count() > 0:
                            promo_code = await promo_code_cell.text_content()
                            promo_code = promo_code.strip()
                            if target_promo in promo_code:
                                secondary_promotion_found = True
                                logger.info(
                                    f"Found selected secondary promotion: {promo_code}"
                                )

                                await CruiseSelection._select_promotion_for_all_guests(
                                    row, guest_columns, promo_code, session_id, cabin_id
                                )
                                break
                    except Exception as row_err:
                        logger.warning(
                            f"Error processing secondary promo row: {str(row_err)}"
                        )

            if not secondary_promotion_found and secondary_promo_codes:
                logger.info(
                    f"None of the selected secondary promotions were found: {secondary_promo_codes}"
                )
            elif not secondary_promo_codes:
                logger.info("No secondary promotions were selected")

            logger.info("Step 2.5: Verifying all rate codes match frontend selections")
            all_rate_codes = ['ALL4CHO', 'CHOALL42', 'FITOBC', 'STAROBC', 'NATOBC']
            verification_changes_made = False

            for row in promo_rows:
                try:
                    promo_code_cell = row.locator(".l0.r0").first
                    if await promo_code_cell.count() > 0:
                        promo_code = await promo_code_cell.text_content()
                        promo_code = promo_code.strip()

                        for target_rate_code in all_rate_codes:
                            if target_rate_code in promo_code:
                                should_be_selected = rate_codes_config.get(
                                    target_rate_code, False
                                )

                                is_currently_selected = False
                                try:
                                    if guest_columns:
                                        first_guest_col = guest_columns[0][
                                            1]  # Get column index of first guest
                                        checkbox = row.locator(
                                            f".l{first_guest_col}.r{first_guest_col} .checkbox"
                                        ).first
                                        if await checkbox.count() > 0:
                                            checkbox_class = await checkbox.get_attribute(
                                                "class"
                                            )
                                            if "yes" in checkbox_class.lower():
                                                is_currently_selected = True
                                except Exception as check_err:
                                    logger.warning(
                                        f"Error checking current state for {promo_code}: {str(check_err)}"
                                    )
                                    continue

                                if should_be_selected and not is_currently_selected:
                                    logger.info(
                                        f"Rate code {promo_code} should be selected but isn't - selecting it"
                                    )
                                    await CruiseSelection._select_promotion_for_all_guests(
                                        row, guest_columns, promo_code, session_id,
                                        cabin_id
                                    )
                                    verification_changes_made = True
                                elif not should_be_selected and is_currently_selected:
                                    logger.info(
                                        f"Rate code {promo_code} should not be selected but is - deselecting it"
                                    )
                                    await CruiseSelection._deselect_promotion_for_all_guests(
                                        row, guest_columns, promo_code, session_id,
                                        cabin_id
                                    )
                                    verification_changes_made = True
                                else:
                                    logger.info(
                                        f"Rate code {promo_code} is in correct state (should_be_selected={should_be_selected}, is_currently_selected={is_currently_selected})"
                                    )

                                break

                except Exception as row_err:
                    logger.warning(
                        f"Error in verification step for row: {str(row_err)}"
                    )

            if primary_promotion_found or secondary_promotion_found or verification_changes_made:
                if verification_changes_made and not (primary_promotion_found
                                                      or secondary_promotion_found):
                    logger.info(
                        "Step 3: Clicking Apply Promotions button (verification changes require applying)"
                    )
                else:
                    logger.info("Step 3: Clicking Apply Promotions button")
                apply_button = page.locator(
                    "#SWXMLForm_PricePrograms_action_DoContinue"
                ).first

                if await apply_button.count() > 0:
                    await apply_button.scroll_into_view_if_needed()
                    await page.wait_for_timeout(1000)
                    await apply_button.click()

                    logger.info(
                        "Waiting for page to process after applying promotions..."
                    )
                    await page.wait_for_timeout(4000)

                    error_msg = page.locator(".error-message, .alert-message").first
                    if await error_msg.count() > 0 and await error_msg.is_visible():
                        error_text = await error_msg.text_content()
                        logger.error(
                            f"Error message after applying promotions: {error_text}"
                        )
                        return False
                    else:
                        logger.info(
                            "No error message found, assuming promotions applied successfully"
                        )
                        ui_log(
                            "Promotions applied successfully — enjoy your savings",
                            session_id=session_id,
                            cabin_id=cabin_id,
                            module="NCL",
                            step="promotions"
                        )
                        return True
                else:
                    logger.error("Apply Promotions button not found")
                    return False
            else:
                logger.info("No promotions were selected, skipping Apply step")
                ui_log(
                    "No promotions could be selected right now — please try again",
                    session_id=session_id,
                    cabin_id=cabin_id,
                    module="NCL",
                    step="promotions"
                )
                return True

        except Exception as e:
            logger.error(f"Error handling price programs: {str(e)}")
            return False

    @staticmethod
    async def _deselect_promotion_for_all_guests(
        row, guest_columns, promo_code, session_id=None, cabin_id=None
    ):
        """Helper method to deselect a promotion for all guests"""
        try:
            all_cells = await row.locator("[class*='l'][class*='r']").all()
            logger.info(f"Row has {len(all_cells)} cells")

            for guest_num, col_index in guest_columns:
                try:
                    checkbox_found = False
                    for selector in [
                            f".l{col_index}.r{col_index} .checkbox",
                            f".l{col_index}.r{col_index} input[type='checkbox']",
                            f"[class*='l{col_index}'][class*='r{col_index}'] .checkbox"
                    ]:
                        checkbox = row.locator(selector).first
                        if await checkbox.count() > 0:
                            checkbox_class = await checkbox.get_attribute("class")
                            logger.info(
                                f"Guest {guest_num} checkbox class: {checkbox_class}"
                            )

                            if "yes" in checkbox_class.lower():
                                logger.info(
                                    f"Deselecting Guest {guest_num} checkbox for {promo_code}"
                                )
                                await checkbox.click()
                                await checkbox.page.wait_for_timeout(500)
                                checkbox_found = True
                                break
                            elif "no" in checkbox_class.lower():
                                logger.info(
                                    f"Guest {guest_num} checkbox already deselected"
                                )
                                checkbox_found = True
                                break

                    if not checkbox_found:
                        logger.warning(
                            f"Could not find or interact with checkbox for Guest {guest_num}"
                        )

                except Exception as guest_err:
                    logger.warning(
                        f"Error deselecting checkbox for Guest {guest_num}: {str(guest_err)}"
                    )

            ui_log(
                f"Promotion {promo_code} has been deselected",
                session_id=session_id,
                cabin_id=cabin_id,
                module="NCL",
                step="promotions"
            )

        except Exception as e:
            logger.error(f"Error deselecting promotion for all guests: {str(e)}")

    @staticmethod
    async def _select_promotion_for_all_guests(
        row, guest_columns, promo_code, session_id=None, cabin_id=None
    ):
        """Helper method to select a promotion for all guests"""
        try:

            all_cells = await row.locator("[class*='l'][class*='r']").all()
            logger.info(f"Row has {len(all_cells)} cells")

            for guest_num, col_index in guest_columns:
                try:
                    checkbox_found = False
                    for selector in [
                            f".l{col_index}.r{col_index} .checkbox",
                            f".l{col_index}.r{col_index} input[type='checkbox']",
                            f"[class*='l{col_index}'][class*='r{col_index}'] .checkbox"
                    ]:
                        checkbox = row.locator(selector).first
                        if await checkbox.count() > 0:
                            checkbox_class = await checkbox.get_attribute("class")
                            logger.info(
                                f"Guest {guest_num} checkbox class: {checkbox_class}"
                            )

                            if "no" in checkbox_class.lower():
                                logger.info(
                                    f"Selecting Guest {guest_num} checkbox for {promo_code}"
                                )
                                await checkbox.click()
                                await checkbox.page.wait_for_timeout(500)
                                checkbox_found = True
                                break
                            elif "yes" in checkbox_class.lower():
                                logger.info(
                                    f"Guest {guest_num} checkbox already selected"
                                )
                                checkbox_found = True
                                break

                    if not checkbox_found:
                        logger.warning(
                            f"Could not find or interact with checkbox for Guest {guest_num}"
                        )

                except Exception as guest_err:
                    logger.warning(
                        f"Error selecting checkbox for Guest {guest_num}: {str(guest_err)}"
                    )

            ui_log(
                f"Promotion {promo_code} applied — you're all set",
                session_id=session_id,
                cabin_id=cabin_id,
                module="NCL",
                step="promotions"
            )

        except Exception as e:
            logger.error(f"Error selecting promotion for all guests: {str(e)}")

    @staticmethod
    async def navigate_to_pricing(page):
        try:
            logger.info("Checking if we're already on the pricing page")

            pricing_button = page.locator("#SWXMLForm_Pricing_action_DoContinue").first
            if await pricing_button.count() > 0:
                logger.info("Already on pricing page")
                return True

            try:
                current_url = page.url

                if "/tva/new/agent-price-programs/" in current_url:
                    pricing_url = current_url.replace(
                        "agent-price-programs", "agent-pricing"
                    )
                    logger.info(
                        f"Attempting direct URL navigation to pricing: {pricing_url}"
                    )
                    await page.goto(pricing_url)
                    await page.wait_for_timeout(3000)

                    if "agent-pricing" in page.url:
                        logger.success("Successfully navigated to pricing page via URL")
                        return True
            except Exception as url_err:
                logger.warning(f"Direct URL navigation failed: {str(url_err)}")

            try:
                continue_button = page.locator(
                    "#SWXMLForm_PricePrograms_action_DoContinue"
                ).first

                if await continue_button.count() > 0 and await continue_button.is_visible():
                    logger.info("Clicking Continue button to navigate to pricing page")

                    await continue_button.scroll_into_view_if_needed()
                    await page.wait_for_timeout(1000)

                    async with page.expect_navigation(timeout=15000):
                        await continue_button.click()

                    pricing_element = page.locator(
                        "#SWXMLForm_Pricing_action_DoContinue"
                    ).first
                    if await pricing_element.count() > 0:
                        logger.success("Successfully navigated to pricing page")
                        return True

                    if "/agent-pricing/" in page.url:
                        logger.info(
                            "URL indicates we are on pricing page despite element not found"
                        )
                        return True
            except Exception as button_err:
                logger.warning(
                    f"Could not find/click Continue button: {str(button_err)}"
                )

            try:
                nav_elements = await page.locator(
                    "#Content .res-form li a, #Content .nav-tabs li a"
                ).all()
                for nav in nav_elements:
                    try:
                        text = await nav.text_content()
                        text = text.strip().lower()
                        if "pricing" in text or "payment" in text or "review" in text:
                            logger.info(f"Found navigation element with text: {text}")
                            await nav.click()
                            await page.wait_for_timeout(2000)

                            if "/agent-pricing/" in page.url:
                                logger.success(
                                    "Successfully navigated to pricing page via tab"
                                )
                                return True
                    except:
                        continue
            except Exception as nav_err:
                logger.warning(f"Navigation via tabs failed: {str(nav_err)}")

            try:
                clicked = await page.evaluate(
                    """
                    var links = document.querySelectorAll('a');
                    for (var i=0; i < links.length; i++) {
                        if (links[i].textContent.toLowerCase().includes('pricing') || 
                            links[i].href.includes('pricing')) {
                            links[i].click();
                            return true;
                        }
                    }
                    return false;
                """
                )

                if clicked:
                    await page.wait_for_timeout(2000)

                    if "/agent-pricing/" in page.url:
                        logger.success(
                            "Successfully navigated to pricing page via JavaScript"
                        )
                return True
            except Exception as js_err:
                logger.warning(f"JavaScript navigation failed: {str(js_err)}")

            logger.error("All attempts to navigate to pricing page failed")
            return False

        except Exception as e:
            logger.error(f"Error in navigate_to_pricing: {str(e)}")
            return False

    @staticmethod
    async def fill_search_form_improved(page, cruise_details):
        try:
            logger.info("Filling improved search form with details")

            date_input = cruise_details.get('travel_date', '')
            ship_input = cruise_details.get('ship_name', '')
            adults = cruise_details.get('adults', 2)
            children = cruise_details.get('children', 0)
            infants = cruise_details.get('infants', 0)

            logger.info(
                f"Search parameters - Date: {date_input}, Ship: {ship_input}, Adults: {adults}, Children: {children}, Infants: {infants}"
            )

            if not date_input:
                logger.error("Missing travel date")
                return False

            if not ship_input:
                logger.error("Missing ship name")
                return False

            date_filled = False
            for date_selector in ["#SWXMLForm_SearchVacation_From",
                                  "#SWXMLForm_SearchVacation_date"]:
                try:
                    date_field = page.locator(date_selector)
                    if await date_field.count() > 0:
                        await date_field.fill("")
                        await date_field.fill(date_input)
                        await date_field.press("Tab")
                        await asyncio.sleep(0.5)
                        logger.info(f"Date filled using {date_selector}")
                        date_filled = True
                        break
                except Exception as date_err:
                    logger.debug(
                        f"Could not fill date using {date_selector}: {str(date_err)}"
                    )

            if date_filled:
                try:
                    from datetime import datetime, timedelta
                    from_date = datetime.strptime(date_input, '%m/%d/%Y')
                    to_date = from_date + timedelta(days=1)
                    to_date_str = to_date.strftime('%m/%d/%Y')

                    logger.info(f"Setting To date: {to_date_str}")
                    to_date_field = page.locator("#SWXMLForm_SearchVacation_To")
                    if await to_date_field.count() > 0:
                        await to_date_field.fill("")
                        await to_date_field.fill(to_date_str)
                        await to_date_field.press("Tab")
                except Exception as to_date_err:
                    logger.warning(f"Error setting To date: {str(to_date_err)}")
                    try:
                        to_date_field = page.locator("#SWXMLForm_SearchVacation_To")
                        if await to_date_field.count() > 0:
                            await to_date_field.fill("")
                            await to_date_field.fill(date_input)
                            await to_date_field.press("Tab")
                    except:
                        logger.warning("Could not set To date at all")

            if not date_filled:
                logger.warning("Could not fill date field using any known selector")

                try:
                    await page.evaluate(
                        f"""
                        var dateInputs = document.querySelectorAll('input[type="text"][id*="date"], input[type="text"][id*="From"]');
                        for (var i=0; i < dateInputs.length; i++) {{
                            dateInputs[i].value = "{date_input}";
                        }}
                    """
                    )
                    logger.info("Filled date using JavaScript")
                    date_filled = True
                except Exception as js_date_err:
                    logger.warning(f"JavaScript date fill failed: {str(js_date_err)}")

            if not date_filled:
                logger.error("Could not fill date field")
                return False

            ship_filled = False

            try:
                ship_dropdown = page.locator("#SWXMLForm_SearchVacation_Ship")
                if await ship_dropdown.count() > 0:
                    tag_name = await ship_dropdown.evaluate("el => el.tagName")
                    if tag_name and tag_name.lower() == "select":
                        options = await page.locator("#SWXMLForm_SearchVacation_Ship option"
                                               ).all()

                        option_found = False
                        for option in options:
                            option_text = await option.text_content()
                            option_text = option_text.lower()
                            if ship_input.lower() in option_text:
                                option_value = await option.get_attribute("value")
                                if option_value:
                                    await page.select_option(
                                        "#SWXMLForm_SearchVacation_Ship",
                                        value=option_value
                                    )
                                    logger.info(
                                        f"Ship selected from dropdown: {option_text}"
                                    )
                                    ship_filled = True
                                    option_found = True
                                    break

                        if not option_found and len(options) > 1:
                            for option in options:
                                option_value = await option.get_attribute("value")
                                if option_value:
                                    await page.select_option(
                                        "#SWXMLForm_SearchVacation_Ship",
                                        value=option_value
                                    )
                                    option_text = await option.text_content()
                                    logger.info(
                                        f"Selected first available ship from dropdown: {option_text}"
                                    )
                                    ship_filled = True
                                    break
            except Exception as select_err:
                logger.debug(f"Ship dropdown approach failed: {str(select_err)}")

            if not ship_filled:
                try:
                    ship_field = page.locator("#SWXMLForm_SearchVacation_Ship")
                    if await ship_field.count() > 0:
                        await ship_field.fill("")
                        await ship_field.fill(ship_input)
                        await asyncio.sleep(0.5)
                        await ship_field.press("ArrowDown")
                        await ship_field.press("Enter")
                        logger.info("Ship filled using text input with selection")
                        ship_filled = True
                except Exception as text_err:
                    logger.debug(f"Ship text field approach failed: {str(text_err)}")

            if not ship_filled:
                logger.warning("Using JavaScript fallback for ship field")
                try:
                    await page.evaluate(
                        f"""
                        var shipField = document.getElementById('SWXMLForm_SearchVacation_Ship');
                        if (shipField) {{
                            if (shipField.tagName === 'SELECT') {{
                                // Handle dropdown
                                for (var i=0; i < shipField.options.length; i++) {{
                                    if (shipField.options[i].text.toLowerCase().includes('{ship_input.lower()}')) {{
                                        shipField.selectedIndex = i;
                                        return;
                                    }}
                                }}
                                // Select first non-empty option if no match
                                for (var i=0; i < shipField.options.length; i++) {{
                                    if (shipField.options[i].value) {{
                                        shipField.selectedIndex = i;
                                        return;
                                    }}
                                }}
                            }} else {{
                                // Handle text input
                                shipField.value = "{ship_input}";
                            }}
                        }}
                    """
                    )
                    logger.info("Ship field filled using JavaScript")
                    ship_filled = True
                except Exception as js_ship_err:
                    logger.warning(f"JavaScript ship fill failed: {str(js_ship_err)}")

            if not ship_filled:
                logger.error("Could not fill ship field")
                return False

            try:
                adults_field = page.locator("#SWXMLForm_SearchVacation_age_ADULT")
                if await adults_field.count() > 0:
                    await adults_field.fill("")
                    await adults_field.fill(str(adults))
                    logger.info(f"Adults set to {adults}")
                else:
                    await page.evaluate(
                        f"""
                        var adultsField = document.getElementById('SWXMLForm_SearchVacation_age_ADULT');
                        if (adultsField) {{
                            adultsField.value = "{adults}";
                        }}
                    """
                    )
                    logger.info(f"Adults set to {adults} using JavaScript")
            except Exception as adults_err:
                logger.warning(f"Error setting adults: {str(adults_err)}")
                try:
                    await page.evaluate(
                        f"""
                        var adultsField = document.getElementById('SWXMLForm_SearchVacation_age_ADULT');
                        if (adultsField) {{
                            adultsField.value = "{adults}";
                        }}
                    """
                    )
                    logger.info(f"Adults set to {adults} using JavaScript fallback")
                except Exception as js_adults_err:
                    logger.warning(
                        f"JavaScript adults fill failed: {str(js_adults_err)}"
                    )

            if children and children > 0:
                try:
                    children_field = page.locator("#SWXMLForm_SearchVacation_age_CHILD")
                    if await children_field.count() > 0:
                        await children_field.fill("")
                        await children_field.fill(str(children))
                        logger.info(f"Children set to {children}")
                    else:
                        await page.evaluate(
                            f"""
                            var childrenField = document.getElementById('SWXMLForm_SearchVacation_age_CHILD');
                            if (childrenField) {{
                                childrenField.value = "{children}";
                            }}
                        """
                        )
                        logger.info(f"Children set to {children} using JavaScript")
                except Exception as children_err:
                    logger.warning(f"Error setting children: {str(children_err)}")
                    try:
                        await page.evaluate(
                            f"""
                            var childrenField = document.getElementById('SWXMLForm_SearchVacation_age_CHILD');
                            if (childrenField) {{
                                childrenField.value = "{children}";
                            }}
                        """
                        )
                        logger.info(
                            f"Children set to {children} using JavaScript fallback"
                        )
                    except Exception as js_children_err:
                        logger.warning(
                            f"JavaScript children fill failed: {str(js_children_err)}"
                        )

                for i in range(1, children + 1):
                    try:
                        age_field_id = f"SWXMLForm_SearchVacation_birthdate_CHILD_{i}"
                        age_field = page.locator(f"#{age_field_id}")
                        if await age_field.count() > 0:
                            logger.info(f"Setting age for child {i}")

                            try:
                                age_field_tag = await age_field.evaluate("el => el.tagName")
                                if age_field_tag.lower() == "select":
                                    await page.select_option(f"#{age_field_id}", value="10")
                                else:
                                    await age_field.fill("10")
                            except Exception as field_err:
                                logger.warning(
                                    f"Error with standard approach for child {i} age: {str(field_err)}"
                                )
                                await page.evaluate(
                                    f"""
                                    var ageField = document.getElementById('{age_field_id}');
                                    if (ageField) {{
                                        if (ageField.tagName === 'SELECT') {{
                                            for (var i=0; i < ageField.options.length; i++) {{
                                                if (ageField.options[i].value === '10') {{
                                                    ageField.selectedIndex = i;
                                                    break;
                                                }}
                                            }}
                                        }} else {{
                                            ageField.value = "10";
                                        }}
                                    }}
                                """
                                )
                    except Exception as child_error:
                        logger.warning(
                            f"Error setting age for child {i}: {str(child_error)}"
                        )

            if infants and infants > 0:
                try:
                    infants_field = page.locator("#SWXMLForm_SearchVacation_age_INFANT")
                    if await infants_field.count() > 0:
                        await infants_field.fill("")
                        await infants_field.fill(str(infants))
                        logger.info(f"Infants set to {infants}")
                    else:
                        await page.evaluate(
                            f"""
                            var infantsField = document.getElementById('SWXMLForm_SearchVacation_age_INFANT');
                            if (infantsField) {{
                                infantsField.value = "{infants}";
                            }}
                        """
                        )
                        logger.info(f"Infants set to {infants} using JavaScript")
                except Exception as infants_err:
                    logger.warning(f"Error setting infants: {str(infants_err)}")
                    try:
                        await page.evaluate(
                            f"""
                            var infantsField = document.getElementById('SWXMLForm_SearchVacation_age_INFANT');
                            if (infantsField) {{
                                infantsField.value = "{infants}";
                            }}
                        """
                        )
                        logger.info(
                            f"Infants set to {infants} using JavaScript fallback"
                        )
                    except Exception as js_infants_err:
                        logger.warning(
                            f"JavaScript infants fill failed: {str(js_infants_err)}"
                        )

                for i in range(1, infants + 1):
                    try:
                        age_field_id = f"SWXMLForm_SearchVacation_birthdate_INFANT_{i}"
                        age_field = page.locator(f"#{age_field_id}")
                        if await age_field.count() > 0:
                            logger.info(f"Setting age for infant {i}")

                            try:
                                age_field_tag = await age_field.evaluate("el => el.tagName")
                                if age_field_tag.lower() == "select":
                                    await page.select_option(f"#{age_field_id}", value="1")
                                else:
                                    await age_field.fill("1")
                            except Exception as field_err:
                                logger.warning(
                                    f"Error with standard approach for infant {i} age: {str(field_err)}"
                                )
                                await page.evaluate(
                                    f"""
                                    var ageField = document.getElementById('{age_field_id}');
                                    if (ageField) {{
                                        if (ageField.tagName === 'SELECT') {{
                                            for (var i=0; i < ageField.options.length; i++) {{
                                                if (ageField.options[i].value === '1') {{
                                                    ageField.selectedIndex = i;
                                                    break;
                                                }}
                                            }}
                                        }} else {{
                                            ageField.value = "1";
                                        }}
                                    }}
                                """
                                )
                    except Exception as infant_error:
                        logger.warning(
                            f"Error setting age for infant {i}: {str(infant_error)}"
                        )

            try:
                logger.info("Ensuring insurance checkbox is unchecked")
                insurance_checkbox = page.locator(
                    "#SWXMLForm_SearchVacation_incInsurance"
                )

                if await insurance_checkbox.count() > 0:
                    if await insurance_checkbox.is_checked():
                        logger.info("Insurance checkbox is checked, unchecking it")
                        await insurance_checkbox.uncheck()
                    else:
                        logger.info("Insurance checkbox already unchecked")
                else:
                    logger.info("Insurance checkbox not found with standard selector")
                    await page.evaluate(
                        """
                        var insurance = document.getElementById('SWXMLForm_SearchVacation_incInsurance');
                        if (insurance && insurance.checked) {
                            insurance.checked = false;
                        }
                    """
                    )
            except Exception as e:
                logger.warning(f"Could not uncheck insurance checkbox: {str(e)}")

            logger.success("Search form filled successfully")
            return True
        except Exception as e:
            logger.error(f"Error filling search form: {str(e)}")
            return False

    @staticmethod
    async def start_search_improved(page):
        try:
            logger.info("Starting improved search")

            search_button = page.locator(
                "#SWXMLForm_SearchVacation_action_DoSearchVacation"
            ).first

            if await search_button.count() > 0:
                logger.info(
                    "Found search button with ID: SWXMLForm_SearchVacation_action_DoSearchVacation"
                )

                await search_button.scroll_into_view_if_needed()
                await asyncio.sleep(1)

                try:
                    async with page.expect_navigation(timeout=45000) as navigation_info:
                        await search_button.click()

                    navigation_response = await navigation_info.value
                    logger.info(f"Navigation completed to: {navigation_response.url}")

                    try:
                        await page.wait_for_selector(
                            "#SWXMLForm_SelectVoyage_voyage", timeout=30000
                        )
                        logger.success("Search results loaded successfully")
                        return True
                    except Exception as wait_error:
                        logger.warning(
                            f"Did not detect voyage selection element: {str(wait_error)}"
                        )

                        current_url = page.url
                        if "select-voyage" in current_url or "selectvoyage" in current_url:
                            logger.info("URL indicates we are on voyage selection page")
                            return True

                        try:
                            search_form_btn = page.locator(
                                "#SWXMLForm_SearchVacation_action_DoSearchVacation"
                            )
                            if await search_form_btn.count() == 0:
                                logger.info(
                                    "Search form button no longer present, navigation successful"
                                )
                                return True
                        except:
                            pass

                        logger.warning(
                            f"Search may have completed but on unexpected page. Current URL: {current_url}"
                        )
                except Exception as click_error:
                    logger.warning(
                        f"Error during button click or navigation: {str(click_error)}"
                    )
            else:
                logger.warning("Search button not found with primary selector")

            alt_selectors = [
                "#SWXMLForm_SearchVacation_action_search",
                "#SWXMLForm_SearchVacation_action_submit", "input[value='Search']",
                "button[value='Search']", "input[type='submit']", "button.search",
                "[id*='search' i]:visible", "[id*='Search' i]:visible",
                "[name*='search' i]:visible", "[name*='Search' i]:visible"
            ]

            for selector in alt_selectors:
                try:
                    alt_button = page.locator(selector).first
                    if await alt_button.count() > 0 and await alt_button.is_visible():
                        logger.info(
                            f"Found alternative search button with selector: {selector}"
                        )

                        await alt_button.scroll_into_view_if_needed()
                        await asyncio.sleep(1)

                        try:
                            async with page.expect_navigation(timeout=45000):
                                await alt_button.click()

                            try:
                                await page.wait_for_selector(
                                    "#SWXMLForm_SelectVoyage_voyage", timeout=30000
                                )
                                logger.success(
                                    "Cruise search completed with alternative button"
                                )
                                return True
                            except:
                                if "select-voyage" in page.url or "selectvoyage" in page.url:
                                    logger.info(
                                        "URL indicates we are on voyage selection page after using alternative button"
                                    )
                                    return True
                        except Exception as alt_click_error:
                            logger.warning(
                                f"Error clicking alternative button: {str(alt_click_error)}"
                            )
                            continue
                except Exception:
                    continue

            logger.warning(
                "Standard button clicks failed, attempting JavaScript approach"
            )

            try:
                js_clicked = await page.evaluate(
                    """
                    function findAndClickSearchButton() {
                        // Try specific IDs first
                        var buttonIds = [
                            'SWXMLForm_SearchVacation_action_DoSearchVacation',
                            'SWXMLForm_SearchVacation_action_search',
                            'SWXMLForm_SearchVacation_action_submit'
                        ];
                        
                        for (var i = 0; i < buttonIds.length; i++) {
                            var btn = document.getElementById(buttonIds[i]);
                            if (btn) {
                                btn.click();
                                return true;
                            }
                        }
                        
                        // Try search buttons by value or type
                        var searchBtns = document.querySelectorAll('input[value="Search"], button[value="Search"], input[type="submit"]');
                        if (searchBtns.length > 0) {
                            searchBtns[0].click();
                            return true;
                        }
                        
                        // Last resort - try to find any button with search in the name
                        var allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
                        for (var j = 0; j < allButtons.length; j++) {
                            var button = allButtons[j];
                            if (button.id && button.id.toLowerCase().includes('search')) {
                                button.click();
                                return true;
                            }
                            if (button.name && button.name.toLowerCase().includes('search')) {
                                button.click();
                                return true;
                            }
                            if (button.value && button.value.toLowerCase().includes('search')) {
                                button.click();
                                return true;
                            }
                        }
                        
                        return false;
                    }
                    
                    return findAndClickSearchButton();
                """
                )

                if js_clicked:
                    logger.info("Clicked search button using JavaScript")
                    await asyncio.sleep(5)

                    if "select-voyage" in page.url or "selectvoyage" in page.url:
                        logger.success(
                            "Successfully navigated to voyage selection page via JavaScript"
                        )
                        return True
            except Exception as js_error:
                logger.warning(f"JavaScript approach failed: {str(js_error)}")

            logger.error("Search button click did not navigate away from search form")
            return False

        except Exception as e:
            logger.error(f"Error in start_search_improved: {str(e)}")
            return False

    @staticmethod
    async def calculate_onboard_credit(page, percentage=10):

        try:
            logger.info("Extracting commission amount and calculating onboard credit")

            logger.info("Extracting invoice data")
            await page.wait_for_selector(
                "#xslt > div > div > div:nth-child(2)", timeout=15000
            )

            invoice_data = {}
            commission_amount = 0.0

            invoice_rows = await page.locator(
                "#xslt > div > div > div:nth-child(2) table tr"
            ).all()
            logger.info("\n=== INVOICE TABLE ===")

            for row in invoice_rows:
                try:
                    cells = await row.locator("td").all()
                    header = row.locator("th").first

                    if header and cells and len(cells) >= 3:
                        header_text = (await header.text_content()).strip()
                        total_cell = (await cells[2].text_content()).strip()
                        logger.info(f"{header_text}: {total_cell}")
                        invoice_data[header_text] = total_cell
                except Exception as e:
                    logger.debug(f"Error parsing invoice row: {str(e)}")
                    continue

            logger.info("Extracting commission data")
            await page.wait_for_selector(
                "#xslt > div > div > div:nth-child(3)", timeout=15000
            )

            commission_rows = await page.locator(
                "#xslt > div > div > div:nth-child(3) table tr"
            ).all()
            logger.info("\n=== COMMISSION TABLE ===")

            for row in commission_rows:
                try:
                    cells = await row.locator("td").all()
                    headers = await row.locator("th").all()

                    if headers and len(headers) > 0:
                        header_text = (await headers[0].text_content()).strip()

                        if cells and len(cells) >= 1:
                            if header_text == "Total" or (len(headers) > 1 and "Total"
                                                          in await headers[0].text_content()):
                                total_commission = (await cells[-1].text_content()).strip()
                                logger.info(f"Total Commission: {total_commission}")

                                commission_amount = float(
                                    total_commission.replace('$', '').replace(',', '')
                                )
                            else:
                                code = header_text
                                percent = (await cells[0].text_content()).strip(
                                ) if len(cells) > 0 else ""
                                amount = (await cells[1].text_content()).strip(
                                ) if len(cells) > 1 else ""
                                logger.info(
                                    f"Commission Code: {code}, Percent: {percent}, Amount: {amount}"
                                )
                except Exception as row_err:
                    logger.warning(f"Error parsing commission row: {str(row_err)}")
                    continue

            mas_din_package = 0.0
            mas_bev_package = 0.0

            for key, value in invoice_data.items():
                if "MAS DIN PACKAGE" in key:
                    try:
                        mas_din_package = float(value.replace('$', '').replace(',', ''))
                        logger.info(f"MAS DIN PACKAGE: ${mas_din_package:.2f}")
                    except:
                        logger.warning(
                            f"Could not parse MAS DIN PACKAGE value: {value}"
                        )

                if "MAS BEV PACKAGE" in key:
                    try:
                        mas_bev_package = float(value.replace('$', '').replace(',', ''))
                        logger.info(f"MAS BEV PACKAGE: ${mas_bev_package:.2f}")
                    except:
                        logger.warning(
                            f"Could not parse MAS BEV PACKAGE value: {value}"
                        )

            logger.info(f"Extracted commission amount: ${commission_amount:.2f}")

            onboard_credit = (commission_amount / 16 * percentage)
            logger.info(f"Using onboard credit percentage: {percentage}%")
            logger.info(
                f"Calculated onboard credit before rounding: ${onboard_credit:.2f}"
            )

            logger.info("Looking for cruise add-ons table to find OB codes")
            add_on_amounts = 0

            try:
                add_ons_tables = await page.locator(
                    "table:has(th:text('Component Type')):has(th:text('Code')):has(th:text('Guest Name'))"
                ).all()

                if add_ons_tables and len(add_ons_tables) > 0:
                    logger.info(f"Found {len(add_ons_tables)} potential add-ons tables")

                    for table_idx, table in enumerate(add_ons_tables):
                        logger.info(f"Processing add-ons table #{table_idx+1}")

                        rows = await table.locator("tbody tr").all()
                        logger.info(
                            f"Found {len(rows)} rows in add-ons table #{table_idx+1}"
                        )

                        logger.info(f"\n=== CRUISE ADD-ONS TABLE #{table_idx+1} ===")

                        header_cells = await table.locator("thead tr th").all()
                        code_column_idx = -1

                        for idx, header in enumerate(header_cells):
                            header_text = (await header.text_content()).strip()
                            if header_text == "Code":
                                code_column_idx = idx
                                logger.info(f"'Code' column found at index {idx}")
                                break

                        if code_column_idx == -1:
                            logger.warning(
                                "Could not find 'Code' column in table headers"
                            )
                            continue

                        for row_idx, row in enumerate(rows):
                            try:
                                cells = await row.locator("td").all()
                                component_type = (await row.locator("th").text_content()).strip(
                                ) if await row.locator("th").count() > 0 else "Unknown"

                                if len(cells) > code_column_idx:
                                    code_cell = cells[code_column_idx]
                                    code = (await code_cell.text_content()).strip()

                                    guest_name = (await cells[0].text_content()).strip(
                                    ) if len(cells) > 0 else "Unknown"

                                    logger.info(
                                        f"Row {row_idx+1}: Type={component_type}, Guest={guest_name}, Code={code}"
                                    )

                                    if code.startswith("OB"):
                                        import re  # Local import to ensure availability without affecting global scope
                                        match = re.match(r"OB(\d+(?:\.\d*)?)(?:[^\d.]|$)", code)
                                        if match:
                                            ob_value = float(match.group(1))
                                            logger.info(
                                                f"Found OB code: {code} with value: {ob_value}"
                                            )
                                            add_on_amounts += ob_value
                            except Exception as row_err:
                                logger.warning(
                                    f"Error processing row {row_idx+1}: {str(row_err)}"
                                )
                                continue

                if add_on_amounts == 0:
                    logger.info("Attempting direct HTML analysis as fallback")
                    html_content = await page.content()

                    import re
                    ob_matches = re.findall(
                        r'<td[^>]*>(\s*OB\d+(?:\.\d+)?\s*)</td>', html_content
                    )

                    if ob_matches:
                        logger.info(
                            f"Found {len(ob_matches)} OB codes via HTML analysis"
                        )

                        for match in ob_matches:
                            code = match.strip()
                            match_num = re.match(r"OB(\d+(?:\.\d*)?)(?:[^\d.]|$)", code)
                            if match_num:
                                ob_value = float(match_num.group(1))
                                logger.info(
                                    f"Found OB code via HTML: {code} with value: {ob_value}"
                                )
                                add_on_amounts += ob_value

                if add_on_amounts > 0:
                    logger.info(
                        f"Adding {add_on_amounts} from OB codes to onboard credit"
                    )
                    onboard_credit += add_on_amounts
                    logger.info(
                        f"Updated onboard credit before rounding: ${onboard_credit:.2f}"
                    )
                else:
                    logger.warning("No OB codes found in any tables or HTML analysis")
            except Exception as add_on_err:
                logger.warning(f"Error processing add-ons table: {str(add_on_err)}")
                logger.info("Continuing with original onboard credit calculation")

            def custom_round_to_5(amount):
                last_digit = int(amount) % 10
                base = int(amount) - last_digit

                if last_digit in [0, 5]:
                    return int(amount)
                elif last_digit in [1, 2, 3]:
                    return base
                elif last_digit == 4:
                    return base + 5
                elif last_digit in [6, 7, 8]:
                    return base + 5
                elif last_digit == 9:
                    return base + 10

            rounded_credit = custom_round_to_5(onboard_credit)
            logger.info(f"Rounded onboard credit: ${rounded_credit:.2f}")

            return rounded_credit, commission_amount

        except Exception as e:
            logger.error(f"Error calculating onboard credit: {str(e)}")
            return 0, 0

    @staticmethod
    async def extract_detailed_pricing(
        page,
        reservation_data,
        onboard_percentage=10,
        session_id=None,
        cabin_id=None,
        config=None
    ):

        try:
            logger.info("Extracting detailed pricing information")

            await page.wait_for_selector("#xslt", state="visible", timeout=30000)

            selected_category = reservation_data.get('selected_category', '')

            if 'selected_category' in reservation_data:
                del reservation_data['selected_category']
            if 'selected_promo_code' in reservation_data:
                del reservation_data['selected_promo_code']
            if 'total_cost' in reservation_data:
                del reservation_data['total_cost']
            if 'detailed_pricing' in reservation_data:
                del reservation_data['detailed_pricing']

            category_code = ""
            if selected_category:

                match = re.search(r'(\d+\.\s+)?([A-Z0-9]{1,3})\s+-', selected_category)
                if match:
                    category_code = match.group(2)
                    logger.info(
                        f"Extracted category code from selected_category: {category_code}"
                    )
                    reservation_data['category_code'] = category_code

            if not category_code:
                try:
                    category_headers = await page.locator(".category-heading, .bread-crumb"
                                                    ).all()

                    for header in category_headers:
                        header_text = await header.text_content()
                        header_text = header_text.strip()
                        if header_text:
                            match = re.search(
                                r"Category:\s*([A-Z0-9]{2,3})|^([A-Z0-9]{2,3})$",
                                header_text
                            )
                            if match:
                                category_code = match.group(1) or match.group(2)
                                logger.info(
                                    f"Found category code in header: {category_code}"
                                )
                                reservation_data['category_code'] = category_code
                            break

                    if not category_code:
                        page_content = await page.content()
                        patterns = [
                            r"Category(?::\s*|\s+)([A-Z0-9]{1,3})",
                            r"category[=\"\s]+([A-Z0-9]{1,3})",
                            r"Category\s*Code:?\s*([A-Z0-9]{1,3})",
                            r"cabin-category[^>]*>([A-Z0-9]{1,3})",
                            r"price-code[^>]*>([A-Z0-9]{1,3})"
                        ]

                        for pattern in patterns:
                            matches = re.findall(pattern, page_content)
                            if matches:
                                category_code = matches[0]
                                logger.info(
                                    f"Found category code using pattern '{pattern}': {category_code}"
                                )
                                reservation_data['category_code'] = category_code

                    if not category_code:
                        tab_elements = await page.locator(
                            ".tab-text, .breadcrumb-item, .nav-item"
                        ).all()
                        for tab in tab_elements:
                            tab_text = await tab.text_content()
                            tab_text = tab_text.strip()
                            if tab_text:
                                match = re.search(r'\b([A-Z0-9]{1,3})\b', tab_text)
                                if match:
                                    category_code = match.group(1)
                                    logger.info(
                                        f"Found category code in tab: {category_code}"
                                    )
                                    reservation_data['category_code'] = category_code

                except Exception as e:
                    logger.warning(f"Could not extract category code: {str(e)}")

            if category_code and len(category_code) > 0:
                category_code = category_code.strip().upper()
                reservation_data['category_code'] = category_code
                logger.info(f"Final category code: {category_code}")

            percentage_to_use = onboard_percentage

            if config and 'ncl_category_percentages' in config:
                category_percentages = config['ncl_category_percentages']

                cabin_category_type = None

                if category_code:
                    for category_type, codes in CabinCategories.CABIN_RATE_CODES.items(
                    ):
                        if category_code.upper() in codes:
                            if category_type == 'mini-suite':
                                cabin_category_type = 'junior_suite'
                            elif category_type == 'haven':
                                cabin_category_type = 'suite'
                            else:
                                cabin_category_type = category_type
                            break

                if not cabin_category_type:
                    category_text = selected_category.lower(
                    ) if selected_category else category_code.lower(
                    ) if category_code else ""

                    if any(keyword in category_text
                           for keyword in ['inside', 'interior']):
                        cabin_category_type = 'inside'
                    elif any(keyword in category_text
                             for keyword in ['outside', 'oceanview', 'ocean']):
                        cabin_category_type = 'outside'
                    elif any(keyword in category_text
                             for keyword in ['balcony', 'verandah', 'veranda']):
                        cabin_category_type = 'balcony'
                    elif any(keyword in category_text
                             for keyword in ['junior suite', 'jr suite', 'mini suite']):
                        cabin_category_type = 'junior_suite'
                    elif any(keyword in category_text
                             for keyword in ['suite', 'penthouse']):
                        cabin_category_type = 'suite'

                if cabin_category_type and cabin_category_type in category_percentages:
                    percentage_to_use = category_percentages[cabin_category_type]
                    logger.info(
                        f"Using category-specific percentage for {cabin_category_type}: {percentage_to_use}%"
                    )
                    reservation_data['cabin_category_type'] = cabin_category_type
                    reservation_data['onboard_percentage_used'] = percentage_to_use
                else:
                    logger.info(
                        f"Using default percentage: {percentage_to_use}% (category_type: {cabin_category_type}, category_code: {category_code})"
                    )
            else:
                logger.info(
                    f"No category-specific config found, using default percentage: {percentage_to_use}%"
                )

            try:
                logger.info("Extracting invoice table data")
                await page.wait_for_selector(
                    "#xslt > div > div > div:nth-child(2)",
                    state="visible",
                    timeout=15000
                )

                invoice_table = page.locator(
                    "#xslt > div > div > div:nth-child(2) table"
                )
                invoice_rows = await invoice_table.locator("tr").all()

                reservation_total = None
                for row in invoice_rows:
                    try:
                        cells = await row.locator("td").all()
                        headers = await row.locator("th").all()

                        if headers and len(
                                headers) > 0 and "Reservation Total" in await headers[
                                    0].text_content() and cells and len(cells) >= 3:
                            reservation_total = (await cells[2].text_content()).strip()
                            logger.info(f"Found Reservation Total: {reservation_total}")
                            reservation_data['reservation_total_raw'
                                             ] = reservation_total
                            try:
                                reservation_data['reservation_total'] = float(
                                    reservation_total.replace('$', '').replace(',', '')
                                )
                            except ValueError:
                                logger.warning(
                                    f"Could not convert reservation total to float: {reservation_total}"
                                )
                    except Exception as row_err:
                        logger.warning(f"Error parsing invoice row: {str(row_err)}")
            except Exception as e:
                logger.warning(f"Error extracting invoice table: {str(e)}")

            price_map = {
                'VOYAGE FARE including NON-COMM FARE': 'voyage_fare', 'NON-COMM FARE':
                'non_comm_fare', 'SAVINGS': 'savings', 'INSURANCE': 'insurance',
                'GOVERNMENT TAX': 'govt_tax', 'RESERVATION TOTAL': 'reservation_total',
                'MAS DIN PACKAGE': 'dining_package', 'MAS BEV PACKAGE':
                'beverage_package', 'SODA BEV PKG': 'soda_package'
            }

            try:
                pricing_items = {}

                pricing_rows = await page.locator(
                    "//tr[th[contains(text(), 'VOYAGE') or contains(text(), 'SAVINGS') or contains(text(), 'INSURANCE') or contains(text(), 'PACKAGE') or contains(text(), 'TAX') or contains(text(), 'NON-COMM') or contains(text(), 'SODA') or contains(text(), 'Reservation Total')]]"
                ).all()

                if pricing_rows:
                    logger.info(f"Found {len(pricing_rows)} pricing rows")

                    for row in pricing_rows:
                        try:
                            label_elem = row.locator("th").first
                            if await label_elem.count() > 0:
                                label = (await label_elem.text_content()).strip()

                                total_cells = await row.locator("td.right").all()
                                if total_cells:
                                    total_cell = total_cells[-1]
                                    amount_str = (await total_cell.text_content()).strip()

                                    logger.info(
                                        f"Found pricing item: {label} = {amount_str}"
                                    )

                                    for key_pattern, data_key in price_map.items():
                                        if key_pattern in label.upper():
                                            pricing_items[f"{data_key}_raw"
                                                          ] = amount_str

                                            try:
                                                clean_amount = amount_str.replace(
                                                    '$', ''
                                                ).replace(',', '')
                                                if clean_amount.startswith(
                                                        '(') and clean_amount.endswith(
                                                            ')'):
                                                    clean_amount = "-" + clean_amount[
                                                        1:-1]

                                                amount_value = float(clean_amount)
                                                pricing_items[data_key] = amount_value
                                            except ValueError:
                                                logger.warning(
                                                    f"Could not convert {amount_str} to number for {data_key}"
                                                )

                        except Exception as row_err:
                            logger.warning(f"Error parsing pricing row: {str(row_err)}")
                            continue

                if pricing_items:
                    logger.info(
                        f"Updating reservation data with {len(pricing_items)} pricing items"
                    )
                    reservation_data.update(pricing_items)

                if 'reservation_total' in reservation_data and 'reservation_total_raw' in reservation_data:
                    logger.info(
                        f"Using confirmed reservation total: {reservation_data['reservation_total_raw']}"
                    )
            except Exception as e:
                logger.warning(f"Error extracting pricing details: {str(e)}")

                logger.info("No pricing table found, trying alternate methods")

            onboard_credit, commission_amount = await CruiseSelection.calculate_onboard_credit(
                page, percentage_to_use
            )
            if onboard_credit > 0:
                reservation_data['onboard_credit'] = onboard_credit
                reservation_data['commission_amount'] = commission_amount

            try:
                if 'reservation_total' in reservation_data:
                    reservation_total = reservation_data['reservation_total']
                    dining_package = reservation_data.get('dining_package', 0)
                    beverage_package = reservation_data.get('beverage_package', 0)
                    soda_package = reservation_data.get('soda_package', 0)

                    final_price = reservation_total - dining_package - beverage_package - soda_package
                    reservation_data['final_price'] = final_price
                    reservation_data['final_price_raw'] = f"${final_price:,.2f}"

                    logger.info(
                        f"Calculated final price: ${final_price:,.2f} = ${reservation_total:,.2f} - ${dining_package:,.2f} - ${beverage_package:,.2f} - ${soda_package:,.2f}"
                    )
            except Exception as calc_err:
                logger.error(f"Error calculating final price: {str(calc_err)}")

            try:
                known_promo_patterns = [
                    r'ALL4CHO\d?', r'DISC\d+', r'EASYFARE', r'SAILAWAY', r'SAILSHOX',
                    r'FITOBC', r'STRATOBC'
                ]

                promos_found = []

                for selector in ['#locked_list', '.promo-code', '.program-code',
                                 '.program-name']:
                    try:
                        promo_elements = await page.locator(selector).all()
                        for elem in promo_elements:
                            text = (await elem.text_content()).strip()
                            if text:
                                for pattern in known_promo_patterns:
                                    matches = re.findall(pattern, text)
                                    if matches:
                                        promos_found.extend(matches)
                    except:
                        continue

                if not promos_found:
                    price_program_elements = await page.locator(
                        ".slick-row .l0, .slick-row .l1, .promo-item, .program-item"
                    ).all()

                    for elem in price_program_elements:
                        text = (await elem.text_content()).strip()
                        if text:
                            for pattern in known_promo_patterns:
                                matches = re.findall(pattern, text)
                                if matches:
                                    promos_found.extend(matches)

                if not promos_found:
                    body_text = await page.locator("body").text_content()
                    for pattern in known_promo_patterns:
                        matches = re.findall(pattern, body_text)
                        if matches:
                            promos_found.extend(matches)

                if promos_found:
                    unique_promos = list(set(promos_found))
                    reservation_data['current_promos'] = unique_promos
                    logger.info(f"Found promo codes: {unique_promos}")
                else:
                    reservation_data['current_promos'] = []
                    logger.warning("No promo codes found")

            except Exception as promo_err:
                logger.warning(f"Error extracting promo codes: {str(promo_err)}")
                if 'current_promos' not in reservation_data:
                    reservation_data['current_promos'] = []

            if 'category_code' in reservation_data:
                logger.info(
                    f"Category code in final data: {reservation_data['category_code']}"
                )
            else:
                if selected_category and 'category_code' not in reservation_data:
                    match = re.search(
                        r'(\d+\.\s+)?([A-Z0-9]{1,3})\s+-', selected_category
                    )
                    if match:
                        category_code = match.group(2)
                        logger.info(
                            f"Re-extracted category code from selected_category: {category_code}"
                        )
                        reservation_data['category_code'] = category_code
                    else:
                        logger.warning("Category code not found in final data")

            ui_log(
                "Price retrieved successfully — thanks for waiting",
                session_id=session_id,
                cabin_id=cabin_id,
                module="NCL",
                step="pricing"
            )

            return reservation_data

        except Exception as e:
            logger.error(f"Error extracting detailed pricing: {str(e)}")
            return None

    @staticmethod
    async def initialize_driver(session_id=None, cabin_id=None):
        NCL_Login = NCLLogin()
        return await NCL_Login.setup_driver(session_id=session_id, cabin_id=cabin_id)

    @staticmethod
    async def login_to_ncl(page, session_id=None, cabin_id=None):
        NCL_Login = NCLLogin()
        credentials = NCL_Login.get_credentials()
        return await NCL_Login.login(
            page,
            credentials["url"],
            credentials["username"],
            credentials["password"],
            session_id=session_id,
            cabin_id=cabin_id
        )

    @staticmethod
    def create_reservation_container(cruise_details):
        return {
            "cruise_details": {
                "ship_name": cruise_details.get('ship_name', ''),
                "travel_date": cruise_details.get('travel_date', ''),
                "nights": cruise_details.get('nights', 0),
            }, "passengers": {
                "total": cruise_details.get('total_passengers', 0), "adults":
                cruise_details.get('adults',
                                   0), "children": cruise_details.get('children', 0),
                "infants": cruise_details.get('infants', 0)
            }, "selected_category": "", "selected_promo_code": "", "total_cost": "0",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    @staticmethod
    async def navigate_to_cruise_listing(page):
        from NCL.reservation import Reservation
        reservation = Reservation()
        return await reservation.click_new_reservation_and_continue(page)

    @staticmethod
    async def update_cruise_details(page, reservation_data):

        onboard_percentage = 10

        try:
            onboard_credit, commission_amount = await CruiseSelection.calculate_onboard_credit(
                page, onboard_percentage
            )

            reservation_total = 0.0
            try:
                total_elem = page.locator(
                    "#SWXMLForm_Pricing_reservationTotal, .reservation-total, [id*='total']"
                ).first
                if await total_elem.count() > 0:
                    reservation_total_text = await total_elem.text_content()
                    reservation_total_text = reservation_total_text.strip()
                    reservation_total = float(
                        reservation_total_text.replace('$', '').replace(',', '')
                    )
            except Exception:
                reservation_total = commission_amount * 2

            reservation_data["total_cost"] = float(reservation_total)
            reservation_data["final_price"] = float(reservation_total)
            reservation_data["onboard_credit"] = float(onboard_credit)
            reservation_data["commission_amount"] = float(commission_amount)

            reservation_data["formatted_total_cost"] = f"${reservation_total:.2f}"
            reservation_data["formatted_final_price"] = f"${reservation_total:.2f}"
            reservation_data["formatted_onboard_credit"] = f"${onboard_credit:.2f}"

            detailed_pricing = await CruiseSelection.extract_detailed_pricing(
                page, reservation_data, onboard_percentage
            )
            reservation_data["detailed_pricing"] = detailed_pricing

            logger.info(
                f"Updated reservation data with total cost: ${reservation_total:.2f}"
            )
            logger.info(
                f"Onboard credit ({onboard_percentage}%): ${onboard_credit:.2f}"
            )
            logger.info(f"Commission amount: ${commission_amount:.2f}")

        except Exception as e:
            logger.error(f"Error updating cruise details: {str(e)}")

        return reservation_data

    @staticmethod
    async def process_single_cabin(page, reservation_data, cabin_id=1):

        try:
            extracted_pricing_data = await CruiseSelection.extract_detailed_pricing(
                page, reservation_data.copy(), onboard_percentage=10
            )

            extracted_pricing_data['cabin_id'] = cabin_id

            logger.info(
                f"[Cabin {cabin_id}] Extracted pricing data: {extracted_pricing_data}"
            )

            data_to_save = copy.deepcopy(extracted_pricing_data)

            NCL_Utils = NCLUtils()
            logger.info(
                f"[Cabin {cabin_id}] Saving ONLY the extracted pricing data to database"
            )
            NCL_Utils.save_reservation_data(data_to_save, cabin_id)

            await CruiseSelection.update_cruise_details(page, reservation_data)

            return reservation_data

        except Exception as e:
            logger.error(f"Error processing cabin: {str(e)}")
            return None

    @staticmethod
    async def cruise_reservation_process(cruise_details=None, session_id=None):
        try:
            reservation_data = CruiseSelection.create_reservation_container(
                cruise_details
            )
            if not reservation_data:
                logger.error("Failed to create reservation container")
                return None

            cabin_id = 1

            if not session_id:
                import uuid
                session_id = str(uuid.uuid4())

            page = await CruiseSelection.initialize_driver(
                session_id=session_id, cabin_id=cabin_id
            )
            ui_log(
                "Browser setup complete — ready to move forward",
                session_id=session_id,
                cabin_id=cabin_id,
                module="NCL",
                step="setup"
            )

            logger.info("Logging in to NCL")
            if not await CruiseSelection.login_to_ncl(page, session_id=session_id,
                                                      cabin_id=cabin_id):
                logger.error("Failed to login")
                return None

            result = await CruiseSelection.process_single_cabin(page, reservation_data)
            return result

        except Exception as e:
            logger.error(f"Error in cruise reservation process: {str(e)}")
            ui_log(
                "There was an error during the booking process — please try again",
                session_id=session_id,
                module="NCL",
                step="booking"
            )
            return None