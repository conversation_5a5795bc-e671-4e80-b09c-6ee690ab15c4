"""
Main module for cruise booking automation in the Studio system.

This module provides the core functionality for processing cruise quotes, searching for
available cabins, and automating the cabin selection process. It orchestrates the
entire booking flow including initialization of browser sessions, parsing of cruise details,
matching of cabin categories, and parallel processing of multiple cabin bookings.

The module includes performance tracking capabilities to measure execution times and
identify potential bottlenecks in the booking workflow.
"""

import asyncio
import logging
import os
import time
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List

from dotenv import load_dotenv
import google.generativeai as genai

from Studio.Quote_Processor import QuoteProcessor
from Studio.base_scraper import BaseScraper
from Studio.cruise_scraper import CruiseScraper
from Core.ui_logger import ui_log

# Load environment variables from .env file
load_dotenv()

# Initialize Gemini AI for category matching
try:
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
    if not GEMINI_API_KEY:
        logging.error("GEMINI_API_KEY not found in environment variables")
        category_genai_model = None
    else:
        # Configure the Gemini API with the API key
        genai.configure(api_key=GEMINI_API_KEY)

        # Set up deterministic configuration for consistent results
        category_generation_config = {
            "temperature": 0,
            "max_output_tokens": 8192,
            "response_mime_type": "text/plain",
        }

        # Initialize the Gemini model with the flash variant for faster responses
        category_genai_model = genai.GenerativeModel(
            model_name="gemini-2.0-flash", generation_config=category_generation_config
        )
        logging.info("Gemini API initialized for category matching")
except Exception as e:
    logging.error(f"Failed to initialize Gemini API: {e}")
    category_genai_model = None


class ExecutionTimer:
    """
    Timer class to track execution times and performance metrics for booking operations.
    
    This class provides methods to track the overall execution time of the booking process,
    record specific delay points, and capture detailed timing metrics for individual cabin
    booking operations. This helps identify bottlenecks and optimize performance.
    
    Attributes:
        start_time (float): Timestamp when the timer was started
        end_time (float): Timestamp when the timer was stopped
        cabin_timings (Dict): Dictionary mapping cabin numbers to timing data
        delay_points (List): List of delay events with timestamps and durations
    """

    def __init__(self):
        """Initialize the ExecutionTimer with empty tracking containers."""
        self.start_time = None
        self.end_time = None
        self.cabin_timings: Dict[int, Dict[str, float]] = {}
        self.delay_points: List[Dict[str, any]] = []

    def start(self):
        """
        Start the execution timer and record the initial timestamp.
        
        This marks the beginning of the overall process and creates the first
        delay point with zero delay.
        """
        self.start_time = time.time()
        self.delay_points.append(
            {'timestamp': datetime.now(), 'event': 'Process Started', 'delay': 0}
        )

    def end(self):
        """
        End the execution timer and record the final timestamp.
        
        This marks the completion of the overall process and creates the final
        delay point with zero delay.
        """
        self.end_time = time.time()
        self.delay_points.append(
            {'timestamp': datetime.now(), 'event': 'Process Completed', 'delay': 0}
        )

    def add_delay_point(self, event: str, delay: float):
        """
        Record a delay point in the execution process.
        
        This method tracks specific events that caused delays in the booking process,
        enabling identification of performance bottlenecks.
        
        Args:
            event (str): Description of the delay event
            delay (float): Duration of the delay in seconds
        """
        self.delay_points.append(
            {'timestamp': datetime.now(), 'event': event, 'delay': delay}
        )

    def record_cabin_timing(self, cabin_number: int, operation: str, duration: float):
        """
        Record the execution time for a specific cabin operation.
        
        This method tracks how long different operations take for each cabin,
        allowing for detailed performance analysis.
        
        Args:
            cabin_number (int): The cabin number being processed
            operation (str): The name of the operation being timed
            duration (float): Duration of the operation in seconds
        """
        if cabin_number not in self.cabin_timings:
            self.cabin_timings[cabin_number] = {}
        self.cabin_timings[cabin_number][operation] = duration

    def get_total_execution_time(self) -> float:
        """
        Calculate the total execution time of the process.
        
        Returns:
            float: Total execution time in seconds, or 0 if timer wasn't properly started/stopped
        """
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0

    def get_total_delay(self) -> float:
        """
        Calculate the total time spent in known delay points.
        
        Returns:
            float: Sum of all recorded delays in seconds
        """
        return sum(point['delay'] for point in self.delay_points)

    def get_cabin_summary(self) -> Dict[int, float]:
        """
        Calculate the total processing time for each cabin.
        
        Returns:
            Dict[int, float]: Dictionary mapping cabin numbers to their total processing time
        """
        return {
            cabin: sum(timings.values())
            for cabin, timings in self.cabin_timings.items()
        }

    def print_execution_summary(self):
        """
        Print a formatted summary of execution timings and delays.
        
        This method outputs a detailed performance report including total execution time,
        delay time, per-cabin processing times, and significant delay points.
        """
        total_execution = self.get_total_execution_time()
        total_delay = self.get_total_delay()
        cabin_times = self.get_cabin_summary()

        print("\n" + "=" * 50)
        print("EXECUTION SUMMARY")
        print("=" * 50)

        print(f"\nTotal Execution Time: {timedelta(seconds=int(total_execution))}")
        print(f"Total Delay Time: {timedelta(seconds=int(total_delay))}")
        print(
            f"Net Processing Time: {timedelta(seconds=int(total_execution - total_delay))}"
        )

        if cabin_times:
            print("\nPer-Cabin Processing Times:")
            for cabin, duration in sorted(cabin_times.items()):
                print(f"Cabin {cabin}: {timedelta(seconds=int(duration))}")

        if self.delay_points:
            print("\nSignificant Delay Points:")
            for point in self.delay_points:
                if point['delay'] > 0:
                    print(
                        f"{point['timestamp'].strftime('%H:%M:%S')} - "
                        f"{point['event']}: {timedelta(seconds=int(point['delay']))}"
                    )

        print("\n" + "=" * 50)


class StudioManager:
    """
    Main class for Studio module operations and cruise booking orchestration.
    
    This class provides methods to process cruise quote details, construct search URLs,
    match cabin categories, and coordinate the overall booking process including parallel
    cabin processing. It implements various strategies for optimal category selection
    and cabin matching.
    """

    @staticmethod
    def setup_main_logging():
        """
        Set up the main logging configuration for the application.
        
        Creates a logger with console handler to ensure
        comprehensive logging of operations.
        
        Returns:
            logging.Logger: Configured logger instance
        """
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        logger = logging.getLogger('main')
        logger.propagate = False
        return logger

    @staticmethod
    async def calculate_total_delay(func, *args, **kwargs):
        """
        Execute an async function and calculate the time spent.
        
        This utility method measures how long an async function takes to execute,
        useful for tracking performance bottlenecks.
        
        Args:
            func (callable): The async function to execute and time
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            tuple: (Result of function call, time elapsed in seconds)
        """
        start_time = time.time()
        result = await func(*args, **kwargs)
        end_time = time.time()
        return result, end_time - start_time

    @staticmethod
    def format_date(input_date):
        """
        Format a date string for use in URL construction.
        
        Converts a date in MM-DD-YYYY format to the URL-friendly MM%2FDD%2FYYYY format
        required by the booking system.
        
        Args:
            input_date (str): Date string in MM-DD-YYYY format
            
        Returns:
            str: Formatted date string for URL use
            
        Raises:
            Exception: If the date format is invalid
        """
        try:
            # Split the date into components and ensure zero-padding
            month, day, year = input_date.split('-')
            # Format with URL encoding (%2F is the encoded forward slash)
            return f"{month.zfill(2)}%2F{day.zfill(2)}%2F{year}"
        except Exception as e:
            logging.error("Invalid date format. Use MM-DD-YYYY.")
            raise e

    @staticmethod
    def get_cruise_details():
        """
        Interactively collect cruise details from user input.
        
        Prompts the user to enter cruise quote information and processes it to extract
        the necessary booking parameters.
        
        Returns:
            dict: Extracted cruise details including sailing date, duration, ship ID, etc.
        """
        input_lines = []
        print(
            "\nEnter the cruise details quote to extract inputs (end with a blank line):"
        )
        # Read multiple lines of input until an empty line is entered
        while True:
            try:
                line = input()
                if line.strip() == "":
                    break
                input_lines.append(line)
            except EOFError:
                break

        # Join all input lines and process the complete quote
        quote = " ".join(input_lines)
        return StudioManager.process_cruise_details(quote)

    @staticmethod
    def process_cruise_details(quote):
        """
        Process a cruise quote to extract booking details.
        
        Uses the QuoteProcessor to parse the quote text and extract structured
        booking information.
        
        Args:
            quote (str): The full text of the cruise quote
            
        Returns:
            dict: Structured cruise details for booking
        """
        extracted_inputs = QuoteProcessor.extract_cruise_details(quote)
        return StudioManager.get_cruise_details_from_input(extracted_inputs)

    @staticmethod
    def get_cruise_details_from_input(extracted_details):
        """
        Convert extracted details list into a structured dictionary.
        
        Takes the list of extracted details from QuoteProcessor and organizes them
        into a dictionary with named fields for easier access.
        
        Args:
            extracted_details (list): List of extracted cruise details
            
        Returns:
            dict: Dictionary with organized cruise details
            
        Raises:
            ValueError: If required information is missing
        """
        # Check if any required fields are missing
        if any("not found" in str(field) for field in extracted_details):
            raise ValueError("Unable to extract all required inputs from the quote")

        # Unpack the extracted details
        sailing_date, duration, ship_id, residency_airport, num_cabins, *details = extracted_details

        # Get passenger counts and category types
        num_cabins_int = int(num_cabins)
        passengers = list(map(int, details[:num_cabins_int]))
        category_types = [
            category.strip().upper() for category in details[num_cabins_int:]
        ]

        # Organize details into a structured dictionary
        return {
            'sailing_date':
            StudioManager.format_date(sailing_date), 'duration':
            duration, 'ship_id':
            ship_id, 'residency_airport':
            residency_airport, 'num_cabins':
            int(num_cabins), 'passengers':
            list(map(int, details[:int(num_cabins)])), 'category_types':
            [category.strip().upper() for category in details[int(num_cabins):]]
        }

    @staticmethod
    def get_cabin_details(cabin_number, num_passengers):
        """
        Create a standardized cabin details dictionary.
        
        Args:
            cabin_number (int): The cabin number
            num_passengers (int): Number of passengers in this cabin
            
        Returns:
            dict: Cabin details dictionary
        """
        return {'cabin_number': cabin_number, 'num_passengers': num_passengers}

    @staticmethod
    def construct_passenger_params(num_passengers, residency_airport):
        """
        Construct URL parameters for passenger information.
        
        Builds the URL query string parameters for passenger details required
        by the booking system.
        
        Args:
            num_passengers (int): Number of passengers
            residency_airport (str): Airport code for passenger residency
            
        Returns:
            str: URL query string for passenger parameters
        """
        params = []
        # Generate parameters for each passenger
        for i in range(num_passengers):
            # All passengers are adults by default
            passenger_type = "ADT"
            # Format: passengers[1][1][type]=ADT&passengers[1][1][residency_airport]=MIA
            params.append(f"&passengers[1][{i+1}][type]={passenger_type}")
            params.append(
                f"&passengers[1][{i+1}][residency_airport]={residency_airport}"
            )
        return "".join(params)

    @staticmethod
    def group_identical_cabins(cruise_details):
        """
        Group identical cabin requests to optimize processing.
        
        Identifies cabins with the same passenger count and category type to
        process them as a group, reducing redundant operations.
        
        Args:
            cruise_details (dict): Dictionary with cruise booking details
            
        Returns:
            list: List of tuples (passengers, category_type, count) for grouped cabins
        """
        cabin_groups = {}

        # Group cabins by passenger count and category type
        for i in range(cruise_details['num_cabins']):
            key = (cruise_details['passengers'][i], cruise_details['category_types'][i])
            cabin_groups[key] = cabin_groups.get(key, 0) + 1

        # Convert to list of tuples (passengers, category_type, count)
        return [(*key, count) for key, count in cabin_groups.items()]

    @staticmethod
    def match_category_with_gemini(user_input, available_categories):
        """
        Use Gemini AI to match user-specified category to available options.
        
        This method leverages the Gemini AI model to intelligently match
        the user's requested cabin category with the actual available options.
        
        Args:
            user_input (str): User's specified cabin category
            available_categories (list): List of available category dictionaries
            
        Returns:
            str: The matched category code, or default category if no match
        """
        # If AI model or categories not available, use default
        if not category_genai_model or not available_categories:
            logging.warning("Gemini model unavailable - using default category")
            return available_categories[0]['code'] if available_categories else None

        try:
            # Create a formatted list of available categories for the AI to match against
            category_data = "\n".join(
                [f"{cat['title']} | {cat['code']}" for cat in available_categories]
            )

            # Construct a prompt for the AI to match the category
            prompt = f"""Match '{user_input}' to FIRST matching cabin category code:

{category_data}

Critical rules:
1. Categories are listed in price order (cheapest first)
2. ALWAYS select the FIRST matching category from the list
3. For "ocean balcony"/"oceanview balcony" inputs, match to first category containing both terms
4. Return only the category code (e.g., XB, 2E)
5. If no match, return {available_categories[0]['code']}"""

            # Generate the response from the AI model
            response = category_genai_model.generate_content(prompt)
            response_text = response.text.strip()
            logging.info(f"Category match result: {response_text}")

            # Validate that the returned code exists in the available categories
            if any(cat['code'] == response_text for cat in available_categories):
                return response_text
            else:
                logging.warning(f"Invalid category code returned: {response_text}")
                # Fallback to the first (usually cheapest) category
                return available_categories[0]['code']

        except Exception as e:
            logging.error(f"Category matching error: {e}")
            # Fallback to the first category if available
            return available_categories[0]['code'] if available_categories else None

    @staticmethod
    def select_optimal_category(requested_category, available_categories):
        """
        Select the optimal cabin category based on availability and preferences.
        
        This method implements intelligent category selection logic:
        1. If the requested category is available, it uses that
        2. If not available, it looks for upgrades
        3. If no upgrades, it looks for downgrades
        4. It also considers better categories at lower prices
        
        Args:
            requested_category (str): The requested cabin category
            available_categories (list): List of available category names
            
        Returns:
            str: The selected optimal category
        """
        # Standard ordered category types from lowest to highest quality
        standard_categories = ["INSIDE", "OCEANVIEW", "BALCONY", "JR SUITE", "SUITE"]

        # If requested category is non-standard, return it directly
        if requested_category not in standard_categories:
            return requested_category

        # If no available categories, return the requested one
        if not available_categories:
            return requested_category

        # Find the position of the requested category in the standard hierarchy
        requested_index = standard_categories.index(requested_category)

        # Helper function to find better categories available at cheaper prices
        def find_better_cheaper_option(selected_category, selected_position):
            selected_index = standard_categories.index(selected_category)

            # Look for better categories than what we've selected
            for i in range(selected_index + 1, len(standard_categories)):
                better_category = standard_categories[i]
                if better_category in available_categories:
                    better_position = available_categories.index(better_category)

                    # If the better category appears earlier in the list, it's cheaper
                    if better_position < selected_position:
                        logging.info(
                            f"Found better category at lower price: {better_category}"
                        )
                        return better_category
            return selected_category

        # Case 1: If requested category is available
        if requested_category in available_categories:
            requested_position = available_categories.index(requested_category)
            # Check if there's a better category at a lower price
            return find_better_cheaper_option(requested_category, requested_position)

        # Case 2: Look for an upgrade (better category)
        # Skip automatic upgrade when requested category is JR SUITE –
        # decision will be taken later once we know sub-category prices.
        if requested_category != "JR SUITE":
            for i in range(requested_index + 1, len(standard_categories)):
                upper_category = standard_categories[i]
                if upper_category in available_categories:
                    upper_position = available_categories.index(upper_category)
                    logging.info(
                        f"Using upgrade to {upper_category} (requested {requested_category})"
                    )
                    # Check if there's an even better category at a lower price
                    return find_better_cheaper_option(upper_category, upper_position)

        # Case 3: Look for a downgrade (lower category)
        for i in range(requested_index - 1, -1, -1):
            lower_category = standard_categories[i]
            if lower_category in available_categories:
                lower_position = available_categories.index(lower_category)
                logging.info(
                    f"Using downgrade to {lower_category} (requested {requested_category})"
                )
                # Check if there's a better category at a lower price
                return find_better_cheaper_option(lower_category, lower_position)

        # Case 4: No match found, use the first available category
        logging.warning(
            f"No matching categories - using default: {available_categories[0]}"
        )
        return available_categories[0]

    @staticmethod
    async def process_single_cabin(
        browser_type,
        session_id,
        cruise_details,
        cabin_info,
        category,
        request_id,
        rate_selection_strategy='Cheapest',
        video_auditing=False,
        provider='Studio.Sales.CabinCloseOut'
    ):
        """
        Process a single cabin booking from start to finish.
        
        This method handles the complete booking flow for a single cabin:
        1. Initializes the scraper
        2. Loads the booking page
        3. Finds available categories
        4. Matches the requested category
        5. Selects the cabin and rate
        
        Args:
            browser_type (str): The browser to use (e.g., 'firefox', 'chromium')
            session_id (str): Unique session identifier
            cruise_details (dict): Dictionary with cruise booking details
            cabin_info (dict): Dictionary with cabin details
            category (str): Requested cabin category
            request_id (str): Unique request identifier
            rate_selection_strategy (str): Strategy for selecting rates
            video_auditing (bool): Whether to enable video recording for audit purposes
            provider (str): The provider for the cabin booking
            
        Returns:
            dict: Result of the cabin selection process with pricing info
            
        Raises:
            ValueError: If required information cannot be obtained
            Exception: For other processing errors
        """
        # Initialize the scraper with the specified browser and session
        scraper = CruiseScraper(request_id, browser_type, session_id, provider=provider)
        scraper.rate_selection_strategy = rate_selection_strategy
        try:
            # Construct the base URL for the cruise
            base_url = scraper.construct_base_url(
                cruise_details['sailing_date'], cruise_details['duration'],
                cruise_details['ship_id']
            )
            # Add passenger parameters to the URL
            passenger_params = StudioManager.construct_passenger_params(
                cabin_info['num_passengers'], cruise_details['residency_airport']
            )
            url = base_url + passenger_params

            logging.info(f"Processing cabin {cabin_info['cabin_number']} ({category})")
            
            # Initialize the browser and setup driver with video auditing configuration
            # Pass the cabin_id to the setup_driver method
            cabin_id = cabin_info['cabin_number']
            await scraper.setup_driver(headless=True, video_auditing=video_auditing, cabin_id=cabin_id)
            
            # Load the URL in the browser
            await scraper.load_url(url)

            # Get available cabin categories using retry logic to handle potential failures
            available_categories = await BaseScraper.retry_with_delay(
                lambda: scraper.get_available_category_types(cabin_info['cabin_number']),
                max_retries=2,
                delay=5,
                increasing_delay=True
            )

            if not available_categories:
                raise ValueError("Failed to fetch available categories")

            # Process the specified category to find the best match
            original_requested = category.upper()
            specified_category = original_requested  # may be adjusted below
            standard_categories = ["INSIDE", "OCEANVIEW", "BALCONY", "JR SUITE", "SUITE"]
            is_category_code = False

            # Case 1: Standard category type (e.g., "BALCONY")
            if specified_category in standard_categories:
                logging.info(f"Processing standard category: {specified_category}")

                # Special handling: if JR SUITE requested and SUITE is available, treat as SUITE panel
                if original_requested == "JR SUITE" and "SUITE" in available_categories:
                    optimal_category = "SUITE"
                    logging.info("JR Suite requested – using SUITE panel to inspect sub-categories")
                else:
                    # Use existing optimal-category logic
                    optimal_category = StudioManager.select_optimal_category(
                        specified_category, available_categories
                    )

                if optimal_category != specified_category:
                    logging.info(
                        f"Optimized selection: {specified_category} → {optimal_category}"
                    )

                specified_category = optimal_category
            else:
                # Case 2: Non-standard category - could be a code or specialized name
                if specified_category not in available_categories:
                    # Check if it's likely a category code (1-2 characters, alphanumeric)
                    if len(specified_category) <= 2 and len(specified_category) >= 1:
                        if len(specified_category
                               ) == 1 and specified_category.isalpha():
                            is_category_code = True
                        elif len(specified_category) == 2:
                            if specified_category.isalpha() or \
                              (specified_category[0].isalpha() and specified_category[1].isdigit()) or \
                              (specified_category[0].isdigit() and specified_category[1].isalpha()):
                                is_category_code = True

                    # If not a category code, try AI matching
                    if not is_category_code:
                        logging.info(
                            f"Attempting to match category: '{specified_category}'"
                        )

                        # Get detailed category information
                        all_category_info = await BaseScraper.retry_with_delay(
                            lambda: scraper.get_category_info(),
                            max_retries=2,
                            delay=5,
                            increasing_delay=True
                        )

                        if all_category_info:
                            # Use AI to match the category
                            matched_category_code = StudioManager.match_category_with_gemini(
                                specified_category, all_category_info
                            )

                            if matched_category_code:
                                logging.info(
                                    f"Matched to category code: {matched_category_code}"
                                )
                                is_category_code = True
                                specified_category = matched_category_code
                            else:
                                logging.warning(
                                    f"No match found - using default category"
                                )
                                specified_category = available_categories[
                                    0] if available_categories else None
                        else:
                            logging.warning("Failed to get category information")
                            specified_category = available_categories[
                                0] if available_categories else None

                    # Final fallback if no match found
                    if not is_category_code and not specified_category:
                        logging.warning(f"Requested category unavailable: {category}")
                        specified_category = available_categories[
                            0] if available_categories else None

            # Ensure we have a valid category to proceed
            if not specified_category:
                raise ValueError(
                    f"No categories available for cabin {cabin_info['cabin_number']}"
                )

            # Process the booking based on whether we have a category code or type
            if is_category_code:
                logging.info(f"Using category code: {specified_category}")
                # Process with direct category code
                result = await scraper.process_cabin_selection(
                    "UNKNOWN", specified_category, cabin_info['cabin_number']
                )
                if result:
                    result['passenger_count'] = cabin_info['num_passengers']
                return result
            else:
                # Get specific category codes for the selected category type
                category_codes = await BaseScraper.retry_with_delay(
                    lambda: scraper.get_category_codes(specified_category),
                    max_retries=2,
                    delay=5,
                    increasing_delay=True
                )

                if not category_codes:
                    raise ValueError(
                        f"No category codes found for {specified_category}"
                    )

                # Smart selection for Suite vs JR Suite distinction
                if original_requested == "JR SUITE":
                    # JR Suite requested – we are looking inside the SUITE panel
                    suite_keywords = ["JR", "JUNIOR", "MINI"]
                    cheapest_title = category_codes[0].get("title", "").upper()
                    if any(k in cheapest_title for k in suite_keywords):
                        selected_category_code = category_codes[0]['code']
                        logging.info(f"Cheapest option is JR Suite: {selected_category_code}")
                    else:
                        selected_category_code = category_codes[0]['code']
                        logging.info(f"Cheapest option is full Suite – upgrading to: {selected_category_code}")

                elif specified_category.upper() == "SUITE":
                    # User specifically requested a full Suite
                    suite_keywords = ["JR", "JUNIOR", "MINI"]
                    full_suite_index = None
                    
                    # Look for the first entry that is NOT a junior suite
                    for idx, info in enumerate(category_codes):
                        title = info.get("title", "").upper()
                        # Check if this title indicates a junior suite
                        is_junior_suite = any(keyword in title for keyword in suite_keywords)
                        if not is_junior_suite:
                            full_suite_index = idx
                            logging.info(f"Found full Suite at index {idx}: {info.get('title', '')}")
                            break
                    
                    if full_suite_index is not None:
                        selected_category_code = category_codes[full_suite_index]['code']
                        logging.info(f"Selected full Suite code: {selected_category_code}")
                    else:
                        # No full suite found, fall back to cheapest (likely JR Suite)
                        selected_category_code = category_codes[0]['code']
                        logging.info(f"No full Suite found, falling back to: {selected_category_code}")
                        
                elif specified_category.upper() in ["JR SUITE", "JUNIOR SUITE", "MINI SUITE"]:
                    # User requested a Junior Suite – choose the cheapest option,
                    # but if a junior suite exists and it is the cheapest (index 0), select it.
                    # Otherwise honour price order (which the site already sorts low-to-high).
                    suite_keywords = ["JR", "JUNIOR", "MINI"]
                    cheapest_title = category_codes[0].get("title", "").upper()
                    if any(k in cheapest_title for k in suite_keywords):
                        # Cheapest option is already a JR Suite – pick it
                        selected_category_code = category_codes[0]['code']
                        logging.info(f"Cheapest option is JR Suite: {selected_category_code}")
                    else:
                        # Cheapest is a full Suite – accept the upgrade/cheaper cabin
                        selected_category_code = category_codes[0]['code']
                        logging.info(f"Cheapest option is full Suite – upgrading to: {selected_category_code}")
                        
                else:
                    # For all other categories, select the first (cheapest) option
                    selected_category_code = category_codes[0]['code']

                # Process the cabin selection with the category type and code
                result = await scraper.process_cabin_selection(
                    specified_category, selected_category_code,
                    cabin_info['cabin_number']
                )
                if result:
                    result['passenger_count'] = cabin_info['num_passengers']
                return result

        except Exception as e:
            logging.error(f"Error processing cabin {cabin_info['cabin_number']}: {e}")
            raise
        finally:
            # Ensure browser is properly closed even if an error occurs
            await scraper.cleanup()

    @classmethod
    async def process_cabins(
        cls,
        extracted_details=None,
        request_id=None,
        browser_type='firefox',
        rate_selection_strategy='Cheapest',
        video_auditing=False,
        provider='Studio.Sales.CabinCloseOut',
        session_id=None
    ):
        """
        Process multiple cabin bookings concurrently using asyncio.
        
        This method orchestrates the booking of multiple cabins, using asyncio.gather
        to process cabins concurrently.
        
        Args:
            extracted_details (list, optional): Pre-extracted cruise details
            request_id (str, optional): Unique request identifier
            browser_type (str): The browser to use (default: 'firefox')
            rate_selection_strategy (str): Strategy for selecting rates
            video_auditing (bool): Whether to enable video recording for audit purposes
            provider (str): The provider for the cabin booking
            session_id (str, optional): Session ID to use for all cabins
            
        Returns:
            list: Results for all cabin bookings
        """
        # Get cruise details either from extracted_details or interactively
        if extracted_details:
            cruise_details = cls.get_cruise_details_from_input(extracted_details)
        else:
            cruise_details = cls.get_cruise_details()


        # Group identical cabins to optimize processing
        cabin_groups = cls.group_identical_cabins(cruise_details)

        # Create tasks for each cabin group
        tasks = []
        cabin_info = {}
        cabin_number = 1
        
        # Create or use the provided session ID
        if not session_id:
            session_id = str(uuid.uuid4())
            logging.info(f"Generated new session ID for all cabins: {session_id}")

        # Create task for each cabin group
        for num_passengers, category, count in cabin_groups:
            task = cls.process_single_cabin(
                browser_type,
                session_id,  # Use the same session ID for all cabins
                cruise_details,
                cls.get_cabin_details(cabin_number, num_passengers),
                category,
                request_id,
                rate_selection_strategy,
                video_auditing,
                provider
            )
            tasks.append(task)
            cabin_info[len(tasks) - 1] = (cabin_number, count)
            cabin_number += count

        # Use asyncio.gather to process cabins concurrently
        results = []
        
        # Process all tasks concurrently
        gather_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process the results
        for i, result in enumerate(gather_results):
            cabin_number, count = cabin_info[i]
            
            # Check if the result is an exception
            if isinstance(result, Exception):
                logging.error(f"Error processing cabin group {cabin_number}: {result}")
                # Record failure for each cabin in the group
                for j in range(count):
                    results.append({
                        'cabin_number': cabin_number + j,
                        'error': str(result),
                        'status': 'failed',
                        'session_id': session_id  # Add session ID to the result
                    })
            else:
                # Success case - create individual results for each cabin in the group
                if result:
                    for j in range(count):
                        cabin_result = result.copy()
                        cabin_result['cabin_number'] = cabin_number + j
                        cabin_result['session_id'] = session_id  # Add session ID to the result
                        results.append(cabin_result)

        return results

    @classmethod
    async def wrap_process_cabins_with_timing(
        cls,
        extracted_details=None,
        request_id=None,
        browser_type='firefox',
        rate_selection_strategy='Cheapest',
        session_id=None,
        video_auditing=False,
        provider='Studio.Sales.CabinCloseOut'
    ):
        """
        Wrap the cabin processing with execution timing functionality.
        
        This method enhances the process_cabins method with timing metrics to track
        performance and execution time for each operation.
        
        Args:
            extracted_details (list, optional): Pre-extracted cruise details
            request_id (str, optional): Unique request identifier
            browser_type (str): The browser to use (default: 'firefox')
            rate_selection_strategy (str): Strategy for selecting rates
            session_id (str, optional): Database session ID for tracking
            video_auditing (bool): Whether to enable video recording for audit purposes
            provider (str): The provider for the cabin booking
            
        Returns:
            tuple: (results, execution_summary) where:
                - results is the list of cabin booking results
                - execution_summary is a dictionary with timing metrics
            
        Raises:
            Exception: If an error occurs during processing
        """
        # Initialize the timer
        timer = ExecutionTimer()
        timer.start()

        # Create a session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())
            
        # Log booking process start
        ui_log("Kindly stand by - we're processing your booking now", 
               session_id=session_id, 
               step="booking_start", module="Studio")

        # Extract cruise information for UI log
        cruise_info = {}
        if extracted_details:
            cruise_info = cls.get_cruise_details_from_input(extracted_details)
            ship = cruise_info.get('ship_name', 'Unknown Ship')
            date = cruise_info.get('travel_date', 'Unknown Date')
            duration = cruise_info.get('nights', 'Unknown')
            cabins = len(cruise_info.get('cabin_categories', []))
            passengers = cruise_info.get('adults', 0) + cruise_info.get('children', 0)

        try:
            # Store reference to original method
            original_process_single_cabin = cls.process_single_cabin

            # Define a timed version that tracks execution duration
            async def timed_process_single_cabin(*args, **kwargs):
                cabin_info = args[3]
                cabin_number = cabin_info['cabin_number']

                ui_log(f"Working on processing cabin {cabin_number}", 
                       session_id=session_id, 
                       cabin_id=cabin_number,
                       step="cabin_start", module="Studio")

                # Measure execution time
                start_time = time.time()
                try:
                    result = await original_process_single_cabin(*args, **kwargs)
                    duration = time.time() - start_time
                    # Record successful processing time
                    timer.record_cabin_timing(
                        cabin_number, 'total_processing', duration
                    )
                    
                    # Log cabin completion status
                    if result and result.get('status') == 'success':
                        price = result.get('cabin_total', 0)
                        ui_log(f"Got the price for cabin {cabin_number} — thanks for waiting", 
                               session_id=session_id, 
                               cabin_id=cabin_number,
                               step="cabin_success", module="Studio")
                    else:
                        error = result.get('error', 'Unknown error') if result else 'Failed to process cabin'
                        ui_log(f"Oops! Booking for cabin {cabin_number} didn’t go through this time", 
                               session_id=session_id, 
                               cabin_id=cabin_number,
                               step="cabin_failed", module="Studio")
                        
                    return result
                except Exception as e:
                    duration = time.time() - start_time
                    # Record failed processing time
                    timer.record_cabin_timing(
                        cabin_number, 'failed_processing', duration
                    )
                    
                    ui_log(f"Oops! Booking for cabin {cabin_number} didn’t go through this time", 
                           session_id=session_id, 
                           cabin_id=cabin_number,
                           step="cabin_failed", module="Studio")
                    raise e

            # Replace method temporarily
            temp_method = cls.process_single_cabin
            cls.process_single_cabin = timed_process_single_cabin

            # Run the process with timing
            ui_log("Getting the cabin booking underway", 
                   session_id=session_id, 
                   step="cabins_processing", module="Studio")
            results = await cls.process_cabins(
                extracted_details, 
                request_id, 
                browser_type, 
                rate_selection_strategy, 
                video_auditing, 
                provider,
                session_id
            )

            # Restore original method
            cls.process_single_cabin = temp_method

            # Finalize timing and prepare summary
            timer.end()
            
            # Calculate success stats for UI log
            total_cabins = len(results)
            successful_cabins = sum(1 for r in results if r.get('status') == 'success')
            total_price = sum(r.get('cabin_total', 0) for r in results if r.get('status') == 'success')
            
            ui_log(f"Success! Your booking is all set", 
                   session_id=session_id, 
                   step="booking_complete", module="Studio")
            
            execution_time = timer.get_total_execution_time()
            #ui_log(f"Total processing time: {int(execution_time)} seconds", 
            #       session_id=session_id, 
            #       step="booking_time", module="Studio")

            execution_summary = {
                'total_execution_time': timer.get_total_execution_time(), 
                'total_delay': timer.get_total_delay(), 
                'cabin_summary': timer.get_cabin_summary(),
                'session_id': session_id,
                'total_price': total_price,
                'successful_cabins': successful_cabins,
                'total_cabins': total_cabins
            }

            return results, execution_summary

        except asyncio.CancelledError:
            logging.warning(f"Studio booking cancelled for session {session_id}")
            ui_log("Booking cancelled by user", 
                   session_id=session_id, 
                   step="booking_cancelled", module="Studio")
            timer.end()
            # Perform any necessary cleanup
            # The contexts will be closed by the cancellation service
            raise  # Re-raise to ensure proper cancellation propagation
        except Exception as e:
            ui_log(f"Oops! Booking didn’t go through this time", 
                   session_id=session_id, 
                   step="booking_failed", module="Studio")
            timer.end()
            raise e

    @classmethod
    async def main(cls):
        """
        Main entry point for the Studio Manager application.
        
        Sets up logging and executes the cabin processing workflow with timing,
        then logs the results.
        """
        # Set up logging
        cls.setup_main_logging()
        logger = logging.getLogger('main')

        try:
            # Run the cabin processing with timing and get results
            results, execution_summary = await cls.wrap_process_cabins_with_timing()

            # Log the results for each cabin
            for result in results:
                if result:
                    logger.info(f"Cabin processed: {result}")
                else:
                    logger.error("Cabin processing failed.")

        except Exception as e:
            logger.error(f"Error in main execution: {str(e)}")


if __name__ == "__main__":
    asyncio.run(StudioManager.main())
