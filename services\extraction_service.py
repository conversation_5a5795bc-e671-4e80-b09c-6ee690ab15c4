import sys
import os
from loguru import logger

# Add parent directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from NCL.extraction import CruiseDetailsExtractor
from Cruising_Power.extraction import extract_cruise_info
from Studio.Quote_Processor import QuoteProcessor
from OneSource.extraction import OneSourceExtractor
def merge_quote_inputs(text_input, url_input):
    """Merge text and URL inputs into a single quote"""
    text_parts = []
    
    if text_input:
        lines = [line.strip() for line in text_input.split('\n') if line.strip()]
        text_parts.extend(lines)
    
    if url_input:
        url = url_input.strip()
        text_parts.append(f"Booking URL: {url}")
    
    combined_quote = ' '.join(text_parts)
   
    
    return combined_quote
class ExtractionService:
    """
    Unified service for extracting cruise details from different providers
    """
    
    @staticmethod
    def extract_ncl_details(text_input):
        """
        Extract cruise details from NCL text input
        
        Args:
            text_input (str): Raw cruise details text
            
        Returns:
            dict: Extracted cruise details
        """
        logger.info("Extracting NCL cruise details")
        cruise_details_extractor = CruiseDetailsExtractor()
        details = cruise_details_extractor.extract_cruise_details(text_input)
        logger.info("NCL extraction completed")
        return details
    
    @staticmethod
    def extract_cruising_power_details(text_input):
        """
        Extract cruise details from Cruising Power text input
        
        Args:
            text_input (str): Raw cruise details text
            
        Returns:
            tuple: (formatted_info, details_dict)
        """
        logger.info("Extracting Cruising Power cruise details")
        formatted_info, details = extract_cruise_info(text_input)
        logger.info("Cruising Power extraction completed")
        return formatted_info, details
    
    @staticmethod
    def extract_studio_details(text_input, url_input=""):
        """
        Extract cruise details from Studio text and URL input
        
        Args:
            text_input (str): Raw cruise details text
            url_input (str, optional): Booking URL
            
        Returns:
            dict: Extracted cruise details
        """
        logger.info("Extracting Studio cruise details")
        combined_quote = merge_quote_inputs(text_input, url_input)
        details = QuoteProcessor.extract_cruise_details(combined_quote)
        logger.info("Studio extraction completed")
        return details
    
    @staticmethod
    def extract_onesource_details(text_input):
        """
        Extract cruise details from OneSource text input
        
        Args:
            text_input (str): Raw cruise details text
            
        Returns:
            dict: Extracted cruise details
        """
        logger.info("Extracting OneSource cruise details")
        onesource_extractor = OneSourceExtractor()
        details = onesource_extractor.extract_cruise_details(text_input)
        logger.info("OneSource extraction completed")
        return details
    
    @staticmethod
    def extract_details(provider, text_input, url_input="", request_id=None):
        """
        Unified method to extract details based on provider
        
        Args:
            provider (str): Provider name ("NCL", "Cruising Power", "Studio", or "OneSource")
            text_input (str): Raw cruise details text
            url_input (str, optional): Booking URL (for Studio)
            request_id (str, optional): Request ID to include in results
            
        Returns:
            dict: Extracted cruise details
        """
        logger.info(f"Extracting cruise details for provider: {provider}")
        
        if provider == "NCL":
            details = ExtractionService.extract_ncl_details(text_input)
        elif provider == "Cruising Power":
            _, details = ExtractionService.extract_cruising_power_details(text_input)
            # Store original text and request_id
            details['original_text'] = text_input
            if request_id:
                details['request_id'] = request_id
                logger.info(f"Added request_id {request_id} to extracted data")
        elif provider == "OneSource":
            details = ExtractionService.extract_onesource_details(text_input)
            # Store original text and request_id
            details['original_text'] = text_input
            if request_id:
                details['request_id'] = request_id
                logger.info(f"Added request_id {request_id} to extracted data")
        else:  # Studio
            details = ExtractionService.extract_studio_details(text_input, url_input)
            
        logger.info(f"Extraction complete for {provider}")
        return details 