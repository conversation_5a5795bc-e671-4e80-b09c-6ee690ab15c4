{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "names": ["getHash", "detectContentType", "ImageOptimizerCache", "ImageError", "getMaxAge", "optimizeImage", "imageOptimizer", "sendResponse", "getImageSize", "AVIF", "WEBP", "PNG", "JPEG", "GIF", "SVG", "ICO", "CACHE_VERSION", "ANIMATABLE_TYPES", "VECTOR_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "sharp", "require", "process", "env", "NEXT_SHARP_PATH", "concurrency", "divisor", "NODE_ENV", "Math", "floor", "max", "cpus", "length", "e", "showSharpMissingWarning", "getSupportedMimeType", "options", "accept", "mimeType", "mediaType", "includes", "items", "hash", "createHash", "item", "update", "String", "digest", "replace", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "etag", "filename", "join", "promises", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "every", "b", "i", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "url", "w", "q", "href", "Log", "warnOnce", "errorMessage", "Array", "isArray", "isAbsolute", "startsWith", "hrefParsed", "URL", "toString", "_error", "protocol", "hasMatch", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "revalidateAfter", "curRevalidate", "isStale", "_", "set", "revalidate", "Error", "err", "error", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "age", "endsWith", "slice", "n", "contentType", "height", "nextConfigOutput", "optimizedBuffer", "transformer", "sequentialRead", "rotate", "resize", "undefined", "withoutEnlargement", "avif", "avifQuality", "chromaSubsampling", "webp", "png", "jpeg", "progressive", "<PERSON><PERSON><PERSON><PERSON>", "orientation", "getOrientation", "operations", "Orientation", "RIGHT_TOP", "type", "numRotations", "BOTTOM_RIGHT", "LEFT_BOTTOM", "processBuffer", "_req", "_res", "paramsResult", "handleRequest", "upstreamBuffer", "upstreamType", "upstreamRes", "fetch", "ok", "status", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "mocked", "createRequestResponseMocks", "method", "socket", "res", "nodeUrl", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "cacheControl", "dangerouslyAllowSVG", "vector", "animate", "isAnimated", "getExtension", "output", "getMetadata", "meta", "opts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getImageBlurSvg", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "sendEtagResponse", "finished", "contentDisposition", "contentDispositionType", "contentSecurityPolicy", "getContentType", "result", "byteLength", "end", "metadata", "decodeBuffer", "imageSizeOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IA+EgBA,OAAO;eAAPA;;IAiCAC,iBAAiB;eAAjBA;;IAqCHC,mBAAmB;eAAnBA;;IAmNAC,UAAU;eAAVA;;IA+BGC,SAAS;eAATA;;IAeMC,aAAa;eAAbA;;IAgHAC,cAAc;eAAdA;;IA4NNC,YAAY;eAAZA;;IAgCMC,YAAY;eAAZA;;;wBAlwBK;oBACF;oBACJ;wBAEK;2EACK;gCACa;kEACpB;mEACD;sBACF;4DAC4B;8BAEjB;oCAEP;6BAEkB;6BAWV;6BACY;6DACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIrB,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACP;IAAMC;IAAKE;CAAI;AACzC,MAAMK,eAAe;IAACJ;CAAI;AAC1B,MAAMK,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEJ,IAAI;IACFA,QAAQC,QAAQC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAC/C,IAAIJ,SAASA,MAAMK,WAAW,KAAK,GAAG;QACpC,2DAA2D;QAC3D,8DAA8D;QAC9D,0DAA0D;QAC1D,MAAMC,UAAUJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,gBAAgB,IAAI;QAC7DP,MAAMK,WAAW,CAACG,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACC,IAAAA,QAAI,IAAGC,MAAM,GAAGN,SAAS;IACjE;AACF,EAAE,OAAOO,GAAG;AACV,iEAAiE;AACnE;AAEA,IAAIC,0BAA0BZ,QAAQC,GAAG,CAACI,QAAQ,KAAK;AAavD,SAASQ,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAWC,IAAAA,iBAAS,EAACF,QAAQD;IACnC,OAAOC,OAAOG,QAAQ,CAACF,YAAYA,WAAW;AAChD;AAEO,SAASvC,QAAQ0C,KAAmC;IACzD,MAAMC,OAAOC,IAAAA,kBAAU,EAAC;IACxB,KAAK,IAAIC,QAAQH,MAAO;QACtB,IAAI,OAAOG,SAAS,UAAUF,KAAKG,MAAM,CAACC,OAAOF;aAC5C;YACHF,KAAKG,MAAM,CAACD;QACd;IACF;IACA,qDAAqD;IACrD,OAAOF,KAAKK,MAAM,CAAC,UAAUC,OAAO,CAAC,OAAO;AAC9C;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdC,IAAY;IAEZ,MAAMC,WAAWC,IAAAA,UAAI,EAACP,KAAK,CAAC,EAAEE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAEJ,UAAU,CAAC;IAEvE,MAAMO,YAAQ,CAACC,EAAE,CAACT,KAAK;QAAEU,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAMJ,YAAQ,CAACK,KAAK,CAACb,KAAK;QAAEU,WAAW;IAAK;IAC5C,MAAMF,YAAQ,CAACM,SAAS,CAACR,UAAUF;AACrC;AAOO,SAAStD,kBAAkBsD,MAAc;IAC9C,IAAI;QAAC;QAAM;QAAM;KAAK,CAACW,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACvD,OAAOvD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACsD,KAAK,CACpD,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAE1B;QACA,OAAOxD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACuD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOtD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAACqD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAOzD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACwD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACnE,OAAOrD;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACoD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAO1D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACyD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOpD;IACT;IACA,OAAO;AACT;AAEO,MAAMb;IAIX,OAAOmE,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD;QARvB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGb;QACtB,IAAIc;QAEJ,IAAIP,QAAQ7C,MAAM,GAAG,GAAG;YACtBqD,KAAIC,QAAQ,CACV;QAEJ;QAEA,IAAI,CAACL,KAAK;YACR,OAAO;gBAAEM,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACR,MAAM;YAC7B,OAAO;gBAAEM,cAAc;YAAqC;QAC9D;QAEA,IAAIG;QAEJ,IAAIT,IAAIU,UAAU,CAAC,MAAM;YACvBP,OAAOH;YACPS,aAAa;QACf,OAAO;YACL,IAAIE;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAIZ;gBACrBG,OAAOQ,WAAWE,QAAQ;gBAC1BJ,aAAa;YACf,EAAE,OAAOK,QAAQ;gBACf,OAAO;oBAAER,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAAC/C,QAAQ,CAACoD,WAAWI,QAAQ,GAAG;gBACtD,OAAO;oBAAET,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAACU,IAAAA,4BAAQ,EAACpB,SAASG,gBAAgBY,aAAa;gBAClD,OAAO;oBAAEL,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACL,GAAG;YACN,OAAO;gBAAEK,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACP,IAAI;YAC3B,OAAO;gBAAEK,cAAc;YAA2C;QACpE;QAEA,IAAI,CAACJ,GAAG;YACN,OAAO;gBAAEI,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACN,IAAI;YAC3B,OAAO;gBAAEI,cAAc;YAA6C;QACtE;QAEA,MAAMW,QAAQC,SAASjB,GAAG;QAE1B,IAAIgB,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLX,cAAc;YAChB;QACF;QAEA,MAAMc,QAAQ;eAAK1B,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACT6B,MAAMC,IAAI,CAACpF;QACb;QAEA,MAAMqF,cACJF,MAAM7D,QAAQ,CAAC0D,UAAW1B,SAAS0B,SAAShF;QAE9C,IAAI,CAACqF,aAAa;YAChB,OAAO;gBACLhB,cAAc,CAAC,yBAAyB,EAAEW,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAAShB;QAEzB,IAAIiB,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLjB,cACE;YACJ;QACF;QAEA,MAAMjD,WAAWH,qBAAqB4C,WAAW,EAAE,EAAEV,IAAIoC,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAWzB,IAAIU,UAAU,CAC7B,CAAC,EAAEpB,WAAWoC,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACLvB;YACAiB;YACAX;YACAgB;YACAR;YACAM;YACAlE;YACAwC;QACF;IACF;IAEA,OAAO8B,YAAY,EACjBxB,IAAI,EACJc,KAAK,EACLM,OAAO,EACPlE,QAAQ,EAMT,EAAU;QACT,OAAOvC,QAAQ;YAACgB;YAAeqE;YAAMc;YAAOM;YAASlE;SAAS;IAChE;IAEAuE,YAAY,EACVC,OAAO,EACPvC,UAAU,EAIX,CAAE;QACD,IAAI,CAACwC,QAAQ,GAAGtD,IAAAA,UAAI,EAACqD,SAAS,SAAS;QACvC,IAAI,CAACvC,UAAU,GAAGA;IACpB;IAEA,MAAMyC,IAAIC,QAAgB,EAAyC;QACjE,IAAI;YACF,MAAMF,WAAWtD,IAAAA,UAAI,EAAC,IAAI,CAACsD,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAMxD,YAAQ,CAACyD,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAYjE,MAAMJ,UAAU,GAAGmE,KAAKG,KAAK,CAAC,KAAK;gBAChE,MAAMnE,SAAS,MAAMI,YAAQ,CAACgE,QAAQ,CAACjE,IAAAA,UAAI,EAACsD,UAAUO;gBACtD,MAAMjE,WAAWsE,OAAOH;gBACxB,MAAMpE,SAASuE,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAM;wBACNtE;wBACAD;wBACAH;oBACF;oBACA2E,iBACElG,KAAKE,GAAG,CAACsB,QAAQ,IAAI,CAACmB,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3DuC,KAAKD,GAAG;oBACVW,eAAe3E;oBACf4E,SAASZ,MAAM/D;gBACjB;YACF;QACF,EAAE,OAAO4E,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMC,IACJjB,QAAgB,EAChBW,KAAmC,EACnC,EACEO,UAAU,EAGX,EACD;QACA,IAAIP,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,SAAS;YAC3B,MAAM,IAAIO,MAAM;QAClB;QAEA,IAAI,OAAOD,eAAe,UAAU;YAClC,MAAM,IAAIC,MAAM;QAClB;QACA,MAAM/E,WACJzB,KAAKE,GAAG,CAACqG,YAAY,IAAI,CAAC5D,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/DuC,KAAKD,GAAG;QAEV,IAAI;YACF,MAAMnE,gBACJQ,IAAAA,UAAI,EAAC,IAAI,CAACsD,QAAQ,EAAEE,WACpBW,MAAMzE,SAAS,EACfgF,YACA9E,UACAuE,MAAMtE,MAAM,EACZsE,MAAMrE,IAAI;QAEd,EAAE,OAAO8E,KAAK;YACZhD,KAAIiD,KAAK,CAAC,CAAC,+BAA+B,EAAErB,SAAS,CAAC,EAAEoB;QAC1D;IACF;AACF;AACO,MAAMnI,mBAAmBkI;IAG9BvB,YAAY0B,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBAAkBC,GAAkB;IAC3C,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAIjB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACqB,KAAKlB,MAAM,GAAGiB,UAAUE,IAAI,GAAGtB,KAAK,CAAC,KAAK;QAC/CqB,MAAMA,IAAIE,WAAW;QACrB,IAAIpB,OAAO;YACTA,QAAQA,MAAMoB,WAAW;QAC3B;QACAL,IAAIT,GAAG,CAACY,KAAKlB;IACf;IACA,OAAOe;AACT;AAEO,SAASxI,UAAUuI,GAAkB;IAC1C,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIM,MAAMN,IAAI3B,GAAG,CAAC,eAAe2B,IAAI3B,GAAG,CAAC,cAAc;QACvD,IAAIiC,IAAItD,UAAU,CAAC,QAAQsD,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIjD,SAAS8C,KAAK;QACxB,IAAI,CAAC7C,MAAMgD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AAEO,eAAehJ,cAAc,EAClCkD,MAAM,EACN+F,WAAW,EACX7C,OAAO,EACPN,KAAK,EACLoD,MAAM,EACNC,gBAAgB,EAQjB;IACC,IAAIC,kBAAkBlG;IACtB,IAAIlC,OAAO;QACT,mCAAmC;QACnC,MAAMqI,cAAcrI,MAAMkC,QAAQ;YAChCoG,gBAAgB;QAClB;QAEAD,YAAYE,MAAM;QAElB,IAAIL,QAAQ;YACVG,YAAYG,MAAM,CAAC1D,OAAOoD;QAC5B,OAAO;YACLG,YAAYG,MAAM,CAAC1D,OAAO2D,WAAW;gBACnCC,oBAAoB;YACtB;QACF;QAEA,IAAIT,gBAAgB7I,MAAM;YACxB,IAAIiJ,YAAYM,IAAI,EAAE;gBACpB,MAAMC,cAAcxD,UAAU;gBAC9BiD,YAAYM,IAAI,CAAC;oBACfvD,SAAS5E,KAAKE,GAAG,CAACkI,aAAa;oBAC/BC,mBAAmB;gBACrB;YACF,OAAO;gBACL5E,KAAIC,QAAQ,CACV,CAAC,wIAAwI,CAAC,GACxI;gBAEJmE,YAAYS,IAAI,CAAC;oBAAE1D;gBAAQ;YAC7B;QACF,OAAO,IAAI6C,gBAAgB5I,MAAM;YAC/BgJ,YAAYS,IAAI,CAAC;gBAAE1D;YAAQ;QAC7B,OAAO,IAAI6C,gBAAgB3I,KAAK;YAC9B+I,YAAYU,GAAG,CAAC;gBAAE3D;YAAQ;QAC5B,OAAO,IAAI6C,gBAAgB1I,MAAM;YAC/B8I,YAAYW,IAAI,CAAC;gBAAE5D;gBAAS6D,aAAa;YAAK;QAChD;QAEAb,kBAAkB,MAAMC,YAAYa,QAAQ;IAC5C,iCAAiC;IACnC,OAAO;QACL,IAAIpI,2BAA2BqH,qBAAqB,cAAc;YAChElE,KAAIiD,KAAK,CACP,CAAC,0LAA0L,CAAC;YAE9L,MAAM,IAAIpI,WAAW,KAAK;QAC5B;QACA,wCAAwC;QACxC,IAAIgC,yBAAyB;YAC3BmD,KAAIC,QAAQ,CACV,CAAC,wLAAwL,CAAC,GACxL;YAEJpD,0BAA0B;QAC5B;QAEA,qCAAqC;QACrC,MAAMqI,cAAc,MAAMC,IAAAA,8BAAc,EAAClH;QAEzC,MAAMmH,aAA0B,EAAE;QAElC,IAAIF,gBAAgBG,2BAAW,CAACC,SAAS,EAAE;YACzCF,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIN,gBAAgBG,2BAAW,CAACI,YAAY,EAAE;YACnDL,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIN,gBAAgBG,2BAAW,CAACK,WAAW,EAAE;YAClDN,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO;QACL,kCAAkC;QAClC,6DAA6D;QAC7D,+BAA+B;QACjC;QAEA,IAAIvB,QAAQ;YACVmB,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAU1E;gBAAOoD;YAAO;QAClD,OAAO;YACLmB,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAU1E;YAAM;QAC1C;QAEA,MAAM,EAAE8E,aAAa,EAAE,GACrB3J,QAAQ;QAEV,IAAIgI,gBAAgB7I,MAAM;YACxBgJ,kBAAkB,MAAMwB,cAAc1H,QAAQmH,YAAY,QAAQjE;QACpE,OAAO,IAAI6C,gBAAgB5I,MAAM;YAC/B+I,kBAAkB,MAAMwB,cAAc1H,QAAQmH,YAAY,QAAQjE;QACpE,OAAO,IAAI6C,gBAAgB3I,KAAK;YAC9B8I,kBAAkB,MAAMwB,cAAc1H,QAAQmH,YAAY,OAAOjE;QACnE,OAAO,IAAI6C,gBAAgB1I,MAAM;YAC/B6I,kBAAkB,MAAMwB,cAAc1H,QAAQmH,YAAY,QAAQjE;QACpE;IACF;IAEA,OAAOgD;AACT;AAEO,eAAenJ,eACpB4K,IAAqB,EACrBC,IAAoB,EACpBC,YAA+B,EAC/B5G,UAA8B,EAC9BC,KAA0B,EAC1B4G,aAIkB;IAElB,IAAIC;IACJ,IAAIC;IACJ,IAAIlI;IACJ,MAAM,EAAEsC,UAAU,EAAEN,IAAI,EAAEc,KAAK,EAAE5D,QAAQ,EAAEkE,OAAO,EAAE,GAAG2E;IAEvD,IAAIzF,YAAY;QACd,MAAM6F,cAAc,MAAMC,MAAMpG;QAEhC,IAAI,CAACmG,YAAYE,EAAE,EAAE;YACnBpG,KAAIiD,KAAK,CAAC,sCAAsClD,MAAMmG,YAAYG,MAAM;YACxE,MAAM,IAAIxL,WACRqL,YAAYG,MAAM,EAClB;QAEJ;QAEAL,iBAAiBM,OAAOC,IAAI,CAAC,MAAML,YAAYM,WAAW;QAC1DP,eACEtL,kBAAkBqL,mBAClBE,YAAY9E,OAAO,CAACO,GAAG,CAAC;QAC1B5D,SAASjD,UAAUoL,YAAY9E,OAAO,CAACO,GAAG,CAAC;IAC7C,OAAO;QACL,IAAI;YACF,MAAM8E,SAASC,IAAAA,uCAA0B,EAAC;gBACxC9G,KAAKG;gBACL4G,QAAQf,KAAKe,MAAM,IAAI;gBACvBvF,SAASwE,KAAKxE,OAAO;gBACrBwF,QAAQhB,KAAKgB,MAAM;YACrB;YAEA,MAAMb,cAAcU,OAAOzH,GAAG,EAAEyH,OAAOI,GAAG,EAAEC,YAAO,CAACC,KAAK,CAAChH,MAAM;YAChE,MAAM0G,OAAOI,GAAG,CAACG,WAAW;YAE5B,IAAI,CAACP,OAAOI,GAAG,CAAC3D,UAAU,EAAE;gBAC1BlD,KAAIiD,KAAK,CAAC,6BAA6BlD,MAAM0G,OAAOI,GAAG,CAAC3D,UAAU;gBAClE,MAAM,IAAIrI,WACR4L,OAAOI,GAAG,CAAC3D,UAAU,EACrB;YAEJ;YAEA8C,iBAAiBM,OAAOW,MAAM,CAACR,OAAOI,GAAG,CAACK,OAAO;YACjDjB,eACEtL,kBAAkBqL,mBAClBS,OAAOI,GAAG,CAACM,SAAS,CAAC;YACvB,MAAMC,eAAeX,OAAOI,GAAG,CAACM,SAAS,CAAC;YAC1CpJ,SAASqJ,eAAetM,UAAUsM,gBAAgB;QACpD,EAAE,OAAOpE,KAAK;YACZhD,KAAIiD,KAAK,CAAC,sCAAsClD,MAAMiD;YACtD,MAAM,IAAInI,WACR,KACA;QAEJ;IACF;IAEA,IAAIoL,cAAc;QAChBA,eAAeA,aAAatC,WAAW,GAAGD,IAAI;QAE9C,IACEuC,aAAa3F,UAAU,CAAC,gBACxB,CAACpB,WAAWG,MAAM,CAACgI,mBAAmB,EACtC;YACArH,KAAIiD,KAAK,CACP,CAAC,wBAAwB,EAAElD,KAAK,YAAY,EAAEkG,aAAa,qCAAqC,CAAC;YAEnG,MAAM,IAAIpL,WACR,KACA;QAEJ;QACA,MAAMyM,SAAS1L,aAAauB,QAAQ,CAAC8I;QACrC,MAAMsB,UACJ5L,iBAAiBwB,QAAQ,CAAC8I,iBAAiBuB,IAAAA,mBAAU,EAACxB;QAExD,IAAIsB,UAAUC,SAAS;YACrB,OAAO;gBAAEtJ,QAAQ+H;gBAAgBhC,aAAaiC;gBAAclI;YAAO;QACrE;QACA,IAAI,CAACkI,aAAa3F,UAAU,CAAC,aAAa2F,aAAa9I,QAAQ,CAAC,MAAM;YACpE6C,KAAIiD,KAAK,CACP,kDACAlD,MACA,YACAkG;YAEF,MAAM,IAAIpL,WAAW,KAAK;QAC5B;IACF;IAEA,IAAImJ;IAEJ,IAAI/G,UAAU;QACZ+G,cAAc/G;IAChB,OAAO,IACLgJ,CAAAA,gCAAAA,aAAc3F,UAAU,CAAC,cACzBmH,IAAAA,yBAAY,EAACxB,iBACbA,iBAAiB7K,QACjB6K,iBAAiB9K,MACjB;QACA6I,cAAciC;IAChB,OAAO;QACLjC,cAAc1I;IAChB;IACA,IAAI;QACF,IAAI6I,kBAAkB,MAAMpJ,cAAc;YACxCkD,QAAQ+H;YACRhC;YACA7C;YACAN;YACAqD,kBAAkBhF,WAAWwI,MAAM;QACrC;QACA,IAAIvD,iBAAiB;YACnB,IAAIhF,SAAS0B,SAAShF,iBAAiBsF,YAAYrF,cAAc;gBAC/D,MAAM,EAAE6L,WAAW,EAAE,GACnB3L,QAAQ;gBACV,8EAA8E;gBAC9E,gFAAgF;gBAChF,qFAAqF;gBACrF,MAAM4L,OAAO,MAAMD,YAAYxD;gBAC/B,MAAM0D,OAAO;oBACXC,WAAWF,KAAK/G,KAAK;oBACrBkH,YAAYH,KAAK3D,MAAM;oBACvB+D,aAAa,CAAC,KAAK,EAAEhE,YAAY,QAAQ,EAAEG,gBAAgB1D,QAAQ,CACjE,UACA,CAAC;gBACL;gBACA0D,kBAAkBmC,OAAOC,IAAI,CAAC0B,SAASC,IAAAA,6BAAe,EAACL;gBACvD7D,cAAc;YAChB;YACA,OAAO;gBACL/F,QAAQkG;gBACRH;gBACAjG,QAAQxB,KAAKE,GAAG,CAACsB,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC5D;QACF,OAAO;YACL,MAAM,IAAI5E,WAAW,KAAK;QAC5B;IACF,EAAE,OAAOoI,OAAO;QACd,IAAI+C,kBAAkBC,cAAc;YAClC,yDAAyD;YACzD,OAAO;gBACLhI,QAAQ+H;gBACRhC,aAAaiC;gBACblI,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC3C;QACF,OAAO;YACL,MAAM,IAAI5E,WACR,KACA;QAEJ;IACF;AACF;AAEA,SAASsN,yBACPvI,GAAW,EACXoE,WAA0B;IAE1B,MAAM,CAACoE,sBAAsB,GAAGxI,IAAIwC,KAAK,CAAC,KAAK;IAC/C,MAAMiG,wBAAwBD,sBAAsBhG,KAAK,CAAC,KAAKkG,GAAG;IAClE,IAAI,CAACtE,eAAe,CAACqE,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsBjG,KAAK,CAAC,KAAK;IACpD,MAAMtE,YAAY2J,IAAAA,yBAAY,EAACzD;IAC/B,OAAO,CAAC,EAAEuE,SAAS,CAAC,EAAEzK,UAAU,CAAC;AACnC;AAEA,SAAS0K,mBACPxJ,GAAoB,EACpB6H,GAAmB,EACnBjH,GAAW,EACX1B,IAAY,EACZ8F,WAA0B,EAC1B3C,QAAiB,EACjBoH,MAAoB,EACpBC,YAAiC,EACjC3K,MAAc,EACdoB,KAAc;IAEd0H,IAAI8B,SAAS,CAAC,QAAQ;IACtB9B,IAAI8B,SAAS,CACX,iBACAtH,WACI,yCACA,CAAC,gBAAgB,EAAElC,QAAQ,IAAIpB,OAAO,iBAAiB,CAAC;IAE9D,IAAI6K,IAAAA,6BAAgB,EAAC5J,KAAK6H,KAAK3I,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAE2K,UAAU;QAAK;IAC1B;IACA,IAAI7E,aAAa;QACf6C,IAAI8B,SAAS,CAAC,gBAAgB3E;IAChC;IAEA,MAAMuE,WAAWJ,yBAAyBvI,KAAKoE;IAC/C6C,IAAI8B,SAAS,CACX,uBACAG,IAAAA,2BAAkB,EAACP,UAAU;QAAEhD,MAAMmD,aAAaK,sBAAsB;IAAC;IAG3ElC,IAAI8B,SAAS,CAAC,2BAA2BD,aAAaM,qBAAqB;IAC3EnC,IAAI8B,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEI,UAAU;IAAM;AAC3B;AAEO,SAAS5N,aACd+D,GAAoB,EACpB6H,GAAmB,EACnBjH,GAAW,EACX9B,SAAiB,EACjBG,MAAc,EACdoD,QAAiB,EACjBoH,MAAoB,EACpBC,YAAiC,EACjC3K,MAAc,EACdoB,KAAc;IAEd,MAAM6E,cAAciF,IAAAA,2BAAc,EAACnL;IACnC,MAAMI,OAAOxD,QAAQ;QAACuD;KAAO;IAC7B,MAAMiL,SAASV,mBACbxJ,KACA6H,KACAjH,KACA1B,MACA8F,aACA3C,UACAoH,QACAC,cACA3K,QACAoB;IAEF,IAAI,CAAC+J,OAAOL,QAAQ,EAAE;QACpBhC,IAAI8B,SAAS,CAAC,kBAAkBrC,OAAO6C,UAAU,CAAClL;QAClD4I,IAAIuC,GAAG,CAACnL;IACV;AACF;AAEO,eAAe/C,aACpB+C,MAAc,EACd,8BAA8B;AAC9BH,SAA2C;IAK3C,qDAAqD;IACrD,0DAA0D;IAC1D,IAAIA,cAAc,QAAQ;QACxB,IAAI/B,OAAO;YACT,MAAMqI,cAAcrI,MAAMkC;YAC1B,MAAM,EAAE4C,KAAK,EAAEoD,MAAM,EAAE,GAAG,MAAMG,YAAYiF,QAAQ;YACpD,OAAO;gBAAExI;gBAAOoD;YAAO;QACzB,OAAO;YACL,MAAM,EAAEqF,YAAY,EAAE,GACpBtN,QAAQ;YACV,MAAM,EAAE6E,KAAK,EAAEoD,MAAM,EAAE,GAAG,MAAMqF,aAAarL;YAC7C,OAAO;gBAAE4C;gBAAOoD;YAAO;QACzB;IACF;IAEA,MAAM,EAAEpD,KAAK,EAAEoD,MAAM,EAAE,GAAGsF,IAAAA,kBAAW,EAACtL;IACtC,OAAO;QAAE4C;QAAOoD;IAAO;AACzB"}