{"version": 3, "sources": ["../../../../src/client/dev/error-overlay/hot-dev-client.ts"], "names": ["register", "onBuildError", "onBuildOk", "onBeforeRefresh", "onRefresh", "stripAnsi", "addMessageListener", "sendMessage", "formatWebpackMessages", "HMR_ACTIONS_SENT_TO_BROWSER", "window", "__nextDevClientId", "Math", "round", "random", "Date", "now", "hadRuntimeError", "customHmrEventHandler", "MODE", "connect", "mode", "payload", "processMessage", "err", "console", "warn", "stack", "subscribeToHmrEvent", "handler", "onUnrecoverableError", "isFirstCompilation", "mostRecentCompilationHash", "hasCompileErrors", "clearOutdatedErrors", "clear", "handleSuccess", "isHotUpdate", "__NEXT_DATA__", "page", "isUpdateAvailable", "tryApplyUpdates", "onBeforeFastRefresh", "onFastRefresh", "handleWarnings", "warnings", "printWarnings", "formatted", "errors", "i", "length", "handleErrors", "error", "process", "env", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "startLatency", "undefined", "updatedModules", "endLatency", "latency", "log", "JSON", "stringify", "event", "id", "startTime", "endTime", "location", "pathname", "isPageHidden", "document", "visibilityState", "__NEXT_HMR_LATENCY_CB", "handleAvailableHash", "hash", "obj", "action", "BUILDING", "BUILT", "SYNC", "hasErrors", "Boolean", "errorCount", "clientId", "hasWarnings", "warningCount", "SERVER_COMPONENT_CHANGES", "reload", "SERVER_ERROR", "errorJSON", "message", "parse", "Error", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "onBeforeHotUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "performFullReload", "check", "then", "apply", "stackTrace", "split", "slice", "join"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,8EAA8E;AAC9E,qBAAqB;AACrB,2GAA2G;AAE3G,SACEA,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,eAAe,EACfC,SAAS,QACJ,yDAAwD;AAC/D,OAAOC,eAAe,gCAA+B;AACrD,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,cAAa;AAC7D,OAAOC,2BAA2B,4BAA2B;AAC7D,SAASC,2BAA2B,QAAQ,yCAAwC;AAmBpFC,OAAOC,iBAAiB,GAAGC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AAEpE,IAAIC,kBAAkB;AACtB,IAAIC;AACJ,IAAIC,OAAgC;AACpC,eAAe,SAASC,QAAQC,IAA6B;IAC3DF,OAAOE;IACPrB;IAEAM,mBAAmB,CAACgB;QAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;YAC1B;QACF;QAEA,IAAI;YACFC,eAAeD;QACjB,EAAE,OAAOE,KAAU;gBAE+BA;YADhDC,QAAQC,IAAI,CACV,4BAA4BJ,UAAU,OAAQE,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKG,KAAK,YAAVH,aAAc,EAAC;QAEjE;IACF;IAEA,OAAO;QACLI,qBAAoBC,OAAY;YAC9BX,wBAAwBW;QAC1B;QACAC;YACEb,kBAAkB;QACpB;IACF;AACF;AAEA,yDAAyD;AACzD,IAAIc,qBAAqB;AACzB,IAAIC,4BAA2C;AAC/C,IAAIC,mBAAmB;AAEvB,SAASC;IACP,4CAA4C;IAC5C,IAAI,OAAOT,YAAY,eAAe,OAAOA,QAAQU,KAAK,KAAK,YAAY;QACzE,IAAIF,kBAAkB;YACpBR,QAAQU,KAAK;QACf;IACF;AACF;AAEA,0BAA0B;AAC1B,SAASC;IACPF;IAEA,IAAIf,SAAS,WAAW;QACtB,MAAMkB,cACJ,CAACN,sBACArB,OAAO4B,aAAa,CAACC,IAAI,KAAK,aAAaC;QAC9CT,qBAAqB;QACrBE,mBAAmB;QAEnB,0CAA0C;QAC1C,IAAII,aAAa;YACfI,gBAAgBC,qBAAqBC;QACvC;IACF,OAAO;QACLzC;IACF;AACF;AAEA,2CAA2C;AAC3C,SAAS0C,eAAeC,QAAa;IACnCX;IAEA,MAAMG,cAAc,CAACN;IACrBA,qBAAqB;IACrBE,mBAAmB;IAEnB,SAASa;QACP,iCAAiC;QACjC,MAAMC,YAAYvC,sBAAsB;YACtCqC,UAAUA;YACVG,QAAQ,EAAE;QACZ;QAEA,IAAI,OAAOvB,YAAY,eAAe,OAAOA,QAAQC,IAAI,KAAK,YAAY;gBACpDqB;YAApB,IAAK,IAAIE,IAAI,GAAGA,MAAIF,sBAAAA,UAAUF,QAAQ,qBAAlBE,oBAAoBG,MAAM,GAAED,IAAK;gBACnD,IAAIA,MAAM,GAAG;oBACXxB,QAAQC,IAAI,CACV,+CACE;oBAEJ;gBACF;gBACAD,QAAQC,IAAI,CAACrB,UAAU0C,UAAUF,QAAQ,CAACI,EAAE;YAC9C;QACF;IACF;IAEAH;IAEA,0CAA0C;IAC1C,IAAIT,aAAa;QACfI,gBAAgBC,qBAAqBC;IACvC;AACF;AAEA,kEAAkE;AAClE,SAASQ,aAAaH,MAAW;IAC/Bd;IAEAH,qBAAqB;IACrBE,mBAAmB;IAEnB,8BAA8B;IAC9B,IAAIc,YAAYvC,sBAAsB;QACpCwC,QAAQA;QACRH,UAAU,EAAE;IACd;IAEA,6BAA6B;IAC7B5C,aAAa8C,UAAUC,MAAM,CAAC,EAAE;IAEhC,gCAAgC;IAChC,IAAI,OAAOvB,YAAY,eAAe,OAAOA,QAAQ2B,KAAK,KAAK,YAAY;QACzE,IAAK,IAAIH,IAAI,GAAGA,IAAIF,UAAUC,MAAM,CAACE,MAAM,EAAED,IAAK;YAChDxB,QAAQ2B,KAAK,CAAC/C,UAAU0C,UAAUC,MAAM,CAACC,EAAE;QAC7C;IACF;IAEA,gCAAgC;IAChC,0CAA0C;IAC1C,IAAII,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QAChC,IAAIC,KAAKC,aAAa,EAAE;YACtBD,KAAKC,aAAa,CAACV,UAAUC,MAAM,CAAC,EAAE;YACtCQ,KAAKC,aAAa,GAAG;QACvB;IACF;AACF;AAEA,IAAIC,eAAoBC;AAExB,SAASjB,oBAAoBkB,cAAwB;IACnD,IAAIA,eAAeV,MAAM,GAAG,GAAG;QAC7B,2DAA2D;QAC3D,sBAAsB;QACtB/C;IACF;AACF;AAEA,SAASwC,cAAciB,cAAwB;IAC7C1D;IACA,IAAI0D,eAAeV,MAAM,GAAG,GAAG;QAC7B,sDAAsD;QACtD,4BAA4B;QAC5B9C;IACF;IAEA,IAAIsD,cAAc;QAChB,MAAMG,aAAa9C,KAAKC,GAAG;QAC3B,MAAM8C,UAAUD,aAAaH;QAC7BjC,QAAQsC,GAAG,CAAC,AAAC,4BAAyBD,UAAQ;QAC9CvD,YACEyD,KAAKC,SAAS,CAAC;YACbC,OAAO;YACPC,IAAIzD,OAAOC,iBAAiB;YAC5ByD,WAAWV;YACXW,SAASR;YACTtB,MAAM7B,OAAO4D,QAAQ,CAACC,QAAQ;YAC9BX;YACA,oEAAoE;YACpE,sDAAsD;YACtDY,cAAcC,SAASC,eAAe,KAAK;QAC7C;QAEF,IAAIlB,KAAKmB,qBAAqB,EAAE;YAC9BnB,KAAKmB,qBAAqB,CAACb;QAC7B;IACF;AACF;AAEA,kDAAkD;AAClD,SAASc,oBAAoBC,IAAY;IACvC,sCAAsC;IACtC7C,4BAA4B6C;AAC9B;AAEA,mCAAmC;AACnC,SAAStD,eAAeuD,GAAqB;IAC3C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;QACtB;IACF;IAEA,OAAQA,IAAIC,MAAM;QAChB,KAAKtE,4BAA4BuE,QAAQ;YAAE;gBACzCtB,eAAe3C,KAAKC,GAAG;gBACvBS,QAAQsC,GAAG,CAAC;gBACZ;YACF;QACA,KAAKtD,4BAA4BwE,KAAK;QACtC,KAAKxE,4BAA4ByE,IAAI;YAAE;gBACrC,IAAIJ,IAAID,IAAI,EAAE;oBACZD,oBAAoBE,IAAID,IAAI;gBAC9B;gBAEA,MAAM,EAAE7B,MAAM,EAAEH,QAAQ,EAAE,GAAGiC;gBAC7B,MAAMK,YAAYC,QAAQpC,UAAUA,OAAOE,MAAM;gBACjD,IAAIiC,WAAW;oBACb5E,YACEyD,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPmB,YAAYrC,OAAOE,MAAM;wBACzBoC,UAAU5E,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOwC,aAAaH;gBACtB;gBAEA,MAAMuC,cAAcH,QAAQvC,YAAYA,SAASK,MAAM;gBACvD,IAAIqC,aAAa;oBACfhF,YACEyD,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPsB,cAAc3C,SAASK,MAAM;wBAC7BoC,UAAU5E,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOiC,eAAeC;gBACxB;gBAEAtC,YACEyD,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPoB,UAAU5E,OAAOC,iBAAiB;gBACpC;gBAEF,OAAOyB;YACT;QACA,KAAK3B,4BAA4BgF,wBAAwB;YAAE;gBACzD/E,OAAO4D,QAAQ,CAACoB,MAAM;gBACtB;YACF;QACA,KAAKjF,4BAA4BkF,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGd;gBACtB,IAAIc,WAAW;oBACb,MAAM,EAAEC,OAAO,EAAElE,KAAK,EAAE,GAAGqC,KAAK8B,KAAK,CAACF;oBACtC,MAAMxC,QAAQ,IAAI2C,MAAMF;oBACxBzC,MAAMzB,KAAK,GAAGA;oBACdwB,aAAa;wBAACC;qBAAM;gBACtB;gBACA;YACF;QACA;YAAS;gBACP,IAAIlC,uBAAuB;oBACzBA,sBAAsB4D;oBACtB;gBACF;gBACA;YACF;IACF;AACF;AAEA,mDAAmD;AACnD,SAAStC;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOR,8BAA8BgE;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,yIAAyI;IACzI,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAc;IACvC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASzE,QAAQuE,MAAc;YAC7B,IAAIA,WAAW,QAAQ;gBACrB,yIAAyI;gBACzIF,OAAOC,GAAG,CAACI,mBAAmB,CAAC1E;gBAC/ByE;YACF;QACF;QACA,yIAAyI;QACzIJ,OAAOC,GAAG,CAACK,gBAAgB,CAAC3E;IAC9B;AACF;AAEA,iEAAiE;AACjE,SAASY,gBACPgE,iBAAsE,EACtEC,kBAAyD;IAEzD,yIAAyI;IACzI,IAAI,CAACR,OAAOC,GAAG,EAAE;QACf,8DAA8D;QAC9D1E,QAAQ2B,KAAK,CAAC;QACd,4BAA4B;QAC5B;IACF;IAEA,IAAI,CAACZ,uBAAuB,CAACyD,mBAAmB;QAC9C/F;QACA;IACF;IAEA,SAASyG,mBAAmBnF,GAAQ,EAAEoC,cAA+B;QACnE,IAAIpC,OAAOP,mBAAmB,CAAC2C,gBAAgB;YAC7C,IAAIpC,KAAK;gBACPC,QAAQC,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAIT,iBAAiB;gBAC1BQ,QAAQC,IAAI,CACV;YAEJ;YACAkF,kBAAkBpF;YAClB;QACF;QAEA,IAAI,OAAOkF,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmB9C;QACrB;QAEA,IAAIpB,qBAAqB;YACvB,+DAA+D;YAC/D,6DAA6D;YAC7DC,gBACEmB,eAAeV,MAAM,GAAG,IAAIS,YAAY8C,mBACxC7C,eAAeV,MAAM,GAAG,IAAIhD,YAAYwG;QAE5C,OAAO;YACLxG;YACA,IAAImD,QAAQC,GAAG,CAACC,gBAAgB,EAAE;gBAChC8C,kBAAkB;oBAChB,IAAI7C,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,yIAAyI;IACzIyC,OAAOC,GAAG,CACPU,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAAClD;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAO6C,sBAAsB,YAAY;YAC3CA,kBAAkB7C;QACpB;QACA,yIAAyI;QACzI,OAAOsC,OAAOC,GAAG,CAACY,KAAK;IACzB,GACCD,IAAI,CACH,CAAClD;QACC+C,mBAAmB,MAAM/C;IAC3B,GACA,CAACpC;QACCmF,mBAAmBnF,KAAK;IAC1B;AAEN;AAEA,SAASoF,kBAAkBpF,GAAQ;IACjC,MAAMwF,aACJxF,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAACsF,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpD3F,IAAIqE,OAAO,IACXrE,MAAM,EAAC;IAEXjB,YACEyD,KAAKC,SAAS,CAAC;QACbC,OAAO;QACP8C;QACA/F,iBAAiB,CAAC,CAACA;IACrB;IAGFP,OAAO4D,QAAQ,CAACoB,MAAM;AACxB"}