{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "names": ["Log", "NextBuildContext", "Worker", "origDebug", "path", "exportTraceState", "recordTraceEvents", "debug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNames", "config", "telemetryPlugin", "buildSpinner", "nextBuildSpan", "prunedBuildContext", "getWorker", "compilerName", "_worker", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "getStderr", "pipe", "stderr", "getStdout", "stdout", "worker", "_workerPool", "_workers", "_child", "on", "code", "signal", "console", "error", "combinedResult", "duration", "buildTraceContext", "curR<PERSON>ult", "worker<PERSON>ain", "buildContext", "traceState", "defaultParentSpanId", "getId", "shouldSaveTraceEvents", "debugTraceEvents", "end", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "stopAndPersist", "event", "webpackBuild", "experimental", "webpackBuildWorker", "webpackBuildImpl", "require"], "mappings": "AACA,YAAYA,SAAS,gBAAe;AACpC,SAASC,gBAAgB,QAAQ,mBAAkB;AAEnD,SAASC,MAAM,QAAQ,iCAAgC;AACvD,OAAOC,eAAe,2BAA0B;AAEhD,OAAOC,UAAU,OAAM;AACvB,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,cAAa;AAEjE,MAAMC,QAAQJ,UAAU;AAExB,MAAMK,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACxDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACjB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAA+CZ,sBAAsB;IAErE,MAAM,EACJa,MAAM,EACNC,eAAe,EACfC,YAAY,EACZC,aAAa,EACb,GAAGC,oBACJ,GAAGxB;IAEJwB,mBAAmBhB,WAAW,GAAGA;IAEjC,MAAMiB,YAAY,CAACC;YAeK;QAdtB,MAAMC,UAAU,IAAI1B,OAAOE,KAAKyB,IAAI,CAACC,WAAW,YAAY;YAC1DC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;gBAC7B;YACF;QACF;QACAT,QAAQU,SAAS,GAAGC,IAAI,CAACH,QAAQI,MAAM;QACvCZ,QAAQa,SAAS,GAAGF,IAAI,CAACH,QAAQM,MAAM;QAEvC,KAAK,MAAMC,UAAW,EAAA,sBAAA,AAACf,QAAgBgB,WAAW,qBAA5B,oBAA8BC,QAAQ,KAAI,EAAE,CAE7D;YACHF,OAAOG,MAAM,CAACC,EAAE,CAAC,QAAQ,CAACC,MAAMC;gBAC9B,IAAID,QAASC,UAAUA,WAAW,UAAW;oBAC3CC,QAAQC,KAAK,CACX,CAAC,SAAS,EAAExB,aAAa,gCAAgC,EAAEqB,KAAK,aAAa,EAAEC,OAAO,CAAC;gBAE3F;YACF;QACF;QAEA,OAAOrB;IACT;IAEA,MAAMwB,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,KAAK,MAAM3B,gBAAgBP,cAAe;YAwBpCmC;QAvBJ,MAAMZ,SAASjB,UAAUC;QAEzB,MAAM4B,YAAY,MAAMZ,OAAOa,UAAU,CAAC;YACxCC,cAAchC;YACdE;YACA+B,YAAY;gBACV,GAAGrD,kBAAkB;gBACrBsD,mBAAmB,EAAEnC,iCAAAA,cAAeoC,KAAK;gBACzCC,uBAAuB;YACzB;QACF;QACA,IAAIrC,iBAAiB+B,UAAUO,gBAAgB,EAAE;YAC/CxD,kBAAkBiD,UAAUO,gBAAgB;QAC9C;QACA,0DAA0D;QAC1D,MAAMnB,OAAOoB,GAAG;QAEhB,sBAAsB;QACtBtD,cAAcC,UAAUD,aAAa8C,UAAU9C,WAAW;QAC1DgB,mBAAmBhB,WAAW,GAAGA;QAEjC2C,eAAeC,QAAQ,IAAIE,UAAUF,QAAQ;QAE7C,KAAIE,+BAAAA,UAAUD,iBAAiB,qBAA3BC,6BAA6BS,YAAY,EAAE;gBAUzCT;YATJ,MAAM,EAAEU,YAAY,EAAE,GAAGV,UAAUD,iBAAiB,CAACU,YAAY;YAEjE,IAAIC,cAAc;gBAChBb,eAAeE,iBAAiB,CAACU,YAAY,GAC3CT,UAAUD,iBAAiB,CAACU,YAAY;gBAC1CZ,eAAeE,iBAAiB,CAACU,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAIV,gCAAAA,UAAUD,iBAAiB,qBAA3BC,8BAA6BW,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAGZ,UAAUD,iBAAiB,CAACY,WAAW;gBAErE,IAAIC,mBAAmB;oBACrBf,eAAeE,iBAAiB,CAACY,WAAW,GAC1CX,UAAUD,iBAAiB,CAACY,WAAW;oBAEzCd,eAAeE,iBAAiB,CAACY,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAI/C,cAAcgD,MAAM,KAAK,GAAG;QAC9B7C,gCAAAA,aAAc8C,cAAc;QAC5BrE,IAAIsE,KAAK,CAAC;IACZ;IAEA,OAAOlB;AACT;AAEA,OAAO,eAAemB,aACpBnD,aAA6C;IAE7C,MAAMC,SAASpB,iBAAiBoB,MAAM;IAEtC,IAAIA,OAAOmD,YAAY,CAACC,kBAAkB,EAAE;QAC1ClE,MAAM;QACN,OAAO,MAAMY,uBAAuBC;IACtC,OAAO;QACLb,MAAM;QACN,MAAMmE,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,OAAO,MAAMA;IACf;AACF"}