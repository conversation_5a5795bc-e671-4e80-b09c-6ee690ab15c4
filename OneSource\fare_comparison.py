"""
OneSource Fare Comparison Module

This module provides automated fare comparison functionality for OneSource cruise booking system.
It handles the complete workflow of comparing multiple promotional codes to find the best deal
for a specific cabin category.

Key Features:
    - Automated promo code extraction and filtering
    - Multi-table fare comparison with iterative testing
    - Best fare selection based on category matching and price optimization
    - Redis-based data persistence for session management
    - Screenshot capture for audit trail
    - Support for both single and multiple rate code scenarios

The module integrates with Playwright for web automation and uses Redis for temporary
data storage during the comparison process.

Example:
    fare_comparison = OneSourceFareComparison(
        page=playwright_page,
        session_id="session_123",
        cabin_id=456,
        request_id="req_789"
    )
    
    result = await fare_comparison.process_fare_comparison(
        extracted_category="balcony",
        perk_selection="No"
    )

Author: Cruise Dev Team
Version: 3.0
"""
import asyncio
import json
import logging
import os
from typing import Dict, Any, List, Optional, Tuple

from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError
import redis.asyncio as aioredis

from Core.ui_logger import ui_log
from OneSource.screenshot_utils import OneSourceScreenshotManager
from OneSource.category_selector import OneSourceCategorySelector

logger = logging.getLogger(__name__)

REDIS_URL = os.getenv('REDIS_URL')


class OneSourceFareComparison:
    """
    Automated fare comparison system for OneSource cruise booking platform.
    
    This class handles the complete workflow of comparing multiple promotional codes
    to determine the best available fare for a specific cabin category. It manages
    the complex multi-step process including promo code extraction, filtering,
    iterative comparison, and final selection.
    
    The system supports both single rate code scenarios (when only one valid promo 
    exists) and multi-rate comparisons (when multiple promos need to be tested 
    against each other).
    
    Attributes:
        page (Page): Playwright page instance for web automation
        session_id (str): Unique session identifier for tracking
        cabin_id (int): Database ID for the cabin being processed
        request_id (str): Unique request identifier for this operation
        passenger_count (int): Number of passengers (affects availability filtering)
        screenshot_manager (OneSourceScreenshotManager): Manages screenshot capture
        redis_client (aioredis.Redis): Redis client for data persistence
    
    Note:
        The class maintains state between method calls and uses Redis for temporary
        data storage. Always ensure proper cleanup by allowing the process to complete
        or manually calling _close_redis_client().
    """

    def __init__(
        self,
        page: Page,
        session_id: str = None,
        cabin_id: int = None,
        request_id: str = None,
        passenger_count: int = 2
    ):
        """
        Initialize the OneSource fare comparison system.
        
        Args:
            page (Page): Playwright page instance for web automation
            session_id (str, optional): Session ID for tracking. Defaults to None.
            cabin_id (int, optional): Database cabin ID. Defaults to None.
            request_id (str, optional): Request ID for this operation. Defaults to None.
            passenger_count (int, optional): Number of passengers. Defaults to 2.
        
        Note:
            If session_id, cabin_id, and request_id are provided, screenshot 
            management will be automatically enabled for audit trail purposes.
        """
        self.page = page
        self.session_id = session_id
        self.cabin_id = cabin_id
        self.request_id = request_id
        self.passenger_count = passenger_count
        self.screenshot_manager = None
        self.redis_client = None

        # Initialize screenshot manager if all required IDs are provided
        if session_id and cabin_id and request_id:
            self.screenshot_manager = OneSourceScreenshotManager(
                page=page,
                request_id=request_id,
                cabin_id=cabin_id,
                session_id=session_id
            )

    async def _get_redis_client(self):
        """
        Get or create Redis client instance.
        
        Returns:
            aioredis.Redis: Connected Redis client instance
            
        Note:
            Uses lazy initialization - client is created on first access.
        """
        if not self.redis_client:
            self.redis_client = aioredis.from_url(REDIS_URL, decode_responses=True)
        return self.redis_client

    async def _store_redis_data(self, key: str, data: Any):
        """
        Store data in Redis with session-specific key prefix.
        
        Args:
            key (str): Data key (will be prefixed with session info)
            data (Any): Data to store (will be JSON serialized)
            
        Note:
            Data expires after 1 hour (3600 seconds) to prevent memory leaks.
        """
        try:
            redis_client = await self._get_redis_client()
            redis_key = f"fare_comparison:{self.session_id}:{key}"
            await redis_client.set(redis_key, json.dumps(data), ex=3600)
            logger.info(f"Stored data in Redis with key: {redis_key}")
        except Exception as e:
            logger.error(f"Error storing data in Redis: {e}")

    async def _get_redis_data(self, key: str) -> Optional[Any]:
        """
        Retrieve data from Redis using session-specific key prefix.
        
        Args:
            key (str): Data key (will be prefixed with session info)
            
        Returns:
            Optional[Any]: Retrieved data or None if not found/error
        """
        try:
            redis_client = await self._get_redis_client()
            redis_key = f"fare_comparison:{self.session_id}:{key}"
            data = await redis_client.get(redis_key)
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"Error retrieving data from Redis: {e}")
            return None

    async def _close_redis_client(self):
        """
        Close Redis client connection and cleanup resources.
        
        Note:
            Called automatically in finally block of main process method.
        """
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None

    async def process_fare_comparison(
        self, extracted_category: str, perk_selection: str = "No"
    ) -> Dict[str, Any]:
        """
        Execute the complete fare comparison workflow.
        
        This is the main entry point that orchestrates the entire fare comparison process.
        It handles both single and multiple rate code scenarios, performs iterative
        comparisons, and returns the best available fare option.
        
        The workflow includes:
        1. Click fare comparison button to enter comparison mode
        2. Extract and filter available promo codes
        3. Handle single rate code scenario OR multi-rate comparison
        4. Process category comparisons and find optimal selection
        5. Apply final selection and complete the booking flow
        
        Args:
            extracted_category (str): Target cabin category (e.g., "balcony", "interior")
            perk_selection (str, optional): Perk preference ("Yes", "No"). Defaults to "No".
        
        Returns:
            Dict[str, Any]: Result dictionary containing:
                - success (bool): Whether comparison completed successfully
                - selected_category (str): Final selected category name
                - selected_promo (str): Best promotional code used
                - fare_amount (str): Final fare amount
                - status (str): Availability status
                OR
                - use_normal_flow (bool): True if comparison failed, fallback needed
        
        Raises:
            Exception: Various exceptions may occur during web automation steps.
                      All exceptions are caught and logged, returning fallback result.
        
        Note:
            If any step fails, the method returns {"use_normal_flow": True} to indicate
            that the calling code should proceed with the standard booking flow.
        """
        try:
            logger.info("Starting OneSource fare comparison process")
            ui_log(
                "Starting fare comparison process",
                session_id=self.session_id,
                cabin_id=self.cabin_id,
                step="fare_comparison_start",
                module="OneSource"
            )

            # Step 1: Navigate to fare comparison page
            if not await self._click_fare_comparison_button():
                logger.warning(
                    "Fare Comparison button not found, proceeding with normal flow"
                )
                return {"use_normal_flow": True}

            # Step 2: Extract available promotional codes
            promo_codes = await self._extract_promo_codes()
            if not promo_codes:
                logger.error("Failed to extract promo codes")
                return {"use_normal_flow": True}

            # Step 3: Filter codes based on perk preferences and validity
            filtered_codes = await self._filter_promo_codes(promo_codes, perk_selection)
            if not filtered_codes:
                logger.error("No promo codes remaining after filtering")
                return {"use_normal_flow": True}

            logger.info(f"Filtered promo codes: {len(filtered_codes)} codes remaining")
            await self._store_redis_data("filtered_codes", filtered_codes)

            # Step 4: Handle single rate code scenario (simplified workflow)
            if len(filtered_codes) == 1:
                logger.info("Single rate code scenario detected")
                return await self._handle_single_rate_code(
                    filtered_codes[0], extracted_category
                )

            # Step 5: Multi-rate comparison workflow
            if not await self._select_initial_rate_codes(filtered_codes[:2]):
                logger.error("Failed to select initial rate codes")
                return {"use_normal_flow": True}

            # Handle any notice/disclaimer checkboxes that may appear
            await self._handle_notice_checkboxes()

            # Step 6: Process comparison tables and find best option
            best_fare = await self._process_category_comparison(
                filtered_codes, extracted_category
            )

            if best_fare:
                logger.info(f"Fare comparison completed successfully: {best_fare}")
                ui_log(
                    "Fare comparison completed successfully",
                    session_id=self.session_id,
                    cabin_id=self.cabin_id,
                    step="fare_comparison_complete",
                    module="OneSource"
                )
                return best_fare
            else:
                logger.warning("Fare comparison failed, using normal flow")
                return {"use_normal_flow": True}

        except Exception as e:
            logger.error(f"Error in fare comparison process: {e}")
            ui_log(
                f"Fare comparison error: {e}",
                session_id=self.session_id,
                cabin_id=self.cabin_id,
                step="fare_comparison_error",
                module="OneSource"
            )
            return {"use_normal_flow": True}
        finally:
            # Ensure Redis connection is properly closed
            await self._close_redis_client()

    async def _click_fare_comparison_button(self) -> bool:
        """
        Locate and click the Fare Comparison button to enter comparison mode.
        
        Tries multiple selector strategies to find the fare comparison button,
        as the exact selector may vary depending on page layout or updates.
        
        Returns:
            bool: True if button was found and clicked successfully, False otherwise
        
        Note:
            After clicking, waits for network idle to ensure page has fully loaded
            before proceeding with the next steps.
        """
        try:
            logger.info("Looking for Fare Comparison button")

            # Try multiple selectors in order of specificity
            selectors = [
                'a.capsule_btn:has-text("Fare Comparisons")',
                'a[href*="DFH_PF21"]:has-text("Fare Comparisons")',
                'a:has-text("Fare Comparisons")',
                '//a[contains(@class, "capsule_btn") and contains(text(), "Fare Comparisons")]'
            ]

            for selector in selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=3000)
                    await self.page.click(selector)
                    logger.info(
                        f"Successfully clicked Fare Comparison button with selector: {selector}"
                    )

                    # Wait for page to load after navigation
                    await self.page.wait_for_load_state('networkidle')
                    return True
                except PlaywrightTimeoutError:
                    continue

            logger.warning("Fare Comparison button not found with any selector")
            return False

        except Exception as e:
            logger.error(f"Error clicking Fare Comparison button: {e}")
            return False

    async def _extract_promo_codes(self) -> List[Dict[str, Any]]:
        """
        Extract promotional codes from the fare comparison table.
        
        Scans the promo codes table and extracts relevant information including
        promo code, expiration date, description, and advisory notes. Filters
        out invalid entries using regex pattern matching.
        
        Returns:
            List[Dict[str, Any]]: List of promo code dictionaries, each containing:
                - promo_code (str): The promotional code (e.g., "T33", "KG3")
                - expiration_date (str): Code expiration date
                - description (str): Human-readable description of the promo
                - advisory (str): Advisory notes or warnings
                - row_index (int): Table row index for reference
        
        Note:
            Only codes matching the pattern ^[A-Z0-9]{2,4}$ are considered valid.
            Invalid or malformed codes are filtered out during extraction.
        """
        try:
            import re
            logger.info("Extracting promo codes from table")

            # Wait for the promo codes table to load
            await self.page.wait_for_selector("tr:has-text('Promo')", timeout=10000)

            # Take screenshot for audit trail
            if self.screenshot_manager:
                await self.screenshot_manager.take_full_page_screenshot(
                    "05_promo_codes_page", "Promo Codes"
                )

            promo_codes: List[Dict[str, Any]] = []
            # Regex pattern to validate promo codes (2-4 alphanumeric characters)
            code_pattern = re.compile(r"^[A-Z0-9]{2,4}$")

            # Extract data from each table row
            rows = await self.page.query_selector_all("tr")
            for i, row in enumerate(rows, 1):
                cells = await row.query_selector_all("td")
                # Skip rows with insufficient columns (header/footer rows)
                if len(cells) < 9:
                    continue

                # Extract promo code from 3rd column (index 2)
                promo_text = (await cells[2].inner_text()).strip()
                if not code_pattern.match(promo_text):
                    continue

                # Extract additional metadata from remaining columns
                expiration = (await cells[4].inner_text()).strip()
                description = (await cells[6].inner_text()).strip()
                advisory = (await cells[8].inner_text()).strip()

                promo_codes.append(
                    {
                        "promo_code": promo_text,
                        "expiration_date": expiration,
                        "description": description,
                        "advisory": advisory,
                        "row_index": i,
                    }
                )
                logger.debug(f"Extracted promo: {promo_text} – {description}")

            logger.info(f"Extracted {len(promo_codes)} promo codes (pre-filter)")
            await self._store_redis_data("raw_promo_codes", promo_codes)
            return promo_codes

        except Exception as e:
            logger.error(f"Error extracting promo codes: {e}")
            return []

    async def _filter_promo_codes(
        self, promo_codes: List[Dict[str, Any]], perk_selection: str
    ) -> List[Dict[str, Any]]:
        """
        Filter promotional codes based on validity and perk preferences.
        
        Applies multiple filtering criteria to narrow down the promo codes to only
        those that are valid and match the customer's perk preferences. The filtering
        process removes expired codes, advisory-flagged codes, and applies perk-based
        filtering.
        
        Filtering Logic:
        1. Remove codes with advisory warnings (usually indicates restrictions)
        2. Apply perk filtering based on customer preference:
           - "No": Exclude perk packages (Princess Plus, Premier, etc.)
           - "Yes": Include only perk packages, prioritize "Have it all" if available
        3. Apply secondary "Have it all" filtering for perk=Yes scenarios
        
        Args:
            promo_codes (List[Dict[str, Any]]): Raw promo codes from extraction
            perk_selection (str): Customer perk preference ("Yes" or "No")
        
        Returns:
            List[Dict[str, Any]]: Filtered list of valid promo codes
        
        Note:
            The method prioritizes "Have it all" packages when perk_selection="Yes"
            as these typically offer the best value proposition.
        """
        try:
            logger.info(f"Filtering promo codes with perk selection: {perk_selection}")

            # Step 1: Remove codes with advisory warnings
            filtered_codes = [
                code for code in promo_codes if not code['advisory'].strip()
            ]
            logger.info(f"After advisory filter: {len(filtered_codes)} codes remaining")

            # Step 2: Apply perk-based filtering
            if perk_selection == "No":
                # Exclude perk packages for customers who don't want them
                perk_keywords = [
                    'Perks', 'PPlus', 'Premier', 'Princess Plus', 'Princess Premier',
                    'Have it all'
                ]
                filtered_codes = [
                    code for code in filtered_codes if not any(
                        keyword.lower() in code['description'].lower()
                        for keyword in perk_keywords
                    )
                ]
                logger.info(
                    f"After perk=No filter: {len(filtered_codes)} codes remaining"
                )

            elif perk_selection == "Yes":
                # Include only perk packages for customers who want them
                perk_keywords = [
                    'Perks', 'PPlus', 'Premier', 'Princess Plus', 'Princess Premier',
                    'Have it all'
                ]
                filtered_codes = [
                    code for code in filtered_codes if any(
                        keyword.lower() in code['description'].lower()
                        for keyword in perk_keywords
                    )
                ]
                logger.info(
                    f"After perk=Yes filter: {len(filtered_codes)} codes remaining"
                )

            # Step 3: Apply "Have it all" prioritization for perk=Yes
            if perk_selection == "Yes":
                # Prioritize "Have it all" packages as they typically offer best value
                have_it_all_codes = [
                    code for code in filtered_codes
                    if "have it all" in code['description'].lower()
                ]
                if have_it_all_codes:
                    filtered_codes = have_it_all_codes
                    logger.info(
                        f"After perk=Yes 'Have it all' filter: {len(filtered_codes)} codes remaining"
                    )
                else:
                    logger.info(
                        "No 'Have it all' codes found, keeping existing perk filtered codes"
                    )
            elif perk_selection == "No":
                # Explicitly exclude "Have it all" for no-perk customers
                filtered_codes = [
                    code for code in filtered_codes
                    if "have it all" not in code['description'].lower()
                ]
                logger.info(
                    f"After perk=No (non-'Have it all') filter: {len(filtered_codes)} codes remaining"
                )

            logger.info(
                f"Final filtered codes: {[code['promo_code'] for code in filtered_codes]}"
            )
            return filtered_codes

        except Exception as e:
            logger.error(f"Error filtering promo codes: {e}")
            return promo_codes

    async def _select_initial_rate_codes(
        self, rate_codes: List[Dict[str, Any]]
    ) -> bool:
        """
        Select the first two rate codes for initial comparison.
        
        This method handles the UI interaction to select checkboxes for the first
        two promotional codes, which will be used to populate the comparison tables.
        It includes robust error handling for various checkbox states and layouts.
        
        The process includes:
        1. Navigate to top of page for better element visibility
        2. Find and check boxes for first two promo codes
        3. Uncheck any "Include expired promos" master checkbox
        4. Click Save & Continue to proceed to comparison view
        
        Args:
            rate_codes (List[Dict[str, Any]]): List of rate codes to select from
        
        Returns:
            bool: True if checkboxes were successfully selected and saved, False otherwise
        
        Note:
            Only the first two codes from the list are selected, as the OneSource
            interface displays two-column comparison tables.
        """
        try:
            logger.info("Selecting first two rate codes")
            # Scroll to top for better element visibility
            await self.page.evaluate("window.scrollTo(0,0)")

            async def _tick_checkbox(cb_element):
                """Helper function to safely check a checkbox element."""
                try:
                    # Scroll element into view for better interaction
                    await cb_element.evaluate(
                        "el => el.scrollIntoView({block:'center'})"
                    )
                except Exception:
                    pass

                # Check current state to avoid unnecessary clicks
                already = False
                try:
                    already = await cb_element.evaluate("el => el.checked === true")
                except Exception:
                    pass

                # Click only if not already checked
                if not already:
                    await cb_element.evaluate("el => el.click()")

                # Verify final state
                is_checked = await cb_element.evaluate("el => el.checked === true")
                return is_checked

            # Process first two rate codes
            for code in rate_codes[:2]:
                promo_code = code["promo_code"]
                # Try specific row selector first, then fallback to generic
                row_selector = f"tr:has(> td:nth-child(3):has-text('{promo_code}'))"
                checkbox = None
                try:
                    row = await self.page.wait_for_selector(row_selector, timeout=5000)
                    checkbox = await row.query_selector("input[type='checkbox']")
                except PlaywrightTimeoutError:
                    # Fallback to less specific selector
                    try:
                        checkbox = await self.page.query_selector(
                            f"tr:has-text('{promo_code}') input[type='checkbox']"
                        )
                    except Exception:
                        pass

                if not checkbox:
                    logger.warning(f"Checkbox not found for promo {promo_code}")
                    continue

                if not await _tick_checkbox(checkbox):
                    logger.error(f"Failed to check checkbox for promo {promo_code}")
                    return False
                logger.info(f"Checkbox confirmed for rate code: {promo_code}")
                await asyncio.sleep(0.5)  # Brief pause between selections

            # Ensure "Include expired promos" is unchecked to avoid confusion
            try:
                master_cb = await self.page.query_selector(
                    "input[name='SHOW_EXPIRED_PROMOS']"
                )
                if master_cb and await master_cb.evaluate("el => el.checked"):
                    logger.info("Unchecking 'Include expired promos' master checkbox")
                    await master_cb.evaluate("el => el.click()")
            except Exception:
                pass

            # Use the category selector helper to click Save & Continue
            cat_selector_helper = OneSourceCategorySelector(page=self.page)
            if await cat_selector_helper._click_save_continue():
                try:
                    # Wait for promo list to disappear, indicating successful navigation
                    await self.page.wait_for_selector(
                        "text='PROMO LIST'", state='detached', timeout=10000
                    )
                except Exception:
                    pass
                return True

            logger.error(
                "Could not click Save & Continue button via CategorySelector implementation"
            )
            return False

        except Exception as e:
            logger.error(f"Error selecting initial rate codes: {e}")
            return False

    async def _handle_notice_checkboxes(self) -> bool:
        """
        Handle notice/disclaimer checkboxes that may appear after promo selection.
        
        Some promotional codes require acknowledgment of specific terms and conditions.
        This method detects and handles any notice checkboxes that appear, ensuring
        all required acknowledgments are completed before proceeding.
        
        The process includes:
        1. Check if notice checkbox page appears
        2. Find and check all required notice acknowledgment boxes
        3. Take screenshot for audit purposes
        4. Click Save & Continue to proceed
        
        Returns:
            bool: True if notices were handled successfully or no notices found
        
        Note:
            This step is optional - if no notice page appears, the method returns True
            to continue with the normal flow. The method searches across all frames
            to handle various page layouts.
        """
        try:
            # Multiple selectors to detect notice checkbox pages
            notice_selectors = [
                'input[name="RECAPFLAG"]',
                'input[type="checkbox"]:has-text("I have read each notice listed above")',
                '//input[@type="checkbox" and contains(following-sibling::text(), "I have read")]'
            ]

            # Ensure the page has settled before searching for notice checkboxes
            await self.page.wait_for_load_state('networkidle')

            # Check if any notice selectors are present
            notice_found = False
            for selector in notice_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=2000)
                    notice_found = True
                    break
                except PlaywrightTimeoutError:
                    continue

            if not notice_found:
                logger.info("No notice checkboxes found, proceeding")
                return True

            logger.info("Notice checkbox page detected, selecting all checkboxes")

            # Take screenshot for audit trail
            if self.screenshot_manager:
                await self.screenshot_manager.take_full_page_screenshot(
                    "06_notice_checkboxes", "Notice Checkboxes"
                )

            async def _tick_notice_box(box):
                """Helper function to check notice boxes safely."""
                try:
                    await box.evaluate("el => el.scrollIntoView({block:'center'})")
                except Exception:
                    pass
                await box.evaluate("el => el.click()")
                return await box.evaluate("el => el.checked === true")

            # Search across all frames for notice checkboxes (some may be in iframes)
            total_checked = 0
            for frame in self.page.frames:
                try:
                    boxes = await frame.query_selector_all("input[name='RECAPFLAG']")
                    for b in boxes:
                        try:
                            if await _tick_notice_box(b):
                                total_checked += 1
                        except Exception:
                            continue
                except Exception:
                    continue

            if total_checked == 0:
                logger.warning(
                    "No notice checkboxes were found/checked – page structure may have changed"
                )
            else:
                logger.info(f"Checked {total_checked} notice checkbox(es)")

            # Find and click Save & Continue button
            save_continue_selectors = [
                'a.capsule_save_continue_btn:has-text("Save & Continue")',
                'a[href*="DFH_ENTER"]:has-text("Save & Continue")'
            ]

            for selector in save_continue_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=3000)
                    await self.page.click(selector)
                    logger.info("Clicked Save & Continue on notice page")
                    # Wait for possible navigation or ajax completion
                    await self.page.wait_for_load_state('networkidle')
                    return True
                except PlaywrightTimeoutError:
                    continue

            logger.warning("Could not find Save & Continue on notice page")
            return True  # Continue anyway as this is non-critical

        except Exception as e:
            logger.error(f"Error handling notice checkboxes: {e}")
            return True  # Continue anyway as this is non-critical

    async def _process_category_comparison(
        self, filtered_codes: List[Dict[str, Any]], extracted_category: str
    ) -> Optional[Dict[str, Any]]:
        """
        Process the main category comparison workflow using multiple rate codes.
        
        This method orchestrates the complex multi-step comparison process that
        evaluates different promotional codes across various cabin categories to
        find the optimal fare. It handles both the initial two-code comparison
        and iterative testing of additional codes.
        
        The workflow includes:
        1. Extract data from initial two-table comparison
        2. Create best deal table from initial comparison
        3. Perform iterative comparison with remaining codes
        4. Find best fare matching customer's category preference
        5. Apply final selection and complete booking
        
        Args:
            filtered_codes (List[Dict[str, Any]]): Filtered promotional codes to compare
            extracted_category (str): Target cabin category for selection
        
        Returns:
            Optional[Dict[str, Any]]: Best fare result containing:
                - success (bool): Whether selection was successful
                - selected_category (str): Final category selected
                - selected_promo (str): Best promotional code
                - fare_amount (str): Final fare amount
                - status (str): Availability status
            Returns None if comparison fails.
        
        Note:
            This method handles the most complex part of the fare comparison process,
            managing state across multiple page interactions and data extractions.
        """
        try:
            logger.info("Processing category comparison with two tables")

            # Wait for comparison tables to load
            await self.page.wait_for_selector('table', timeout=10000)

            # Take screenshot for audit trail
            if self.screenshot_manager:
                await self.screenshot_manager.take_full_page_screenshot(
                    "07_category_comparison_initial", "Category Comparison Initial"
                )

            # Step 1: Extract data from initial two-table comparison
            comparison_data = await self._extract_comparison_tables(filtered_codes[:2])
            if not comparison_data:
                logger.error("Failed to extract comparison table data")
                return None

            # Step 2: Create initial best deal table by comparing first two codes
            best_deal_table = await self._create_best_deal_table(
                comparison_data, filtered_codes[:2]
            )
            # Display comparison results for debugging/monitoring
            await self._print_side_by_side_comparison(
                filtered_codes[:2], comparison_data
            )
            await self._print_best_deal_table(best_deal_table, "Initial Best Deals")

            # Step 3: If more than 2 codes, perform iterative comparison
            if len(filtered_codes) > 2:
                best_deal_table = await self._perform_iterative_comparison(
                    best_deal_table, filtered_codes[2:], extracted_category
                )

            # Step 4: Find the best fare for the requested category
            best_fare = await self._find_best_fare_from_best_deal_table(
                best_deal_table, extracted_category
            )

            # Step 5: Apply final selection if best fare found
            if best_fare:
                return await self._apply_final_selection(best_fare)

            return None

        except Exception as e:
            logger.error(f"Error in category comparison: {e}")
            return None

    async def _extract_comparison_tables(
        self, initial_codes: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Extract fare data from the two-column comparison tables.
        
        OneSource displays comparison data in two side-by-side tables, each showing
        categories and fares for different promotional codes. This method extracts
        all available data from both tables for comparison analysis.
        
        The extraction process:
        1. Find all rows containing radio buttons (category selection rows)
        2. Extract left table data (first promo code)
        3. Extract right table data (second promo code)
        4. Structure data for comparison processing
        
        Args:
            initial_codes (List[Dict[str, Any]]): First two promotional codes being compared
        
        Returns:
            Dict[str, Any]: Comparison data structure containing:
                - left_table (List[Dict]): Categories and fares for first promo
                - right_table (List[Dict]): Categories and fares for second promo
                - promo_codes (List[str]): The promotional codes being compared
        
        Note:
            Each table entry contains category name, status, fare amount, promo code,
            and radio button value for selection. Invalid entries are filtered out
            during extraction.
        """
        try:
            logger.info("Extracting data from comparison tables")

            # Find all category rows with radio buttons for selection
            category_rows = await self.page.query_selector_all(
                'tr:has(input[type="radio"])'
            )

            if not category_rows:
                logger.error("No category rows found with radio buttons")
                return {}

            left_table_data = []
            right_table_data = []

            # Process each row to extract left and right table data
            for row in category_rows:
                try:
                    # Check for left table radio button (first promo code)
                    left_radio = await row.query_selector(
                        'input[name="CATEGORY_SELECTION"]'
                    )
                    if left_radio:
                        left_data = await self._extract_row_data(
                            row, "left", initial_codes[0]['promo_code']
                            if len(initial_codes) > 0 else "T33"
                        )
                        if left_data:
                            left_table_data.append(left_data)

                    # Check for right table radio button (second promo code)
                    right_radio = await row.query_selector(
                        'input[name="CATEGORY_GROUP_SELECTION"]'
                    )
                    if right_radio:
                        right_data = await self._extract_row_data(
                            row, "right", initial_codes[1]['promo_code']
                            if len(initial_codes) > 1 else "KG3"
                        )
                        if right_data:
                            right_table_data.append(right_data)

                except Exception as e:
                    logger.warning(f"Error processing row: {e}")
                    continue

            # Structure the comparison data
            comparison_data = {
                'left_table': left_table_data, 'right_table': right_table_data,
                'promo_codes': [code['promo_code'] for code in initial_codes]
            }

            logger.info(
                f"Extracted {len(left_table_data)} left table categories and {len(right_table_data)} right table categories"
            )

            # Log sample entries for debugging
            if left_table_data:
                logger.debug(f"Sample left table entry: {left_table_data[0]}")
            if right_table_data:
                logger.debug(f"Sample right table entry: {right_table_data[0]}")

            # Store in Redis for potential debugging/recovery
            await self._store_redis_data("comparison_data", comparison_data)

            return comparison_data

        except Exception as e:
            logger.error(f"Error extracting comparison tables: {e}")
            return {}

    async def _extract_row_data(self, row, table_side: str,
                                promo_code: str) -> Optional[Dict[str, Any]]:
        """
        Extract fare data from a single table row.
        
        Parses individual table rows to extract category information, pricing,
        and selection metadata. Applies validation to ensure data quality and
        filters out invalid or placeholder entries.
        
        Data Validation:
        - Category names must be meaningful (not system placeholders)
        - Fare amounts must be valid numeric values within reasonable range
        - Filters out navigation elements and system text
        
        Args:
            row: Playwright element representing the table row
            table_side (str): "left" or "right" to determine column positions
            promo_code (str): Promotional code associated with this data
        
        Returns:
            Optional[Dict[str, Any]]: Row data dictionary containing:
                - category (str): Category name (e.g., "Interior", "Balcony")
                - status (str): Availability status (e.g., "Available", "G")
                - fare_amount (str): Cleaned fare amount as string
                - promo (str): Associated promotional code
                - radio_value (str): Radio button value for selection
                - table_side (str): Which table this data came from
            Returns None if row data is invalid or extraction fails.
        
        Note:
            Column positions differ between left and right tables, requiring
            different extraction logic based on table_side parameter.
        """
        try:
            cells = await row.query_selector_all('td')
            if len(cells) < 10:  # Ensure row has enough columns
                return None

            # Extract data based on table side (columns differ between tables)
            if table_side == "left":
                category_cell = cells[1]
                status_cell = cells[2]
                fare_cell = cells[3]
                radio_value = await (
                    await row.query_selector('input[name="CATEGORY_SELECTION"]')
                ).get_attribute('value')
            else:  # right table
                category_cell = cells[7]
                status_cell = cells[8]
                fare_cell = cells[9]
                radio_input = await row.query_selector(
                    'input[name="CATEGORY_GROUP_SELECTION"]'
                )
                radio_value = await radio_input.get_attribute(
                    'value'
                ) if radio_input else None

            # Extract text content from cells
            category_text = (await category_cell.inner_text()).strip()
            status_text = (await status_cell.inner_text()).strip()
            fare_text = (await fare_cell.inner_text()).strip()

            # Validate category text (filter out system elements/placeholders)
            if (not category_text or len(category_text) < 2
                    or 'promo' in category_text.lower()
                    or 'currency' in category_text.lower()
                    or category_text.startswith('Promo ')
                    or category_text.upper() in ['PREVIOUSNEXT', 'PREVIOUS', 'NEXT',
                                                 'UPDATE'] or category_text.isdigit()):
                return None

            # Clean and validate fare amount
            import re
            fare_clean = re.sub(r'[^\d.,]', '', fare_text).replace(',', '')

            # Validate fare format and range
            if (not fare_clean or fare_clean == '..' or fare_clean == '.'
                    or not fare_clean.replace('.', '').isdigit()):
                return None

            try:
                fare_float = float(fare_clean)
                # Sanity check: fare should be reasonable (not negative or extremely high)
                if fare_float <= 0 or fare_float > 50000:
                    return None
            except ValueError:
                return None

            return {
                'category': category_text, 'status': status_text, 'fare_amount':
                fare_clean, 'promo': promo_code, 'radio_value': radio_value,
                'table_side': table_side
            }

        except Exception as e:
            logger.error(f"Error extracting row data: {e}")
            return None

    async def _create_best_deal_table(
        self, comparison_data: Dict[str, Any], initial_codes: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Create a best deal table by comparing fares between left and right tables.
        
        Analyzes the extracted comparison data to determine the best fare for each
        cabin category by comparing prices between the two promotional codes.
        Creates a consolidated table containing the winning option for each category.
        
        Comparison Logic:
        - For each category appearing in both tables, select the lower fare
        - Include categories that appear in only one table
        - Handle price comparison with error handling for invalid data
        - Log comparison decisions for debugging
        
        Args:
            comparison_data (Dict[str, Any]): Extracted data from both comparison tables
            initial_codes (List[Dict[str, Any]]): The two promotional codes being compared
        
        Returns:
            Dict[str, Any]: Best deal table where keys are category names and values
            are the winning fare data dictionaries containing all original row data
            plus the best fare selection.
        
        Note:
            This method forms the foundation for the iterative comparison process,
            establishing the initial "best deals" that subsequent codes will be
            compared against.
        """
        try:
            best_deal_table = {}
            left_table = comparison_data.get('left_table', [])
            right_table = comparison_data.get('right_table', [])

            # Create lookup map for right table data by category name
            right_table_map = {item['category']: item for item in right_table}

            # Compare each left table entry against right table equivalent
            for left_item in left_table:
                category = left_item['category']
                best_item = left_item.copy()  # Default to left table item

                if category in right_table_map:
                    right_item = right_table_map[category]

                    try:
                        # Convert fare amounts to float for comparison
                        left_price = float(
                            left_item['fare_amount']
                        ) if left_item['fare_amount'] else float('inf')
                        right_price = float(
                            right_item['fare_amount']
                        ) if right_item['fare_amount'] else float('inf')

                        # Select the better (lower) price
                        if right_price < left_price:
                            best_item = right_item.copy()
                            logger.info(
                                f"Right table wins for {category}: {right_price} < {left_price} (promo: {right_item['promo']})"
                            )
                        else:
                            logger.info(
                                f"Left table wins for {category}: {left_price} <= {right_price} (promo: {left_item['promo']})"
                            )

                    except (ValueError, TypeError):
                        logger.warning(f"Could not compare prices for {category}")

                best_deal_table[category] = best_item

            # Add any right table categories not present in left table
            for right_item in right_table:
                if right_item['category'] not in best_deal_table:
                    best_deal_table[right_item['category']] = right_item

            # Store results and log summary statistics
            await self._store_redis_data("best_deal_table", best_deal_table)
            logger.info(
                f"Created best deal table with {len(best_deal_table)} categories"
            )

            # Validate data quality
            valid_entries = [
                k for k, v in best_deal_table.items()
                if v.get('category') and len(v.get('category', '')) > 2
                and 'promo' not in v.get('category', '').lower()
            ]
            logger.debug(
                f"Best deal table has {len(valid_entries)} valid entries out of {len(best_deal_table)} total"
            )

            if valid_entries:
                sample_entry = best_deal_table[valid_entries[0]]
                logger.debug(
                    f"Sample best deal table entry: {valid_entries[0]} -> {sample_entry}"
                )

            return best_deal_table

        except Exception as e:
            logger.error(f"Error creating best deal table: {e}")
            return {}

    async def _print_side_by_side_comparison(
        self, promo_codes: List[Dict[str, Any]], comparison_data: Dict[str, Any]
    ):
        """
        Display side-by-side comparison of two promotional codes.
        
        Formats and prints a readable comparison table showing categories,
        statuses, fares, and promotional codes for the two codes being compared.
        Used for debugging and monitoring the comparison process.
        
        Args:
            promo_codes (List[Dict[str, Any]]): The two promotional codes being compared
            comparison_data (Dict[str, Any]): Extracted comparison table data
        
        Note:
            Output is printed to console for debugging purposes. In production,
            this could be redirected to logging or monitoring systems.
        """
        try:
            left_table = comparison_data.get('left_table', [])
            right_table = comparison_data.get('right_table', [])
            left_promo = promo_codes[0]['promo_code'] if len(promo_codes) > 0 else "T33"
            right_promo = promo_codes[1]['promo_code'] if len(
                promo_codes
            ) > 1 else "KG3"

            print(f"\n{'='*120}")
            print(f"SIDE-BY-SIDE COMPARISON: {left_promo} vs {right_promo}")
            print(f"{'='*120}")
            print(
                f"{'Category':<15} {'Status':<8} {'Fare':<10} {'Promo':<6} {'Category':<15} {'Status':<8} {'Fare':<10} {'Promo':<6}"
            )
            print(f"{'-'*120}")

            # Filter valid entries for display
            valid_left_table = [
                item for item in left_table
                if item.get('category') and len(item.get('category', '')) > 2
                and 'promo' not in item.get('category', '').lower()
            ]

            # Create lookup map for right table entries
            right_map = {
                item['category']: item
                for item in right_table
                if item.get('category') and len(item.get('category', '')) > 2
                and 'promo' not in item.get('category', '').lower()
            }

            # Display comparison rows
            for left_item in valid_left_table:
                category = left_item['category']
                right_item = right_map.get(category, {})

                # Format left table data (truncate for display)
                left_cat = left_item.get('category', '')[:14]
                left_status = left_item.get('status', '')[:7]
                left_fare = left_item.get('fare_amount', '')[:9]
                left_promo_short = left_item.get('promo', '')[:5]

                # Format right table data (truncate for display)
                right_cat = right_item.get('category', '')[:14]
                right_status = right_item.get('status', '')[:7]
                right_fare = right_item.get('fare_amount', '')[:9]
                right_promo_short = right_item.get('promo', '')[:5]

                print(
                    f"{left_cat:<15} {left_status:<8} {left_fare:<10} {left_promo_short:<6} {right_cat:<15} {right_status:<8} {right_fare:<10} {right_promo_short:<6}"
                )

            print(f"{'='*120}\n")

        except Exception as e:
            logger.error(f"Error printing side-by-side comparison: {e}")

    async def _print_iterative_side_by_side_comparison(
        self, best_deal_table: Dict[str, Any],
        updated_right_table: List[Dict[str, Any]], promo_code: str
    ):
        """
        Display comparison between current best deals and new promotional code.
        
        Shows the iterative comparison process by displaying current best deals
        alongside the updated pricing from a new promotional code being tested.
        
        Args:
            best_deal_table (Dict[str, Any]): Current best deals by category
            updated_right_table (List[Dict[str, Any]]): New pricing data to compare
            promo_code (str): The promotional code being tested
        
        Note:
            Used during the iterative comparison process to show how each new
            promotional code affects the overall best deals table.
        """
        try:
            print(f"\n{'='*120}")
            print(f"SIDE-BY-SIDE COMPARISON: Best Deal vs {promo_code}")
            print(f"{'='*120}")
            print(
                f"{'Category':<15} {'Status':<8} {'Fare':<10} {'Promo':<6} {'Category':<15} {'Status':<8} {'Fare':<10} {'Promo':<6}"
            )
            print(f"{'-'*120}")

            # Filter valid best deal entries
            valid_deal = {
                k: v
                for k, v in best_deal_table.items()
                if v.get('category') and len(v.get('category', '')) > 2
                and 'promo' not in v.get('category', '').lower()
            }

            # Create lookup map for new pricing data
            right_map = {
                item['category']: item
                for item in updated_right_table
                if item.get('category') and len(item.get('category', '')) > 2
                and 'promo' not in item.get('category', '').lower()
            }

            # Display comparison rows
            for category, best_item in valid_deal.items():
                right_item = right_map.get(category, {})

                # Format best deal data (left side)
                left_cat = best_item.get('category', '')[:14]
                left_status = best_item.get('status', '')[:7]
                left_fare = best_item.get('fare_amount', '')[:9]
                left_promo_short = best_item.get('promo', '')[:5]

                # Format new pricing data (right side)
                right_cat = right_item.get('category', '')[:14]
                right_status = right_item.get('status', '')[:7]
                right_fare = right_item.get('fare_amount', '')[:9]
                right_promo_short = right_item.get('promo', '')[:5]

                print(
                    f"{left_cat:<15} {left_status:<8} {left_fare:<10} {left_promo_short:<6} {right_cat:<15} {right_status:<8} {right_fare:<10} {right_promo_short:<6}"
                )

            print(f"{'='*120}\n")

        except Exception as e:
            logger.error(f"Error printing iterative side-by-side comparison: {e}")

    async def _print_best_deal_table(self, best_deal_table: Dict[str, Any], title: str):
        """
        Display the current best deals table in a formatted layout.
        
        Presents the best deals table in a readable format, sorted by price,
        showing the optimal fare for each category along with associated
        promotional codes and availability status.
        
        Args:
            best_deal_table (Dict[str, Any]): Best deals data by category
            title (str): Display title for the table
        
        Note:
            Filters out invalid entries and sorts by price to provide a clear
            view of available options. Used for debugging and monitoring.
        """
        try:
            print(f"\n{'='*80}")
            print(f"{title.upper()}")
            print(f"{'='*80}")
            print(f"{'Category':<20} {'Status':<8} {'Fare':<12} {'Promo':<6}")
            print(f"{'-'*80}")

            valid_items = []
            # Filter and validate entries for display
            for category, item in best_deal_table.items():
                if not item.get('category') or not item.get('fare_amount'):
                    continue

                category_name = item.get('category', '').strip()
                # Skip system entries and invalid categories
                if (len(category_name) < 3 or 'promo' in category_name.lower()
                        or 'currency' in category_name.lower()
                        or category_name.startswith('Promo ')
                        or category_name in ['PREVIOUSNEXT', 'PREVIOUS', 'NEXT']):
                    continue

                # Parse and validate fare amounts
                fare_amount = item.get('fare_amount', '')
                try:
                    fare_clean = fare_amount.replace(',', '').replace('$', '').strip()
                    if fare_clean and fare_clean != '..' and fare_clean.replace(
                            '.', '').isdigit():
                        fare_float = float(fare_clean)
                        valid_items.append((category, item, fare_float))
                except (ValueError, AttributeError):
                    valid_items.append((category, item, 999999))  # Sort invalid fares last

            # Sort by fare amount (lowest first)
            valid_items.sort(key=lambda x: x[2])

            if not valid_items:
                print(f"{'No valid deals found':<20} {'':<8} {'':<12} {'':<6}")
            else:
                # Display sorted entries
                for category, item, _ in valid_items:
                    cat_name = item.get('category', '')[:19]
                    status = item.get('status', '')[:7]
                    fare = item.get('fare_amount', '')[:11]
                    promo = item.get('promo', '')[:5]

                    print(f"{cat_name:<20} {status:<8} {fare:<12} {promo:<6}")

            print(f"{'='*80}\n")

        except Exception as e:
            logger.error(f"Error printing best deal table: {e}")
            logger.debug(f"Best deal table contents: {best_deal_table}")

    async def _perform_iterative_comparison(
        self, best_deal_table: Dict[str, Any], remaining_codes: List[Dict[str, Any]],
        extracted_category: str
    ) -> Dict[str, Any]:
        """
        Perform iterative comparison testing additional promotional codes.
        
        Tests each remaining promotional code against the current best deals table,
        updating entries when better fares are found. This ensures comprehensive
        testing of all available promotional codes to find the absolute best deals.
        
        The iterative process:
        1. For each remaining promotional code:
           a. Enter the code in the comparison interface
           b. Click update to refresh pricing
           c. Extract updated right table data
           d. Compare against current best deals
           e. Update best deals table with improvements
           f. Log and display comparison results
        
        Args:
            best_deal_table (Dict[str, Any]): Current best deals by category
            remaining_codes (List[Dict[str, Any]]): Additional codes to test
            extracted_category (str): Customer's target category (for logging)
        
        Returns:
            Dict[str, Any]: Updated best deal table with improvements from additional codes
        
        Note:
            The method continues testing even if individual codes fail, ensuring
            maximum coverage of available promotional options. Each iteration
            updates the Redis cache and displays comparison results.
        """
        try:
            logger.info(
                f"Starting iterative comparison with {len(remaining_codes)} remaining codes"
            )

            # Test each remaining promotional code
            for code_info in remaining_codes:
                promo_code = code_info['promo_code']
                logger.info(f"Testing promo code: {promo_code}")

                # Step 1: Enter the promotional code
                if not await self._enter_promo_code(promo_code):
                    logger.warning(f"Failed to enter promo code: {promo_code}")
                    continue

                # Step 2: Click update to refresh pricing with new code
                if not await self._click_update_button():
                    logger.warning(
                        f"Failed to click update for promo code: {promo_code}"
                    )
                    continue

                # Wait until network is idle after applying promo update
                await self.page.wait_for_load_state('networkidle')

                # Step 3: Take screenshot for audit trail
                if self.screenshot_manager:
                    await self.screenshot_manager.take_full_page_screenshot(
                        f"08_comparison_{promo_code}", f"Comparison {promo_code}"
                    )

                # Step 4: Extract updated pricing data
                updated_right_table = await self._extract_updated_right_table(
                    promo_code
                )

                if not updated_right_table:
                    logger.warning(
                        f"Failed to extract right table data for {promo_code}"
                    )
                    continue

                # Step 5: Compare against current best deals and update improvements
                updated_count = 0
                for item in updated_right_table:
                    category = item['category']

                    try:
                        current_best = best_deal_table.get(category)
                        if current_best:
                            # Compare prices and update if new code is better
                            current_price = float(
                                current_best['fare_amount']
                            ) if current_best['fare_amount'] else float('inf')
                            new_price = float(
                                item['fare_amount']
                            ) if item['fare_amount'] else float('inf')

                            if new_price < current_price:
                                best_deal_table[category] = item
                                updated_count += 1
                                logger.info(
                                    f"Updated {category}: {promo_code} has better price ({new_price} vs {current_price})"
                                )
                        else:
                            # Add new category if not previously available
                            best_deal_table[category] = item
                            updated_count += 1

                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error comparing prices for {category}: {e}")

                logger.info(f"Updated {updated_count} categories with {promo_code}")

                # Step 6: Store updated results and display comparison
                await self._store_redis_data("best_deal_table", best_deal_table)

                await self._print_iterative_side_by_side_comparison(
                    best_deal_table, updated_right_table, promo_code
                )

                await self._print_best_deal_table(
                    best_deal_table, f"Updated Best Deals (after {promo_code})"
                )

            logger.info("Iterative comparison completed")
            return best_deal_table

        except Exception as e:
            logger.error(f"Error in iterative comparison: {e}")
            return best_deal_table

    async def _extract_updated_right_table(self,
                                           promo_code: str) -> List[Dict[str, Any]]:
        """
        Extract updated pricing data from the right comparison table.
        
        After entering a new promotional code and clicking update, this method
        extracts the refreshed pricing data from the right comparison table
        for comparison against current best deals.
        
        Args:
            promo_code (str): The promotional code that was just applied
        
        Returns:
            List[Dict[str, Any]]: Updated right table data with refreshed pricing
        
        Note:
            Uses the standard _extract_row_data method to maintain consistency
            with initial table extraction logic.
        """
        try:
            # Find all right table rows (contain right table radio buttons)
            right_rows = await self.page.query_selector_all(
                'tr:has(input[name="CATEGORY_GROUP_SELECTION"])'
            )

            if not right_rows:
                logger.error("No right table rows found")
                return []

            right_table_data = []

            # Extract data from each right table row
            for row in right_rows:
                try:
                    right_data = await self._extract_row_data(row, "right", promo_code)
                    if right_data:
                        right_table_data.append(right_data)

                except Exception as e:
                    logger.warning(f"Error processing right table row: {e}")
                    continue

            logger.info(
                f"Extracted {len(right_table_data)} categories from updated right table"
            )
            return right_table_data

        except Exception as e:
            logger.error(f"Error extracting updated right table data: {e}")
            return []

    async def _find_best_fare_from_best_deal_table(
        self, best_deal_table: Dict[str, Any], extracted_category: str
    ) -> Optional[Dict[str, Any]]:
        """
        Find the optimal fare from the best deal table for the requested category.
        
        Implements sophisticated category matching and fare selection logic to find
        the best available option for the customer's requested cabin category.
        Handles various category name variations and applies business rules for
        availability filtering.
        
        Selection Process:
        1. Apply passenger count and status filtering (removes invalid options)
        2. Sort remaining options by price (lowest first)
        3. Apply category matching with flexible keyword matching
        4. Return best match or cheapest alternative if no exact match
        
        Category Matching:
        - Supports fuzzy matching for common category variations
        - Maps customer input to standard category types
        - Handles edge cases like "obstructed" oceanview vs standard oceanview
        
        Args:
            best_deal_table (Dict[str, Any]): Consolidated best deals by category
            extracted_category (str): Customer's requested category type
        
        Returns:
            Optional[Dict[str, Any]]: Best fare data if found, None if no valid options
        
        Note:
            The method applies business rules like filtering out "G" status categories
            for groups larger than 2 passengers, as these typically have restricted
            availability for larger parties.
        """
        try:
            logger.info(f"Finding best fare for category: {extracted_category}")

            valid_categories = {}

            # Step 1: Apply status and passenger count filtering
            for name, data in best_deal_table.items():
                status_raw = data.get('status', '') or ''
                status = status_raw.strip().upper()

                # Check for 'G' (group) status or numeric-only status
                contains_g = 'G' in status
                is_numeric_only = status.isdigit()

                # Only allow categories with valid status indicators
                if not (contains_g or is_numeric_only):
                    continue

                # Filter out 'G' status for larger groups (restricted availability)
                if self.passenger_count > 2 and contains_g:
                    continue

                valid_categories[name] = data

            logger.info(
                f"After applying status/passenger filters: {len(valid_categories)} of {len(best_deal_table)} categories remain"
            )

            # Fallback if filtering removes all options
            if not valid_categories:
                logger.warning(
                    "No categories passed status/passenger filters, falling back to original table"
                )
                valid_categories = best_deal_table

            # Step 2: Sort categories by price (lowest first)
            sorted_categories = []
            for name, data in valid_categories.items():
                try:
                    # Clean price string and convert to float
                    price = float(data['fare_amount'].replace(',', '').replace('*', ''))
                    sorted_categories.append(
                        {'name': name, 'price': price, 'data': data}
                    )
                except ValueError:
                    logger.warning(
                        f"Could not parse price for {name}: {data['fare_amount']}"
                    )

            sorted_categories.sort(key=lambda x: x['price'])
            logger.info(f"Sorted {len(sorted_categories)} categories by price")

            # Step 3: Category matching with flexible keyword matching
            category_mapping = {
                'interior': ['interior', 'inside'], 'oceanview':
                ['oceanview', 'ocean view', 'outside', 'obstructed'], 'balcony':
                ['balcony', 'veranda', 'bal'], 'suite': ['suite', 'suites']
            }

            normalized_category = extracted_category.lower().strip()
            target_keywords = []
            matched_category_type = None

            # Find matching category type through exact and substring matching
            for cat_type, keywords in category_mapping.items():
                if normalized_category in [kw.lower() for kw in keywords]:
                    target_keywords = keywords
                    matched_category_type = cat_type
                    logger.info(f"Exact match - category type: {cat_type}")
                    break
                elif any(keyword.lower() in normalized_category and len(keyword) > 3
                         for keyword in keywords):
                    target_keywords = keywords
                    matched_category_type = cat_type
                    logger.info(f"Substring match - category type: {cat_type}")
                    break

            # Default to balcony if no match found (most common request)
            if not target_keywords:
                target_keywords = category_mapping['balcony']
                matched_category_type = 'balcony'
                logger.info(f"No category match found, defaulting to balcony")

            logger.info(
                f"Searching for {matched_category_type} categories with keywords: {target_keywords}"
            )

            # Step 4: Find best matching category
            for category_item in sorted_categories:
                name = category_item['name']
                name_lower = name.lower()

                # Apply sophisticated keyword matching logic
                category_found = False
                for keyword in target_keywords:
                    keyword_lower = keyword.lower()
                    if (keyword_lower in name_lower and
                        (len(keyword_lower) <= 3 or keyword_lower == normalized_category
                         or f" {keyword_lower}" in name_lower or f"{keyword_lower} "
                         in name_lower or name_lower.startswith(keyword_lower)
                         or name_lower.endswith(keyword_lower))):
                        category_found = True
                        break

                if category_found:
                    logger.info(
                        f"Found matching category: {name} - ${category_item['price']} - Promo: {category_item['data']['promo']}"
                    )
                    return category_item['data']

            # Fallback: return cheapest available if no category match
            if sorted_categories:
                logger.warning(
                    f"No {extracted_category} category found, selecting cheapest available category"
                )
                best_category = sorted_categories[0]
                logger.info(
                    f"Selected cheapest available: {best_category['name']} - ${best_category['price']} - Promo: {best_category['data']['promo']}"
                )
                return best_category['data']

            logger.error("No valid categories found")
            return None

        except Exception as e:
            logger.error(f"Error finding best fare from best deal table: {e}")
            return None

    async def _apply_final_selection(self, best_fare: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply the final fare selection and complete the booking process.
        
        Takes the identified best fare option and applies it through the OneSource
        interface by entering the promotional code, updating pricing, selecting
        the category, and proceeding to the next booking step.
        
        Final Selection Process:
        1. Enter the winning promotional code
        2. Click update to apply the code
        3. Select the radio button for the winning category
        4. Click Save & Continue to proceed with booking
        
        Args:
            best_fare (Dict[str, Any]): Best fare data containing:
                - category (str): Category name to select
                - promo (str): Promotional code to apply
                - fare_amount (str): Expected fare amount
                - status (str): Availability status
                - table_side (str): Which table the selection came from
                - radio_value (str): Radio button value for selection
        
        Returns:
            Dict[str, Any]: Success result containing:
                - success (bool): True if selection completed successfully
                - selected_category (str): Final category selected
                - selected_promo (str): Promotional code applied
                - fare_amount (str): Final fare amount
                - status (str): Availability status
            OR fallback result {"use_normal_flow": True} if any step fails
        
        Note:
            This method represents the culmination of the fare comparison process,
            applying the optimal selection identified through the comparison workflow.
        """
        try:
            category_name = best_fare['category']
            promo_code = best_fare['promo']

            logger.info(
                f"Applying final selection: {category_name} with promo {promo_code}"
            )

            # Step 1: Enter the winning promotional code
            if not await self._enter_promo_code(promo_code):
                logger.error("Failed to enter final promo code")
                return {"use_normal_flow": True}

            # Step 2: Update pricing with the final code
            if not await self._click_update_button():
                logger.error("Failed to click final update")
                return {"use_normal_flow": True}

            # Wait until network is idle after final update click
            await self.page.wait_for_load_state('networkidle')

            # Step 3: Take final screenshot for audit trail
            if self.screenshot_manager:
                await self.screenshot_manager.take_full_page_screenshot(
                    "09_final_selection", "Final Selection"
                )

            # Step 4: Select the winning category's radio button
            if not await self._select_final_category_fixed(best_fare):
                logger.error("Failed to select final category")
                return {"use_normal_flow": True}

            # Step 5: Proceed to next booking step
            if not await self._click_final_save_continue():
                logger.error("Failed to click final Save & Continue")
                return {"use_normal_flow": True}

            logger.info("Fare comparison workflow completed successfully")

            # Return success result with all relevant details
            return {
                "success": True, "selected_category": category_name, "selected_promo":
                promo_code, "fare_amount": best_fare['fare_amount'], "status":
                best_fare['status']
            }

        except Exception as e:
            logger.error(f"Error in final selection: {e}")
            return {"use_normal_flow": True}

    async def _select_final_category_fixed(self, best_fare: Dict[str, Any]) -> bool:
        """
        Select the radio button for the final chosen category.
        
        Locates and selects the radio button corresponding to the optimal fare
        category identified through the comparison process. Handles both left
        and right table selections with robust selector strategies.
        
        Args:
            best_fare (Dict[str, Any]): Best fare data containing:
                - category (str): Category name to select
                - table_side (str): "left" or "right" table origin
                - radio_value (str): Radio button value if available
        
        Returns:
            bool: True if category radio button was successfully selected
        
        Note:
            Uses multiple selector strategies to handle variations in table
            structure and radio button implementation across different pages.
        """
        try:
            category_name = best_fare['category']
            table_side = best_fare.get('table_side', 'right')
            radio_value = best_fare.get('radio_value')

            logger.info(f"Selecting category: {category_name} from {table_side} table")

            selectors_to_try = []

            # Build selectors based on table side and available data
            if table_side == 'right':
                if radio_value:
                    selectors_to_try.append(
                        f'input[name="CATEGORY_GROUP_SELECTION"][value="{radio_value}"]'
                    )
                selectors_to_try.extend(
                    [
                        f'tr:has-text("{category_name}") input[name="CATEGORY_GROUP_SELECTION"]',
                        f'//tr[contains(., "{category_name}")]//input[@name="CATEGORY_GROUP_SELECTION"]'
                    ]
                )
            else:  # left table
                if radio_value:
                    selectors_to_try.append(
                        f'input[name="CATEGORY_SELECTION"][value="{radio_value}"]'
                    )
                selectors_to_try.extend(
                    [
                        f'tr:has-text("{category_name}") input[name="CATEGORY_SELECTION"]',
                        f'//tr[contains(., "{category_name}")]//input[@name="CATEGORY_SELECTION"]'
                    ]
                )

            # Try each selector until one succeeds
            for selector in selectors_to_try:
                try:
                    await self.page.wait_for_selector(selector, timeout=5000)
                    await self.page.check(selector)
                    logger.info(
                        f"Successfully selected radio button for category: {category_name}"
                    )
                    return True
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue

            logger.error(f"Could not find radio button for category: {category_name}")
            return False

        except Exception as e:
            logger.error(f"Error selecting final category: {e}")
            return False

    async def _enter_promo_code(self, promo_code: str) -> bool:
        """
        Enter a promotional code into the promo code input field.
        
        Locates the promotional code input field using multiple selector strategies
        and enters the specified code. Includes input clearing and validation.
        
        Args:
            promo_code (str): The promotional code to enter (e.g., "T33", "KG3")
        
        Returns:
            bool: True if code was successfully entered, False otherwise
        
        Note:
            The method tries multiple selectors as the input field structure
            may vary across different OneSource pages or updates.
        """
        try:
            # Multiple selectors to find the promo code input field
            input_selectors = [
                'input[name*="FARE2COD"]', 'input#promoInput',
                'input[type="text"][size="3"]',
                '//input[@type="text" and @maxlength="3"]'
            ]

            for selector in input_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=3000)

                    # Clear existing content first
                    await self.page.fill(selector, '')
                    await asyncio.sleep(0.5)

                    # Enter the new promotional code
                    await self.page.fill(selector, promo_code)
                    logger.info(f"Entered promo code {promo_code} in input field")
                    return True

                except PlaywrightTimeoutError:
                    continue

            logger.error("Could not find promo code input field")
            return False

        except Exception as e:
            logger.error(f"Error entering promo code: {e}")
            return False

    async def _click_update_button(self) -> bool:
        """
        Click the Update button to refresh pricing with new promotional code.
        
        Locates and clicks the Update button that applies promotional code changes
        and refreshes the comparison tables with updated pricing information.
        
        Returns:
            bool: True if Update button was found and clicked, False otherwise
        
        Note:
            Uses multiple selector strategies to handle variations in button
            implementation across different OneSource pages.
        """
        try:
            # Multiple selectors for the Update button
            update_selectors = [
                'a.capsule_btn:has-text("Update")',
                'a[href*="DFH_ENTER"]:has-text("Update")',
                '//a[contains(@class, "capsule_btn") and contains(text(), "Update")]'
            ]

            for selector in update_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=3000)
                    await self.page.click(selector)
                    logger.info("Clicked Update button")
                    return True
                except PlaywrightTimeoutError:
                    continue

            logger.error("Could not find Update button")
            return False

        except Exception as e:
            logger.error(f"Error clicking update button: {e}")
            return False

    async def _click_final_save_continue(self) -> bool:
        """
        Click the final Save & Continue button to proceed with selected fare.
        
        Completes the fare comparison process by clicking the Save & Continue
        button that confirms the final category and promotional code selection
        and advances to the next step in the booking workflow.
        
        Returns:
            bool: True if button was clicked successfully, False otherwise
        
        Note:
            Waits for network idle after clicking to ensure the next page
            loads completely before the method returns.
        """
        try:
            # Multiple selectors for Save & Continue button
            save_continue_selectors = [
                'a.capsule_save_continue_btn:has-text("Save & Continue")',
                'a[href*="DFH_ENTER"]:has-text("Save & Continue")',
                '//a[contains(@class, "capsule_save_continue_btn") and contains(text(), "Save") and contains(text(), "Continue")]'
            ]

            for selector in save_continue_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=3000)
                    await self.page.click(selector)
                    logger.info("Clicked final Save & Continue button")
                    # Wait for navigation / network idle before continuing
                    await self.page.wait_for_load_state('networkidle')
                    return True
                except PlaywrightTimeoutError:
                    continue

            logger.error("Could not find final Save & Continue button")
            return False

        except Exception as e:
            logger.error(f"Error clicking final Save & Continue: {e}")
            return False

    async def _handle_single_rate_code(
        self, rate_code: Dict[str, Any], extracted_category: str
    ) -> Dict[str, Any]:
        """
        Handle the simplified workflow when only one promotional code is available.
        
        When filtering results in only a single valid promotional code, the comparison
        process is simplified since there's no need for multi-code comparison tables.
        This method handles the streamlined single-code selection workflow.
        
        Single Rate Code Process:
        1. Select the single promotional code
        2. Handle any notice checkboxes
        3. Extract categories from the resulting single table
        4. Find best category match for customer request
        5. Select category and complete booking
        
        Args:
            rate_code (Dict[str, Any]): The single promotional code to use
            extracted_category (str): Customer's requested cabin category
        
        Returns:
            Dict[str, Any]: Result dictionary with same structure as multi-code process:
                - success (bool): Whether selection completed successfully
                - selected_category (str): Final category selected
                - selected_promo (str): Promotional code used
                - fare_amount (str): Final fare amount
                - status (str): Availability status
            OR {"use_normal_flow": True} if process fails
        
        Note:
            This simplified workflow avoids the complexity of multi-table comparisons
            while still providing category selection and fare optimization within
            the single promotional code's available options.
        """
        try:
            logger.info(f"Processing single rate code: {rate_code['promo_code']}")

            # Step 1: Select the single promotional code
            if not await self._select_initial_rate_codes([rate_code]):
                logger.error("Failed to select single rate code")
                return {"use_normal_flow": True}

            # Step 2: Handle any notice/disclaimer checkboxes
            await self._handle_notice_checkboxes()

            # Step 3: Wait for and locate the category table
            await self.page.wait_for_selector('table', timeout=10000)

            if self.screenshot_manager:
                await self.screenshot_manager.take_full_page_screenshot(
                    "10_single_rate_code", "Single Rate Code"
                )

            table_element = await self.page.query_selector('table')
            if not table_element:
                logger.error("No table found on single-promo category page")
                return {"use_normal_flow": True}

            # Step 4: Extract available categories from the single table
            categories = await self._extract_categories_from_single_table(
                table_element, rate_code['promo_code']
            )

            if not categories:
                logger.error("No categories extracted from single rate table")
                return {"use_normal_flow": True}

            # Convert to comparison format for unified processing
            comparison_data = {cat['category']: cat for cat in categories}

            # Step 5: Find best category match using standard logic
            best_fare = await self._find_best_fare_from_best_deal_table(
                comparison_data, extracted_category
            )

            if not best_fare:
                logger.error("No valid fare found in single rate code table")
                return {"use_normal_flow": True}

            # Step 6: Select the chosen category
            if not await self._select_final_category_fixed(best_fare):
                logger.error("Failed to select category in single rate code scenario")
                return {"use_normal_flow": True}

            # Step 7: Complete the booking process
            if not await self._click_final_save_continue():
                logger.error(
                    "Failed to click Save & Continue in single rate code scenario"
                )
                return {"use_normal_flow": True}

            logger.info(
                f"Single rate code scenario completed: {best_fare['category']} with {rate_code['promo_code']}"
            )

            return {
                "success": True, "selected_category": best_fare['category'],
                "selected_promo": rate_code['promo_code'], "fare_amount":
                best_fare['fare_amount'], "status": best_fare['status']
            }

        except Exception as e:
            logger.error(f"Error in single rate code handling: {e}")
            return {"use_normal_flow": True}

    async def _extract_categories_from_single_table(
        self, table_element, promo_code: str
    ) -> List[Dict[str, Any]]:
        """
        Extract category data from a single promotional code table.
        
        When only one promotional code is available, OneSource displays a single
        table with all available categories for that code. This method extracts
        the category data using the standard row extraction logic.
        
        Args:
            table_element: Playwright element representing the category table
            promo_code (str): The promotional code associated with this table
        
        Returns:
            List[Dict[str, Any]]: List of category data dictionaries with same
            structure as multi-table extraction, each containing:
                - category (str): Category name
                - status (str): Availability status
                - fare_amount (str): Fare amount
                - promo (str): Associated promotional code
                - radio_value (str): Radio button value for selection
        
        Note:
            Uses the standard _extract_row_data method with "left" table side
            since single tables follow the left table column structure.
        """
        try:
            # Find all rows with radio buttons (category selection rows)
            rows = await table_element.query_selector_all('tr:has(input[type="radio"])')
            if not rows:
                return []

            categories: List[Dict[str, Any]] = []
            for row in rows:
                try:
                    # Extract using left table logic (single tables use left structure)
                    data = await self._extract_row_data(row, "left", promo_code)
                    if data:
                        categories.append(data)
                except Exception:
                    continue

            return categories
        except Exception as e:
            logger.error(f"Error extracting categories from single table: {e}")
            return []
