import React, { useRef, useEffect, useState } from 'react';
import { getVideoStreamUrl } from '../../services/video';

interface VideoPlayerProps {
  videoId: number;
  onClose: () => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ videoId, onClose }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [error, setError] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [errorDetails, setErrorDetails] = useState<string>('');
  // Try alternative formats if the main one fails
  const [formatIndex, setFormatIndex] = useState<number>(0);
  const formats = ['webm', 'mp4'];

  useEffect(() => {
    // Focus on video when it appears
    if (videoRef.current) {
      videoRef.current.focus();
    }
    
    // Reset error state when video ID changes
    setError(false);
    setLoading(true);
    setErrorDetails('');
    setFormatIndex(0);
  }, [videoId]);

  const handleError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error('Video playback error:', e);
    
    // Try next format if available
    if (formatIndex < formats.length - 1) {
      setFormatIndex(formatIndex + 1);
      return;
    }
    
    setError(true);
    setLoading(false);
    
    // Try to get more detailed error information
    const videoElement = e.currentTarget;
    let details = 'Unknown error';
    
    if (videoElement.error) {
      switch (videoElement.error.code) {
        case MediaError.MEDIA_ERR_ABORTED:
          details = 'You aborted the video playback.';
          break;
        case MediaError.MEDIA_ERR_NETWORK:
          details = 'A network error caused the video download to fail.';
          break;
        case MediaError.MEDIA_ERR_DECODE:
          details = 'The video playback was aborted due to a corruption problem or because the video format is not supported.';
          break;
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          details = 'The video format is not supported or the video file is corrupted.';
          break;
        default:
          details = `An unknown error occurred (code: ${videoElement.error.code}).`;
      }
    }
    
    setErrorDetails(details);
  };

  const handleLoad = () => {
    setLoading(false);
  };
  
  // Try to download the video directly
  const handleDownload = async () => {
    try {
      const videoUrl = getVideoStreamUrl(videoId);
      
      // Create a temporary link element and click it
      const link = document.createElement('a');
      link.href = videoUrl;
      link.download = `video_${videoId}.${formats[formatIndex]}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading video:', error);
      alert('Error downloading video. Please try again later.');
    }
  };
  
  // Get the video URL with the current format
  const videoUrl = getVideoStreamUrl(videoId);
  
  // For debugging
  console.log(`Video URL: ${videoUrl} (format attempt: ${formats[formatIndex]})`);
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-4 w-4/5 max-w-4xl">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium">Cabin Video Recording</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
        
        {loading && !error && (
          <div className="flex justify-center items-center p-6">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <span className="ml-3">Loading video...</span>
          </div>
        )}
        
        {error ? (
          <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
            <p className="font-medium">Error playing video</p>
            <p className="text-sm mt-1">
              {errorDetails || "The video format is not supported or the video file is corrupted."}
            </p>
            <p className="text-xs mt-2 text-gray-500">
              Video ID: {videoId} | Attempted formats: {formats.join(', ')}
            </p>
            <p className="text-sm mt-3">Try downloading the video instead:</p>
            <div className="mt-3 flex space-x-3">
              <button 
                onClick={handleDownload}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
              >
                Download Video
              </button>
              <a 
                href={videoUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-sm"
              >
                Open in New Tab
              </a>
            </div>
          </div>
        ) : (
          <video 
            ref={videoRef}
            src={videoUrl}
            controls 
            autoPlay 
            className="w-full" 
            style={{ maxHeight: '80vh', display: loading ? 'none' : 'block' }}
            onError={handleError}
            onLoadedData={handleLoad}
          />
        )}
      </div>
    </div>
  );
};

export default VideoPlayer;