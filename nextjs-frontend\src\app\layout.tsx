import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "../context/AuthContext";
import SessionTimer from "../components/ui/SessionTimer";

// Use Inter for body text
const inter = Inter({ 
  subsets: ["latin"],
  variable: '--font-inter',
  display: 'swap'
});

// Use Poppins for headings
const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ["latin"],
  variable: '--font-poppins',
  display: 'swap'
});

export const metadata: Metadata = {
  title: "OceanMind | HAI Agent",
  description: "Automated cruise booking platform with AI-driven extraction for multiple providers including NCL, Studio, and Cruising Power",
  keywords: "cruise booking, AI booking, NCL, Cruising Power, automation",
  icons: {
    icon: "./logo.png"
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} ${poppins.variable} font-sans antialiased`}>
        <AuthProvider>
          <div className="min-h-screen flex flex-col">
            {children}
            <SessionTimer />
            <div className="noise-overlay"></div>
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
