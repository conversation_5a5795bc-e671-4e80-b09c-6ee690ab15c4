# This file now acts as a facade to maintain backward compatibility with existing code
# It imports from the modular database package

import logging
from db_service import get_db_connection, init_db_pool
from db.postgres_schema import initialize_database, migrate_to_minio_storage
from db.session_manager import SessionManager
from db.studio_provider import save_studio_results, get_studio_results
from db.ncl_provider import save_ncl_results, get_ncl_results
from db.cruising_power_provider import save_cruising_power_results, get_cruising_power_results
# OneSource support
from db.onesource_provider import save_onesource_results, get_onesource_results
from db.screenshot_manager import (
    save_screenshot_to_db,
    get_screenshots_from_db,
    get_db_screenshot_categories,
    link_screenshots_to_booking
)
from db.video_manager import (
    save_video_to_db,
    get_video_data,
    get_videos_from_db,
    save_video_to_cabin
)
from db.utils import (
    register_session,
    generate_session_id,
    get_current_session_id,
    start_booking_session,
    get_active_session,
    complete_booking_session,
    list_all_entries,
    view_booking_details,
    cleanup_database
)
from db.user_auth import (
    authenticate_user,
    create_user,
    get_user,
    update_user,
    delete_user,
    list_users,
    update_user_role,
    get_provider_list,
    add_provider,
    update_provider,
    delete_provider,
    # create_default_admin
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("database_helper")

# Initialize singleton session manager
session_manager = SessionManager.get_instance()

# Export all functions and classes to maintain backward compatibility
__all__ = [
    'get_db_connection',
    'init_db_pool',
    'initialize_database',
    'migrate_to_minio_storage',
    'SessionManager',
    'save_studio_results',
    'get_studio_results',
    'save_ncl_results',
    'get_ncl_results',
    'save_cruising_power_results',
    'get_cruising_power_results',
    'save_onesource_results',
    'get_onesource_results',
    'save_screenshot_to_db',
    'get_screenshots_from_db',
    'get_db_screenshot_categories',
    'link_screenshots_to_booking',
    'save_video_to_db',
    'get_video_data',
    'get_videos_from_db',
    'save_video_to_cabin',
    'register_session',
    'generate_session_id',
    'get_current_session_id',
    'start_booking_session',
    'get_active_session',
    'complete_booking_session',
    'list_all_entries',
    'view_booking_details',
    'cleanup_database',
    'session_manager',
    'authenticate_user',
    'create_user',
    'get_user',
    'update_user',
    'delete_user',
    'list_users',
    'update_user_role',
    'get_provider_list',
    'add_provider',
    'update_provider',    
    'delete_provider',
    # 'create_default_admin'
]