// src/services/video.ts
import { getToken as getAuthToken } from './auth';
import { API_BASE_URL } from './api';

interface VideoMetadata {
  id: number;
  cabin_id?: string | number;
  video_type: string;
  timestamp: string;
  file_name: string;
  duration?: number;
  format?: string;
  size_kb?: number;
}

/**
 * Fetches video metadata for a specific session
 * @param sessionId The session ID to get videos for
 * @returns List of videos associated with the session
 */
export const getSessionVideos = async (sessionId: string) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_BASE_URL}/videos/${sessionId}`, {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching session videos:', error);
    return { success: false, videos: [], error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

/**
 * Returns the URL for streaming a video
 * @param videoId The ID of the video to stream
 * @returns URL for video streaming
 */
export const getVideoStreamUrl = (videoId: number) => {
  const token = getAuthToken();
  // Use the public endpoint which accepts token as a query parameter
  return `${API_BASE_URL}/public/video/${videoId}${token ? `?token=${token}` : ''}`;
};

/**
 * Fetches video metadata for a specific cabin
 * @param cabinId The cabin ID to get videos for
 * @param provider Optional provider filter
 * @param sessionId Optional session ID filter
 * @returns List of videos associated with the cabin
 */
export const getCabinVideos = async (
  cabinId: string | number, 
  provider?: string, 
  sessionId?: string
) => {
  try {
    const token = getAuthToken();
    let url = `${API_BASE_URL}/videos/cabin/${cabinId}`;
    
    // Add optional query parameters
    const params = new URLSearchParams();
    if (provider) params.append('provider', provider);
    if (sessionId) params.append('session_id', sessionId);
    
    // Add params to URL if any exist
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    console.log(`Fetching videos from: ${url}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error (${response.status}): ${errorText}`);
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Video data received:', data);
    return data;
  } catch (error) {
    console.error('Error fetching cabin videos:', error);
    return { 
      success: false, 
      videos: [], 
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};