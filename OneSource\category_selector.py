"""
OneSource Category Selector Module

This module handles category selection after clicking "Search by Category List" button.
It includes clicking Save & Continue, extracting category information from tables,
and selecting the appropriate category based on extracted cabin data.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Tuple
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError
from Core.ui_logger import ui_log
from OneSource.screenshot_utils import OneSourceScreenshotManager

logger = logging.getLogger(__name__)

class OneSourceCategorySelector:
    """
    Class for handling OneSource category selection operations
    """
    
    def __init__(self, page: Page, session_id=None, cabin_id=None):
        """
        Initialize the OneSource category selector
        
        Args:
            page: Playwright Page instance
            session_id: Session ID for tracking
            cabin_id: Cabin ID for tracking
        """
        self.page = page
        self.session_id = session_id
        self.cabin_id = cabin_id
        self.screenshot_manager = None
        self.request_id = None
        
        # Category mapping for matching extracted data
        self.category_mapping = {
            'interior': ['interior', 'inside', 'inner', 'int'],
            'oceanview': ['oceanview', 'ocean view', 'outside', 'ext'],
            'balcony': ['balcony', 'veranda', 'bal'],
            'suite': ['suite', 'suites', 'luxury', 'premium']
        }
    
    def set_screenshot_config(self, request_id: str, cabin_id: int = None, session_id: str = None):
        """Set screenshot configuration"""
        self.request_id = request_id
        if cabin_id is not None:
            self.cabin_id = cabin_id
        if session_id is not None:
            self.session_id = session_id
        self.screenshot_manager = OneSourceScreenshotManager(
            self.page, request_id, self.cabin_id, self.session_id
        )
    
    async def process_category_selection(self, extracted_category: str, passenger_count: int = 2, 
                                       config: Dict[str, Any] = None) -> bool:
        """
        Complete category selection process with optional fare comparison
        
        Args:
            extracted_category: The category extracted from cruise details
            passenger_count: Number of passengers for this cabin
            config: Configuration dictionary containing perk selection and other settings
            
        Returns:
            bool: True if category selection was successful, False otherwise
        """
        try:
            logger.info("Starting OneSource category selection process")
            logger.info(f"Passenger count for filtering: {passenger_count}")
            
            # Step 1: Click Save & Continue button on the current page
            if not await self._click_save_continue():
                logger.error("Failed to click Save & Continue button")
                return False
            
            # Step 2: Wait for category page to load
            await asyncio.sleep(2)
            
            # Step 3: Check if fare comparison should be used
            perk_selection = "No"  # Default
            if config and 'onesource_perk' in config:
                perk_selection = config['onesource_perk']
                logger.info(f"Perk selection from config: {perk_selection}")
            
            # Step 3.1: Try fare comparison workflow first – pass passenger_count so G-row rules are correct
            fare_comparison_result = await self._try_fare_comparison(extracted_category, perk_selection, passenger_count)
            
            if fare_comparison_result and not fare_comparison_result.get('use_normal_flow', False):
                logger.info("Fare comparison workflow completed successfully")
                # Store the fare comparison result
                self.selected_category_info = {
                    'name': fare_comparison_result.get('selected_category', extracted_category),
                    'status': fare_comparison_result.get('status', ''),
                    'price': fare_comparison_result.get('fare_amount', ''),
                    'promo': fare_comparison_result.get('selected_promo', ''),
                    'source': 'fare_comparison'
                }
                return True
            
            logger.info("Using normal category selection workflow")
            
            # Step 4: Extract category information from the table (normal flow)
            categories = await self._extract_category_information()
            if not categories:
                logger.error("Failed to extract category information")
                return False
            
            # Step 5: Find and select the appropriate category (normal flow)
            selected_category = await self._select_category(extracted_category, categories, passenger_count)
            if not selected_category:
                logger.error("Failed to select category")
                return False
            
            # Store selected category information
            self.selected_category_info = selected_category
            self.selected_category_info['source'] = 'normal_selection'
            
            # Step 6: Click final Save & Continue button (normal flow)
            if not await self._click_final_save_continue():
                logger.error("Failed to click final Save & Continue button")
                return False
            
            logger.info("OneSource category selection completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error in OneSource category selection: {e}")
            return False
    
    async def _try_fare_comparison(self, extracted_category: str, perk_selection: str, passenger_count: int) -> Optional[Dict[str, Any]]:
        """
        Try to use fare comparison workflow
        
        Args:
            extracted_category: The category extracted from cruise details
            perk_selection: "Yes" or "No" for perk-based filtering
            passenger_count: Number of passengers in the booking (for G-row filter rules)
            
        Returns:
            Dict with fare comparison result or None if failed/not available
        """
        try:
            # Import fare comparison module
            from OneSource.fare_comparison import OneSourceFareComparison
            
            # Create fare comparison instance
            fare_comparison = OneSourceFareComparison(
                page=self.page,
                session_id=self.session_id,
                cabin_id=self.cabin_id,
                request_id=self.request_id,
                passenger_count=passenger_count
            )
            
            # Process fare comparison
            result = await fare_comparison.process_fare_comparison(extracted_category, perk_selection)
            
            return result
            
        except Exception as e:
            logger.warning(f"Fare comparison failed, falling back to normal flow: {e}")
            return {"use_normal_flow": True}
    
    async def _click_save_continue(self) -> bool:
        """
        Click the Save & Continue button after search by category
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Take screenshot after search results are loaded, before save & continue
            if self.screenshot_manager:
                await self.screenshot_manager.take_search_results_screenshot()
            
            logger.info("Clicking Save & Continue button")
            
            # Save & Continue button selectors
            save_continue_selectors = [
                "#footerSec > table > tbody > tr > td:nth-child(1) > table > tbody > tr:nth-child(2) > td > table > tbody > tr > td:nth-child(1) > table > tbody > tr > td:nth-child(3) > a",
                "xpath=//*[@id=\"footerSec\"]/table/tbody/tr/td[1]/table/tbody/tr[2]/td/table/tbody/tr/td[1]/table/tbody/tr/td[3]/a",
                "a.capsule_save_continue_btn",
                "a:has-text('Save & Continue')"
            ]
            
            retry_count = 0
            max_retries = 1  # One normal try + one fallback try
            while retry_count <= max_retries:
                try:
                    clicked = False
                    for selector in save_continue_selectors:
                        try:
                            await self.page.wait_for_selector(selector, timeout=3000)
                            await self.page.click(selector)
                            logger.info("Save & Continue button clicked successfully")
                            return True
                        except PlaywrightTimeoutError:
                            continue
                        except Exception as e:
                            logger.debug(f"Error with selector {selector}: {e}")
                            continue
                    
                    if clicked:
                        return True
                    else:
                        raise PlaywrightTimeoutError("Save & Continue selectors timed out")
                except PlaywrightTimeoutError:
                    retry_count += 1
                    if retry_count <= max_retries:
                        logger.warning(f"Timeout clicking Save & Continue, attempting fallback: click Next button first (retry {retry_count})")
                        if await self._click_next_button():
                            await asyncio.sleep(1)  # Brief pause after clicking Next
                            continue
                        else:
                            logger.error("Fallback Next button click failed")
                            return False
                    else:
                        logger.error("Could not find Save & Continue button after fallback")
                        return False
                except Exception as e:
                    logger.error(f"Error clicking Save & Continue button: {e}")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"Error in _click_save_continue: {e}")
            return False

    async def _click_next_button(self) -> bool:
        """Click the Next button as fallback"""
        try:
            next_selectors = [
                "a[href=\"javascript:doSimpleSubmit(document.CMRDLV,'DFH_PF8')\"]",
                "#CMRDLV > table > tbody > tr:nth-child(2) > td > table > tbody > tr:nth-child(2) > td:nth-child(4) > div > span > a.tapres_results_nav_btn.tapres_results_nav_wht_btn",
                "xpath=//*[@id=\"CMRDLV\"]/table/tbody/tr[2]/td/table/tbody/tr[2]/td[4]/div/span/a[2]",
                "a:has-text('Next')",
                "a.tapres_results_nav_btn.tapres_results_nav_wht_btn"
            ]
            
            for selector in next_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=2000)
                    await self.page.click(selector)
                    logger.info(f"Next button clicked successfully using selector: {selector}")
                    return True
                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    logger.debug(f"Error clicking Next with selector {selector}: {e}")
                    continue
            
            logger.error("Could not find and click Next button")
            return False
        except Exception as e:
            logger.error(f"Error in _click_next_button: {e}")
            return False
    
    async def _extract_category_information(self) -> List[Dict[str, Any]]:
        """
        Extract category information from both tables on the page
        
        Returns:
            List[Dict]: List of category information with names and prices from both tables
        """
        try:
            logger.info("Extracting category information from all tables")
            
            all_categories = []
            processed_table_signatures = set()  # To avoid duplicates
            
            # First approach: Look for the main container and find all tables with category data
            try:
                # Wait for the main content area to load
                await self.page.wait_for_selector("#CMRDIC", timeout=10000)
                
                # Try to find tables that contain radio buttons (indicating category tables)
                category_tables = await self.page.query_selector_all("#CMRDIC table:has(input[type='radio'])")
                logger.info(f"Found {len(category_tables)} tables with radio buttons")
                
                if category_tables:
                    valid_table_count = 0
                    for table_index, table in enumerate(category_tables):
                        # Create a signature for this table to detect duplicates
                        table_signature = await self._get_table_signature(table)
                        if table_signature in processed_table_signatures:
                            logger.debug(f"Skipping duplicate table {table_index + 1} with signature: {table_signature}")
                            continue
                        
                        processed_table_signatures.add(table_signature)
                        logger.info(f"Processing category table {valid_table_count + 1} (original index: {table_index + 1})")
                        
                        categories_from_table = await self._extract_categories_from_table(table, valid_table_count)
                        if categories_from_table:
                            logger.info(f"Extracted {len(categories_from_table)} categories from table {valid_table_count + 1}")
                            all_categories.extend(categories_from_table)
                            valid_table_count += 1
                        else:
                            logger.debug(f"No valid categories found in table {table_index + 1}")
                    
                    if all_categories:
                        # Remove any remaining duplicates based on category name and price
                        unique_categories = self._deduplicate_categories(all_categories)
                        logger.info(f"Successfully extracted {len(unique_categories)} unique categories from {valid_table_count} tables")
                        return unique_categories
                        
            except Exception as e:
                logger.debug(f"Radio button table approach failed: {e}")
            
            # Second approach: Look for tables within specific container patterns
            logger.info("Trying container-based table detection")
            container_selectors = [
                "#CMRDIC > table > tbody > tr > td > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td > table > tbody > tr:nth-child(4) > td > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td",
                "#CMRDIC > table > tbody > tr > td > table",
                "#CMRDIC > table",
                "#CMRDIC"
            ]
            
            for container_selector in container_selectors:
                try:
                    container = await self.page.wait_for_selector(container_selector, timeout=5000)
                    if container:
                        # Find all tables within this container
                        tables = await container.query_selector_all('table')
                        logger.info(f"Found {len(tables)} tables in container {container_selector}")
                        
                        # Filter tables that actually contain category data (have radio buttons)
                        category_tables = []
                        for table in tables:
                            radio_buttons = await table.query_selector_all('input[type="radio"]')
                            if radio_buttons:
                                # Check if this table is not a duplicate
                                table_signature = await self._get_table_signature(table)
                                if table_signature not in processed_table_signatures:
                                    category_tables.append(table)
                                    processed_table_signatures.add(table_signature)
                        
                        logger.info(f"Found {len(category_tables)} unique tables with radio buttons in container")
                        
                        for table_index, table in enumerate(category_tables):
                            logger.info(f"Processing table {table_index + 1}")
                            categories_from_table = await self._extract_categories_from_table(table, table_index)
                            if categories_from_table:
                                logger.info(f"Extracted {len(categories_from_table)} categories from table {table_index + 1}")
                                all_categories.extend(categories_from_table)
                        
                        if all_categories:
                            unique_categories = self._deduplicate_categories(all_categories)
                            logger.info(f"Successfully extracted {len(unique_categories)} unique categories from all tables")
                            return unique_categories
                            
                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    logger.debug(f"Error with container selector {container_selector}: {e}")
                    continue
            
            # Fallback: try the original table selectors
            logger.warning("All advanced approaches failed, trying original table selectors")
            table_selectors = [
                "#CMRDIC > table > tbody > tr > td > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td > table > tbody > tr:nth-child(4) > td > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td > table",
                "xpath=//*[@id=\"CMRDIC\"]/table/tbody/tr/td/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr[4]/td/table/tbody/tr/td/table/tbody/tr[2]/td/table",
                "#CMRDIC table",
                ".category-table"
            ]
            
            for selector in table_selectors:
                try:
                    if selector.startswith('xpath='):
                        table = await self.page.wait_for_selector(selector, timeout=5000)
                    else:
                        table = await self.page.wait_for_selector(selector, timeout=5000)
                    
                    if table:
                        categories_from_table = await self._extract_categories_from_table(table, 0)
                        if categories_from_table:
                            logger.info(f"Successfully extracted {len(categories_from_table)} categories using fallback")
                            return categories_from_table
                        
                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    logger.debug(f"Error with table selector {selector}: {e}")
                    continue
            
            logger.error("Could not extract category information from any table")
            return []
            
        except Exception as e:
            logger.error(f"Error extracting category information: {e}")
            return []
    
    async def _get_table_signature(self, table) -> str:
        """
        Generate a unique signature for a table to detect duplicates
        
        Args:
            table: Playwright table element
            
        Returns:
            str: Unique signature for the table
        """
        try:
            # Get the first few radio button values and category names to create a signature
            radio_buttons = await table.query_selector_all('input[type="radio"]')
            if not radio_buttons:
                return "no-radios"
            
            # Get first 3 radio button values and names
            signature_parts = []
            for i, radio in enumerate(radio_buttons[:3]):
                try:
                    radio_value = await radio.get_attribute('value') or f"radio-{i}"
                    # Find the category name in the same row
                    row = await radio.evaluate('radio => radio.closest("tr")')
                    if row:
                        cells = await row.query_selector_all('td')
                        category_name = ""
                        if len(cells) > 1:
                            category_name = await cells[1].inner_text()
                        signature_parts.append(f"{radio_value}:{category_name}")
                    else:
                        signature_parts.append(radio_value)
                except:
                    signature_parts.append(f"unknown-{i}")
            
            return "|".join(signature_parts)
            
        except Exception as e:
            logger.debug(f"Error generating table signature: {e}")
            return "unknown-signature"
    
    def _deduplicate_categories(self, categories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Remove duplicate categories based on name and price
        
        Args:
            categories: List of category dictionaries
            
        Returns:
            List[Dict]: Deduplicated list of categories
        """
        seen = set()
        unique_categories = []
        
        for category in categories:
            # Create a signature based on name, status, and price
            signature = f"{category['name']}|{category['status']}|{category['price']}"
            if signature not in seen:
                seen.add(signature)
                unique_categories.append(category)
            else:
                logger.debug(f"Removing duplicate category: {category['name']} | Status: {category['status']} | Price: {category['price']}")
        
        logger.info(f"Deduplicated {len(categories)} categories to {len(unique_categories)} unique categories")
        return unique_categories
    
    async def _extract_categories_from_table(self, table, table_index: int) -> List[Dict[str, Any]]:
        """
        Extract categories from a specific table
        
        Args:
            table: Playwright element representing the table
            table_index: Index of the table for unique identification
            
        Returns:
            List[Dict]: List of categories extracted from this table
        """
        try:
            categories = []
            
            # Extract table rows
            rows = await table.query_selector_all('tr')
            logger.debug(f"Table {table_index + 1} has {len(rows)} rows")
            
            for i, row in enumerate(rows):
                try:
                    # Skip header rows (usually first 2-3 rows)
                    if i < 3:
                        continue
                    
                    # Get cells in the row
                    cells = await row.query_selector_all('td')
                    if len(cells) >= 4:  # Minimum for name, status, price, promo
                        # Extract each column
                        category_text = await cells[1].inner_text() if len(cells) > 1 else ""
                        status_text = await cells[2].inner_text() if len(cells) > 2 else ""
                        price_text = await cells[3].inner_text() if len(cells) > 3 else ""
                        promo_text = await cells[4].inner_text() if len(cells) > 4 else ""

                        # Check if row has a radio button (indicating it's a selectable category)
                        radio_button = await row.query_selector('input[type="radio"]')
                        if radio_button and category_text.strip():
                            # Create unique identifiers for each table
                            table_prefix = f"table{table_index + 1}"
                            
                            # Append category with table information
                            category_data = {
                                'row_index': i,
                                'table_index': table_index,
                                'name': category_text.strip(),
                                'status': status_text.strip(),
                                'price': price_text.strip(),
                                'promo': promo_text.strip(),
                                'radio_selector': f"#CMRDIC table:nth-of-type({table_index + 1}) tbody tr:nth-child({i+1}) td:nth-child(1) input[type=radio]",
                                'table_prefix': table_prefix
                            }
                            
                            categories.append(category_data)

                            # Log only if status does not start with 'W' or 'C'
                            stripped_status = status_text.strip()
                            if not (stripped_status.startswith('W') or stripped_status.startswith('C')):
                                logger.info(f"Found category (Table {table_index + 1}): {category_text.strip()} | Status: {stripped_status} | Price: {price_text.strip()} | Promo: {promo_text.strip()}")

                except Exception as e:
                    logger.debug(f"Error processing row {i} in table {table_index + 1}: {e}")
                    continue
            
            return categories
            
        except Exception as e:
            logger.error(f"Error extracting categories from table {table_index + 1}: {e}")
            return []
    
    async def _select_category(self, extracted_category: str, available_categories: List[Dict[str, Any]], passenger_count: int) -> Dict[str, Any]:
        """
        Select the appropriate category based on extracted data
        
        Args:
            extracted_category: The category from extracted cruise details
            available_categories: List of available categories from the table
            passenger_count: Number of passengers for this cabin
            
        Returns:
            Dict: Selected category info if successful, None if failed
        """
        try:
            logger.info(f"Selecting category based on extracted type: {extracted_category}")
            
            # Filter available categories to exclude status starting with 'W' or 'C'
            filtered_categories = [cat for cat in available_categories if not cat['status'].startswith(('W', 'C'))]
            
            # Additional filtering: exclude status 'G' when passenger count > 2
            if passenger_count > 2:
                pre_g_filter_count = len(filtered_categories)
                g_categories = [cat for cat in filtered_categories if cat['status'].startswith('G')]
                filtered_categories = [cat for cat in filtered_categories if not cat['status'].startswith('G')]
                g_filtered_count = pre_g_filter_count - len(filtered_categories)
                if g_filtered_count > 0:
                    logger.info(f"Filtered out {g_filtered_count} categories with status 'G' due to passenger count ({passenger_count}) > 2")
                    for g_cat in g_categories:
                        logger.info(f"  Excluded: {g_cat['name']} (Status: {g_cat['status']}, Price: {g_cat['price']})")

            # Normalize the extracted category
            normalized_category = self._normalize_category(extracted_category)
            if not normalized_category:
                logger.warning(f"Could not normalize category: {extracted_category}")
                # Fallback to first available filtered category (already excludes W, C, and G if passenger_count > 2)
                if filtered_categories:
                    selected = filtered_categories[0]
                    if await self._click_category_radio(selected):
                        return selected
                elif available_categories:  # Ultimate fallback if no filtered categories available
                    # Apply same filtering logic as above for ultimate fallback
                    ultimate_fallback = [cat for cat in available_categories if not cat['status'].startswith(('W', 'C'))]
                    if passenger_count > 2:
                        ultimate_fallback = [cat for cat in ultimate_fallback if not cat['status'].startswith('G')]
                    
                    if ultimate_fallback:
                        selected = ultimate_fallback[0]
                        logger.warning(f"Using ultimate fallback category: {selected['name']} (Status: {selected['status']})")
                        if await self._click_category_radio(selected):
                            return selected
                return None

            # Find matching categories from filtered list
            matching_categories = []
            for category in filtered_categories:
                category_name = category['name'].lower()
                for keyword in self.category_mapping[normalized_category]:
                    if keyword in category_name:
                        matching_categories.append(category)
                        break

            if not matching_categories:
                logger.warning(f"No matching categories found for {normalized_category} in filtered list, selecting first filtered")
                if filtered_categories:
                    selected = filtered_categories[0]
                    if await self._click_category_radio(selected):
                        return selected
                elif available_categories:
                    # Apply same filtering logic for consistency
                    no_match_fallback = [cat for cat in available_categories if not cat['status'].startswith(('W', 'C'))]
                    if passenger_count > 2:
                        no_match_fallback = [cat for cat in no_match_fallback if not cat['status'].startswith('G')]
                    
                    if no_match_fallback:
                        selected = no_match_fallback[0]
                        logger.warning(f"Using no-match fallback category: {selected['name']} (Status: {selected['status']})")
                        if await self._click_category_radio(selected):
                            return selected
                return None

            # Select the cheapest matching category from filtered matches
            cheapest_category = self._find_cheapest_category(matching_categories)
            logger.info(f"Selected cheapest {normalized_category} category: {cheapest_category['name']} - {cheapest_category['price']}")

            if await self._click_category_radio(cheapest_category):
                return cheapest_category
            else:
                return None
            
        except Exception as e:
            logger.error(f"Error selecting category: {e}")
            return None
    
    def _normalize_category(self, category: str) -> Optional[str]:
        """
        Normalize extracted category to standard types
        
        Args:
            category: Raw category string from extraction
            
        Returns:
            str: Normalized category type or None
        """
        if not category:
            return None
        
        category_lower = category.lower()
        
        for normalized_type, keywords in self.category_mapping.items():
            for keyword in keywords:
                if keyword in category_lower:
                    return normalized_type
        
        return None
    
    def _find_cheapest_category(self, categories: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Find the cheapest category from a list of categories
        
        Args:
            categories: List of category dictionaries
            
        Returns:
            Dict: The cheapest category
        """
        if not categories:
            return None
        
        if len(categories) == 1:
            return categories[0]
        
        # Try to extract numeric prices and find minimum
        cheapest = categories[0]
        lowest_price = float('inf')
        
        for category in categories:
            try:
                # Extract numeric value from price string
                price_str = category['price']
                # Remove currency symbols and commas, extract numbers
                import re
                price_numbers = re.findall(r'[\d,.]+', price_str.replace(',', ''))
                if price_numbers:
                    price_value = float(price_numbers[0])
                    if price_value < lowest_price:
                        lowest_price = price_value
                        cheapest = category
            except:
                continue
        
        return cheapest
    
    async def _click_category_radio(self, category: Dict[str, Any]) -> bool:
        """
        Click the radio button for a specific category
        
        Args:
            category: Category dictionary with radio selector
            
        Returns:
            bool: True if successful
        """
        try:
            category_name = category['name']
            table_index = category.get('table_index', 0)
            logger.info(f"Attempting to select radio for category: {category_name} (Table {table_index + 1})")

            # Precise text-based selector targeting the category name in second td
            precise_selector = f'tr:has(> td:nth-child(2):has-text("{category_name}")) > td:nth-child(1) input[type="radio"]'

            # Table-specific selectors for multiple tables
            row_idx = category.get('row_index', 0)
            table_nth = table_index + 1
            
            radio_selectors = [
                precise_selector,
                category.get('radio_selector', ''),  # This now includes table:nth-of-type
                f"#CMRDIC table:nth-of-type({table_nth}) tbody tr:nth-child({row_idx+1}) td:nth-child(1) input[type=radio]",
                f"#CMRDIC > table > tbody > tr > td > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td > table > tbody > tr:nth-child(4) > td > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td > table:nth-of-type({table_nth}) > tbody > tr:nth-child({row_idx+1}) > td:nth-child(1) > input[type=radio]",
                f"xpath=//*[@id=\"CMRDIC\"]/table/tbody/tr/td/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr[4]/td/table/tbody/tr/td/table/tbody/tr[2]/td/table[{table_index}]/tbody/tr[{row_idx}]/td[1]/input",
                f"#CMRDIC table:nth-of-type({table_nth}) tr:nth-child({row_idx+1}) input[type=radio]",
                f"#CMRDIC table:nth-of-type({table_nth}) input[type=radio]:nth-of-type({row_idx-2})",  # Adjust for header rows
                # Generic fallback selectors
                f"#CMRDIC table tr:nth-child({row_idx+1}) input[type=radio]",
                f"#CMRDIC input[type=radio]:nth-of-type({row_idx-2})"  # Original fallback
            ]
            
            for selector in radio_selectors:
                if not selector:
                    continue
                    
                try:
                    radio_button = await self.page.wait_for_selector(selector, timeout=3000)
                    if radio_button:
                        await radio_button.click()
                        logger.info(f"Selected category radio button: {category['name']}")
                        await asyncio.sleep(1)  # Brief pause after selection
                        
                        # Take screenshot after clicking radio button
                        if self.screenshot_manager:
                            await self.screenshot_manager.take_category_selection_screenshot()
                        
                        return True
                        
                except PlaywrightTimeoutError:
                    logger.debug(f"Timeout with selector {selector}")
                    continue
                except Exception as e:
                    logger.debug(f"Error with radio selector {selector}: {e}")
                    continue
            
            logger.error(f"Could not click radio button for category: {category['name']} (Table {table_index + 1})")
            return False
            
        except Exception as e:
            logger.error(f"Error clicking category radio button: {e}")
            return False
    
    async def _click_final_save_continue(self) -> bool:
        """
        Click the final Save & Continue button after category selection

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Clicking final Save & Continue button")

            # Make sure the footer frame row is visible (legacy site sometimes collapses it)
            try:
                await self.page.evaluate(
                    "() => { const fs = document.querySelector('frameset'); if (fs && /,0$/.test(fs.rows)) { fs.rows = fs.rows.replace(/,0$/, ',54'); } }"
                )
            except Exception as e:
                logger.debug(f"Frameset row restore failed (may not be needed): {e}")

            # Try to locate the Save & Continue button in any frame (including main doc)
            button_selectors = [
                "a:has-text('Save & Continue')",
                "input[alt*='Save'][alt*='Continue']",
                "input[value*='Save'][value*='Continue']",
                "a.capsule_save_continue_btn"
            ]

            # Iterate through all frames (main page is also a frame in this list)
            for frame in self.page.frames:
                logger.debug(f"Checking frame: name={frame.name or '[no-name]'}, url={frame.url or 'unknown'}")
                for sel in button_selectors:
                    try:
                        element = await frame.query_selector(sel)
                        if element:
                            # Log button details for debugging
                            button_html = await element.evaluate("el => el.outerHTML")
                            logger.debug(f"Found Save & Continue in frame {frame.name or '[no-name]'} using selector {sel}. HTML: {button_html}")

                            # Trigger native click via JS evaluation (better for firing onclick)
                            await element.evaluate("el => el.click()")

                            # Wait until Pricing Detail header appears in any frame (evidence of next page)
                            try:
                                await self.page.wait_for_selector("text=Pricing Detail", timeout=10000)
                            except:
                                pass  # ignore if not found – some itineraries skip that page

                            await asyncio.sleep(2)
                            logger.info("Save & Continue clicked and Pricing Detail page expected")
                            return True
                    except PlaywrightTimeoutError:
                        continue
                    except Exception as e:
                        logger.debug(f"Error in frame {frame.name}: {e}")
                        continue

            # Ensure footer frame is visible (rows fix already applied above)
            footer_frame = None
            for frame in self.page.frames:
                if frame.name == "footerSec" or "footer" in (frame.name or "").lower():
                    footer_frame = frame
                    break

            if footer_frame:
                logger.debug("Found footer frame – attempting click inside it")
                try:
                    await footer_frame.wait_for_selector("a:has-text('Save & Continue'), input[alt*='Save'][alt*='Continue'], input[value*='Save'][value*='Continue']", timeout=5000)
                    await footer_frame.click("a:has-text('Save & Continue'), input[alt*='Save'][alt*='Continue'], input[value*='Save'][value*='Continue']", force=True)
                    # Wait for navigation triggered by frame script
                    await self.page.wait_for_load_state('domcontentloaded', timeout=10000)
                    await asyncio.sleep(5)
                    logger.info("Clicked Save & Continue inside footer frame")
                    logger.info(f"Current URL after Save & Continue click: {self.page.url}")
                    return True
                except Exception as ef:
                    logger.debug(f"Footer frame click failed: {ef}")
                    # fall through to generic selectors below

            # If no footer frame or click failed, fall back to top-level selectors
            # Scroll bottom and frameset row fix are executed earlier

            # Final Save & Continue button selectors
            final_save_selectors = [
                # Original anchors
                "#footerSec > table > tbody > tr > td:nth-child(1) > table > tbody > tr:nth-child(2) > td > table > tbody > tr > td:nth-child(1) > table > tbody > tr > td:nth-child(3) > a",
                "xpath=//*[@id=\"footerSec\"]/table/tbody/tr/td[1]/table/tbody/tr[2]/td/table/tbody/tr/td[1]/table/tbody/tr/td[3]/a",
                "a.capsule_save_continue_btn",
                "a:has-text('Save & Continue')",
                # Possible <input> variants (type image/submit)
                "input[type='image'][alt*='Save'][alt*='Continue']",
                "input[type='submit'][value*='Save'][value*='Continue']",
                "input[alt='SAVE & CONTINUE']",
                "input[value='Save & Continue']"
            ]

            for selector in final_save_selectors:
                try:
                    if selector.startswith('xpath='):
                        await self.page.wait_for_selector(selector, timeout=5000)
                    else:
                        await self.page.wait_for_selector(selector, timeout=5000)

                    await self.page.click(selector, force=True)
                    logger.info("Final Save & Continue button clicked successfully (top-level)")

                    await self.page.wait_for_load_state('domcontentloaded', timeout=10000)
                    await asyncio.sleep(5)
                    logger.info(f"Current URL after Save & Continue click: {self.page.url}")
                    return True

                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    logger.debug(f"Error with final save selector {selector}: {e}")
                    continue

            logger.error("Could not find final Save & Continue button in any frame")
            # Fallback: try the same helper used at the beginning of category selection
            logger.warning("Fallback: attempting original _click_save_continue logic for final Save & Continue")
            return await self._click_save_continue()

        except Exception as e:
            logger.error(f"Error clicking final Save & Continue button: {e}")
            return False 