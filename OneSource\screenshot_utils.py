from playwright.async_api import Page
import io
import asyncio
from datetime import datetime
from loguru import logger
import os
import sys
from Core.ui_logger import ui_log

# Add parent directory to path for Core imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

class OneSourceScreenshotManager:
    """
    Class for handling OneSource screenshot functionality
    """
    def __init__(self, page, request_id=None, cabin_id=None, session_id=None):
        """
        Initialize the screenshot manager
        
        Args:
            page: Playwright Page instance
            request_id: Request ID for database storage
            cabin_id: Cabin ID for database storage
            session_id: Session ID for consistent database tracking
        """
        self.page = page
        self.request_id = request_id
        self.cabin_id = cabin_id
        self.session_id = session_id

    async def save_to_database(self, screenshot_data, filename, screenshot_type="general"):
        """
        Save screenshot data to database via the screenshot manager
        
        Args:
            screenshot_data: Binary image data
            filename: Name of the screenshot file
            screenshot_type: Type of screenshot for categorization
        """
        try:
            from db.screenshot_manager import save_screenshot_to_db
            
            await save_screenshot_to_db(
                screenshot_data,
                self.request_id,
                "OneSource",
                screenshot_type,
                filename,
                self.cabin_id,
                self.session_id
            )
            
        except Exception as e:
            logger.error(f"Error saving screenshot to database: {e}")

    async def take_full_page_screenshot(self, filename, screenshot_type="general"):
        """
        Takes a full page screenshot using Playwright's built-in functionality
        
        Args:
            filename: Name of the screenshot file
            screenshot_type: Type of screenshot for categorization
        
        Returns:
            str: Filename of the generated screenshot
        """
        try:
            logger.info(f"Taking full page screenshot: {filename}...")
            ui_log(f"Taking screenshot: {screenshot_type}", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_id, 
                  module="OneSource")
            
            # Playwright has built-in full page screenshots
            screenshot_data = await self.page.screenshot(full_page=True)
            
            # Create a filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            full_filename = f"{filename}_full_page_{timestamp}.png"
            
            # Save to database if request_id is provided
            if self.request_id:
                await self.save_to_database(screenshot_data, full_filename, screenshot_type)
                logger.info(f"Full page screenshot saved to database: {full_filename}")
            
            return full_filename
            
        except Exception as e:
            logger.error(f"Error taking full page screenshot: {e}")
            return None

    async def take_scrolling_screenshot(self, filename, screenshot_type="general"):
        """
        Takes a screenshot by scrolling through the page to capture all content
        
        Args:
            filename: Name of the screenshot file
            screenshot_type: Type of screenshot for categorization
        
        Returns:
            str: Filename of the generated screenshot
        """
        try:
            logger.info(f"Taking scrolling screenshot: {filename}...")
            ui_log(f"Taking scrolling screenshot: {screenshot_type}", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_id, 
                  module="OneSource")
            
            # Reset window scroll position to top first
            await self.page.evaluate("() => window.scrollTo(0, 0)")
            await asyncio.sleep(0.5)
            
            # Check for scrollable containers within the page
            container_info = await self.page.evaluate("""
                () => {
                    // Common scrollable container selectors
                    const selectors = [
                        '#ControlPrint',
                        '[id*="control"]',
                        '[id*="Control"]', 
                        '.scrollable',
                        '[style*="overflow"]',
                        'div[style*="height"]',
                        'table',
                        'tbody'
                    ];
                    
                    let bestContainer = null;
                    let maxScrollHeight = 0;
                    
                    for (const selector of selectors) {
                        try {
                            const elements = document.querySelectorAll(selector);
                            for (const element of elements) {
                                if (element.scrollHeight > element.clientHeight) {
                                    const scrollHeight = element.scrollHeight;
                                    const clientHeight = element.clientHeight;
                                    
                                    if (scrollHeight > maxScrollHeight) {
                                        maxScrollHeight = scrollHeight;
                                        bestContainer = {
                                            selector: selector,
                                            element: element,
                                            scrollHeight: scrollHeight,
                                            clientHeight: clientHeight,
                                            id: element.id || 'no-id',
                                            className: element.className || 'no-class'
                                        };
                                    }
                                }
                            }
                        } catch (e) {
                            // Continue to next selector
                        }
                    }
                    
                    // Also check page-level scrolling
                    const pageScrollHeight = Math.max(
                        document.body.scrollHeight,
                        document.documentElement.scrollHeight
                    );
                    const pageClientHeight = window.innerHeight;
                    
                    return {
                        container: bestContainer,
                        pageScrollHeight: pageScrollHeight,
                        pageClientHeight: pageClientHeight,
                        hasScrollableContainer: bestContainer !== null
                    };
                }
            """)
            
            # Determine scrolling strategy
            if container_info['hasScrollableContainer']:
                container = container_info['container']
                
                # Scroll within the container
                screenshot_data = await self._scroll_container_screenshot(container)
                
            elif container_info['pageScrollHeight'] > container_info['pageClientHeight']:
                # Use page-level scrolling
                screenshot_data = await self._scroll_page_screenshot(container_info)
                
            else:
                # No scrolling needed
                screenshot_data = await self.page.screenshot(full_page=True)
            
            # Create a filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            full_filename = f"{filename}_scrolling_{timestamp}.png"
            
            # Save to database if request_id is provided
            if self.request_id:
                await self.save_to_database(screenshot_data, full_filename, screenshot_type)
                logger.info(f"Scrolling screenshot saved to database: {full_filename}")
            
            return full_filename
            
        except Exception as e:
            logger.error(f"Error taking scrolling screenshot: {e}")
            return None

    async def _scroll_container_screenshot(self, container_info):
        """
        Scroll within a specific container and take screenshot
        """
        try:
            # Get container selector
            selector = container_info['selector']
            if container_info['id'] != 'no-id':
                selector = f"#{container_info['id']}"
            
            # Reset container scroll to top
            await self.page.evaluate(f"""
                () => {{
                    const container = document.querySelector('{selector}');
                    if (container) {{
                        container.scrollTop = 0;
                    }}
                }}
            """)
            await asyncio.sleep(0.5)
            
            # Calculate scroll parameters
            scroll_height = container_info['scrollHeight']
            client_height = container_info['clientHeight']
            scroll_increment = min(client_height * 0.8, 300)  # Smaller increments for containers
            
            # Scroll to bottom first to load all content
            await self.page.evaluate(f"""
                () => {{
                    const container = document.querySelector('{selector}');
                    if (container) {{
                        container.scrollTop = {scroll_height};
                    }}
                }}
            """)
            await asyncio.sleep(1)
            
            # Now scroll back to top
            await self.page.evaluate(f"""
                () => {{
                    const container = document.querySelector('{selector}');
                    if (container) {{
                        container.scrollTop = 0;
                    }}
                }}
            """)
            await asyncio.sleep(0.5)
            
            # Take screenshots while scrolling the container
            screenshots = []
            current_scroll = 0
            
            # First, take a screenshot at the top (scroll position 0)
            await self.page.evaluate(f"""
                () => {{
                    const container = document.querySelector('{selector}');
                    if (container) {{
                        container.scrollTop = 0;
                    }}
                }}
            """)
            await asyncio.sleep(0.5)
            top_screenshot = await self.page.screenshot()
            screenshots.append(top_screenshot)
            logger.debug("Top screenshot taken at scroll position 0px")
            
            # Then scroll through the content
            while current_scroll <= scroll_height:
                if current_scroll > 0:  # Skip 0 since we already took it
                    # Scroll container to position
                    await self.page.evaluate(f"""
                        () => {{
                            const container = document.querySelector('{selector}');
                            if (container) {{
                                container.scrollTop = {current_scroll};
                            }}
                        }}
                    """)
                    await asyncio.sleep(0.5)
                    
                    # Take screenshot
                    screenshot_data = await self.page.screenshot()
                    screenshots.append(screenshot_data)
                    logger.debug(f"Container screenshot taken at scroll position {current_scroll}px")
                
                current_scroll += scroll_increment
            
            # Always take a final screenshot at the very bottom
            await self.page.evaluate(f"""
                () => {{
                    const container = document.querySelector('{selector}');
                    if (container) {{
                        container.scrollTop = {scroll_height};
                    }}
                }}
            """)
            await asyncio.sleep(0.5)
            bottom_screenshot = await self.page.screenshot()
            screenshots.append(bottom_screenshot)
            logger.debug(f"Bottom screenshot taken at scroll position {scroll_height}px")
            
            # Reset container scroll to top
            await self.page.evaluate(f"""
                () => {{
                    const container = document.querySelector('{selector}');
                    if (container) {{
                        container.scrollTop = 0;
                    }}
                }}
            """)
            
            # Save multiple screenshots to database if possible
            if len(screenshots) > 1 and self.request_id:
                try:
                    # Save top screenshot
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    top_filename = f"top_content_{timestamp}.png"
                    await self.save_to_database(screenshots[0], top_filename, "Top Content")
                except Exception as e:
                    logger.warning(f"Could not save multiple screenshots: {e}")
            
            # Return a comprehensive screenshot - try to get one that shows more content
            # Scroll to a position that shows both top information and some scrolled content
            try:
                # Position that shows about 30% down to capture top info + some scrolled content
                optimal_position = min(scroll_height * 0.3, client_height)
                await self.page.evaluate(f"""
                    () => {{
                        const container = document.querySelector('{selector}');
                        if (container) {{
                            container.scrollTop = {optimal_position};
                        }}
                    }}
                """)
                await asyncio.sleep(0.5)
                
                # Take the optimal screenshot
                optimal_screenshot = await self.page.screenshot()
                
                return optimal_screenshot
                
            except Exception as e:
                logger.warning(f"Could not take optimal screenshot: {e}")
                # Fallback to top screenshot
                return screenshots[0] if screenshots else await self.page.screenshot()
            
        except Exception as e:
            logger.error(f"Error in container scrolling: {e}")
            return await self.page.screenshot()

    async def _scroll_page_screenshot(self, page_info):
        """
        Scroll the entire page and take screenshot
        """
        try:
            page_height = page_info['pageScrollHeight']
            viewport_height = page_info['pageClientHeight']
            
            # Scroll to bottom first
            await self.page.evaluate(f"() => window.scrollTo(0, {page_height})")
            await asyncio.sleep(1)
            
            # Use Playwright's full page screenshot
            screenshot_data = await self.page.screenshot(full_page=True)
            
            # Reset to top
            await self.page.evaluate("() => window.scrollTo(0, 0)")
            
            return screenshot_data
            
        except Exception as e:
            logger.error(f"Error in page scrolling: {e}")
            return await self.page.screenshot()

    async def take_form_filling_screenshot(self):
        """
        Takes a screenshot of the form filling page before search button click
        
        Returns:
            str: Filename of the generated screenshot
        """
        return await self.take_full_page_screenshot(
            "01_form_filling_before_search",
            "Form Filled"
        )

    async def take_search_results_screenshot(self):
        """
        Takes a screenshot after search button click and before save and continue
        
        Returns:
            str: Filename of the generated screenshot
        """
        return await self.take_scrolling_screenshot(
            "02_search_results_before_continue",
            "Search Results"
        )

    async def take_category_selection_screenshot(self):
        """
        Takes a screenshot of the category page after radio button click
        
        Returns:
            str: Filename of the generated screenshot
        """
        return await self.take_scrolling_screenshot(
            "03_category_selection_after_radio",
            "Category Selection"
        )

    async def take_price_details_screenshot(self):
        """
        Takes a screenshot of the price page after submit changes button click
        
        Returns:
            str: Filename of the generated screenshot
        """
        return await self.take_scrolling_screenshot(
            "04_price_details_after_submit",
            "Price Details"
        )


# Convenience functions for standalone usage
async def take_full_page_screenshot(page, filename, request_id=None, cabin_id=None, session_id=None, screenshot_type="general"):
    """
    Convenience function to take a full page screenshot without needing a manager instance
    
    Args:
        page: Playwright Page instance
        filename: Name of the screenshot file
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
        screenshot_type: Type of screenshot for categorization
    
    Returns:
        str: Filename of the generated screenshot
    """
    manager = OneSourceScreenshotManager(page, request_id, cabin_id, session_id)
    return await manager.take_full_page_screenshot(filename, screenshot_type)

async def take_scrolling_screenshot(page, filename, request_id=None, cabin_id=None, session_id=None, screenshot_type="general"):
    """
    Convenience function to take a scrolling screenshot without needing a manager instance
    
    Args:
        page: Playwright Page instance
        filename: Name of the screenshot file
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
        screenshot_type: Type of screenshot for categorization
    
    Returns:
        str: Filename of the generated screenshot
    """
    manager = OneSourceScreenshotManager(page, request_id, cabin_id, session_id)
    return await manager.take_scrolling_screenshot(filename, screenshot_type)

async def take_form_filling_screenshot(page, request_id=None, cabin_id=None, session_id=None):
    """
    Convenience function to take form filling screenshot
    """
    manager = OneSourceScreenshotManager(page, request_id, cabin_id, session_id)
    return await manager.take_form_filling_screenshot()

async def take_search_results_screenshot(page, request_id=None, cabin_id=None, session_id=None):
    """
    Convenience function to take search results screenshot
    """
    manager = OneSourceScreenshotManager(page, request_id, cabin_id, session_id)
    return await manager.take_search_results_screenshot()

async def take_category_selection_screenshot(page, request_id=None, cabin_id=None, session_id=None):
    """
    Convenience function to take category selection screenshot
    """
    manager = OneSourceScreenshotManager(page, request_id, cabin_id, session_id)
    return await manager.take_category_selection_screenshot()

async def take_price_details_screenshot(page, request_id=None, cabin_id=None, session_id=None):
    """
    Convenience function to take price details screenshot
    """
    manager = OneSourceScreenshotManager(page, request_id, cabin_id, session_id)
    return await manager.take_price_details_screenshot() 