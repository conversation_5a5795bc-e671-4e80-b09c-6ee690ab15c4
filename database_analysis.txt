/** Disk-Space Utilization Report For oceanmind.db

Page size in bytes................................ 4096      
Pages in the whole file (measured)................ 1048996   
Pages in the whole file (calculated).............. 1048996   
Pages that store data............................. 205487      19.6% 
Pages on the freelist (per header)................ 843508      80.4% 
Pages on the freelist (calculated)................ 843509      80.4% 
Pages of auto-vacuum overhead..................... 0            0.0% 
Number of tables in the database.................. 15        
Number of indices................................. 6         
Number of defined indices......................... 0         
Number of implied indices......................... 6         
Size of the file in bytes......................... 4296687616
Bytes of user payload stored...................... 840430788   19.6% 

*** Page counts for all tables with their indices *****************************

CENTRALIZED_VIDEOS................................ 203020      19.4% 
CENTRALIZED_SCREENSHOTS........................... 2015         0.19% 
INPUT_TEXT_STORAGE................................ 125          0.012% 
CRUISING_POWER_CABINS............................. 94           0.009% 
SESSION_TRACKING.................................. 75           0.007% 
USER_BOOKING_TRACKING............................. 47           0.004% 
NCL_CABINS........................................ 46           0.004% 
STUDIO_CABINS..................................... 24           0.002% 
NCL_BOOKINGS...................................... 15           0.001% 
CRUISING_POWER_BOOKINGS........................... 14           0.001% 
SQLITE_SCHEMA..................................... 4            0.0% 
USERS............................................. 4            0.0% 
PRICING_PORTALS................................... 2            0.0% 
SQLITE_SEQUENCE................................... 1            0.0% 
STUDIO_BOOKINGS................................... 1            0.0% 

*** Page counts for all tables and indices separately *************************

CENTRALIZED_VIDEOS................................ 203020      19.4% 
CENTRALIZED_SCREENSHOTS........................... 2015         0.19% 
INPUT_TEXT_STORAGE................................ 125          0.012% 
CRUISING_POWER_CABINS............................. 94           0.009% 
SESSION_TRACKING.................................. 63           0.006% 
NCL_CABINS........................................ 46           0.004% 
USER_BOOKING_TRACKING............................. 35           0.003% 
STUDIO_CABINS..................................... 24           0.002% 
NCL_BOOKINGS...................................... 15           0.001% 
CRUISING_POWER_BOOKINGS........................... 14           0.001% 
SQLITE_AUTOINDEX_SESSION_TRACKING_1............... 12           0.001% 
SQLITE_AUTOINDEX_USER_BOOKING_TRACKING_1.......... 12           0.001% 
SQLITE_SCHEMA..................................... 4            0.0% 
PRICING_PORTALS................................... 1            0.0% 
SQLITE_AUTOINDEX_PRICING_PORTALS_1................ 1            0.0% 
SQLITE_AUTOINDEX_USERS_1.......................... 1            0.0% 
SQLITE_AUTOINDEX_USERS_2.......................... 1            0.0% 
SQLITE_AUTOINDEX_USERS_3.......................... 1            0.0% 
SQLITE_SEQUENCE................................... 1            0.0% 
STUDIO_BOOKINGS................................... 1            0.0% 
USERS............................................. 1            0.0% 

*** All tables and indices ****************************************************

Percentage of total database......................  19.6%    
Number of entries................................. 10718     
Bytes of storage consumed......................... 841674752 
Bytes of payload.................................. 840511010   99.86% 
Bytes of metadata................................. 1700827      0.20% 
Average payload per entry......................... 78420.51  
Average unused bytes per entry.................... 26.29     
Average metadata per entry........................ 158.69    
Average fanout.................................... 42.00     
Maximum payload per entry......................... 7219189   
Entries that use overflow......................... 206          1.9% 
Index pages used.................................. 13        
Primary pages used................................ 554       
Overflow pages used............................... 204920    
Total pages used.................................. 205487    
Unused bytes on index pages....................... 48339       90.8% 
Unused bytes on primary pages..................... 223541       9.9% 
Unused bytes on overflow pages.................... 9891         0.001% 
Unused bytes on all pages......................... 281771       0.033% 

*** All tables ****************************************************************

Percentage of total database......................  19.6%    
Number of entries................................. 8016      
Bytes of storage consumed......................... 841560064 
Bytes of payload.................................. 840438350   99.87% 
Bytes of metadata................................. 1692409      0.20% 
Average payload per entry......................... 104845.10 
Average unused bytes per entry.................... 30.96     
Average metadata per entry........................ 211.13    
Average fanout.................................... 47.00     
Maximum payload per entry......................... 7219189   
Entries that use overflow......................... 206          2.6% 
Index pages used.................................. 11        
Primary pages used................................ 528       
Overflow pages used............................... 204920    
Total pages used.................................. 205459    
Unused bytes on index pages....................... 40851       90.7% 
Unused bytes on primary pages..................... 197419       9.1% 
Unused bytes on overflow pages.................... 9891         0.001% 
Unused bytes on all pages......................... 248161       0.029% 

*** All indices ***************************************************************

Percentage of total database......................   0.003%  
Number of entries................................. 2702      
Bytes of storage consumed......................... 114688    
Bytes of payload.................................. 72660       63.4% 
Bytes of metadata................................. 8418         7.3% 
Average payload per entry......................... 26.89     
Average unused bytes per entry.................... 12.44     
Average metadata per entry........................ 3.12      
Average fanout.................................... 14.00     
Maximum payload per entry......................... 40        
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 2         
Primary pages used................................ 26        
Overflow pages used............................... 0         
Total pages used.................................. 28        
Unused bytes on index pages....................... 7488        91.4% 
Unused bytes on primary pages..................... 26122       24.5% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 33610       29.3% 

*** Table CENTRALIZED_SCREENSHOTS *********************************************

Percentage of total database......................   0.19%   
Number of entries................................. 12        
Bytes of storage consumed......................... 8253440   
Bytes of payload.................................. 8230140     99.72% 
Bytes of metadata................................. 16272        0.20% 
B-tree depth...................................... 2         
Average payload per entry......................... 685845.00 
Average unused bytes per entry.................... 1250.00   
Average metadata per entry........................ 1356.00   
Average fanout.................................... 9.00      
Non-sequential pages.............................. 4            0.20% 
Maximum payload per entry......................... 1182267   
Entries that use overflow......................... 12         100.0% 
Index pages used.................................. 1         
Primary pages used................................ 9         
Overflow pages used............................... 2005      
Total pages used.................................. 2015      
Unused bytes on index pages....................... 4020        98.1% 
Unused bytes on primary pages..................... 10980       29.8% 
Unused bytes on overflow pages.................... 0            0.0% 
Unused bytes on all pages......................... 15000        0.18% 

*** Table CENTRALIZED_VIDEOS **************************************************

Percentage of total database......................  19.4%    
Number of entries................................. 186       
Bytes of storage consumed......................... 831569920 
Bytes of payload.................................. 830651726   99.89% 
Bytes of metadata................................. 1626329      0.20% 
B-tree depth...................................... 2         
Average payload per entry......................... 4465869.49
Average unused bytes per entry.................... 552.41    
Average metadata per entry........................ 8743.70   
Average fanout.................................... 112.00    
Non-sequential pages.............................. 0            0.0% 
Maximum payload per entry......................... 7219189   
Entries that use overflow......................... 186        100.0% 
Index pages used.................................. 1         
Primary pages used................................ 112       
Overflow pages used............................... 202907    
Total pages used.................................. 203020    
Unused bytes on index pages....................... 3274        79.9% 
Unused bytes on primary pages..................... 89584       19.5% 
Unused bytes on overflow pages.................... 9891         0.001% 
Unused bytes on all pages......................... 102749       0.012% 

*** Table CRUISING_POWER_BOOKINGS *********************************************

Percentage of total database......................   0.001%  
Number of entries................................. 691       
Bytes of storage consumed......................... 57344     
Bytes of payload.................................. 48306       84.2% 
Bytes of metadata................................. 3538         6.2% 
B-tree depth...................................... 2         
Average payload per entry......................... 69.91     
Average unused bytes per entry.................... 7.96      
Average metadata per entry........................ 5.12      
Average fanout.................................... 13.00     
Non-sequential pages.............................. 12          92.3% 
Maximum payload per entry......................... 90        
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 1         
Primary pages used................................ 13        
Overflow pages used............................... 0         
Total pages used.................................. 14        
Unused bytes on index pages....................... 3990        97.4% 
Unused bytes on primary pages..................... 1510         2.8% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 5500         9.6% 

*** Table CRUISING_POWER_CABINS ***********************************************

Percentage of total database......................   0.009%  
Number of entries................................. 1503      
Bytes of storage consumed......................... 385024    
Bytes of payload.................................. 357803      92.9% 
Bytes of metadata................................. 10315        2.7% 
B-tree depth...................................... 2         
Average payload per entry......................... 238.06    
Average unused bytes per entry.................... 11.25     
Average metadata per entry........................ 6.86      
Average fanout.................................... 93.00     
Non-sequential pages.............................. 91          97.8% 
Maximum payload per entry......................... 359       
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 1         
Primary pages used................................ 93        
Overflow pages used............................... 0         
Total pages used.................................. 94        
Unused bytes on index pages....................... 3355        81.9% 
Unused bytes on primary pages..................... 13551        3.6% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 16906        4.4% 

*** Table INPUT_TEXT_STORAGE **************************************************

Percentage of total database......................   0.012%  
Number of entries................................. 1009      
Bytes of storage consumed......................... 512000    
Bytes of payload.................................. 462914      90.4% 
Bytes of metadata................................. 7900         1.5% 
B-tree depth...................................... 2         
Average payload per entry......................... 458.78    
Average unused bytes per entry.................... 40.82     
Average metadata per entry........................ 7.83      
Average fanout.................................... 124.00    
Non-sequential pages.............................. 124        100.0% 
Maximum payload per entry......................... 956       
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 1         
Primary pages used................................ 124       
Overflow pages used............................... 0         
Total pages used.................................. 125       
Unused bytes on index pages....................... 3115        76.0% 
Unused bytes on primary pages..................... 38071        7.5% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 41186        8.0% 

*** Table NCL_BOOKINGS ********************************************************

Percentage of total database......................   0.001%  
Number of entries................................. 598       
Bytes of storage consumed......................... 61440     
Bytes of payload.................................. 52635       85.7% 
Bytes of metadata................................. 3089         5.0% 
B-tree depth...................................... 2         
Average payload per entry......................... 88.02     
Average unused bytes per entry.................... 9.56      
Average metadata per entry........................ 5.17      
Average fanout.................................... 14.00     
Non-sequential pages.............................. 13          92.9% 
Maximum payload per entry......................... 115       
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 1         
Primary pages used................................ 14        
Overflow pages used............................... 0         
Total pages used.................................. 15        
Unused bytes on index pages....................... 3982        97.2% 
Unused bytes on primary pages..................... 1734         3.0% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 5716         9.3% 

*** Table NCL_CABINS **********************************************************

Percentage of total database......................   0.004%  
Number of entries................................. 1174      
Bytes of storage consumed......................... 188416    
Bytes of payload.................................. 170660      90.6% 
Bytes of metadata................................. 7504         4.0% 
B-tree depth...................................... 2         
Average payload per entry......................... 145.37    
Average unused bytes per entry.................... 8.73      
Average metadata per entry........................ 6.39      
Average fanout.................................... 45.00     
Non-sequential pages.............................. 44          97.8% 
Maximum payload per entry......................... 172       
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 1         
Primary pages used................................ 45        
Overflow pages used............................... 0         
Total pages used.................................. 46        
Unused bytes on index pages....................... 3736        91.2% 
Unused bytes on primary pages..................... 6516         3.5% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 10252        5.4% 

*** Table PRICING_PORTALS and all its indices *********************************

Percentage of total database......................   0.0%    
Number of entries................................. 0         
Bytes of storage consumed......................... 8192      
Bytes of payload.................................. 0            0.0% 
Bytes of metadata................................. 16           0.20% 
Average payload per entry......................... 0.0       
Average unused bytes per entry.................... 0.0       
Average metadata per entry........................ 0.0       
Maximum payload per entry......................... 0         
Entries that use overflow......................... 0         
Primary pages used................................ 2         
Overflow pages used............................... 0         
Total pages used.................................. 2         
Unused bytes on primary pages..................... 8176        99.80% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 8176        99.80% 

*** Table PRICING_PORTALS w/o any indices *************************************

Percentage of total database......................   0.0%    
Number of entries................................. 0         
Bytes of storage consumed......................... 4096      
Bytes of payload.................................. 0            0.0% 
Bytes of metadata................................. 8            0.20% 
B-tree depth...................................... 1         
Average payload per entry......................... 0.0       
Average unused bytes per entry.................... 0.0       
Average metadata per entry........................ 0.0       
Maximum payload per entry......................... 0         
Entries that use overflow......................... 0         
Primary pages used................................ 1         
Overflow pages used............................... 0         
Total pages used.................................. 1         
Unused bytes on primary pages..................... 4088        99.80% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 4088        99.80% 

*** Index SQLITE_AUTOINDEX_PRICING_PORTALS_1 of table PRICING_PORTALS *********

Percentage of total database......................   0.0%    
Number of entries................................. 0         
Bytes of storage consumed......................... 4096      
Bytes of payload.................................. 0            0.0% 
Bytes of metadata................................. 8            0.20% 
B-tree depth...................................... 1         
Average payload per entry......................... 0.0       
Average unused bytes per entry.................... 0.0       
Average metadata per entry........................ 0.0       
Maximum payload per entry......................... 0         
Entries that use overflow......................... 0         
Primary pages used................................ 1         
Overflow pages used............................... 0         
Total pages used.................................. 1         
Unused bytes on primary pages..................... 4088        99.80% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 4088        99.80% 

*** Table SESSION_TRACKING and all its indices ********************************

Percentage of total database......................   0.007%  
Number of entries................................. 2678      
Bytes of storage consumed......................... 307200    
Bytes of payload.................................. 270737      88.1% 
Bytes of metadata................................. 13055        4.2% 
Average payload per entry......................... 101.10    
Average unused bytes per entry.................... 8.74      
Average metadata per entry........................ 4.87      
Average fanout.................................... 37.00     
Maximum payload per entry......................... 225       
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 2         
Primary pages used................................ 73        
Overflow pages used............................... 0         
Total pages used.................................. 75        
Unused bytes on index pages....................... 7345        89.7% 
Unused bytes on primary pages..................... 16063        5.4% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 23408        7.6% 

*** Table SESSION_TRACKING w/o any indices ************************************

Percentage of total database......................   0.006%  
Number of entries................................. 1339      
Bytes of storage consumed......................... 258048    
Bytes of payload.................................. 234702      91.0% 
Bytes of metadata................................. 8898         3.4% 
B-tree depth...................................... 2         
Average payload per entry......................... 175.28    
Average unused bytes per entry.................... 10.79     
Average metadata per entry........................ 6.65      
Average fanout.................................... 62.00     
Non-sequential pages.............................. 62         100.0% 
Maximum payload per entry......................... 225       
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 1         
Primary pages used................................ 62        
Overflow pages used............................... 0         
Total pages used.................................. 63        
Unused bytes on index pages....................... 3601        87.9% 
Unused bytes on primary pages..................... 10847        4.3% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 14448        5.6% 

*** Index SQLITE_AUTOINDEX_SESSION_TRACKING_1 of table SESSION_TRACKING *******

Percentage of total database......................   0.001%  
Number of entries................................. 1339      
Bytes of storage consumed......................... 49152     
Bytes of payload.................................. 36035       73.3% 
Bytes of metadata................................. 4157         8.5% 
B-tree depth...................................... 2         
Average payload per entry......................... 26.91     
Average unused bytes per entry.................... 6.69      
Average metadata per entry........................ 3.10      
Average fanout.................................... 12.00     
Non-sequential pages.............................. 10          90.9% 
Maximum payload per entry......................... 40        
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 1         
Primary pages used................................ 11        
Overflow pages used............................... 0         
Total pages used.................................. 12        
Unused bytes on index pages....................... 3744        91.4% 
Unused bytes on primary pages..................... 5216        11.6% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 8960        18.2% 

*** Table SQLITE_SCHEMA *******************************************************

Percentage of total database......................   0.0%    
Number of entries................................. 20        
Bytes of storage consumed......................... 16384     
Bytes of payload.................................. 7562        46.2% 
Bytes of metadata................................. 243          1.5% 
B-tree depth...................................... 2         
Average payload per entry......................... 378.10    
Average unused bytes per entry.................... 428.95    
Average metadata per entry........................ 12.15     
Average fanout.................................... 3.00      
Non-sequential pages.............................. 3          100.0% 
Maximum payload per entry......................... 1330      
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 1         
Primary pages used................................ 3         
Overflow pages used............................... 0         
Total pages used.................................. 4         
Unused bytes on index pages....................... 3970        96.9% 
Unused bytes on primary pages..................... 4609        37.5% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 8579        52.4% 

*** Table SQLITE_SEQUENCE *****************************************************

Percentage of total database......................   0.0%    
Number of entries................................. 9         
Bytes of storage consumed......................... 4096      
Bytes of payload.................................. 196          4.8% 
Bytes of metadata................................. 44           1.1% 
B-tree depth...................................... 1         
Average payload per entry......................... 21.78     
Average unused bytes per entry.................... 428.44    
Average metadata per entry........................ 4.89      
Maximum payload per entry......................... 28        
Entries that use overflow......................... 0            0.0% 
Primary pages used................................ 1         
Overflow pages used............................... 0         
Total pages used.................................. 1         
Unused bytes on primary pages..................... 3856        94.1% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 3856        94.1% 

*** Table STUDIO_BOOKINGS *****************************************************

Percentage of total database......................   0.0%    
Number of entries................................. 43        
Bytes of storage consumed......................... 4096      
Bytes of payload.................................. 3643        88.9% 
Bytes of metadata................................. 180          4.4% 
B-tree depth...................................... 1         
Average payload per entry......................... 84.72     
Average unused bytes per entry.................... 6.35      
Average metadata per entry........................ 4.19      
Maximum payload per entry......................... 102       
Entries that use overflow......................... 0            0.0% 
Primary pages used................................ 1         
Overflow pages used............................... 0         
Total pages used.................................. 1         
Unused bytes on primary pages..................... 273          6.7% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 273          6.7% 

*** Table STUDIO_CABINS *******************************************************

Percentage of total database......................   0.002%  
Number of entries................................. 85        
Bytes of storage consumed......................... 98304     
Bytes of payload.................................. 84611       86.1% 
Bytes of metadata................................. 715          0.73% 
B-tree depth...................................... 2         
Average payload per entry......................... 995.42    
Average unused bytes per entry.................... 152.68    
Average metadata per entry........................ 8.41      
Average fanout.................................... 15.00     
Non-sequential pages.............................. 14          60.9% 
Maximum payload per entry......................... 4813      
Entries that use overflow......................... 8            9.4% 
Index pages used.................................. 1         
Primary pages used................................ 15        
Overflow pages used............................... 8         
Total pages used.................................. 24        
Unused bytes on index pages....................... 3986        97.3% 
Unused bytes on primary pages..................... 8992        14.6% 
Unused bytes on overflow pages.................... 0            0.0% 
Unused bytes on all pages......................... 12978       13.2% 

*** Table USER_BOOKING_TRACKING and all its indices ***************************

Percentage of total database......................   0.004%  
Number of entries................................. 2678      
Bytes of storage consumed......................... 192512    
Bytes of payload.................................. 166608      86.5% 
Bytes of metadata................................. 11483        6.0% 
Average payload per entry......................... 62.21     
Average unused bytes per entry.................... 5.38      
Average metadata per entry........................ 4.29      
Average fanout.................................... 23.00     
Maximum payload per entry......................... 399       
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 2         
Primary pages used................................ 45        
Overflow pages used............................... 0         
Total pages used.................................. 47        
Unused bytes on index pages....................... 7566        92.4% 
Unused bytes on primary pages..................... 6855         3.7% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 14421        7.5% 

*** Table USER_BOOKING_TRACKING w/o any indices *******************************

Percentage of total database......................   0.003%  
Number of entries................................. 1339      
Bytes of storage consumed......................... 143360    
Bytes of payload.................................. 130573      91.1% 
Bytes of metadata................................. 7326         5.1% 
B-tree depth...................................... 2         
Average payload per entry......................... 97.52     
Average unused bytes per entry.................... 4.08      
Average metadata per entry........................ 5.47      
Average fanout.................................... 34.00     
Non-sequential pages.............................. 34         100.0% 
Maximum payload per entry......................... 399       
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 1         
Primary pages used................................ 34        
Overflow pages used............................... 0         
Total pages used.................................. 35        
Unused bytes on index pages....................... 3822        93.3% 
Unused bytes on primary pages..................... 1639         1.2% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 5461         3.8% 

*** Index SQLITE_AUTOINDEX_USER_BOOKING_TRACKING_1 of table USER_BOOKING_TRACKING 

Percentage of total database......................   0.001%  
Number of entries................................. 1339      
Bytes of storage consumed......................... 49152     
Bytes of payload.................................. 36035       73.3% 
Bytes of metadata................................. 4157         8.5% 
B-tree depth...................................... 2         
Average payload per entry......................... 26.91     
Average unused bytes per entry.................... 6.69      
Average metadata per entry........................ 3.10      
Average fanout.................................... 12.00     
Non-sequential pages.............................. 10          90.9% 
Maximum payload per entry......................... 40        
Entries that use overflow......................... 0            0.0% 
Index pages used.................................. 1         
Primary pages used................................ 11        
Overflow pages used............................... 0         
Total pages used.................................. 12        
Unused bytes on index pages....................... 3744        91.4% 
Unused bytes on primary pages..................... 5216        11.6% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 8960        18.2% 

*** Table USERS and all its indices *******************************************

Percentage of total database......................   0.0%    
Number of entries................................. 32        
Bytes of storage consumed......................... 16384     
Bytes of payload.................................. 3469        21.2% 
Bytes of metadata................................. 144          0.88% 
Average payload per entry......................... 108.41    
Average unused bytes per entry.................... 399.09    
Average metadata per entry........................ 4.50      
Maximum payload per entry......................... 381       
Entries that use overflow......................... 0            0.0% 
Primary pages used................................ 4         
Overflow pages used............................... 0         
Total pages used.................................. 4         
Unused bytes on primary pages..................... 12771       77.9% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 12771       77.9% 

*** Table USERS w/o any indices ***********************************************

Percentage of total database......................   0.0%    
Number of entries................................. 8         
Bytes of storage consumed......................... 4096      
Bytes of payload.................................. 2879        70.3% 
Bytes of metadata................................. 48           1.2% 
B-tree depth...................................... 1         
Average payload per entry......................... 359.88    
Average unused bytes per entry.................... 146.12    
Average metadata per entry........................ 6.00      
Maximum payload per entry......................... 381       
Entries that use overflow......................... 0            0.0% 
Primary pages used................................ 1         
Overflow pages used............................... 0         
Total pages used.................................. 1         
Unused bytes on primary pages..................... 1169        28.5% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 1169        28.5% 

*** Indices of table USERS ****************************************************

Percentage of total database......................   0.0%    
Number of entries................................. 24        
Bytes of storage consumed......................... 12288     
Bytes of payload.................................. 590          4.8% 
Bytes of metadata................................. 96           0.78% 
Average payload per entry......................... 24.58     
Average unused bytes per entry.................... 483.42    
Average metadata per entry........................ 4.00      
Maximum payload per entry......................... 40        
Entries that use overflow......................... 0            0.0% 
Primary pages used................................ 3         
Overflow pages used............................... 0         
Total pages used.................................. 3         
Unused bytes on primary pages..................... 11602       94.4% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 11602       94.4% 

*** Index SQLITE_AUTOINDEX_USERS_1 of table USERS *****************************

Percentage of total database......................   0.0%    
Number of entries................................. 8         
Bytes of storage consumed......................... 4096      
Bytes of payload.................................. 319          7.8% 
Bytes of metadata................................. 32           0.78% 
B-tree depth...................................... 1         
Average payload per entry......................... 39.88     
Average unused bytes per entry.................... 468.12    
Average metadata per entry........................ 4.00      
Maximum payload per entry......................... 40        
Entries that use overflow......................... 0            0.0% 
Primary pages used................................ 1         
Overflow pages used............................... 0         
Total pages used.................................. 1         
Unused bytes on primary pages..................... 3745        91.4% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 3745        91.4% 

*** Index SQLITE_AUTOINDEX_USERS_2 of table USERS *****************************

Percentage of total database......................   0.0%    
Number of entries................................. 8         
Bytes of storage consumed......................... 4096      
Bytes of payload.................................. 107          2.6% 
Bytes of metadata................................. 32           0.78% 
B-tree depth...................................... 1         
Average payload per entry......................... 13.38     
Average unused bytes per entry.................... 494.62    
Average metadata per entry........................ 4.00      
Maximum payload per entry......................... 18        
Entries that use overflow......................... 0            0.0% 
Primary pages used................................ 1         
Overflow pages used............................... 0         
Total pages used.................................. 1         
Unused bytes on primary pages..................... 3957        96.6% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 3957        96.6% 

*** Index SQLITE_AUTOINDEX_USERS_3 of table USERS *****************************

Percentage of total database......................   0.0%    
Number of entries................................. 8         
Bytes of storage consumed......................... 4096      
Bytes of payload.................................. 164          4.0% 
Bytes of metadata................................. 32           0.78% 
B-tree depth...................................... 1         
Average payload per entry......................... 20.50     
Average unused bytes per entry.................... 487.50    
Average metadata per entry........................ 4.00      
Maximum payload per entry......................... 27        
Entries that use overflow......................... 0            0.0% 
Primary pages used................................ 1         
Overflow pages used............................... 0         
Total pages used.................................. 1         
Unused bytes on primary pages..................... 3900        95.2% 
Unused bytes on overflow pages.................... 0         
Unused bytes on all pages......................... 3900        95.2% 

*** Definitions ***************************************************************

Page size in bytes

    The number of bytes in a single page of the database file.  
    Usually 1024.

Number of pages in the whole file

    The number of 4096-byte pages that go into forming the complete
    database

Pages that store data

    The number of pages that store data, either as primary B*Tree pages or
    as overflow pages.  The number at the right is the data pages divided by
    the total number of pages in the file.

Pages on the freelist

    The number of pages that are not currently in use but are reserved for
    future use.  The percentage at the right is the number of freelist pages
    divided by the total number of pages in the file.

Pages of auto-vacuum overhead

    The number of pages that store data used by the database to facilitate
    auto-vacuum. This is zero for databases that do not support auto-vacuum.

Number of tables in the database

    The number of tables in the database, including the SQLITE_SCHEMA table
    used to store schema information.

Number of indices

    The total number of indices in the database.

Number of defined indices

    The number of indices created using an explicit CREATE INDEX statement.

Number of implied indices

    The number of indices used to implement PRIMARY KEY or UNIQUE constraints
    on tables.

Size of the file in bytes

    The total amount of disk space used by the entire database files.

Bytes of user payload stored

    The total number of bytes of user payload stored in the database. The
    schema information in the SQLITE_SCHEMA table is not counted when
    computing this number.  The percentage at the right shows the payload
    divided by the total file size.

Percentage of total database

    The amount of the complete database file that is devoted to storing
    information described by this category.

Number of entries

    The total number of B-Tree key/value pairs stored under this category.

Bytes of storage consumed

    The total amount of disk space required to store all B-Tree entries
    under this category.  The is the total number of pages used times
    the pages size.

Bytes of payload

    The amount of payload stored under this category.  Payload is the data
    part of table entries and the key part of index entries.  The percentage
    at the right is the bytes of payload divided by the bytes of storage 
    consumed.

Bytes of metadata

    The amount of formatting and structural information stored in the
    table or index.  Metadata includes the btree page header, the cell pointer
    array, the size field for each cell, the left child pointer or non-leaf
    cells, the overflow pointers for overflow cells, and the rowid value for
    rowid table cells.  In other words, metadata is everything that is neither
    unused space nor content.  The record header in the payload is counted as
    content, not metadata.

Average payload per entry

    The average amount of payload on each entry.  This is just the bytes of
    payload divided by the number of entries.

Average unused bytes per entry

    The average amount of free space remaining on all pages under this
    category on a per-entry basis.  This is the number of unused bytes on
    all pages divided by the number of entries.

Non-sequential pages

    The number of pages in the table or index that are out of sequence.
    Many filesystems are optimized for sequential file access so a small
    number of non-sequential pages might result in faster queries,
    especially for larger database files that do not fit in the disk cache.
    Note that after running VACUUM, the root page of each table or index is
    at the beginning of the database file and all other pages are in a
    separate part of the database file, resulting in a single non-
    sequential page.

Maximum payload per entry

    The largest payload size of any entry.

Entries that use overflow

    The number of entries that user one or more overflow pages.

Total pages used

    This is the number of pages used to hold all information in the current
    category.  This is the sum of index, primary, and overflow pages.

Index pages used

    This is the number of pages in a table B-tree that hold only key (rowid)
    information and no data.

Primary pages used

    This is the number of B-tree pages that hold both key and data.

Overflow pages used

    The total number of overflow pages used for this category.

Unused bytes on index pages

    The total number of bytes of unused space on all index pages.  The
    percentage at the right is the number of unused bytes divided by the
    total number of bytes on index pages.

Unused bytes on primary pages

    The total number of bytes of unused space on all primary pages.  The
    percentage at the right is the number of unused bytes divided by the
    total number of bytes on primary pages.

Unused bytes on overflow pages

    The total number of bytes of unused space on all overflow pages.  The
    percentage at the right is the number of unused bytes divided by the
    total number of bytes on overflow pages.

Unused bytes on all pages

    The total number of bytes of unused space on all primary and overflow 
    pages.  The percentage at the right is the number of unused bytes 
    divided by the total number of bytes.

*******************************************************************************
The entire text of this report can be sourced into any SQL database
engine for further analysis.  All of the text above is an SQL comment.
The data used to generate this report follows:
*/
BEGIN;
CREATE TABLE space_used(
   name clob,        -- Name of a table or index in the database file
   tblname clob,     -- Name of associated table
   is_index boolean, -- TRUE if it is an index, false for a table
   is_without_rowid boolean, -- TRUE if WITHOUT ROWID table  
   nentry int,       -- Number of entries in the BTree
   leaf_entries int, -- Number of leaf entries
   depth int,        -- Depth of the b-tree
   payload int,      -- Total amount of data stored in this table or index
   ovfl_payload int, -- Total amount of data stored on overflow pages
   ovfl_cnt int,     -- Number of entries that use overflow
   mx_payload int,   -- Maximum payload size
   int_pages int,    -- Number of interior pages used
   leaf_pages int,   -- Number of leaf pages used
   ovfl_pages int,   -- Number of overflow pages used
   int_unused int,   -- Number of unused bytes on interior pages
   leaf_unused int,  -- Number of unused bytes on primary pages
   ovfl_unused int,  -- Number of unused bytes on overflow pages
   gap_cnt int,      -- Number of gaps in the page layout
   compressed_size int  -- Total bytes stored on disk
);
INSERT INTO space_used VALUES('sqlite_schema','sqlite_schema',0,0,22,20,2,7562,0,0,1330,1,3,0,3970,4609,0,3,16384);
INSERT INTO space_used VALUES('users','users',0,0,8,8,1,2879,0,0,381,0,1,0,0,1169,0,0,4096);
INSERT INTO space_used VALUES('sqlite_autoindex_users_1','users',1,0,8,8,1,319,0,0,40,0,1,0,0,3745,0,0,4096);
INSERT INTO space_used VALUES('sqlite_autoindex_users_2','users',1,0,8,8,1,107,0,0,18,0,1,0,0,3957,0,0,4096);
INSERT INTO space_used VALUES('sqlite_autoindex_users_3','users',1,0,8,8,1,164,0,0,27,0,1,0,0,3900,0,0,4096);
INSERT INTO space_used VALUES('pricing_portals','pricing_portals',0,0,0,0,1,0,0,0,0,0,1,0,0,4088,0,0,4096);
INSERT INTO space_used VALUES('sqlite_autoindex_pricing_portals_1','pricing_portals',1,0,0,0,1,0,0,0,0,0,1,0,0,4088,0,0,4096);
INSERT INTO space_used VALUES('studio_bookings','studio_bookings',0,0,43,43,1,3643,0,0,102,0,1,0,0,273,0,0,4096);
INSERT INTO space_used VALUES('sqlite_sequence','sqlite_sequence',0,0,9,9,1,196,0,0,28,0,1,0,0,3856,0,0,4096);
INSERT INTO space_used VALUES('studio_cabins','studio_cabins',0,0,99,85,2,84611,32736,8,4813,1,15,8,3986,8992,0,14,98304);
INSERT INTO space_used VALUES('cruising_power_bookings','cruising_power_bookings',0,0,703,691,2,48306,0,0,90,1,13,0,3990,1510,0,12,57344);
INSERT INTO space_used VALUES('cruising_power_cabins','cruising_power_cabins',0,0,1595,1503,2,357803,0,0,359,1,93,0,3355,13551,0,91,385024);
INSERT INTO space_used VALUES('ncl_bookings','ncl_bookings',0,0,611,598,2,52635,0,0,115,1,14,0,3982,1734,0,13,61440);
INSERT INTO space_used VALUES('ncl_cabins','ncl_cabins',0,0,1218,1174,2,170660,0,0,172,1,45,0,3736,6516,0,44,188416);
INSERT INTO space_used VALUES('session_tracking','session_tracking',0,0,1400,1339,2,234702,0,0,225,1,62,0,3601,10847,0,62,258048);
INSERT INTO space_used VALUES('sqlite_autoindex_session_tracking_1','session_tracking',1,0,1339,1329,2,36035,0,0,40,1,11,0,3744,5216,0,10,49152);
INSERT INTO space_used VALUES('centralized_screenshots','centralized_screenshots',0,0,20,12,2,8230140,8204460,12,1182267,1,9,2005,4020,10980,0,4,8253440);
INSERT INTO space_used VALUES('user_booking_tracking','user_booking_tracking',0,0,1372,1339,2,130573,0,0,399,1,34,0,3822,1639,0,34,143360);
INSERT INTO space_used VALUES('sqlite_autoindex_user_booking_tracking_1','user_booking_tracking',1,0,1339,1329,2,36035,0,0,40,1,11,0,3744,5216,0,10,49152);
INSERT INTO space_used VALUES('input_text_storage','input_text_storage',0,0,1132,1009,2,462914,0,0,956,1,124,0,3115,38071,0,124,512000);
INSERT INTO space_used VALUES('centralized_videos','centralized_videos',0,0,297,186,2,830651726,830285553,186,7219189,1,112,202907,3274,89584,9891,0,831569920);
COMMIT;
