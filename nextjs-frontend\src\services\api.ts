import { ProviderType } from '../components/booking/ProviderSelector';
import { QuoteInputData } from '../components/booking/QuoteInput';
import { ConfigState } from '../components/ui/Config';
import { getToken } from './auth';

// API configuration
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

// Helper function to add auth token to headers
const getAuthHeaders = (): Record<string, string> => {
  const token = getToken();
  const headers: Record<string, string> = {};
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
};

// Helper function to handle 401 responses
const handleUnauthorized = () => {
  // Import logout function dynamically to avoid circular imports
  const { logout } = require('./auth');
  logout();
  
  // Show session expired message
  alert('Session expired. Please log in again.');
  
  // Redirect to home page
  if (typeof window !== 'undefined') {
    window.location.href = '/';
  }
};

// Enhanced fetch function that handles 401 responses
const authFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const headers = {
    'Content-Type': 'application/json',
    ...getAuthHeaders(),
    ...options.headers,
  };
  
  const response = await fetch(url, { ...options, headers });
  
  // Check for 401 Unauthorized
  if (response.status === 401) {
    handleUnauthorized();
    throw new Error('Session expired. Please log in again.');
  }
  
  return response;
};

// Interface for extraction response
export interface ExtractionResponse {
  success: boolean;
  details: any; // This will be different based on provider
}

// Interface for booking response
export interface BookingResponse {
  success: boolean;
  message: string;
  session_id: string;
  request_id: string;
}

// Interface for booking status
export interface BookingStatus {
  success: boolean;
  session_id: string;
  status: {
    status: 'pending' | 'in_progress' | 'completed' | 'error';
    metadata: {
      execution_time?: number;
      error?: string;
      [key: string]: any;
    };
  };
}

// Interface for screenshot response
export interface ScreenshotResponse {
  success: boolean;
  session_id: string;
  screenshots: Screenshot[];
}

export interface Screenshot {
  id: number;
  cabin_id: string;
  screenshot_type: string;
  timestamp: string;
  file_name: string;
}

// Interface for booking results
export interface BookingResults {
  success: boolean;
  error?: string;
  bookingInfo?: any;
  results?: any;
  status?: string;
}

// Interface for user tracking data
export interface UserTrackingData {
  session_id: string;
  username: string;
  provider: string;
  request_id: string;
  timestamp: string;
  val_status?: string; // Changed from boolean to string to support 'validated', 'reprocessed', 'reset'
  val_mark?: string;
  as_per_feature?: string;
  val_comment?: string;
  issue_codes?: IssueCodeEntry[] | string;
  screenshot_count?: number;
  booking_id?: string;
  booking_timestamp?: string;
  booking_status?: string;
  execution_time?: number;
}

// Extraction service
export const extractDetails = async (
  provider: ProviderType,
  data: QuoteInputData
): Promise<ExtractionResponse> => {
  try {
    console.log(`Extracting details for provider: ${provider}`, data);
    
    const response = await authFetch(`${API_BASE_URL}/extraction`, {
      method: 'POST',
      body: JSON.stringify({
        provider,
        text_input: data.textInput,
        url_input: data.urlInput,
        request_id: data.requestId
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log("Extraction response:", result);
    
    return result;
  } catch (error) {
    console.error('Error in extraction service:', error);
    throw error;
  }
};

// Booking service
export const startBooking = async (
  provider: ProviderType,
  cruiseDetails: any,
  config: ConfigState,
  requestId: string
): Promise<BookingResponse> => {
  try {
    let endpoint = '';
    
    // Adjust config as needed for specific providers
    const finalConfig = { ...config, provider };
    
    // Log booking request parameters
    console.log(`Booking request for ${provider} with config:`, finalConfig);
    
    // Handle different providers
    if (provider.startsWith('Studio')) {
      endpoint = '/studio-booking';
    } else {
      switch (provider) {
        case 'NCL':
          endpoint = '/ncl-booking';
          break;
        case 'Cruising Power':
          endpoint = '/cruising-power-booking';
          break;
        case 'OneSource':
          endpoint = '/onesource-booking';
          break;
        default:
          throw new Error(`Unknown provider: ${provider}`);
      }
    }
    
    const response = await authFetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      body: JSON.stringify({
        provider,
        cruise_details: cruiseDetails,
        config: finalConfig,
        request_id: requestId
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error ${response.status}: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error in booking service:', error);
    throw error;
  }
};

// Get screenshots
export const getScreenshots = async (sessionId: string): Promise<ScreenshotResponse> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/screenshots/${sessionId}`);

    if (!response.ok) {
      throw new Error(`API Error: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching screenshots:', error);
    throw error;
  }
};

// Get booking status
export const getBookingStatus = async (sessionId: string): Promise<any> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/booking-status/${sessionId}`);

    if (!response.ok) {
      throw new Error(`API Error: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching booking status:', error);
    throw error;
  }
};

// Cancel booking
export const cancelBooking = async (sessionId: string, reset: boolean = true): Promise<any> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/cancel-booking/${sessionId}?reset=${reset}`, {
      method: 'POST',
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error ${response.status}: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error cancelling booking:', error);
    throw error;
  }
};

// Get Studio booking results
export const getStudioResults = async (requestId: string, sessionId?: string): Promise<BookingResults> => {
  try {
    const url = sessionId 
      ? `${API_BASE_URL}/studio-results/${requestId}?session_id=${sessionId}`
      : `${API_BASE_URL}/studio-results/${requestId}`;
      
    const response = await authFetch(url);

    if (!response.ok) {
      throw new Error(`API Error: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching Studio results:', error);
    throw error;
  }
};

// Get NCL booking results
export const getNCLResults = async (requestId: string, sessionId?: string): Promise<BookingResults> => {
  try {
    const url = sessionId 
      ? `${API_BASE_URL}/ncl-results/${requestId}?session_id=${sessionId}`
      : `${API_BASE_URL}/ncl-results/${requestId}`;
      
    const response = await authFetch(url);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `API Error: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching NCL results:', error);
    throw error;
  }
};

// Get Cruising Power booking results
export const getCruisingPowerResults = async (requestId: string, sessionId?: string): Promise<BookingResults> => {
  try {
    const url = sessionId
      ? `${API_BASE_URL}/cruising-power-results/${requestId}?session_id=${sessionId}`
      : `${API_BASE_URL}/cruising-power-results/${requestId}`;
      
    const response = await authFetch(url);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `API Error: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching Cruising Power results:', error);
    throw error;
  }
};

// Get OneSource booking results
export const getOneSourceResults = async (requestId: string, sessionId?: string): Promise<BookingResults> => {
  try {
    const url = sessionId
      ? `${API_BASE_URL}/onesource-results/${requestId}?session_id=${sessionId}`
      : `${API_BASE_URL}/onesource-results/${requestId}`;
      
    const response = await authFetch(url);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `API Error: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching OneSource results:', error);
    throw error;
  }
};

// -----------------  Consolidated Results View ----------------
export interface ResultsViewResponse {
  success: boolean;
  provider: string;
  request_id: string;
  session_id?: string;
  quote_input?: any;
  bookingInfo?: any;
  results?: any;
  screenshots?: any[];
  videos?: any[];
  error?: string;
}

export const getResultsView = async (
  provider: ProviderType,
  requestId: string,
  sessionId?: string
): Promise<ResultsViewResponse> => {
  try {
    const query = new URLSearchParams({ provider });
    if (sessionId) query.append('session_id', sessionId);
    const response = await authFetch(`${API_BASE_URL}/results-view/${requestId}?${query.toString()}`);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `API Error: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching consolidated results view:', error);
    throw error;
  }
};

// Validation submission service
 type IssueCodeEntry = {
  category: string;
  code: string;
};

type ValidationRequestData = {
  session_id: string;
  provider: string;
  request_id: string;
  val_status: string;
  val_mark: string;
  as_per_feature: string;
  val_comment: string;
  issue_codes?: IssueCodeEntry[];
};

export const submitValidation = async (
  data: ValidationRequestData
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(`${API_BASE_URL}/api/user-tracking/validate`, {
        method: 'PUT',
        body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error submitting validation:', error);
    throw error;
  }
};

// Input Text Storage functions
export const getStoredInputText = async (requestId: string): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/input-text/${requestId}`, {
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching stored input text:', error);
    throw error;
  }
};

// Get user tracking data with filters
export const getUserTrackingData = async (
  provider?: string,
  username?: string
): Promise<UserTrackingData[]> => {
  try {
    const queryParams = new URLSearchParams();
    if (provider && provider !== 'all') queryParams.append('provider', provider);
    if (username) queryParams.append('username', username);
    
    const response = await fetch(
      `${API_BASE_URL}/api/user-tracking/data?${queryParams.toString()}`,
      {
        headers: getAuthHeaders()
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `API Error: ${response.statusText}`);
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.detail || 'Failed to fetch tracking data');
    }
    
    return result.data;
  } catch (error) {
    console.error('Error fetching user tracking data:', error);
    throw error;
  }
};

export const fetchProviders = async () => {
  try {
    const response = await authFetch(`${API_BASE_URL}/api/providers`, {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error('Failed to fetch providers');
    }

    const data = await response.json();
    return data.providers;
  } catch (error) {
    console.error('Error fetching providers:', error);
    throw error;
  }
};

export const fetchAgencies = async () => {
  try {
    const response = await authFetch(`${API_BASE_URL}/api/agencies`, {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error('Failed to fetch agencies');
    }

    const data = await response.json();
    return data.agencies;
  } catch (error) {
    console.error('Error fetching agencies:', error);
    throw error;
  }
};

/**
 * Update a provider's status.
 * @param providerId The ID of the provider to update
 * @param status One of 'active' | 'coming_soon' | 'maintenance' | 'deprecated'
 */
export const updateProviderStatus = async (
  providerId: number,
  status: 'active' | 'coming_soon' | 'maintenance'
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await authFetch(
      `${API_BASE_URL}/api/providers/${providerId}/status`,
      {
        method: 'PUT',
        body: JSON.stringify({ status }),
      }
    );

    if (!response.ok) {
      throw new Error('Failed to update provider status');
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating provider status:', error);
    throw error;
  }
};

// Interface for discount data response
export interface CabinDiscount {
  cabin_number: number;
  discount_text: string;
}

export interface DiscountDataResponse {
  success: boolean;
  request_id: string;
  session_id?: string;
  cabin_discounts: CabinDiscount[];
  error?: string;
}

// Get discount data for Studio bookings
export const getDiscountData = async (requestId: string, sessionId?: string): Promise<DiscountDataResponse> => {
  try {
    let url = `${API_BASE_URL}/api/discount-data/${requestId}`;
    if (sessionId) {
      url += `?session_id=${sessionId}`;
    }
    
    const response = await authFetch(url, {
      method: 'GET',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `API Error: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting discount data:', error);
    throw error;
  }
}; 