import asyncio
import logging
from typing import Any, Dict

import asyncpg

DB_CONFIG: Dict[str, Any] = {
    "user": "kanwar_raj",
    "password": "Kanwar@123",
    "database": "oceanmind",
    "host": "***********",
    "port": 5432,                     
}

logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")
logger = logging.getLogger("migration.val_status.dev")


async def migrate_val_status() -> None:
    try:
        logger.info(
            "Connecting to database %(database)s on %(host)s:%(port)s as %(user)s",
            DB_CONFIG,
        )
        conn = await asyncpg.connect(**DB_CONFIG)
        try:
            logger.info("Starting migration: val_status BOOLEAN → TEXT")

            logger.info("Converting column type and mapping existing values …")
            await conn.execute(
                """
                ALTER TABLE user_booking_tracking
                ALTER COLUMN val_status TYPE TEXT
                USING (CASE
                    WHEN val_status = TRUE THEN 'validated'
                    WHEN val_status = FALSE THEN 'reprocessed'
                    ELSE 'reprocessed'
                END);
                """
            )

            logger.info("Setting default value to 'reprocessed' …")
            await conn.execute(
                """
                ALTER TABLE user_booking_tracking
                ALTER COLUMN val_status SET DEFAULT 'reprocessed';
                """
            )

            logger.info("Ensuring NOT NULL constraint …")
            await conn.execute(
                """
                ALTER TABLE user_booking_tracking
                ALTER COLUMN val_status SET NOT NULL;
                """
            )

            result = await conn.fetchrow(
                """
                SELECT
                    COUNT(*)                              AS total,
                    COUNT(*) FILTER (WHERE val_status = 'validated')   AS validated_count,
                    COUNT(*) FILTER (WHERE val_status = 'reprocessed') AS reprocessed_count
                FROM user_booking_tracking;
                """
            )
            logger.info("Migration completed successfully!")
            logger.info("Total records:        %s", result["total"])
            logger.info("Validated:            %s", result["validated_count"])
            logger.info("Reprocessed:          %s", result["reprocessed_count"])
        finally:
            await conn.close()
            logger.info("Database connection closed")

    except Exception as exc:
        logger.error("Migration failed: %s", exc)
        raise  


async def main() -> None:
    try:
        await migrate_val_status()
    except Exception:
        logger.error("Migration script failed")
        exit(1)
    else:
        logger.info("Migration script finished successfully")


if __name__ == "__main__":
    asyncio.run(main())
