import re
from loguru import logger

CABIN_RATE_CODES = {
    'inside': ['IX', 'IF', 'IE', 'ID', 'IC', 'IB', 'IA', 'I1', 'I4', 'IT',
               'T1'], 'outside':
    ['OX', 'OF', 'OE', 'OD', 'OC', 'OB', 'OA', 'O2', 'O4', 'O5', 'OK', 'O3',
     'OT'], 'balcony':
    ['BX', 'BF', 'BE', 'BD', 'BC', 'BB', 'BA', 'B1', 'B4', 'B6',
     'BT'], 'mini-suite': ['MX', 'MC', 'MB', 'MA', 'M1', 'M2', 'M4', 'M6'], 'suite': [
         'SJ', 'SI', 'SH', 'SK', 'SL', 'S1', 'S2', 'S3', 'S4', 'S6', 'SF', 'SN', 'SM',
         'SD', 'SC', 'SP'
     ], 'haven': [
         'HI', 'HG', 'HF', 'HE', 'HD', 'HC', 'HB', 'HA', 'H1', 'H2', 'H3', 'H4', 'H5',
         'H6', 'H7'
     ]
}


class CabinCategories:

    CABIN_RATE_CODES = CABIN_RATE_CODES

    CATEGORY_MAPPING = {
        'inside': 'inside', 'interior': 'inside', 'outside': 'outside', 'oceanview':
        'outside', 'ocean view': 'outside', 'balcony': 'balcony', 'verandah': 'balcony',
        'mini suite': 'mini-suite', 'jr suite': 'mini-suite', 'mini-suite':
        'mini-suite', 'suite': 'suite', 'penthouse': 'suite', 'haven': 'haven'
    }

    @classmethod
    def normalize_category(cls, category_name):
        """Convert various category names to standardized format"""
        if not category_name:
            return None

        clean_name = re.sub(r'[^a-zA-Z0-9\s-]', '', category_name.lower())

        for key, value in cls.CATEGORY_MAPPING.items():
            if key in clean_name:
                return value

        return clean_name