from playwright.async_api import expect
import asyncio
from loguru import logger
import os
from Core.browser_setup import AsyncBrowserSetup
from Core.ui_logger import ui_log
import random
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get credentials from environment variables
my_username = os.getenv("CRUISING_POWER_USERNAME")
my_password = os.getenv("CRUISING_POWER_PASSWORD")
login_url = os.getenv("CRUISING_POWER_LOGIN_URL")

class CruisingPowerLogin:
    """
    Class for handling login functionality to Cruising Power website
    """
    def __init__(self, username, password, log_dir=None, headless=False, existing_page=None, video_auditing=False, session_id=None):
        """
        Initialize the Cruising Power login handler.
        
        Args:
            username: Username for login
            password: Password for login
            log_dir: Directory to save logs (optional)
            headless: Whether to run the browser in headless mode (defaults to False)
            existing_page: Existing Playwright Page instance (optional)
            video_auditing: Whether to enable video recording for audit purposes (defaults to False)
        """
        self.username = username
        self.password = password
        self.log_dir = log_dir
        self.headless = headless
        self.existing_page = existing_page
        self.video_auditing = video_auditing
        self.page = None
        self.context = None
        self.playwright = None
        self.browser = None
        # Session identifier for UI log scoping
        self.session_id = session_id
        
    async def login(self):
        """
        Perform login to Cruising Power website
        
        Returns:
            Tuple (page, success) with Playwright Page and boolean success indicator
        """
        # Use existing page if provided, otherwise create a new one
        if self.existing_page:
            logger.info("Using existing Playwright page")
            #ui_log("Using existing browser", module="Cruising_Power")
            self.page = self.existing_page
        else:
            # Use shared browser for Cruising Power
            logger.info("Using shared Cruising Power browser")
            ui_log("Allocating resources - just a moment", session_id=self.session_id, step="starting_browser", module="Cruising_Power")

            from Core.browser_setup import browser_manager, AsyncBrowserSetup
            
            # Get the shared browser instance for Cruising Power
            browser = await browser_manager.get_or_launch_browser('Cruising_Power')
            logger.info("Retrieved shared Cruising Power browser")
            
            # Create browser setup instance for context creation
            browser_setup = AsyncBrowserSetup()
            
            # Create an optimized context with custom viewport and user agent (manual configuration)
            context_options = {
                'viewport': {'width': 1920, 'height': 1080},
                'user_agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
                'device_scale_factor': 1.0,
                'locale': "en-US",
                'timezone_id': "America/New_York"
            }
            
            # Add video recording if enabled
            if self.video_auditing:
                from Core.browser_setup import ensure_video_directory
                from datetime import datetime
                video_dir = ensure_video_directory()
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                context_options['record_video_dir'] = video_dir
                context_options['record_video_size'] = {'width': 1920, 'height': 1080}
                logger.info(f"Video recording enabled: {video_dir}/video_{timestamp}.webm")
                #ui_log("Video auditing enabled - default settings maintained", module="Cruising_Power")
            
            self.context = await browser.new_context(**context_options)
            
            # Create a new page and configure it
            self.page = await self.context.new_page()
            
            # Set default timeout for page operations to 30 seconds
            self.page.set_default_timeout(30000)  
            self.playwright = browser_manager.get_playwright('Cruising_Power')
            self.browser_setup = browser_setup  # Store reference for video recording
            
            # Apply enhanced anti-detection for Cruising Power
            await self._apply_enhanced_stealth(self.page)
            
            # Allow images and essential resources - Cruising Power maintains default settings
            await browser_setup.setup_resource_blocking(self.page, logger, block=False)
            
            logger.info("Browser context setup complete with enhanced anti-detection") 
            ui_log("Resource allocation complete - taking input", session_id=self.session_id, step="browser_ready", module="Cruising_Power")
        
        try:
            # Navigate to the login page
            logger.info(f"Navigating to the login page: {login_url}")
            ui_log("Doing some security house keeping - one moment", session_id=self.session_id, step="loading_login_page", module="Cruising_Power")
            # Navigate to login page
            await self.page.goto(login_url)
            
            # Add a human-like delay
            await self._random_delay(1, 3)
            
            # Find and fill in the username field
            logger.info("Entering username...")
            ui_log("Entering Agency Credentials - just a moment", session_id=self.session_id, step="submitting_credentials", module="Cruising_Power")
            username_field = await self.page.wait_for_selector("input[data-qa='ushome.input.username']", timeout=15000)
            await self._human_like_typing(username_field, self.username)
            
            # Add human-like delay before typing password
            await self._random_delay(0.5, 1.5)
            
            # Find and fill in the password field
            logger.info("Entering password...")
            password_field = await self.page.wait_for_selector("input[data-qa='ushome.input.password']", timeout=5000)
            await self._human_like_typing(password_field, self.password)
            
            # Add delay before clicking sign-in
            await self._random_delay(0.5, 1.5)
            
            # Click the sign-in button
            logger.info("Clicking sign in button...")
            #ui_log("Submitting login", module="Cruising_Power")
            sign_in_button = await self.page.wait_for_selector("//*[@id='root']/div/div[1]/div/div/div/div[1]/form/div/button/span/span", timeout=5000)
            
            # Click and wait for navigation
            async with self.page.expect_navigation(timeout=30000):
                await sign_in_button.click()
                
            # Check if an error message is displayed
            try:
                error_message = await self.page.query_selector(".login-error-message")
                if error_message:
                    error_text = await error_message.text_content()
                    logger.warning(f"Login error message: {error_text}")
            except Exception as e:
                logger.debug(f"Error checking for login error message: {e}")
            
            # Check if login was successful
            if "login" not in self.page.url:
                logger.success("Login successful!")
                ui_log("I'm logged in successfully — let's move forward", session_id=self.session_id, step="logged_in", module="Cruising_Power")
                success = True
            else:
                logger.warning("Login may have failed. Please check credentials.")
                ui_log("Login failed — retrying now", session_id=self.session_id, step="retrying_login", module="Cruising_Power")
                success = False
            
            return self.page, success
            
        except Exception as e:
            logger.error(f"An error occurred during login: {e}")
            ui_log("There was an error during login — retrying now", session_id=self.session_id, step="login_error", module="Cruising_Power")
            return self.page, False
            
    async def _apply_enhanced_stealth(self, page):
        """Apply enhanced anti-detection measures"""
        try:
            # Override JavaScript properties that can reveal automation
            await page.add_init_script("""
            // Override WebDriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
                configurable: true
            });
            
            // Override Chrome property
            Object.defineProperty(window, 'chrome', {
                get: () => ({
                    runtime: {},
                    app: {},
                    // Add other chrome properties as needed
                }),
                configurable: true
            });
            
            // Override permissions
            Object.defineProperty(navigator, 'permissions', {
                get: () => ({
                    query: () => Promise.resolve({ state: 'granted' })
                }),
                configurable: true
            });
            
            // Override plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => {
                    return [1, 2, 3, 4, 5];
                },
                configurable: true
            });
            
            // Override languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
                configurable: true
            });
            """)
            
            logger.info("Applied enhanced stealth measures")
        except Exception as e:
            logger.error(f"Error applying enhanced stealth: {e}")

    async def _random_delay(self, min_seconds=0.5, max_seconds=2.0):
        """Add a random delay to simulate human behavior"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
        
    async def _human_like_typing(self, element, text, min_delay=0.05, max_delay=0.15):
        """Type text in a human-like manner with varying delays between keystrokes"""
        await element.click()
        for char in text:
            await element.type(char, delay=random.uniform(min_delay, max_delay) * 1000)  # Playwright expects milliseconds
            

# Function to maintain backward compatibility
async def login_to_cruising_power(username, password, log_dir=None, headless=False, existing_page=None, video_auditing=False, session_id=None):
    """
    Wrapper function to maintain backward compatibility
    
    Args:
        username: Username for login
        password: Password for login
        log_dir: Directory to save logs (optional)
        headless: Whether to run the browser in headless mode (defaults to False)
        existing_page: Existing Playwright Page instance (optional)
        video_auditing: Whether to enable video recording for audit purposes (defaults to False)
        
    Returns:
        Tuple (page, success) with Playwright Page and boolean success indicator
    """
    login_handler = CruisingPowerLogin(username, password, log_dir, headless, existing_page, video_auditing, session_id)
    return await login_handler.login()


if __name__ == "__main__":
    # Perform login
    login_handler = CruisingPowerLogin(my_username, my_password)
    page, success = login_handler.login()
    
    if not success:
        logger.error("Login failed in standalone mode")
    
    # Close the browser
    if hasattr(login_handler, 'playwright') and login_handler.playwright:
        if login_handler.page:
            login_handler.page.close()
        if login_handler.context:
            login_handler.context.close()
        if login_handler.browser:
            login_handler.browser.close()
        if login_handler.playwright:
            login_handler.playwright.stop()