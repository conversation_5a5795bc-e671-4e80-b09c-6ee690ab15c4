{"version": 3, "sources": ["../../../src/shared/lib/match-remote-pattern.ts"], "names": ["makeRe", "matchRemotePattern", "pattern", "url", "protocol", "undefined", "actualProto", "slice", "port", "hostname", "Error", "JSON", "stringify", "test", "pathname", "hasMatch", "domains", "remotePatterns", "some", "domain", "p"], "mappings": "AACA,SAASA,MAAM,QAAQ,gCAA+B;AAEtD,OAAO,SAASC,mBAAmBC,OAAsB,EAAEC,GAAQ;IACjE,IAAID,QAAQE,QAAQ,KAAKC,WAAW;QAClC,MAAMC,cAAcH,IAAIC,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC;QAC3C,IAAIL,QAAQE,QAAQ,KAAKE,aAAa;YACpC,OAAO;QACT;IACF;IACA,IAAIJ,QAAQM,IAAI,KAAKH,WAAW;QAC9B,IAAIH,QAAQM,IAAI,KAAKL,IAAIK,IAAI,EAAE;YAC7B,OAAO;QACT;IACF;IAEA,IAAIN,QAAQO,QAAQ,KAAKJ,WAAW;QAClC,MAAM,IAAIK,MACR,AAAC,+CAA4CC,KAAKC,SAAS,CAACV;IAEhE,OAAO;QACL,IAAI,CAACF,OAAOE,QAAQO,QAAQ,EAAEI,IAAI,CAACV,IAAIM,QAAQ,GAAG;YAChD,OAAO;QACT;IACF;QAEYP;IAAZ,IAAI,CAACF,OAAOE,CAAAA,oBAAAA,QAAQY,QAAQ,YAAhBZ,oBAAoB,MAAMW,IAAI,CAACV,IAAIW,QAAQ,GAAG;QACxD,OAAO;IACT;IAEA,OAAO;AACT;AAEA,OAAO,SAASC,SACdC,OAAiB,EACjBC,cAA+B,EAC/Bd,GAAQ;IAER,OACEa,QAAQE,IAAI,CAAC,CAACC,SAAWhB,IAAIM,QAAQ,KAAKU,WAC1CF,eAAeC,IAAI,CAAC,CAACE,IAAMnB,mBAAmBmB,GAAGjB;AAErD"}