from playwright.async_api import Page
import io
import asyncio
from datetime import datetime
from loguru import logger
import os
import sys
from Core.ui_logger import ui_log

# Add parent directory to path for Core imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

class ScreenshotManager:
    """
    Class for handling screenshot functionality
    """
    def __init__(self, page, request_id=None, cabin_id=None, session_id=None):
        """
        Initialize the screenshot manager
        
        Args:
            page: Playwright Page instance
            request_id: Request ID for database storage
            cabin_id: Cabin ID for database storage
            session_id: Session ID for consistent database tracking
        """
        self.page = page
        self.request_id = request_id
        self.cabin_id = cabin_id
        self.session_id = session_id
        
    async def save_to_database(self, screenshot_data, filename, screenshot_type="general"):
        """
        Save a screenshot to the database
        
        Args:
            screenshot_data: Binary image data
            filename: Name to give the screenshot in the database
            screenshot_type: Type of screenshot
        
        Returns:
            bool: Whether the save was successful
        """
        try:
            # Determine screenshot type from the filename if not specified
            if screenshot_type == "general":
                if "search" in filename.lower():
                    screenshot_type = "Search"
                elif "category" in filename.lower():
                    screenshot_type = "Category Selection"
                elif "cabin" in filename.lower():
                    screenshot_type = "Cabin Selection"
                elif "price" in filename.lower() or "quote" in filename.lower():
                    screenshot_type = "Pricing Details"
                elif "login" in filename.lower():
                    screenshot_type = "Login"
            
            # Log the screenshot activity to UI
            #ui_log(f"Taking {screenshot_type} screenshot", 
            #      session_id=self.session_id, 
            #      cabin_id=self.cabin_id, 
            #      module="Cruising_Power")
            
            # Import utils module only when needed to avoid circular imports
            from db.screenshot_manager import save_screenshot_to_db
            
            # Save to database
            result = await save_screenshot_to_db(
                screenshot_data, 
                self.request_id, 
                "Cruising Power", 
                screenshot_type,  
                filename,
                self.cabin_id,
                self.session_id
            )
            
            if result:
                ui_log(f"Screenshot saved: {screenshot_type}", 
                      session_id=self.session_id, 
                      cabin_id=self.cabin_id, 
                      module="Cruising_Power")
            
            return result
        except Exception as e:
            logger.error(f"Error saving screenshot to database: {e}")
            #ui_log(f"Error saving screenshot: {str(e)}", 
            #      session_id=self.session_id, 
            #      cabin_id=self.cabin_id, 
            #      module="Cruising_Power")
            return False
            
    async def take_scrolling_screenshot(self, filename):
        """
        Takes a full page screenshot using Playwright's built-in functionality
        
        Args:
            filename: Name of the screenshot file
        
        Returns:
            str: Filename of the generated screenshot
        """
        try:
            logger.info(f"Taking full page screenshot: {filename}...")
            #ui_log(f"Taking full page screenshot", 
            #      session_id=self.session_id, 
            #      cabin_id=self.cabin_id, 
            #      module="Cruising_Power")
            
            # Playwright has built-in full page screenshots
            screenshot_data = await self.page.screenshot(full_page=True)
            
            # Create a filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            full_filename = f"{filename}_full_page_{timestamp}.png"
            
            # Save to database if request_id is provided
            if self.request_id:
                await self.save_to_database(screenshot_data, full_filename)
                logger.info(f"Full page screenshot saved to database: {full_filename}")
            
            return full_filename
        except Exception as e:
            logger.error(f"Error taking scrolling screenshot: {e}")
            #ui_log(f"Error taking full page screenshot: {str(e)}", 
            #      session_id=self.session_id, 
            #      cabin_id=self.cabin_id, 
            #      module="Cruising_Power")
            # Fallback to regular screenshot if full page fails
            try:
                # Take a simple screenshot
                screenshot_data = await self.page.screenshot()
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                fallback_filename = f"{filename}_{timestamp}.png"
                
                # Save fallback to database if request_id is provided
                if self.request_id:
                    await self.save_to_database(screenshot_data, fallback_filename)
                    logger.info(f"Fallback screenshot saved to database: {fallback_filename}")
                    
                return fallback_filename
            except Exception as inner_e:
                logger.error(f"Error taking fallback screenshot: {inner_e}")
                return None
                
    async def take_modal_screenshot(self, filename):
        """
        Takes a screenshot of a modal dialog using Playwright's element screenshot
        
        Args:
            filename: Name of the screenshot file
        
        Returns:
            str: Filename of the generated screenshot
        """
        try:
            # Find the modal
            modal = await self.page.wait_for_selector("#priceQuoteModal", timeout=10000)
            
            # Take a screenshot of just the modal element
            screenshot_data = await modal.screenshot()
            
            # Create a filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            full_filename = f"{filename}_modal_{timestamp}.png"
            
            # Save to database if request_id is provided
            if self.request_id:
                await self.save_to_database(screenshot_data, full_filename, "Pricing Details")
                logger.info(f"Modal screenshot saved to database: {full_filename}")
            
            return full_filename
        except Exception as e:
            logger.error(f"Error taking modal screenshot: {e}")
            return None

    async def take_visible_screenshot(self, filename):
        """
        Takes a screenshot of the currently visible viewport
        
        Args:
            filename: Name of the screenshot file
        
        Returns:
            str: Filename of the generated screenshot
        """
        try:
            logger.info(f"Taking visible screenshot: {filename}...")
            
            # Take a screenshot of just the visible portion
            screenshot_data = await self.page.screenshot(full_page=False)
            
            # Create a filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            full_filename = f"{filename}_visible_{timestamp}.png"
            
            # Save to database if request_id is provided
            if self.request_id:
                await self.save_to_database(screenshot_data, full_filename)
                logger.info(f"Visible screenshot saved to database: {full_filename}")
            
            return full_filename
        except Exception as e:
            logger.error(f"Error taking visible screenshot: {e}")
            return None


# Convenience functions for backward compatibility 

async def save_to_database(screenshot_data, filename, request_id=None, cabin_id=None, screenshot_type="general", session_id=None):
    """
    Convenience function to save a screenshot to the database without needing a manager instance
    
    Args:
        screenshot_data: Binary image data
        filename: Name to give the screenshot in the database
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        screenshot_type: Type of screenshot
        session_id: Session ID for consistent database tracking
    
    Returns:
        bool: Whether the save was successful
    """
    manager = ScreenshotManager(None, request_id, cabin_id, session_id)
    return await manager.save_to_database(screenshot_data, filename, screenshot_type)

async def take_scrolling_screenshot(page, filename, request_id=None, cabin_id=None, session_id=None):
    """
    Convenience function to take a scrolling screenshot without needing a manager instance
    
    Args:
        page: Playwright Page instance
        filename: Name of the screenshot file
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
    
    Returns:
        str: Filename of the generated screenshot
    """
    manager = ScreenshotManager(page, request_id, cabin_id, session_id)
    return await manager.take_scrolling_screenshot(filename)

async def take_modal_screenshot(page, filename, request_id=None, cabin_id=None, session_id=None):
    """
    Convenience function to take a modal screenshot without needing a manager instance
    
    Args:
        page: Playwright Page instance
        filename: Name of the screenshot file
        request_id: Request ID for database storage 
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
    
    Returns:
        str: Filename of the generated screenshot
    """
    manager = ScreenshotManager(page, request_id, cabin_id, session_id)
    return await manager.take_modal_screenshot(filename)

async def take_visible_screenshot(page, filename, request_id=None, cabin_id=None, session_id=None):
    """
    Convenience function to take a visible screenshot without needing a manager instance
    
    Args:
        page: Playwright Page instance
        filename: Name of the screenshot file
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
    
    Returns:
        str: Filename of the generated screenshot
    """
    manager = ScreenshotManager(page, request_id, cabin_id, session_id)
    return await manager.take_visible_screenshot(filename)

