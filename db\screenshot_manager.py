"""
Handles storage and retrieval of screenshots across all providers using MinIO.
"""
import logging
import traceback
from datetime import datetime
from db_service import get_db_connection, db_connection
from .session_manager import SessionManager
from services.media_service import media_service

# Configure logging
logger = logging.getLogger("database.screenshot_manager")

async def save_screenshot_to_db(image_data: bytes, request_id: str, provider: str, 
                          screenshot_type: str = "general", file_name: str = None, 
                          cabin_id: int = None, session_id: str = None):
    """
    Save a screenshot to MinIO and store metadata in the database
    
    Args:
        image_data: Binary image data
        request_id: The booking request ID
        provider: The provider (Studio, NCL, or Cruising Power)
        screenshot_type: Type of screenshot (e.g., category_selection, rate_selection)
        file_name: Original file name of the screenshot
        cabin_id: ID of the cabin this screenshot is related to (if applicable)
        session_id: Session ID from the current processing run
    """
    try:
        # Get the global session manager
        session_manager = SessionManager.get_instance()
        
        conn = None  # Initialize to None to avoid potential reference before assignment
        
        # If no session_id was provided, try to get it from the session manager
        if not session_id:
            session_id = session_manager.get_current_session()
        
            # If still no session_id, try to get one for this request
            if not session_id:
                # First try to get session_id from session_tracking table
                async with db_connection() as conn:
                
                    session_result = await conn.fetchrow("""
                        SELECT session_id FROM session_tracking 
                        WHERE request_id = $1 AND provider = $2
                        ORDER BY updated_at DESC LIMIT 1
                    """, request_id, provider.lower())

                    if session_result and session_result['session_id']:
                        session_id = session_result['session_id']
                        logger.info(f"Using most recent session ID from session_tracking: {session_id}")
                    else:
                        # Fall back to booking table
                        booking_table = f"{provider.lower().replace(' ', '_')}_bookings"
                        result = await conn.fetchrow(f"""
                            SELECT session_id FROM {booking_table} 
                            WHERE request_id = $1
                            ORDER BY timestamp DESC LIMIT 1
                        """, request_id)

                        if result and result['session_id']:
                            session_id = result['session_id']
                            logger.info(f"Using most recent session ID from booking table: {session_id}")
                        else:
                            # Last resort: Create a new session ID
                            from .utils import register_session
                            session_id = await register_session(request_id, provider)
                            logger.info(f"Created new session ID for screenshot: {session_id}")


                    # Set this session as the current one in the session manager
                    session_manager.set_current_session(session_id)
        
        # Upload to MinIO
        upload_success, upload_result = await media_service.upload_screenshot(
            image_data, provider, session_id, screenshot_type, cabin_id, file_name
        )
        
        if not upload_success:
            logger.error(f"Failed to upload screenshot to MinIO: {upload_result.get('error', 'Unknown error')}")
            # Fall back to database storage for critical screenshots
            return await _save_screenshot_to_db_fallback(image_data, request_id, provider, screenshot_type, 
                                                 file_name, cabin_id, session_id)
        
        # Extract MinIO metadata
        minio_url = upload_result.get('url')
        minio_bucket = upload_result.get('bucket')
        minio_object_key = upload_result.get('object_key')
        file_size_kb = upload_result.get('file_size_kb', 0)
        
        # Now proceed with the database metadata storage
        async with db_connection() as conn:
        
            # Store screenshots keyed by session_id; booking_id is no longer used
            booking_table = f"{provider.lower().replace(' ', '_')}_bookings"
            booking_id = None
            booking_table_id = None

            # Generate a filename if not provided
            if not file_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"{provider}_{request_id}_{screenshot_type}_{timestamp}.png"

            # Insert the screenshot metadata into the centralized table
            await conn.execute("""
            INSERT INTO centralized_screenshots
            (booking_id, request_id, session_id, provider, cabin_id, screenshot_type, timestamp, 
             minio_url, minio_bucket, minio_object_key, file_name, file_size_kb, booking_table, booking_table_id)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            """,
                booking_id,
                request_id,
                session_id,
                provider.lower(),
                cabin_id,
                screenshot_type,
                datetime.now().isoformat(),
                minio_url,
                minio_bucket,
                minio_object_key,
                file_name,
                file_size_kb,
                booking_table,
                booking_table_id
            )

            logger.info(f"Saved screenshot metadata to database and MinIO with session_id: {session_id} for request_id: {request_id}, provider: {provider}")
            return True
    except Exception as e:
        logger.error(f"Error saving screenshot: {e}")
        logger.error(traceback.format_exc())

        return False

async def _save_screenshot_to_db_fallback(image_data: bytes, request_id: str, provider: str, 
                                  screenshot_type: str, file_name: str, cabin_id: int, session_id: str):
    """Fallback method to save screenshot directly to database if MinIO fails"""
    try:
        async with db_connection() as conn:
        
            booking_table = f"{provider.lower().replace(' ', '_')}_bookings"
        
            if not file_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"{provider}_{request_id}_{screenshot_type}_{timestamp}.png"

            # Store in database with legacy image_data column
            await conn.execute("""
            INSERT INTO centralized_screenshots
            (booking_id, request_id, session_id, provider, cabin_id, screenshot_type, timestamp, 
             image_data, file_name, file_size_kb, booking_table, booking_table_id)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            """,
                None,
                request_id,
                session_id,
                provider.lower(),
                cabin_id,
                screenshot_type,
                datetime.now().isoformat(),
                image_data,
                file_name,
                len(image_data) // 1024,
                booking_table,
                None
            )

            logger.warning(f"Saved screenshot to database fallback storage for session_id: {session_id}")
            return True
    except Exception as e:
        logger.error(f"Error in screenshot fallback storage: {e}")

        return False

async def get_screenshots_from_db(request_id: str, provider: str = None, session_id: str = None):
    """
    Retrieve screenshots from the database for a given request ID
    
    Args:
        request_id: The booking request ID
        provider: Optional provider filter
        session_id: Optional session ID filter
    
    Returns:
        List of screenshot dictionaries with metadata
    """
    try:
        async with db_connection() as conn:
        
            # Build query with optional filters
            query = """
            SELECT id, booking_id, request_id, session_id, provider, cabin_id, 
                   screenshot_type, timestamp, file_name, file_size_kb,
                   minio_url, minio_bucket, minio_object_key,
                   CASE 
                     WHEN minio_url IS NOT NULL THEN 'minio'
                     WHEN image_data IS NOT NULL THEN 'database'
                     ELSE 'missing'
                   END as storage_type
            FROM centralized_screenshots 
            WHERE request_id = $1
            """
            params = [request_id]
            param_idx = 2
        
            if provider:
                query += f" AND provider = ${param_idx}"
                params.append(provider)
                param_idx += 1

            if session_id:
                query += f" AND session_id = ${param_idx}"
                params.append(session_id)

            query += " ORDER BY timestamp DESC"

            screenshots = await conn.fetch(query, *params)

            # Convert records to regular dictionaries
            result = []
            minio_count = 0
            db_count = 0
            missing_count = 0

            for screenshot in screenshots:
                screenshot_dict = dict(screenshot)

                # Count storage types for logging
                storage_type = screenshot_dict.get('storage_type', 'missing')
                if storage_type == 'minio':
                    minio_count += 1
                elif storage_type == 'database':
                    db_count += 1
                else:
                    missing_count += 1

                # Remove binary data to save memory in list operations
                screenshot_dict.pop('image_data', None)
                result.append(screenshot_dict)

            # Log storage distribution
            total_screenshots = len(result)
            if total_screenshots > 0:
                logger.info(f"Retrieved {total_screenshots} screenshots for request_id {request_id}: "
                           f"{minio_count} from MinIO, {db_count} from PostgreSQL, {missing_count} missing data")

            return result

    except Exception as e:
        logger.error(f"Error retrieving screenshots from database: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")

        return []

async def get_db_screenshot_categories(request_id: str, provider: str, session_id: str = None):
    """
    Get the available screenshot categories from the centralized table
    
    Args:
        request_id: The booking request ID
        provider: The provider (Studio, NCL, or Cruising Power)
        session_id: Session ID to ensure consistent categorization
    
    Returns:
        List of distinct screenshot categories
    """
    try:
        async with db_connection() as conn:
        
            # If no session_id provided, get it from the most recent booking
            if not session_id:
                booking_table = f"{provider.lower().replace(' ', '_')}_bookings"
                booking_result = await conn.fetchrow(f"""
                    SELECT session_id FROM {booking_table} 
                    WHERE request_id = $1 
                    ORDER BY timestamp DESC LIMIT 1
                """, request_id)

                if booking_result and booking_result['session_id']:
                    session_id = booking_result['session_id']
                    logger.info(f"Using most recent session ID from booking table for categories: {session_id}")
                else:
                    logger.warning(f"No session ID found in booking table for categories of request_id: {request_id}")
                    return []

            # Get distinct screenshot types from centralized table
            categories = await conn.fetch("""
                SELECT DISTINCT screenshot_type 
                FROM centralized_screenshots
                WHERE request_id = $1 AND provider = $2 AND session_id = $3
            """, request_id, provider.lower(), session_id)

            # FALLBACK: If no categories found with the specific session_id, try without session_id filter
            if not categories:
                logger.info(f"No screenshot categories found for session_id '{session_id}', trying fallback with just request_id")
                categories = await conn.fetch("""
                    SELECT DISTINCT screenshot_type 
                    FROM centralized_screenshots
                    WHERE request_id = $1 AND provider = $2
                """, request_id, provider.lower())

                if categories:
                    logger.info(f"Found {len(categories)} screenshot categories using fallback (ignoring session_id)")

            # Convert to list
            result = [category['screenshot_type'] for category in categories]

            return result
    except Exception as e:
        logger.error(f"Error retrieving screenshot categories from database: {e}")
        logger.error(traceback.format_exc())

        return []

async def link_screenshots_to_booking(request_id: str, provider: str, session_id: str = None):
    """
    Links screenshots to a booking with the current session ID
    
    Args:
        request_id: The booking request ID
        provider: The provider (Studio, NCL, or Cruising Power)
        session_id: Current session ID for this processing run
    
    Returns:
        Number of screenshots linked
    """
    from .utils import execute_with_retry
    
    if not session_id:
        logger.warning(f"No session_id provided for linking {provider} screenshots with request_id: {request_id}")
        return 0
    
    async def do_screenshot_linking():
        linked_count = 0
        conn = None
        try:
            # Create a new connection
            async with db_connection() as conn:
            
            # Use a transaction to make this atomic
                async with conn.transaction():
                    # Step 1: Find the booking ID for this request_id and session_id
                    # Normalize provider name for table lookup
                    # Special handling for Cruising Power - table name is always cruising_power_bookings
                    # regardless of the provider name format in screenshots
                    if provider.lower().replace(' ', '_') == 'cruising_power' or provider.lower() == 'cruising power':
                        booking_table = "cruising_power_bookings"
                        normalized_search_provider = "cruising power"  # Use this for screenshots search
                    else:
                        normalized_provider = provider.lower().replace(' ', '_')
                        booking_table = f"{normalized_provider}_bookings"
                        normalized_search_provider = normalized_provider

                    logger.info(f"Linking screenshots for provider: {provider}, table: {booking_table}, session_id: {session_id}, request_id: {request_id}")

                    # First try with exact session_id match
                    booking_result = await conn.fetchrow(f"""
                        SELECT id FROM {booking_table}
                        WHERE (session_id = $1 OR session_id LIKE $2) 
                        AND request_id = $3
                        ORDER BY id DESC
                        LIMIT 1
                    """, session_id, f"%{request_id}%", request_id)

                    if not booking_result:
                        # If no booking found with this session_id, look for any booking with this request_id
                        booking_result = await conn.fetchrow(f"""
                            SELECT id FROM {booking_table}
                            WHERE request_id = $1
                            ORDER BY id DESC
                            LIMIT 1
                        """, request_id)

                    if not booking_result:
                        logger.info(f"No booking found yet for request_id: {request_id}, session_id: {session_id}. Screenshots will be linked later.")
                        return 0

                    booking_id = booking_result['id']

                    # Step 2: Look for unlinked screenshots with this session or request ID
                    # If this is Cruising Power, check both variants of the provider name
                    if provider.lower().replace(' ', '_') == 'cruising_power' or provider.lower() == 'cruising power':
                        # Check both 'cruising power' and 'cruising_power' variants
                        logger.info("Using special handling for Cruising Power - checking both provider name formats")
                        screenshot_ids = await conn.fetch("""
                            SELECT id FROM centralized_screenshots
                            WHERE (provider = $1 OR provider = $2) AND 
                                  (session_id = $3 OR request_id = $4) AND
                                  (booking_id IS NULL OR booking_table_id IS NULL OR booking_table_id = 0)
                        """, 'cruising power', 'cruising_power', session_id, request_id)
                    else:
                        # For other providers, use standard logic
                        screenshot_ids = await conn.fetch("""
                            SELECT id FROM centralized_screenshots
                            WHERE provider = $1 AND 
                                  (session_id = $2 OR request_id = $3) AND
                                  (booking_id IS NULL OR booking_table_id IS NULL OR booking_table_id = 0)
                        """, normalized_search_provider, session_id, request_id)

                    # Step 3: Update any unlinked screenshots
                    for screenshot_id_row in screenshot_ids:
                        screenshot_id = screenshot_id_row['id']
                        await conn.execute("""
                            UPDATE centralized_screenshots
                            SET booking_id = $1, booking_table_id = $2
                            WHERE id = $3
                        """, booking_id, booking_id, screenshot_id)
                        linked_count += 1

                    if linked_count:
                        logger.info(f"Successfully linked {linked_count} screenshots to booking ID: {booking_id}")

                return linked_count

        except Exception as e:
            logger.error(f"Error linking screenshots: {e}")
            logger.error(traceback.format_exc())

            return 0
    
    return await execute_with_retry(do_screenshot_linking) 