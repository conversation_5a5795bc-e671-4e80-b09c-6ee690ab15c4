import React, { useState } from 'react';
import VideoPlayer from './VideoPlayer';
import { getCabinVideos, getSessionVideos } from '../../services/video';

interface VideoMetadata {
  id: number;
  cabin_id?: string | number;
  video_type?: string;
  timestamp?: string;
  file_name?: string;
  duration?: number;
  format?: string;
  size_kb?: number;
}

interface VideoButtonProps {
  requestId: string;
  sessionId?: string;
  provider: string;
  cabinId?: number | string;
  buttonText?: string;
  buttonClassName?: string;
  iconOnly?: boolean;
}

const VideoButton: React.FC<VideoButtonProps> = ({
  requestId, 
  sessionId, 
  provider, 
  cabinId,
  buttonText = 'View Video',
  buttonClassName = 'text-blue-500 hover:text-blue-700 transition-colors',
  iconOnly = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [videoModalOpen, setVideoModalOpen] = useState(false);
  const [currentVideoId, setCurrentVideoId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleVideoPlayback = async () => {
    if (!sessionId) {
      setError('Session ID is required to fetch videos');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Normalize provider for API filtering
      let providerParam = '';
      if (provider.toLowerCase().includes('cruising')) {
        providerParam = 'cruising_power';
      } else if (provider.toLowerCase().includes('ncl')) {
        providerParam = 'ncl';
      } else if (provider.toLowerCase().includes('studio')) {
        providerParam = 'studio';
      }
      
      console.log(`Attempting to fetch video for provider ${providerParam}, session ${sessionId}`);
      
      // If cabin ID is provided, try to get cabin-specific videos first
      let videoData: { success: boolean; videos: VideoMetadata[] } = { success: false, videos: [] };
      
      if (cabinId) {
        videoData = await getCabinVideos(cabinId, providerParam, sessionId);
        console.log('Cabin API response:', videoData);
      }
      
      // If no videos found by cabin, try getting all session videos
      if (!videoData.success || !videoData.videos || videoData.videos.length === 0) {
        console.log(`No videos found for cabin, trying session videos`);
        videoData = await getSessionVideos(sessionId);
        console.log('Session API response:', videoData);
      }
      
      if (!videoData.success || !videoData.videos || videoData.videos.length === 0) {
        setError('No videos found for this booking session');
        setIsLoading(false);
        return;
      }
      
      // Use the most recent video
      const video = videoData.videos[0]; // Videos are ordered by timestamp DESC
      console.log('Selected video:', video);
      
      // Set the video ID and open the modal
      setCurrentVideoId(video.id);
      setVideoModalOpen(true);
    } catch (error) {
      console.error('Error fetching video metadata:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <button 
        onClick={handleVideoPlayback}
        className={buttonClassName}
        title="View Video Recording"
        disabled={isLoading}
      >
        {isLoading ? (
          <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {!iconOnly && <span className="ml-2">{buttonText}</span>}
          </>
        )}
      </button>

      {error && (
        <div className="text-red-500 text-sm mt-1">
          {error}
        </div>
      )}

      {videoModalOpen && currentVideoId && (
        <VideoPlayer 
          videoId={currentVideoId}
          onClose={() => {
            setVideoModalOpen(false);
            setCurrentVideoId(null);
          }}
        />
      )}
    </>
  );
};

export default VideoButton;