'use client';

import React, { useState, useEffect } from 'react';

// NavLink component for sidebar navigation
type NavLinkProps = {
  href: string;
  active: boolean;
  children: React.ReactNode;
};

const NavLink = ({ href, active, children }: NavLinkProps) => {
  return (
    <a
      href={href}
      className={`block px-4 py-2 text-lg transition-all duration-300 rounded-bl-xl rounded-tr-lg mb-3 ${
        active ? 'font-bold' : 'font-semibold'
      }`}
      style={{
        borderWidth: '0px',
        borderStyle: 'solid',
        borderColor: '#0c6066',
        color: '#036868',
        boxShadow: '0 3px 6px rgba(0, 0, 0, 0.3)',
        background: active
          ? 'linear-gradient(0deg, #dcdcdc 0%, #89aeae 98%)'
          : 'white',
        transition: 'background 0.3s ease'
      }}
      onMouseOver={(e) => {
        if (!active) {
          e.currentTarget.style.background = 'linear-gradient(0deg, #dcdcdc 0%, #89aeae 98%)';
        }
      }}
      onMouseOut={(e) => {
        if (!active) {
          e.currentTarget.style.background = 'white';
        }
      }}
    >
      {children}
    </a>
  );
};

export default function GuideContent() {
  console.log('GuideContent component rendered');
  const [activeSection, setActiveSection] = useState('getting-started');

  // CSS animations removed

  // Handle scroll event to track active section
  useEffect(() => {
    const handleScroll = () => {
      // Update active section based on scroll position
      const sections = document.querySelectorAll('section[id]');
      let currentSectionId = 'getting-started';

      sections.forEach(section => {
        const sectionTop = section.getBoundingClientRect().top;
        if (sectionTop < 100) {
          currentSectionId = section.id;
        }
      });

      setActiveSection(currentSectionId);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Scroll to top on page load/refresh
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo({ top: 0, behavior: 'auto' });
  }, []);

  return (
    <div className="min-h-screen relative font-['Times_New_Roman']">
      {/* Circular gradient background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0" style={{ backgroundColor: '#c0ffee', backgroundImage: 'linear-gradient(45deg, #c0ffee 0%, #d4d4d4 100%)' }}></div>
      </div>

      {/* Content with main sections */}
      <div className="relative z-10 min-h-screen flex flex-col">
        <main className="flex-grow container mx-auto px-6 py-8 pb-6 flex">
          {/* Sidebar Navigation */}
          <div className="hidden lg:block w-80 flex-shrink-0 pr-10">
            <div className="fixed w-72 space-y-5 rounded-lg top-[100px] p-6 ">
              <h3 className="text-3xl font-bold mb-6" style={{ color: '#036868' }}>Guide Contents</h3>
              <NavLink href="#getting-started" active={activeSection === 'getting-started'}>
                Getting Started
              </NavLink>
              <NavLink href="#system-requirements" active={activeSection === 'system-requirements'}>
                System Requirements
              </NavLink>
              <NavLink href="#basic-usage" active={activeSection === 'basic-usage'}>
                Basic Usage
              </NavLink>
              <NavLink href="#cruise-providers" active={activeSection === 'cruise-providers'}>
                Cruise Providers
              </NavLink>
              <NavLink href="#configuration" active={activeSection === 'configuration'}>
                Configuration
              </NavLink>
              <NavLink href="#troubleshooting" active={activeSection === 'troubleshooting'}>
                Troubleshooting
              </NavLink>
              <NavLink href="#faqs" active={activeSection === 'faqs'}>
                FAQs
              </NavLink>
              <NavLink href="#support" active={activeSection === 'support'}>
                Support
              </NavLink>
            </div>
          </div>

          <div className="flex-grow pl-4">
            {/* Hero Banner */}
            <div className="relative  py-10 px-8 rounded-xl overflow-hidden">
              {/* Background with gradient overlay */}
              <div className="absolute inset-0 bg-transparent backdrop-blur-sm"></div>

              <div className="relative z-10 max-w-5xl  text-black">
                <h1 className="text-6xl text-shadow-xl font-bold mb-9 font-['Times_New_Roman']" style={{ color: '#013e5f' }}>Getting Started with Oceanmind</h1>
                <p className="text-xl font-['Times_New_Roman']" style={{ color: '#0F6095' }}>
                  Follow this step-by-step guide to automate your cruise booking process and save valuable time.
                  Oceanmind makes it simple to process quotes with just a few clicks.
                </p>
              </div>
            </div>

            {/* Key Benefits Bar */}
            <div className="bg-white/30 backdrop-blur-sm rounded-xl p-6 mb-12 shadow-lg">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center space-x-4">
                  <div className="bg-blue-600/20 rounded-full p-3">
                    <svg className="w-8 h-8 text-blue-700" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-blue-900">55% Time Savings</h3>
                    <p className="text-lg text-blue-800">Significantly reduce booking time</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="bg-purple-600/20 rounded-full p-3">
                    <svg className="w-8 h-8 text-purple-700" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M18,15H16V17H18M18,11H16V13H18M20,19H12V17H14V15H12V13H14V11H12V9H20M10,7H8V5H10M10,11H8V9H10M10,15H8V13H10M10,19H8V17H10M6,7H4V5H6M6,11H4V9H6M6,15H4V13H6M6,19H4V17H6M12,7V3H2V21H22V7H12Z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-purple-900">100+ Cruise Lines</h3>
                    <p className="text-lg text-purple-800">Comprehensive provider support</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="bg-green-600/20 rounded-full p-3">
                    <svg className="w-8 h-8 text-green-700" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,17V16H9V14H13V13H10A1,1 0 0,1 9,12V9A1,1 0 0,1 10,8H11V7H13V8H15V10H11V11H14A1,1 0 0,1 15,12V15A1,1 0 0,1 14,16H13V17H11Z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-green-900">Cost Effective</h3>
                    <p className="text-lg text-green-800">Maximize earnings per booking</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Booking Process Grid/Roadmap */}
            <div className="mt-28 mb-12">
              <h2 className="text-5xl text-shadow-md font-bold text-sky-800 mb-12 text-center">Booking Process Roadmap</h2>

              {/* Process Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Column 1: Start and Input */}
                <div className="space-y-6">
                  {/* Step 1 */}
                  <div className="bg-white/40 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg">
                    <div className="bg-gradient-to-r from-sky-600 to-blue-900 py-2 px-4">
                      <div className="flex items-center">
                        <div className="bg-white rounded-full w-6 h-6 flex items-center justify-center mr-3">
                          <span className="text-blue-700 font-bold text-xl">1</span>
                        </div>
                        <h3 className="text-2xl font-bold text-white">Select Provider</h3>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="grid grid-cols-3 gap-2 mb-4">
                        <div className="bg-blue-50/70 p-2 rounded-lg border border-blue-100/70 flex flex-col items-center">
                          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mb-1">
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="#1e40af">
                              <path d="M20,21C18.61,21 17.22,20.53 16,19.67C13.56,21.38 10.44,21.38 8,19.67C6.78,20.53 5.39,21 4,21H2V19H4C5.37,19 6.74,18.47 8,17.65C10.44,19.36 13.56,19.36 16,17.65C17.26,18.47 18.62,19 20,19H22V21H20M20,17C18.61,17 17.22,16.53 16,15.67C13.56,17.38 10.44,17.38 8,15.67C6.78,16.53 5.39,17 4,17H2V15H4C5.37,15 6.74,14.46 8,13.64C10.44,15.35 13.56,15.35 16,13.64C17.26,14.46 18.62,15 20,15H22V17H20M22,13V11H17.82C17.93,10.69 18,10.35 18,10C18,8.34 16.66,7 15,7C14,7 13.12,7.5 12.57,8.27C11.93,7.5 11,7 10,7C8.34,7 7,8.34 7,10C7,10.35 7.07,10.69 7.18,11H3V13H22Z" />
                            </svg>
                          </div>
                          <p className="text-sm font-medium text-center text-blue-700">Royal</p>
                        </div>

                        <div className="bg-blue-50/70 p-2 rounded-lg border border-blue-100/70 flex flex-col items-center">
                          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mb-1">
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="#1e40af">
                              <path d="M20,21C18.61,21 17.22,20.53 16,19.67C13.56,21.38 10.44,21.38 8,19.67C6.78,20.53 5.39,21 4,21H2V19H4C5.37,19 6.74,18.47 8,17.65C10.44,19.36 13.56,19.36 16,17.65C17.26,18.47 18.62,19 20,19H22V21H20M20,17C18.61,17 17.22,16.53 16,15.67C13.56,17.38 10.44,17.38 8,15.67C6.78,16.53 5.39,17 4,17H2V15H4C5.37,15 6.74,14.46 8,13.64C10.44,15.35 13.56,15.35 16,13.64C17.26,14.46 18.62,15 20,15H22V17H20M22,13V11H17.82C17.93,10.69 18,10.35 18,10C18,8.34 16.66,7 15,7C14,7 13.12,7.5 12.57,8.27C11.93,7.5 11,7 10,7C8.34,7 7,8.34 7,10C7,10.35 7.07,10.69 7.18,11H3V13H22Z" />
                            </svg>
                          </div>
                          <p className="text-sm font-medium text-center text-blue-700">NCL</p>
                        </div>

                        <div className="bg-blue-50/70 p-2 rounded-lg border border-blue-100/70 flex flex-col items-center">
                          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mb-1">
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="#1e40af">
                              <path d="M20,21C18.61,21 17.22,20.53 16,19.67C13.56,21.38 10.44,21.38 8,19.67C6.78,20.53 5.39,21 4,21H2V19H4C5.37,19 6.74,18.47 8,17.65C10.44,19.36 13.56,19.36 16,17.65C17.26,18.47 18.62,19 20,19H22V21H20M20,17C18.61,17 17.22,16.53 16,15.67C13.56,17.38 10.44,17.38 8,15.67C6.78,16.53 5.39,17 4,17H2V15H4C5.37,15 6.74,14.46 8,13.64C10.44,15.35 13.56,15.35 16,13.64C17.26,14.46 18.62,15 20,15H22V17H20M22,13V11H17.82C17.93,10.69 18,10.35 18,10C18,8.34 16.66,7 15,7C14,7 13.12,7.5 12.57,8.27C11.93,7.5 11,7 10,7C8.34,7 7,8.34 7,10C7,10.35 7.07,10.69 7.18,11H3V13H22Z" />
                            </svg>
                          </div>
                          <p className="text-sm font-medium text-center text-blue-700">Other</p>
                        </div>
                      </div>

                      <div className="text-blue-800 text-base">
                        Choose the appropriate cruise line provider from the available options to start the booking process.
                      </div>
                    </div>
                  </div>

                  {/* Step 2 */}
                  <div className="bg-white/40 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg">
                    <div className="bg-gradient-to-r from-sky-600 to-blue-900 py-2 px-4">
                      <div className="flex items-center">
                        <div className="bg-white rounded-full w-6 h-6 flex items-center justify-center mr-3">
                          <span className="text-blue-700 font-bold text-xl">2</span>
                        </div>
                        <h3 className="text-2xl font-bold text-white">Enter Quote</h3>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="bg-gray-50/70 p-3 rounded-lg border border-gray-200/70 mb-3">
                        <div className="flex justify-between mb-1">
                          <div className="flex space-x-1.5">
                            <div className="w-2 h-2 rounded-full bg-red-500"></div>
                            <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                            <div className="w-2 h-2 rounded-full bg-green-500"></div>
                          </div>
                          <div className="text-sm text-gray-500">Quote</div>
                        </div>
                        <div className="font-mono text-sm text-gray-700 bg-white/80 p-2 rounded border border-gray-300/70">
                          <p>ROYAL CARIBBEAN</p>
                          <p>Voyager of the Seas</p>
                          <p>05/15/2023</p>
                          <p>Western Caribbean</p>
                          <p>Interior 4V</p>
                          <p>2 Adults, 1 Child</p>
                          <p>$2,850.00</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-center my-2">
                        <button className="bg-blue-500 text-white text-base py-1 px-3 rounded-md shadow">
                          Extract Details
                        </button>
                      </div>

                      <div className="text-blue-800 text-base">
                        Paste the quote text and let Oceanmind automatically extract all required booking details.
                      </div>
                    </div>
                  </div>
                </div>

                {/* Column 2: Processing */}
                <div className="space-y-6">
                  {/* Step 3 */}
                  <div className="bg-white/40 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg">
                    <div className="bg-gradient-to-r from-sky-600 to-blue-900 py-2 px-4">
                      <div className="flex items-center">
                        <div className="bg-white rounded-full w-6 h-6 flex items-center justify-center mr-3">
                          <span className="text-blue-700 font-bold text-xl">3</span>
                        </div>
                        <h3 className="text-2xl font-bold text-white">Verify Details</h3>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="grid grid-cols-2 gap-2 mb-3">
                        <div className="bg-blue-50/70 p-2 rounded-lg">
                          <h4 className="text-lg font-semibold text-blue-800 mb-1">Cruise Details</h4>
                          <div className="space-y-1 text-base">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Line:</span>
                              <span className="font-medium">Royal Caribbean</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Ship:</span>
                              <span className="font-medium">Voyager of Seas</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Date:</span>
                              <span className="font-medium">05/15/2023</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-blue-50/70 p-2 rounded-lg">
                          <h4 className="text-lg font-semibold text-blue-800 mb-1">Booking Info</h4>
                          <div className="space-y-1 text-base">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Cabin:</span>
                              <span className="font-medium">Interior 4V</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Guests:</span>
                              <span className="font-medium">2A + 1C</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Price:</span>
                              <span className="font-medium">$2,850.00</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-center space-x-2 my-2">
                        <button className="bg-gray-200 text-gray-700 text-base py-1 px-2 rounded">
                          Edit
                        </button>
                        <button className="bg-green-500 text-white text-base py-1 px-2 rounded">
                          Confirm
                        </button>
                      </div>

                      <div className="text-blue-800 text-base">
                        Review extracted information for accuracy before proceeding to automation.
                      </div>
                    </div>
                  </div>

                  {/* Step 4 */}
                  <div className="bg-white/40 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg">
                    <div className="bg-gradient-to-r from-sky-600 to-blue-900 py-2 px-4">
                      <div className="flex items-center">
                        <div className="bg-white rounded-full w-6 h-6 flex items-center justify-center mr-3">
                          <span className="text-blue-700 font-bold text-xl">4</span>
                        </div>
                        <h3 className="text-2xl font-bold text-white">Processing</h3>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="mb-3">
                        <div className="h-2 w-full bg-white/80 rounded-full overflow-hidden mb-1">
                          <div className="h-full bg-blue-600/70 rounded-full w-3/5 animate-pulse"></div>
                        </div>
                        <div className="flex justify-between text-base text-blue-800">
                          <span>Booking in progress...</span>
                          <span>60%</span>
                        </div>
                      </div>

                      <div className="grid grid-cols-4 gap-1 mb-3">
                        <div className="text-center">
                          <div className="w-6 h-6 rounded-full bg-green-500/70 mx-auto flex items-center justify-center">
                            <svg className="w-3 h-3 text-white" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                            </svg>
                          </div>
                          <p className="text-base mt-1 text-gray-700">Login</p>
                        </div>

                        <div className="text-center">
                          <div className="w-6 h-6 rounded-full bg-green-500/70 mx-auto flex items-center justify-center">
                            <svg className="w-3 h-3 text-white" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                            </svg>
                          </div>
                          <p className="text-base mt-1 text-gray-700">Search</p>
                        </div>

                        <div className="text-center">
                          <div className="w-6 h-6 rounded-full bg-blue-500/70 mx-auto flex items-center justify-center animate-pulse">
                            <svg className="w-3 h-3 text-white" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
                            </svg>
                          </div>
                          <p className="text-base mt-1 text-blue-700 font-medium">Cabin</p>
                        </div>

                        <div className="text-center">
                          <div className="w-6 h-6 rounded-full bg-gray-300 mx-auto flex items-center justify-center">
                            <span className="text-[8px] text-gray-600">4</span>
                          </div>
                          <p className="text-base mt-1 text-gray-500">Book</p>
                        </div>
                      </div>

                      <div className="bg-gray-50/70 p-2 rounded-lg">
                        <div className="text-base text-gray-700 font-mono">
                          <p>{`>`} Logging into portal...</p>
                          <p>{`>`} Searching for cruise...</p>
                          <p>{`>`} Found Voyager of the Seas</p>
                          <p>{`>`} Selecting Interior 4V cabin...</p>
                          <p className="text-blue-600 animate-pulse">{`>`} Processing request...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Column 3: Results */}
                <div className="space-y-6">
                  {/* Step 5 */}
                  <div className="bg-white/40 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg">
                    <div className="bg-gradient-to-r from-sky-600 to-blue-900 py-2 px-4">
                      <div className="flex items-center">
                        <div className="bg-white rounded-full w-6 h-6 flex items-center justify-center mr-3">
                          <span className="text-blue-700 font-bold text-xl">5</span>
                        </div>
                        <h3 className="text-2xl font-bold text-white">Completion</h3>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="bg-green-50/70 p-3 rounded-lg mb-3">
                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center">
                            <div className="w-5 h-5 rounded-full bg-green-500/70 mr-2 flex items-center justify-center">
                              <svg className="w-3 h-3 text-white" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                              </svg>
                            </div>
                            <span className="text-lg font-medium text-green-800">Booking Complete</span>
                          </div>

                          <span className="text-base font-medium bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Success</span>
                        </div>

                        <div className="grid grid-cols-2 gap-2 text-base">
                          <div>
                            <span className="text-gray-500 block">Confirmation #</span>
                            <span className="font-medium text-gray-800">RC38291045</span>
                          </div>

                          <div>
                            <span className="text-gray-500 block">Processing Time</span>
                            <span className="font-medium text-green-700">2:05 min (57% faster)</span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white/50 p-3 rounded-lg mb-3 border border-blue-100">
                        <h4 className="text-lg font-semibold text-blue-800 mb-1">Price Comparison</h4>
                        <div className="space-y-1">
                          <div className="flex justify-between text-base">
                            <span className="text-gray-600">Standard Price:</span>
                            <span className="font-medium">$2,850.00</span>
                          </div>
                          <div className="flex justify-between text-base">
                            <span className="text-gray-600">Found Discount:</span>
                            <span className="font-medium text-green-600">- $312.50</span>
                          </div>
                          <div className="border-t border-gray-200 my-1 pt-1 flex justify-between text-base">
                            <span className="font-medium text-gray-600">Final Price:</span>
                            <span className="font-bold text-green-700">$2,537.50</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-between space-x-2">
                        <button className="bg-blue-600 text-white text-base py-1 px-2 rounded flex-1">
                          Download Details
                        </button>
                        <button className="bg-green-600 text-white text-base py-1 px-2 rounded flex-1">
                          Book Another
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Testimonial/Metrics Card */}
                  <div className="bg-gradient-to-br from-sky-900/80 to-indigo-700/80 font-bold backdrop-blur-sm rounded-xl p-4 shadow-lg text-white">
                    <div className="flex items-start mb-5">
                      <svg className="w-6 h-6 mr-2 text-purple-200" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z" />
                      </svg>
                      <p className="text-2xl italic text-purple-100">Oceanmind has reduced our booking time by over 55% and improved our agents' productivity drastically!</p>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-white/20 rounded-lg p-2">
                        <p className="text-base text-purple-100">Avg. Time Savings</p>
                        <p className="text-2xl font-bold">57%</p>
                      </div>
                      <div className="bg-white/20 rounded-lg p-2">
                        <p className="text-base text-purple-100">Customer Satisfaction</p>
                        <p className="text-2xl font-bold">96%</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}