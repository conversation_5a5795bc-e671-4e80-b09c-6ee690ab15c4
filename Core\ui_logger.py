"""
UI Logger module for user-friendly logs with minimal payload.

This module adds a custom UI log level suitable for frontend display through socket connections.
It can be used across all application modules to provide consistent UI logging.
"""

from loguru import logger
import sys
import json
import asyncio
from datetime import datetime
import contextvars
from typing import Optional

# Add UI log level (between INFO and WARNING)
logger.level("UI", no=25, color="<cyan>")

# Configure standard logger to exclude UI level
logger_id = logger.configure(
    handlers=[
        {"sink": sys.stderr, "level": "INFO", 
         "filter": lambda record: record["level"].name != "UI"}
    ]
)

# Variable to store UI logger service reference - will be set later
_ui_logger_service = None

# Context variable to track the current authenticated user id inside the request lifetime
_current_user_id: contextvars.ContextVar[Optional[str]] = contextvars.ContextVar("current_user_id", default=None)


def set_current_user_context(user_id: Optional[str]):
    """Store the given user_id in a context variable so that ui_log() can pick it up automatically.

    This function should be called by the authentication dependency once the user has been
    validated. Passing *None* clears the current value.
    """
    _current_user_id.set(user_id)


def _get_current_user_from_context() -> Optional[str]:
    """Return the current user_id set in the context var (or *None*)."""
    try:
        return _current_user_id.get()
    except LookupError:  # pragma: no cover – should not happen due to default=None
        return None


def set_ui_logger_service(service):
    """
    Set the UI logger service for SSE broadcasting.
    This function should be called during application startup.
    
    Args:
        service: The UI logger service instance
    """
    global _ui_logger_service
    _ui_logger_service = service

# Helper function for UI logs - optimized for socket transmission
def ui_log(message, session_id=None, cabin_id=None, category=None, step=None, module=None, user_id=None):
    """
    Log a user-friendly message at UI level with minimal payload.
    
    Args:
        message: The main message text
        session_id: Optional session identifier 
        cabin_id: Optional cabin number or id
        category: Optional category information
        step: Optional workflow step identifier
        module: Optional module name (Cruising_Power, Studio, etc.)
    """
    # Create minimal payload structure
    log_data = {
        "timestamp": datetime.now().strftime("%H:%M:%S"),
        "message": message
    }
    
    # If no explicit user_id passed try to infer from contextvar
    if user_id is None:
        user_id = _get_current_user_from_context()

    # Only add fields that have values to keep payload minimal
    if session_id:
        log_data["session_id"] = session_id
    if user_id:
        log_data["user_id"] = user_id
    if cabin_id:
        log_data["cabin_id"] = cabin_id
    if category:
        log_data["category"] = category
    if step:
        log_data["step"] = step
    if module:
        log_data["module"] = module
        
    # Log as both JSON (for programmatic consumption) and formatted text
    logger.log("UI", json.dumps(log_data))
    
    # Send to UI logger service if available
    if _ui_logger_service and getattr(_ui_logger_service, 'loop', None):
        try:
            # Schedule the broadcast on the service's running event loop
            svc_loop = _ui_logger_service.loop
            svc_loop.call_soon_threadsafe(asyncio.create_task,
                                        _ui_logger_service.broadcast_ui_log(log_data))
        except Exception:
            # Fail silently - logging should not break application flow
            pass
    
    # Return the log data for potential direct socket transmission
    return log_data

# Function to get current logger
def get_ui_logger():
    return logger 