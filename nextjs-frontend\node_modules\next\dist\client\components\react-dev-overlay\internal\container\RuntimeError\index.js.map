{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/index.tsx"], "names": ["styles", "RuntimeError", "error", "firstFirstPartyFrameIndex", "React", "useMemo", "frames", "findIndex", "entry", "expanded", "Boolean", "originalCodeFrame", "originalStackFrame", "firstFrame", "allLeadingFrames", "slice", "all", "setAll", "useState", "toggleAll", "useCallback", "v", "leading<PERSON>ram<PERSON>", "filter", "f", "allCallStackFrames", "visibleCallStackFrames", "canShowMore", "length", "stackFramesGroupedByFramework", "groupStackFramesByFramework", "Fragment", "h2", "map", "frame", "index", "CallStackFrame", "key", "CodeFrame", "stackFrame", "codeFrame", "undefined", "componentStackFrames", "componentStackFrame", "ComponentStackFrameRow", "GroupedStackFrames", "groupedStackFrames", "button", "tabIndex", "data-nextjs-data-runtime-error-collapsed-action", "type", "onClick", "css"], "mappings": ";;;;;;;;;;;;;;;IA6HaA,MAAM;eAANA;;IAwFJC,YAAY;eAAZA;;;;;iEArNc;2BACG;8BAEE;6CAEgB;gCACb;oCACI;wCACI;;;;;;;;;;AAIvC,MAAMA,eAA4C,SAASA,aAAa,KAEvE;IAFuE,IAAA,EACtEC,KAAK,EACN,GAFuE;IAGtE,MAAMC,4BAA4BC,OAAMC,OAAO,CAAS;QACtD,OAAOH,MAAMI,MAAM,CAACC,SAAS,CAC3B,CAACC,QACCA,MAAMC,QAAQ,IACdC,QAAQF,MAAMG,iBAAiB,KAC/BD,QAAQF,MAAMI,kBAAkB;IAEtC,GAAG;QAACV,MAAMI,MAAM;KAAC;IACjB,MAAMO,aAAaT,OAAMC,OAAO,CAA4B;YACnDH;QAAP,OAAOA,CAAAA,0CAAAA,MAAMI,MAAM,CAACH,0BAA0B,YAAvCD,0CAA2C;IACpD,GAAG;QAACA,MAAMI,MAAM;QAAEH;KAA0B;IAE5C,MAAMW,mBAAmBV,OAAMC,OAAO,CACpC,IACEF,4BAA4B,IACxB,EAAE,GACFD,MAAMI,MAAM,CAACS,KAAK,CAAC,GAAGZ,4BAC5B;QAACD,MAAMI,MAAM;QAAEH;KAA0B;IAG3C,MAAM,CAACa,KAAKC,OAAO,GAAGb,OAAMc,QAAQ,CAACL,cAAc;IACnD,MAAMM,YAAYf,OAAMgB,WAAW,CAAC;QAClCH,OAAO,CAACI,IAAM,CAACA;IACjB,GAAG,EAAE;IAEL,MAAMC,gBAAgBlB,OAAMC,OAAO,CACjC,IAAMS,iBAAiBS,MAAM,CAAC,CAACC,IAAMA,EAAEf,QAAQ,IAAIO,MACnD;QAACA;QAAKF;KAAiB;IAEzB,MAAMW,qBAAqBrB,OAAMC,OAAO,CACtC,IAAMH,MAAMI,MAAM,CAACS,KAAK,CAACZ,4BAA4B,IACrD;QAACD,MAAMI,MAAM;QAAEH;KAA0B;IAE3C,MAAMuB,yBAAyBtB,OAAMC,OAAO,CAC1C,IAAMoB,mBAAmBF,MAAM,CAAC,CAACC,IAAMA,EAAEf,QAAQ,IAAIO,MACrD;QAACA;QAAKS;KAAmB;IAG3B,MAAME,cAAcvB,OAAMC,OAAO,CAAU;QACzC,OACEoB,mBAAmBG,MAAM,KAAKF,uBAAuBE,MAAM,IAC1DZ,OAAOH,cAAc;IAE1B,GAAG;QACDG;QACAS,mBAAmBG,MAAM;QACzBf;QACAa,uBAAuBE,MAAM;KAC9B;IAED,MAAMC,gCAAgCzB,OAAMC,OAAO,CACjD,IAAMyB,IAAAA,wDAA2B,EAACJ,yBAClC;QAACA;KAAuB;IAG1B,qBACE,qBAACtB,OAAM2B,QAAQ,QACZlB,2BACC,qBAACT,OAAM2B,QAAQ,sBACb,qBAACC,YAAG,WACHV,cAAcW,GAAG,CAAC,CAACC,OAAOC,sBACzB,qBAACC,8BAAc;YACbC,KAAK,AAAC,mBAAgBF,QAAM,MAAGnB;YAC/BkB,OAAOA;2BAGX,qBAACI,oBAAS;QACRC,YAAY1B,WAAWD,kBAAkB;QACzC4B,WAAW3B,WAAWF,iBAAiB;UAGzC8B,WAEHvC,MAAMwC,oBAAoB,iBACzB,0DACE,qBAACV,YAAG,oBACH9B,MAAMwC,oBAAoB,CAACT,GAAG,CAAC,CAACU,qBAAqBR,sBACpD,qBAACS,8CAAsB;YACrBP,KAAKF;YACLQ,qBAAqBA;eAIzB,MAEHd,8BAA8BD,MAAM,iBACnC,qBAACxB,OAAM2B,QAAQ,sBACb,qBAACC,YAAG,6BACJ,qBAACa,sCAAkB;QACjBC,oBAAoBjB;QACpBb,KAAKA;UAGPyB,WACHd,4BACC,qBAACvB,OAAM2B,QAAQ,sBACb,qBAACgB;QACCC,UAAU;QACVC,mDAAAA;QACAC,MAAK;QACLC,SAAShC;OAERH,MAAM,SAAS,QAAO,wBAGzByB;AAGV;AAEO,MAAMzC,aAASoD,kBAAG"}