import React, { useState, useEffect } from 'react';
import { getSessionVideos } from '../../services/video';
import VideoButton from '../common/VideoButton';

interface VideosTabProps {
  provider: string;
  requestId: string;
  sessionId: string;
}

const VideosTab: React.FC<VideosTabProps> = ({ provider, requestId, sessionId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [videos, setVideos] = useState<any[]>([]);

  useEffect(() => {
    const fetchVideos = async () => {
      if (!sessionId) {
        setError("No session ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const data = await getSessionVideos(sessionId);
        
        if (data.success && data.videos && data.videos.length > 0) {
          setVideos(data.videos);
        } else {
          setError("Video auditing is not enabled for this booking session OR no videos found for this session");
        }
      } catch (err) {
        console.error('Error fetching videos:', err);
        setError('Error loading videos. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, [sessionId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-700">Loading videos...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-yellow-50 border border-yellow-300 text-yellow-800 p-4 rounded-lg mb-4">
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }

  // Group videos by cabin_id
  const videosByCabin: Record<string, any[]> = {};
  videos.forEach(video => {
    const cabinId = video.cabin_id ? video.cabin_id.toString() : 'General';
    if (!videosByCabin[cabinId]) {
      videosByCabin[cabinId] = [];
    }
    videosByCabin[cabinId].push(video);
  });

  return (
    <div className="p-4">
    
      {Object.keys(videosByCabin).length === 0 ? (
        <div className="bg-yellow-50 border border-yellow-300 text-yellow-800 p-4 rounded-lg">
          <p>No videos found for this booking session.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(videosByCabin).map(([cabinId, cabinVideos]) => (
            <div key={cabinId} className="bg-white rounded-lg border border-gray-300 overflow-hidden">
              <div className="bg-gradient-to-r from-blue-50 to-teal-50 border-b border-gray-300 p-3">
                <h4 className="font-medium">
                  {cabinId === 'General' ? 'General Videos' : `Cabin ${cabinId} Videos`}
                </h4>
              </div>
              <div className="p-3">
                <div className="space-y-3">
                  {cabinVideos.map((video) => (
                    <div key={video.id} className="flex justify-between items-center p-2 hover:bg-gray-50 border-b border-gray-100">
                      <div>
                        <p className="text-sm font-medium">
                          {video.video_type ? 
                            video.video_type.replace(/_/g, ' ').split(' ').map((word: string) => 
                              word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                            ).join(' ') : 
                            'Video'
                          }
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(video.timestamp).toLocaleString()}
                          {video.duration ? ` • ${Math.floor(video.duration / 60)}:${String(Math.floor(video.duration % 60)).padStart(2, '0')}` : ''}
                          {video.format ? ` • ${video.format.toUpperCase()}` : ''}
                          {video.size_kb ? ` • ${(video.size_kb / 1024).toFixed(1)}MB` : ''}
                        </p>
                      </div>
                      <VideoButton
                        requestId={requestId}
                        sessionId={sessionId}
                        provider={provider}
                        cabinId={video.cabin_id}
                        buttonClassName="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded text-sm"
                        buttonText="Play"
                        iconOnly={false}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default VideosTab;