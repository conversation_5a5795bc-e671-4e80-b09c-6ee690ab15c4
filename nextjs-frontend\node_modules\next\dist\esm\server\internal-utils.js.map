{"version": 3, "sources": ["../../src/server/internal-utils.ts"], "names": ["NEXT_RSC_UNION_QUERY", "INTERNAL_HEADERS", "INTERNAL_QUERY_NAMES", "EDGE_EXTENDED_INTERNAL_QUERY_NAMES", "stripInternalQueries", "query", "name", "stripInternalSearchParams", "url", "isEdge", "isStringUrl", "instance", "URL", "searchParams", "delete", "toString", "stripInternalHeaders", "headers", "key"], "mappings": "AAGA,SAASA,oBAAoB,QAAQ,0CAAyC;AAC9E,SAASC,gBAAgB,QAAQ,0BAAyB;AAE1D,MAAMC,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;IACAF;CACD;AAED,MAAMG,qCAAqC;IAAC;CAAgB;AAE5D,OAAO,SAASC,qBAAqBC,KAAyB;IAC5D,KAAK,MAAMC,QAAQJ,qBAAsB;QACvC,OAAOG,KAAK,CAACC,KAAK;IACpB;AACF;AAEA,OAAO,SAASC,0BACdC,GAAM,EACNC,MAAe;IAEf,MAAMC,cAAc,OAAOF,QAAQ;IACnC,MAAMG,WAAWD,cAAc,IAAIE,IAAIJ,OAAQA;IAC/C,KAAK,MAAMF,QAAQJ,qBAAsB;QACvCS,SAASE,YAAY,CAACC,MAAM,CAACR;IAC/B;IAEA,IAAIG,QAAQ;QACV,KAAK,MAAMH,QAAQH,mCAAoC;YACrDQ,SAASE,YAAY,CAACC,MAAM,CAACR;QAC/B;IACF;IAEA,OAAQI,cAAcC,SAASI,QAAQ,KAAKJ;AAC9C;AAEA;;;;CAIC,GACD,OAAO,SAASK,qBAAqBC,OAA4B;IAC/D,KAAK,MAAMC,OAAOjB,iBAAkB;QAClC,OAAOgB,OAAO,CAACC,IAAI;IACrB;AACF"}