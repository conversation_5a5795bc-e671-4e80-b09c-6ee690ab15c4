from playwright.async_api import Page
from Cruising_Power.screenshot_utils import take_modal_screenshot
import asyncio
import re
from loguru import logger
from Core.ui_logger import ui_log


class PriceQuote:
    """
    Class for handling price quote functionality
    """
    def __init__(self, page, onboard_percentage=12, request_id=None, cabin_id=None, session_id=None):
        """
        Initialize the price quote handler
        
        Args:
            page: Playwright Page instance
            onboard_percentage: Percentage to use when calculating onboard credit (default: 12)
            request_id: Request ID for database storage
            cabin_id: Cabin ID for database storage
            session_id: Session ID for consistent database tracking
        """
        self.page = page
        self.onboard_percentage = onboard_percentage
        self.request_id = request_id
        self.cabin_id = cabin_id
        self.session_id = session_id
        
    async def get_price_quote(self):
        """
        Clicks the price quote button and retrieves the total price.
        Also calculates the onboard credit based on commission and additional table values.
        
        Returns:
            tuple: (success, total_price, onboard_credit)
                success (bool): True if the operation was successful
                total_price (str): The total price or empty string if not found
                onboard_credit (str): The calculated onboard credit or empty string if not calculated
        """
        print("\nClicking the price quote button...")
        ui_log("Getting your price quote - kindly stand by", session_id=self.session_id, cabin_id=self.cabin_id, step="getting_price_quote", module="Cruising_Power")
        try:
            # Find and click the price quote button
            price_quote_button = await self.page.wait_for_selector(
                "#cruiseOnlyAllocation > section.wrapper.additionalOptions.footer > div.buttonsRight > a.alertable.priceQuote.button.secondaryLinks",
                timeout=15000,
                state="visible"
            )
            await price_quote_button.click()
            logger.info("Price quote button clicked")
            
            # Add a fixed delay to allow the modal to fully render
            logger.info("Waiting 2 seconds for modal to stabilize...")
            await asyncio.sleep(2)
            
            # Wait for the price quote modal to appear
            await self.page.wait_for_selector("#priceQuoteModal", timeout=30000)
            
            # Take an initial screenshot of the modal right after it appears
            logger.info("Taking initial modal screenshot...")
            await take_modal_screenshot(self.page, "5_price_quote_initial", self.request_id, self.cabin_id, self.session_id)
            
            # Get the total price using the correct CSS selector provided by the user
            total_price_selector = "#priceQuoteModal > div > div > section > div.popBody > section:nth-child(1) > div.priceInfoBlock > div.pricing > p.tpAll"
            await self.page.wait_for_selector(total_price_selector, timeout=15000)
            
            # Add another short delay before trying to read the text
            await asyncio.sleep(1)
            
            total_price_element = await self.page.query_selector(total_price_selector)
            total_price = (await total_price_element.text_content()).strip()
            print(f"Total price: {total_price}")
            #ui_log(f"Total price: {total_price}", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            
            # Initialize onboard credit
            onboard_credit = "0.00"
            commission_value = 0.0
            additional_table_values = 0.0
            
            # Try to find and click the "View Agency Commission" button
            try:
                print("Looking for 'View Agency Commission' button...")
                #ui_log("Checking commission details", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
                agency_commission_button = await self.page.wait_for_selector(
                    "a.jsDropDownTrigger2[data-drop-down='agencyDetails2']",
                    timeout=5000,
                    state="visible"
                )
                
                logger.info("Clicking 'View Agency Commission' button...")
                await agency_commission_button.click()
                await asyncio.sleep(1)  # Wait for dropdown to open
                
                # Take a complete screenshot of the modal after clicking the button
                logger.info("Taking commission details screenshot...")
                # await take_modal_screenshot(self.page, "5_price_quote_commission", self.request_id, self.cabin_id)
                
                # Extract the commission value
                try:
                    logger.info("Extracting commission value...")
                    # Try the simpler selector first since it's working in the logs
                    commission_selector = "#totalCommission"
                    
                    try:
                        commission_element = await self.page.wait_for_selector(commission_selector, timeout=5000)
                    except:
                        logger.info("Using more specific commission selector...")
                        commission_selector = "#agencyDetails2 p > span.customTitle.formatCurrency#totalCommission"
                        
                        try:
                            commission_element = await self.page.wait_for_selector(commission_selector, timeout=5000)
                        except:
                            logger.info("Using XPath selector for commission...")
                            commission_element = await self.page.wait_for_selector("//span[@id='totalCommission']", timeout=5000)
                    
                    commission_text = (await commission_element.text_content()).strip()
                    logger.info(f"Commission value: {commission_text}")
                    
                    # Extract numeric value using regex
                    commission_match = re.search(r'[\d,]+\.\d+|\d+', commission_text)
                    if commission_match:
                        commission_value = float(commission_match.group().replace(',', ''))
                        logger.info(f"Extracted commission value: {commission_value}")
                        
                        # Calculate onboard credit (commission / 16 * onboard_percentage)
                        onboard_credit_value = (commission_value / 16) * self.onboard_percentage
                        logger.info(f"Initial onboard credit calculation: {onboard_credit_value}")
                    
                        # Look only for table3
                        logger.info("Looking for table3...")
                        table3 = await self.page.query_selector("#table3")
                        if table3:
                            logger.info("Found table3")
                            
                            # Extract the final total from this table
                            try:
                                # Try to find the finalTotal in the footer
                                final_total_element = await table3.query_selector(
                                    "tfoot tr.total td.finalTotal span.formatCurrency"
                                )
                                if final_total_element:
                                    final_total_text = (await final_total_element.text_content()).strip()
                                    logger.info(f"Final total from table3: {final_total_text}")
                                    
                                    # Extract numeric value
                                    final_total_match = re.search(r'[\d,]+\.\d+|\d+', final_total_text)
                                    if final_total_match:
                                        final_total_value = float(final_total_match.group().replace(',', ''))
                                        additional_table_values = final_total_value
                                        logger.info(f"Added {final_total_value} from table3")
                                else:
                                    # Try an alternative, less specific selector
                                    final_total_element = await table3.query_selector("tfoot span.formatCurrency")
                                    if final_total_element:
                                        final_total_text = (await final_total_element.text_content()).strip()
                                        logger.info(f"Final total (alternative selector) from table3: {final_total_text}")
                                        
                                        # Extract numeric value
                                        final_total_match = re.search(r'[\d,]+\.\d+|\d+', final_total_text)
                                        if final_total_match:
                                            final_total_value = float(final_total_match.group().replace(',', ''))
                                            additional_table_values = final_total_value
                                            logger.info(f"Added {final_total_value} from table3")
                                    else:
                                        logger.info("Could not find final total in table3 - no matching elements found")
                            except Exception as e:
                                logger.error(f"Could not extract final total from table3: {e}")
                        else:
                            logger.info("Table3 not found")
                        
                        # Also look for table4
                        logger.info("Looking for table4...")
                        table4 = await self.page.query_selector("#table4")
                        if table4:
                            logger.info("Found table4")
                            
                            # Extract the final total from this table
                            try:
                                # Try to find the finalTotal in the footer
                                final_total_element = await table4.query_selector(
                                    "tfoot tr.total td.finalTotal span.formatCurrency"
                                )
                                if final_total_element:
                                    final_total_text = (await final_total_element.text_content()).strip()
                                    logger.info(f"Final total from table4: {final_total_text}")
                                    
                                    # Extract numeric value
                                    final_total_match = re.search(r'[\d,]+\.\d+|\d+', final_total_text)
                                    if final_total_match:
                                        final_total_value = float(final_total_match.group().replace(',', ''))
                                        additional_table_values += final_total_value
                                        logger.info(f"Added {final_total_value} from table4")
                                else:
                                    # Try an alternative, less specific selector
                                    final_total_element = await table4.query_selector("tfoot span.formatCurrency")
                                    if final_total_element:
                                        final_total_text = (await final_total_element.text_content()).strip()
                                        logger.info(f"Final total (alternative selector) from table4: {final_total_text}")
                                        
                                        # Extract numeric value
                                        final_total_match = re.search(r'[\d,]+\.\d+|\d+', final_total_text)
                                        if final_total_match:
                                            final_total_value = float(final_total_match.group().replace(',', ''))
                                            additional_table_values += final_total_value
                                            logger.info(f"Added {final_total_value} from table4")
                                    else:
                                        logger.info("Could not find final total in table4 - no matching elements found")
                            except Exception as e:
                                logger.error(f"Could not extract final total from table4: {e}")
                        else:
                            logger.info("Table4 not found")
                        
                        # Calculate final onboard credit with additional table values
                        logger.info(f"Additional table values: {additional_table_values}")
                        
                        
                        onboard_credit_value = onboard_credit_value + additional_table_values
                        
                        # Round to nearest integer instead of decimal
                        rounded_onboard_credit = round(onboard_credit_value)
                        
                        # Format as currency string
                        onboard_credit = f"${rounded_onboard_credit:.2f}"
                        print(f"Final onboard credit calculation: {onboard_credit}")
                        ui_log("Calculating your onboard credit — just a moment", session_id=self.session_id, cabin_id=self.cabin_id, step="calculating_onboard_credit", module="Cruising_Power")
                    else:
                        logger.info("Could not extract numeric commission value")
                    
                except Exception as e:
                    logger.error(f"Error extracting commission value: {e}")
            
            except Exception as e:
                print(f"Error finding or clicking agency commission button: {e}")
                #ui_log("Could not retrieve commission details", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            
            # Take another screenshot after all operations are complete
            logger.info("Taking final modal screenshot...")
            await take_modal_screenshot(self.page, "5_price_quote_complete", self.request_id, self.cabin_id, self.session_id)
            
            # Find and click the close button to close the modal
            logger.info("Closing price quote modal...")
            try:
                close_button = await self.page.query_selector("#priceQuoteModal a.alertable-close")
                if close_button:
                    await close_button.click()
                    print("Modal closed successfully")
                    #ui_log("Price quote completed", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
                    await asyncio.sleep(1)  # Wait for modal to close
            except Exception as e:
                print(f"Error closing modal: {e}")
                #ui_log("Error closing price quote modal", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            
            return True, total_price, onboard_credit
        
        except Exception as e:
            print(f"Error getting price quote: {e}")
            ui_log("There was an error while getting the price quote — retrying now", session_id=self.session_id, cabin_id=self.cabin_id, step="price_quote_error", module="Cruising_Power")
            
            # Try to close the modal if it's open
            try:
                close_button = await self.page.query_selector("#priceQuoteModal a.alertable-close")
                if close_button:
                    await close_button.click()
                    logger.info("Modal closed after error")
            except:
                pass
                
            return False, "", ""
    
    async def get_detailed_pricing(self):
        """
        Extracts detailed pricing information from the page.
        
        Returns:
            dict: Dictionary containing detailed pricing information
        """
        try:
            # Get cruise details and pricing sections
            print("Extracting detailed pricing information...")
            #ui_log("Extracting detailed pricing information", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            
            # Initialize the pricing details dictionary
            pricing_details = {
                "cruise_name": "",
                "sailing_date": "",
                "cruise_length": "",
                "stateroom_type": "",
                "stateroom_number": "",
                "total_price": "",
                "base_price": "",
                "taxes_fees": "",
                "cabin_allocation": "",
                "line_items": []
            }
            
            # Try to get the cruise name
            try:
                cruise_name_element = await self.page.query_selector("#cruiseName")
                if cruise_name_element:
                    pricing_details["cruise_name"] = (await cruise_name_element.text_content()).strip()
                    print(f"Cruise name: {pricing_details['cruise_name']}")
                    #ui_log(f"Cruise: {pricing_details['cruise_name']}", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            except Exception as e:
                logger.error(f"Error getting cruise name: {e}")
            
            # Extract cabin allocation information
            try:
                logger.info("Extracting cabin allocation information...")
                # Wait to ensure the page has stabilized
                await asyncio.sleep(2)
                
                # Try to locate the allocation information with the selector
                allocation_selector = "#sailingBlockIdDiv > table > tbody > tr > td.right > section.allocation"
                allocation_element = await self.page.query_selector(allocation_selector)
                
                if allocation_element:
                    allocation_info = (await allocation_element.text_content()).strip()
                    pricing_details["cabin_allocation"] = allocation_info
                    logger.info(f"Cabin allocation information: {allocation_info}")
                else:
                    logger.info("Cabin allocation element not found")
            except Exception as e:
                logger.error(f"Error extracting cabin allocation information: {e}")
            
            # Try to get the fare items (might be multiple)
            try:
                fare_items = await self.page.query_selector_all("#cruiseOnlyAllocation .fareItem")
                for item in fare_items:
                    try:
                        item_name_element = await item.query_selector(".fName")
                        item_price_element = await item.query_selector(".fAmount")
                        
                        if item_name_element and item_price_element:
                            item_name = (await item_name_element.text_content()).strip()
                            item_price = (await item_price_element.text_content()).strip()
                            
                            pricing_details["line_items"].append({
                                "name": item_name,
                                "price": item_price
                            })
                            logger.info(f"Line item: {item_name} - {item_price}")
                    except Exception as e:
                        print(f"Error processing fare item: {e}")
                #ui_log(f"Found {len(pricing_details['line_items'])} price components", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            except Exception as e:
                logger.error(f"Error getting fare items: {e}")
            
            # Check if price modal is currently visible
            modal_visible = await self.page.query_selector("#priceQuoteModal:visible")
            
            # Skip opening the modal again if we already have the total price
            if not modal_visible and pricing_details.get("total_price"):
                logger.info("Skipping modal reopening as we already have pricing information")
                return pricing_details
                
            # Don't try to open the modal again - it's causing too many timeouts
            # Instead, use the information we already have
            print("Using existing pricing information without reopening modal")
            ui_log("Pricing information retrieved — thanks for your patience", session_id=self.session_id, cabin_id=self.cabin_id, step="pricing_retrieved",module="Cruising_Power")
            return pricing_details
            
        except Exception as e:
            print(f"Error getting detailed pricing: {e}")
            ui_log("Error retrieving detailed pricing — retrying now", session_id=self.session_id, cabin_id=self.cabin_id, step="retrying_detailed_pricing", module="Cruising_Power")
            
            # Try to close the modal if it's open
            try:
                close_button = await self.page.query_selector("#priceQuoteModal a.alertable-close")
                if close_button:
                    await close_button.click()
            except:
                pass
                
            return {}


# Functions to maintain backward compatibility
async def get_price_quote(page, onboard_percentage=12, request_id=None, cabin_id=None, session_id=None):
    """
    Convenience function to get the price quote without creating a class instance
    
    Args:
        page: Playwright Page instance
        onboard_percentage: Percentage to use when calculating onboard credit (default: 12)
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
        
    Returns:
        tuple: (success, total_price, onboard_credit)
    """
    price_quote = PriceQuote(page, onboard_percentage, request_id, cabin_id, session_id)
    return await price_quote.get_price_quote()


async def get_detailed_pricing(page, request_id=None, cabin_id=None, session_id=None):
    """
    Convenience function to get detailed pricing without creating a class instance
    
    Args:
        page: Playwright Page instance
        request_id: Request ID for database storage
        cabin_id: Cabin ID for database storage
        session_id: Session ID for consistent database tracking
        
    Returns:
        dict: Dictionary containing detailed pricing information
    """
    price_quote = PriceQuote(page, 12, request_id, cabin_id, session_id)
    return await price_quote.get_detailed_pricing()
