from loguru import logger
import asyncio
from NCL.select_category import CategorySelector
from NCL.utils import NCLUtils


class StateRoomSelector:

    @staticmethod
    async def select_stateroom(
        page, reservation_data=None, session_id=None, cabin_id=None, config=None
    ):
        try:
            logger.info("Checking for available staterooms")

            try:
                stateroom_grid = await page.wait_for_selector(
                    "#SWXMLForm_SelectStateroom_stateroom", timeout=5000
                )

                stateroom_rows = await page.locator(".slick-row").all()

                if stateroom_rows:
                    logger.info(f"Found {len(stateroom_rows)} staterooms")

                    logger.info("\n==== AVAILABLE STATEROOMS ====")
                    for i, row in enumerate(stateroom_rows):
                        try:
                            category = await row.locator(".l1.r1").text_content()
                            room_number = await row.locator(".l2.r2 a").text_content()
                            deck = await row.locator(".l4.r4").text_content()

                            logger.info(
                                f"{i+1}. Category: {category} - Room: {room_number} - {deck}"
                            )
                        except Exception as e:
                            logger.error(f"Error parsing stateroom row {i+1}: {str(e)}")
                            logger.info(f"{i+1}. [Error parsing stateroom information]")
                else:
                    logger.info("No staterooms found")

                logger.info(
                    "Skipping stateroom selection, navigating directly to price programs page"
                )

                NCL_Utils = NCLUtils()
                await NCL_Utils.save_screenshot(
                    page,
                    "stateroom_selection_skipped.png",
                    cabin_id=cabin_id,
                    session_id=session_id
                )

            except Exception as e:
                logger.info(
                    f"Not on stateroom selection page or error finding staterooms: {str(e)}"
                )

            if await StateRoomSelector.navigate_to_price_programs(page):
                logger.success("Successfully navigated to price programs page")

                NCL_Utils = NCLUtils()

                await NCL_Utils.save_screenshot(
                    page,
                    "05_price_programs_page",
                    cabin_id=reservation_data.get('cabin_id'),
                    request_id=reservation_data.get('request_id'),
                    session_id=reservation_data.get('session_id')
                )

                selected_promo_code = ""
                try:
                    promo_elements = await page.locator(".container .program-code").all()
                    checked_elements = await page.locator("input[type='checkbox']:checked"
                                                    ).all()

                    if promo_elements and checked_elements:
                        for promo in promo_elements:
                            promo_code = await promo.text_content()
                            promo_code = promo_code.strip()
                            if "ALL4CHO" in promo_code:
                                selected_promo_code = promo_code
                                logger.info(f"Found promo code: {promo_code}")

                                if reservation_data is not None:
                                    reservation_data['selected_promo_code'] = promo_code
                                break
                except Exception as e:
                    logger.warning(
                        f"Could not capture promo code before handling: {str(e)}"
                    )

                NCL_Utils = NCLUtils()

                logger.info("Taking screenshot before handling price programs")

                from NCL.cruise_selection import CruiseSelection

                logger.info("Handling price programs")
                if not await CruiseSelection.handle_price_programs(
                        page, reservation_data.get('cabin_id'),
                        reservation_data.get('request_id'),
                        reservation_data.get('session_id'), config):
                    logger.warning(
                        "Warning: handle_price_programs returned False, continuing anyway"
                    )

                logger.info(
                    "Taking screenshot after handling price programs and selecting ALL4CHO"
                )
                await NCL_Utils.save_screenshot(
                    page,
                    "after_ALL4CHO_selected.png",
                    cabin_id=cabin_id,
                    session_id=session_id
                )
                if not selected_promo_code:
                    try:
                        promo_elements = await page.locator(".container .program-code").all()
                        checked_elements = await page.locator(
                            "input[type='checkbox']:checked"
                        ).all()

                        if promo_elements and checked_elements:
                            for promo in promo_elements:
                                promo_code = await promo.text_content()
                                promo_code = promo_code.strip()
                                selected_promo_code = promo_code
                                logger.info(f"Selected promo code: {promo_code}")

                                if reservation_data is not None:
                                    reservation_data['selected_promo_code'] = promo_code
                                break
                    except Exception as e:
                        logger.warning(
                            f"Could not capture selected promo code: {str(e)}"
                        )

                return True
            else:
                logger.error("Failed to navigate to price programs page")
                return False

        except Exception as e:
            logger.error(f"Error in stateroom selection process: {str(e)}")

            try:
                if await StateRoomSelector.navigate_to_price_programs(page):
                    logger.info("Navigated to price programs as a fallback")

                    # Import CruiseSelection here to avoid circular import
                    from NCL.cruise_selection import CruiseSelection
                    logger.info("Handling price programs")
                    await CruiseSelection.handle_price_programs(
                        page, reservation_data.get('cabin_id'),
                        reservation_data.get('request_id'),
                        reservation_data.get('session_id'), config
                    )

                    return True
            except:
                pass

            return False

    @staticmethod
    async def navigate_to_price_programs(page):
        return await CategorySelector.navigate_to_price_programs(page)

    @staticmethod
    async def select_stateroom_by_index(page, index):
        try:
            for attempt in range(3):
                try:
                    logger.info(
                        f"Attempt {attempt+1} to select stateroom at index {index}"
                    )

                    rows = await page.locator(".slick-row").all()

                    if index >= len(rows):
                        logger.warning(
                            f"Index {index} is out of range (0-{len(rows)-1})"
                        )
                        return False

                    target_row = rows[index]

                    await target_row.scroll_into_view_if_needed()
                    await asyncio.sleep(1)

                    checkbox = target_row.locator("input[type='checkbox']").first
                    if await checkbox.count() > 0:
                        logger.info("Found checkbox, clicking it")
                        await checkbox.click()
                        await asyncio.sleep(0.5)
                    else:
                        select_link = target_row.locator(
                            "a:has-text('Select'), a.slick-cell"
                        ).first
                        if await select_link.count() > 0:
                            logger.info("Found select link, clicking it")
                            await select_link.click()
                            await asyncio.sleep(0.5)
                        else:
                            logger.info(
                                "No checkbox or select link found, clicking the row"
                            )
                            await target_row.click()
                            await asyncio.sleep(0.5)

                    for selector in ["[id*='DoContinue']", "[id*='Continue']",
                                     "[id*='docontinue']",
                                     "input[type='submit'][value='Continue']",
                                     "button.continue", "input.continue"]:
                        try:
                            continue_button = page.locator(selector).first
                            if await continue_button.count(
                            ) > 0 and await continue_button.is_visible():
                                logger.info(
                                    f"Found continue button with selector: {selector}"
                                )

                                async with page.expect_navigation(timeout=10000):
                                    await continue_button.click()

                                logger.success(
                                    f"Successfully selected stateroom at index {index}"
                                )
                                return True
                        except Exception as button_error:
                            logger.debug(
                                f"Error with button selector {selector}: {str(button_error)}"
                            )

                    if page.url != await page.evaluate("window.location.href"):
                        logger.info(
                            "Navigation occurred without finding a specific button"
                        )
                        return True

                except Exception as e:
                    logger.warning(f"Attempt {attempt+1} failed: {str(e)}")
                    await asyncio.sleep(1)

            logger.error("Failed to select stateroom after multiple attempts")
            return False

        except Exception as e:
            logger.error(f"Error selecting stateroom: {str(e)}")
            return False