import React, { useState, useEffect, useRef } from 'react';
import { useLogContext, UILog } from '../contexts/LogContext';

interface LogViewerProps {
  maxHeight?: string;
  className?: string;
  cabinId?: number | string | null;
  title?: string;
  module?: string;
}

const LogViewer: React.FC<LogViewerProps> = ({ 
  maxHeight = '500px',
  className = '',
  cabinId = null,
  title,
  module,
}) => {
  const { connected, clearLogs, filteredLogs, getCabinLogs } = useLogContext();

  // Determine which logs to display with proper module and cabin filtering
  const displayedLogs = cabinId != null
    ? getCabinLogs(cabinId, module) 
    : filteredLogs(module);

  // Header title for this log panel
  const headerTitle = title ?? (
    cabinId != null 
      ? `Cabin ${cabinId}${module ? ` (${module})` : ''}` 
      : module 
        ? `${module} Logs` 
        : 'All Logs'
  );

  const [autoScroll, setAutoScroll] = useState(true);
  const [expandedLogs, setExpandedLogs] = useState<Record<string, boolean>>({});
  const logContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (autoScroll && logContainerRef.current) {
      logContainerRef.current.scrollTop = 0;
    }
  }, [displayedLogs, autoScroll]);

  // Toggle a log's expanded state
  const toggleExpand = (index: number) => {
    setExpandedLogs(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  // Determine the appropriate background color for a log step
  const getStepColor = (step?: string): string => {
    if (!step) return 'bg-gray-100';
    
    // Studio module specific color mapping (3-color system)
    if (module === 'Studio') {
      // Green (#e5f9c1) - Success and smooth operation
      const successSteps = [
        'browser_setup',
        'browser_ready', 
        'page_loading',
        'page_loaded',
        'Loading',
        'category_info',
        'booking_start',
        'cabin_start',
        'cabin_success',
        'cabins_processing',
        'booking_complete',
        'rate_selected',
        'cabins_find',
        'cabin_selected',
        'cabin_check'
      ];

      // Yellow (#fffac5) - Warnings (errors that can be continued or ignored)
      const warningSteps = [
        'page_load_error',
        'content_warning', 
        'wait_error',
        'Sailing_Unavailable',
        'continue_not_found',
        'handle_cabin_error'
      ];

      // Red (#ffdac9) - Errors (serious problems)
      const errorSteps = [
        'browser_error',
        'load_error',
        'cabin_failed',
        'booking_failed'
      ];

      if (successSteps.includes(step)) {
        return 'bg-studio-success';
      }
      if (warningSteps.includes(step)) {
        return 'bg-studio-warning';
      }
      if (errorSteps.includes(step)) {
        return 'bg-studio-error';
      }
      
      // Default for Studio unknown steps
      return 'bg-studio-success';
    }

    // Cruising Power module specific color mapping (3-color system)
    if (module === 'Cruising_Power') {
      // Green (#e5f9c1) - Success and smooth operation
      const successSteps = [
        'starting_browser',
        'browser_ready',
        'loading_login_page',
        'submitting_credentials',
        'logged_in',
        'getting_price_quote',
        'calculating_onboard_credit',
        'pricing_retrieved',
        'initiating_cruise_search',
        'retrieving_price_info',
        'checking_search_results',
        'evaluating_cabin_options',
        'finding_cheapest_cabin',
        'finding_cheapest_cabin_all_categories',
        'cabin_selected',
        'choosing_cabin_category',
        'analyzing_all_cabin_categories',
        'cabin_analysis_complete'
      ];

      // Yellow (#fffac5) - Warnings (retry operations, process continues)
      const warningSteps = [
        'retrying_login',
        'retrying_detailed_pricing',
        'retrying_cabin_search',
        'retrying_cabin_selection',
        'failed_to_select_cabin_category'
      ];

      // Red (#ffdac9) - Errors (serious problems)
      const errorSteps = [
        'login_error',
        'price_quote_error',
        'search_error',
        'error_selecting_cabin',
        'error_selecting_category',
        'no_cabin_data_found'
      ];

      if (successSteps.includes(step)) {
        return 'bg-studio-success';
      }
      if (warningSteps.includes(step)) {
        return 'bg-studio-warning';
      }
      if (errorSteps.includes(step)) {
        return 'bg-studio-error';
      }
      
      // Default for Cruising Power unknown steps
      return 'bg-studio-success';
    }

    // NCL module specific color mapping (3-color system)
    if (module === 'NCL') {
      // Green (#e5f9c1) - Success and smooth operation
      const successSteps = [
        'promotions',     // "Promotions applied successfully — enjoy your savings"
        'pricing',        // "Price retrieved successfully — thanks for waiting"
        'setup',          // "Browser setup complete — ready to move forward"
        'browser_ready',  // "Browser setup complete — ready to move forward"
        'login_success',  // "I am logged in successfully — ready to get started"
        'category_selection', // "Selecting cabin category — hang tight", "Cabin category selected successfully — moving ahead"
        'cruise_selection'    // "Cruise selected successfully — all set"
      ];

      // Yellow (#fffac5) - Warnings (issues that allow process to continue)
      const warningSteps = [
        'login_failed'    // "I couldn't log in — retrying now"
      ];

      // Red (#ffdac9) - Errors (serious problems)
      const errorSteps = [
        'booking',              // "There was an error during the booking process — please try again"
        'browser_setup_failed'  // "Browser setup failed — trying to recover"
      ];

      if (successSteps.includes(step)) {
        return 'bg-studio-success';
      }
      if (warningSteps.includes(step)) {
        return 'bg-studio-warning';
      }
      if (errorSteps.includes(step)) {
        return 'bg-studio-error';
      }
      
      // Default for NCL unknown steps
      return 'bg-studio-success';
    }

    // Original color mapping for other modules (NCL, Cruising Power, etc.)
    const stepColors: Record<string, string> = {
      // Setup steps
      'setup': 'bg-blue-50',
      'browser_setup': 'bg-blue-50',
      'browser_creation': 'bg-blue-100',
      'browser_context': 'bg-blue-100',
      'browser_ready': 'bg-green-100',
      'cleanup': 'bg-blue-50',
      'cleanup_complete': 'bg-green-100',
      'browser_error': 'bg-red-200',
      
      // Navigation steps
      'navigation': 'bg-purple-50',
      'page_loading': 'bg-purple-100',
      'page_loaded': 'bg-green-100',
      'page_load_error': 'bg-red-200',
      
      // Booking steps
      'booking_start': 'bg-indigo-50',
      'cruise_info': 'bg-indigo-100',
      'booking_details': 'bg-indigo-100',
      'booking_complete': 'bg-green-200',
      'booking_price': 'bg-green-100',
      'booking_failed': 'bg-red-200',
      
      // Cabin steps
      'cabin_start': 'bg-amber-50',
      'cabin_success': 'bg-green-100',
      'cabin_failed': 'bg-red-100',
      'cabin_error': 'bg-red-200',
      
      // Category steps
      'categories_start': 'bg-teal-50',
      'categories_found': 'bg-teal-100',
      'category_select': 'bg-teal-100',
      'category_selected': 'bg-teal-200',
      
      // Rate steps
      'rates_found': 'bg-lime-50',
      'rate_strategy': 'bg-lime-100',
      'rate_selected': 'bg-lime-200',
      
      // Error states
      'error': 'bg-red-200',
    };
    
    return stepColors[step] || 'bg-gray-100';
  };

  // Render a log entry with expandable details
  const renderLogEntry = (log: UILog, index: number) => {
    const isExpanded = expandedLogs[index] || false;
    const stepColor = getStepColor(log.step);
    
    return (
      <div 
        key={`${log.timestamp}-${index}`}
        className={`mb-1 p-2 rounded ${stepColor} hover:bg-opacity-90 transition-colors`}
      >
        <div 
          className="flex justify-between cursor-pointer" 
          onClick={() => toggleExpand(index)}
        >
          <div className="flex items-center">
            <span className="text-xs text-gray-500 mr-2">{log.timestamp}</span>
            <span className="text-sm">{log.message}</span>
          </div>
          {(log.cabin_id || log.step || log.category) && (
            <button className="text-gray-500 p-1">
              {isExpanded ? '▲' : '▼'}
            </button>
          )}
        </div>
        
        {isExpanded && (
          <div className="mt-2 text-xs pl-4 text-gray-600 bg-white bg-opacity-50 p-2 rounded">
            {log.cabin_id && (
              <div>
                <span className="font-medium">Cabin:</span> {log.cabin_id}
              </div>
            )}
            {log.step && (
              <div>
                <span className="font-medium">Step:</span> {log.step}
              </div>
            )}
            {log.category && (
              <div>
                <span className="font-medium">Category:</span> {log.category}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-lg  ${className}`}>
      <div className="p-3 border-b flex justify-between items-center">
        <div className="font-medium">
          {headerTitle}
          <span className={`ml-2 px-1.5 py-0.5 text-xs rounded-full ${connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {connected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
        
        <div className="flex space-x-2">
          <button
            className="text-sm bg-gray-100 text-gray-800 hover:bg-gray-200 px-2 py-1 rounded"
            onClick={clearLogs}
          >
            Clear
          </button>
          <button
            className={`text-sm px-2 py-1 rounded ${autoScroll ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}
            onClick={() => setAutoScroll(!autoScroll)}
          >
            Auto-scroll {autoScroll ? 'On' : 'Off'}
          </button>
        </div>
      </div>
      
      {/* Log container with scroll */}
      <div 
        ref={logContainerRef}
        className="overflow-y-auto p-3"
        style={{ maxHeight }}
      >
        {displayedLogs.length === 0 ? (
          <div className="text-center text-black/80 py-10">
            No logs available. Start a booking process to see logs here.
          </div>
        ) : (
          displayedLogs.map((log, index) => renderLogEntry(log, index))
        )}
      </div>
      
      {/* Log stats footer */}
      <div className="p-2 text-xs text-gray-500 border-t">
        {displayedLogs.length} logs
      </div>
    </div>
  );
};

export default LogViewer; 