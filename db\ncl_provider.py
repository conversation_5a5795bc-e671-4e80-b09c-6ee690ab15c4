"""
Database operations for the NCL provider.
"""
import json
import logging
from datetime import datetime
from typing import Dict, Tuple
from db_service import get_db_connection, db_connection

# Configure logging
logger = logging.getLogger("database.ncl_provider")

async def save_ncl_results(data: Dict, request_id: str = None, session_id: str = None):
    """Save NCL booking results to the database"""
    try:
        async with db_connection() as conn:
        
            # Clean request_id to handle spaces
            if request_id:
                request_id = request_id.strip()
                logger.info(f"Using cleaned request_id for NCL results: {request_id}")

            # Use provided request_id or get from data
            if request_id:
                data['request_id'] = request_id
            else:
                request_id = data.get('request_id', '')
                if request_id:
                    request_id = request_id.strip()
                    data['request_id'] = request_id

            if not request_id:
                logger.error("No request_id provided for NCL results")
                return False

            # Extract main booking data
            timestamp = data.get('timestamp', '')
            booking_mode = data.get('booking_mode', '')

            # Validate cabins exist and have proper data
            cabins = data.get('cabins', [])
            if not cabins:
                logger.error(f"No cabin data found in NCL results for request_id: {request_id}")
                logger.info(f"Data keys available: {list(data.keys())}")
                return False

            total_cabins = len(cabins)
            execution_time = data.get('execution_time', 0.0)

            # Extract summary data
            summary = data.get('summary', {})
            total_passengers = summary.get('total_passengers', 0)
            total_cost = summary.get('total_cost', '')
            total_final_price = summary.get('total_final_price', 0.0)

            logger.info(f"Saving NCL booking with request_id: {request_id}, cabins: {total_cabins}, total price: {total_final_price}")

            # Check if a record with this request_id and session_id already exists
            existing_booking_id = None
            if session_id:
                result = await conn.fetchrow('''
                SELECT id FROM ncl_bookings 
                WHERE request_id = $1 AND session_id = $2
                ORDER BY timestamp DESC LIMIT 1
                ''', request_id, session_id)
                if result:
                    existing_booking_id = result['id']
                    logger.info(f"Found existing booking with ID: {existing_booking_id} for request_id: {request_id}, session_id: {session_id}")

            async with conn.transaction():
                # If existing booking found with same session, update it instead of creating a new one
                if existing_booking_id:
                    await conn.execute('''
                    UPDATE ncl_bookings 
                    SET timestamp = $1, booking_mode = $2, total_cabins = $3, execution_time = $4,
                    total_passengers = $5, total_cost = $6, total_final_price = $7
                    WHERE id = $8
                    ''', 
                    timestamp,
                    booking_mode,
                    total_cabins,
                    execution_time,
                    total_passengers,
                    total_cost,
                    total_final_price,
                    existing_booking_id
                    )

                    # Delete existing cabins for this booking to avoid duplicates
                    await conn.execute('''
                    DELETE FROM ncl_cabins WHERE booking_id = $1
                    ''', existing_booking_id)

                    booking_id = existing_booking_id
                    logger.info(f"Updated existing NCL booking with ID: {booking_id}")
                else:
                    # Insert new record
                    await conn.execute('''
                    INSERT INTO ncl_bookings 
                    (request_id, timestamp, booking_mode, total_cabins, execution_time, 
                        total_passengers, total_cost, total_final_price, session_id)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    ''', 
                    request_id,
                    timestamp,
                    booking_mode,
                    total_cabins,
                    execution_time,
                    total_passengers,
                    total_cost,
                    total_final_price,
                    session_id
                    )

                    # Get the booking ID directly from database to ensure accuracy
                    result = await conn.fetchrow('''
                    SELECT id FROM ncl_bookings 
                    WHERE request_id = $1 AND session_id = $2 
                    ORDER BY id DESC LIMIT 1
                    ''', request_id, session_id)

                    if result:
                        booking_id = result['id']
                    else:
                        # This shouldn't normally happen since we just inserted the record
                        raise Exception("Failed to retrieve booking ID after insertion")

                    logger.info(f"Created new NCL booking with verified ID: {booking_id}")

                # Insert each cabin into ncl_cabins
                cabins_saved = 0
                for cabin in cabins:
                    try:
                        # Process cruise details
                        cruise_details = cabin.get('cruise_details', {})
                        ship_name = cruise_details.get('ship_name', '')
                        travel_date = cruise_details.get('travel_date', '')
                        nights = cruise_details.get('nights', 0)

                        # Process passengers
                        passengers = cabin.get('passengers', {})
                        passengers_total = passengers.get('total', 0)
                        passengers_adults = passengers.get('adults', 0)
                        passengers_children = passengers.get('children', 0)
                        passengers_infants = passengers.get('infants', 0)

                        # Process promos (convert list to string)
                        current_promos = json.dumps(cabin.get('current_promos', []))

                        await conn.execute('''
                        INSERT INTO ncl_cabins
                        (booking_id, cabin_id, ship_name, travel_date, nights, 
                        passengers_total, passengers_adults, passengers_children, passengers_infants,
                        category_code, reservation_total, non_comm_fare, savings, insurance, dining_package,
                        soda_package, beverage_package, govt_tax, onboard_credit, commission_amount, final_price,
                        current_promos, timestamp)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23)
                        ''',
                        booking_id,
                        cabin.get('cabin_id', 0),
                        ship_name,
                        travel_date,
                        nights,
                        passengers_total,
                        passengers_adults,
                        passengers_children,
                        passengers_infants,
                        cabin.get('category_code', ''),
                        cabin.get('reservation_total', 0.0),
                        cabin.get('non_comm_fare', 0.0),
                        cabin.get('savings', 0.0),
                        cabin.get('insurance', 0.0),
                        cabin.get('dining_package', 0.0),
                        cabin.get('soda_package', 0.0),
                        cabin.get('beverage_package', 0.0),
                        cabin.get('govt_tax', 0.0),
                        cabin.get('onboard_credit', 0.0),
                        cabin.get('commission_amount', 0.0),
                        cabin.get('final_price', 0.0),
                        current_promos,
                        cabin.get('timestamp', '')
                        )
                        cabins_saved += 1
                        logger.info(f"Saved cabin {cabin.get('cabin_id', 0)} data to database with booking_id: {booking_id}")
                    except Exception as cabin_error:
                        logger.error(f"Error saving cabin {cabin.get('cabin_id', 0)}: {str(cabin_error)}")
                        logger.error(f"Cabin data that failed: {cabin}")

                # Log how many cabins were saved
                if cabins_saved > 0:
                    logger.info(f"Successfully saved {cabins_saved} cabins for booking ID: {booking_id}")
                else:
                    logger.error(f"Failed to save any cabins for booking ID: {booking_id}")

                # If no cabins were saved but we have reservation data, save a placeholder
                if cabins_saved == 0 and total_final_price > 0:
                    logger.warning(f"No cabins saved despite having price data. Creating placeholder cabin for booking {booking_id}")
                    try:
                        # Create a placeholder cabin with basic information
                        await conn.execute('''
                        INSERT INTO ncl_cabins
                        (booking_id, cabin_id, category_code, reservation_total, final_price, timestamp)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ''',
                        booking_id,
                        1,
                        "PLACEHOLDER",
                        total_final_price,
                        total_final_price,
                        timestamp or datetime.now().isoformat()
                        )
                        logger.info(f"Created placeholder cabin for booking ID: {booking_id}")
                        cabins_saved = 1
                    except Exception as placeholder_error:
                        logger.error(f"Error creating placeholder cabin: {str(placeholder_error)}")

            if cabins_saved > 0:
                logger.info(f"Successfully saved NCL booking data for request_id: {request_id} with {cabins_saved} cabins")
                return True
            else:
                logger.error(f"Failed to save any cabins for NCL booking with request_id: {request_id}")
                return False
    except Exception as e:
        logger.error(f"Error saving NCL results: {e}")
        import traceback
        logger.error(f"Error traceback: {traceback.format_exc()}")
        return False

async def get_ncl_results(request_id: str, session_id: str = None) -> Tuple[Dict, Dict]:
    """Retrieve NCL booking results from the database"""
    try:
        async with db_connection() as conn:
            
            # Clean the request_id to handle potential spaces
            cleaned_request_id = request_id.strip() if request_id else ""

            logger.info(f"Searching for NCL booking with request_id: '{cleaned_request_id}', session_id: {session_id}")

            booking = None

            # First try exact match with the provided session_id
            if session_id:
                booking = await conn.fetchrow('''
                SELECT * FROM ncl_bookings 
                WHERE request_id = $1 AND session_id = $2
                ORDER BY timestamp DESC LIMIT 1
                ''', cleaned_request_id, session_id)
                if booking:
                    logger.info(f"Found exact match with request_id: '{cleaned_request_id}' and session_id: {session_id}")
            else:
                # Without session_id, just use request_id
                booking = await conn.fetchrow('''
                SELECT * FROM ncl_bookings 
                WHERE request_id = $1
                ORDER BY timestamp DESC LIMIT 1
                ''', cleaned_request_id)
                if booking:
                    logger.info(f"Found exact match with request_id: '{cleaned_request_id}'")

            # If no exact match, try with LIKE query
            if not booking:
                logger.info(f"No exact match found, trying with LIKE for request_id: '{cleaned_request_id}'")
                if session_id:
                    booking = await conn.fetchrow('''
                    SELECT * FROM ncl_bookings 
                    WHERE request_id LIKE $1 AND session_id = $2
                    ORDER BY timestamp DESC LIMIT 1
                    ''', f'%{cleaned_request_id}%', session_id)
                else:
                    booking = await conn.fetchrow('''
                    SELECT * FROM ncl_bookings 
                    WHERE request_id LIKE $1
                    ORDER BY timestamp DESC LIMIT 1
                    ''', f'%{cleaned_request_id}%')

                if booking:
                    logger.info(f"Found match with LIKE search for request_id containing: '{cleaned_request_id}'")

            # If still no match, try case-insensitive search
            if not booking:
                logger.info(f"No match found with LIKE, trying case-insensitive search for request_id: '{cleaned_request_id}'")
                if session_id:
                    booking = await conn.fetchrow('''
                    SELECT * FROM ncl_bookings 
                    WHERE LOWER(request_id) = LOWER($1) AND session_id = $2
                    ORDER BY timestamp DESC LIMIT 1
                    ''', cleaned_request_id, session_id)
                else:
                    booking = await conn.fetchrow('''
                    SELECT * FROM ncl_bookings 
                    WHERE LOWER(request_id) = LOWER($1)
                    ORDER BY timestamp DESC LIMIT 1
                    ''', cleaned_request_id)

                if booking:
                    logger.info(f"Found match with case-insensitive search for request_id: '{cleaned_request_id}'")

            # Final attempt: try to find any booking with screenshots matching this request_id
            if not booking:
                logger.info(f"No booking found directly, checking if this request_id has screenshots: '{cleaned_request_id}'")
                screenshot_booking = await conn.fetchrow('''
                SELECT DISTINCT booking_id FROM centralized_screenshots 
                WHERE request_id = $1 AND provider = 'ncl'
                ''', cleaned_request_id)

                if screenshot_booking and screenshot_booking['booking_id']:
                    booking = await conn.fetchrow('''
                    SELECT * FROM ncl_bookings 
                    WHERE id = $1
                    ''', screenshot_booking['booking_id'])
                    if booking:
                        logger.info(f"Found booking through screenshot link: '{cleaned_request_id}'")

            if not booking:
                logger.warning(f"No NCL booking found for request_id: '{cleaned_request_id}'")
                return {}, {}

            booking_dict = dict(booking)
            found_request_id = booking_dict.get('request_id', '')
            if found_request_id != cleaned_request_id:
                logger.info(f"Found NCL booking with similar request_id: '{found_request_id}' (searched for: '{cleaned_request_id}')")

            # Get all cabins for this booking
            cabins_raw = await conn.fetch('''
            SELECT * FROM ncl_cabins 
            WHERE booking_id = $1
            ''', booking['id'])

            # Format the total_final_price for display when needed
            if 'total_final_price' in booking_dict:
                booking_dict['total_final_price_raw'] = f"${booking_dict['total_final_price']:.2f}"

            # Define a helper function to format prices
            def format_price(amount):
                return f"${amount:.2f}" if amount is not None else "$0.00"

            # Convert cabins to the format expected by the frontend
            cabins = []
            for cabin_raw in cabins_raw:
                cabin = dict(cabin_raw)

                # Add the formatted values for UI display
                cabin['reservation_total_raw'] = format_price(cabin['reservation_total'])
                cabin['non_comm_fare_raw'] = format_price(cabin['non_comm_fare'])
                cabin['savings_raw'] = format_price(cabin['savings'])
                cabin['insurance_raw'] = format_price(cabin['insurance'])
                cabin['dining_package_raw'] = format_price(cabin['dining_package'])
                cabin['soda_package_raw'] = format_price(cabin.get('soda_package', 0))
                cabin['beverage_package_raw'] = format_price(cabin['beverage_package'])
                cabin['govt_tax_raw'] = format_price(cabin['govt_tax'])
                cabin['final_price_raw'] = format_price(cabin['final_price'])

                # Reconstruct the passengers dictionary
                passengers = {
                    'total': cabin['passengers_total'],
                    'adults': cabin['passengers_adults'],
                    'children': cabin['passengers_children'],
                    'infants': cabin['passengers_infants']
                }

                # Reconstruct the cruise_details dictionary
                cruise_details = {
                    'ship_name': cabin['ship_name'],
                    'travel_date': cabin['travel_date'],
                    'nights': cabin['nights']
                }

                cabin_dict = {
                    'cabin_id': cabin['cabin_id'],
                    'cruise_details': cruise_details,
                    'passengers': passengers,
                    'timestamp': cabin['timestamp'],
                    'category_code': cabin['category_code'],
                    'reservation_total': cabin['reservation_total'],
                    'non_comm_fare': cabin['non_comm_fare'],
                    'savings': cabin['savings'],
                    'insurance': cabin['insurance'],
                    'dining_package': cabin['dining_package'],
                    'soda_package': cabin.get('soda_package', 0),
                    'beverage_package': cabin['beverage_package'],
                    'govt_tax': cabin['govt_tax'],
                    'onboard_credit': cabin['onboard_credit'],
                    'commission_amount': cabin['commission_amount'],
                    'final_price': cabin['final_price'],
                    'current_promos': cabin['current_promos']
                }

                # Deserialize the JSON string back to a list
                try:
                    # Only attempt to parse current_promos if the key exists
                    if 'current_promos' in cabin and cabin['current_promos']:
                        try:
                            cabin_dict['current_promos'] = json.loads(cabin['current_promos'])
                        except (json.JSONDecodeError, TypeError):
                            # Handle any errors (e.g., if it's already a list or invalid JSON)
                            cabin_dict['current_promos'] = []
                    else:
                        # If key doesn't exist, set to empty list
                        cabin_dict['current_promos'] = []
                except Exception as e:
                    logger.warning(f"Error processing current_promos for cabin {cabin.get('cabin_id', 'unknown')}: {e}")
                    cabin_dict['current_promos'] = []

                cabins.append(cabin_dict)

            # Reconstruct the summary
            summary = {
                'total_passengers': booking_dict['total_passengers'],
                'total_cost': booking_dict['total_cost'],
                'total_final_price': booking_dict['total_final_price']
            }

            # Reconstruct the result in the format expected by the frontend
            result = {
                'timestamp': booking_dict['timestamp'],
                'booking_mode': booking_dict['booking_mode'],
                'total_cabins': booking_dict['total_cabins'],
                'cabins': cabins,
                'request_id': request_id,
                'execution_time': booking_dict['execution_time'],
                'summary': summary,
                'session_id': booking_dict.get('session_id', '')  # Include session_id if available
            }

            logger.info(f"Retrieved NCL booking data for request_id: {request_id}, found {len(cabins)} cabins")
            return booking_dict, result
    except Exception as e:
        logger.error(f"Error retrieving NCL results: {e}")
        return {}, {}