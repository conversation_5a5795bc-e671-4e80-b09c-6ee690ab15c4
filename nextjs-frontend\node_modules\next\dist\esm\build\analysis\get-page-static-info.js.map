{"version": 3, "sources": ["../../../src/build/analysis/get-page-static-info.ts"], "names": ["promises", "fs", "L<PERSON><PERSON><PERSON>", "matcher", "extractExportedConstValue", "UnsupportedValueError", "parseModule", "Log", "SERVER_RUNTIME", "checkCustomRoutes", "tryToParsePath", "isAPIRoute", "isEdgeRuntime", "RSC_MODULE_TYPES", "AUTHORIZED_EXTRA_ROUTER_PROPS", "CLIENT_MODULE_LABEL", "ACTION_MODULE_LABEL", "CLIENT_DIRECTIVE", "SERVER_ACTION_DIRECTIVE", "getRSCModuleInformation", "source", "isReactServerLayer", "clientInfoMatch", "actions<PERSON>son", "match", "actions", "Object", "values", "JSON", "parse", "undefined", "isClientRef", "type", "client", "clientRefs", "split", "clientEntryType", "server", "warnedInvalidValueMap", "runtime", "Map", "preferredRegion", "warnInvalidValue", "pageFilePath", "key", "message", "has", "warn", "set", "checkExports", "swcAST", "exportsSet", "Set", "Array", "isArray", "body", "ssr", "ssg", "generateImageMetadata", "generateSitemaps", "generateStaticParams", "extraProperties", "directives", "hasLeadingNonDirectiveNode", "node", "expression", "directive", "value", "add", "declaration", "declarations", "id", "init", "elements", "element", "push", "identifier", "specifiers", "map", "specifier", "orig", "err", "tryToReadFile", "filePath", "shouldThrow", "readFile", "encoding", "error", "getMiddlewareMatchers", "matcherOrMatchers", "nextConfig", "matchers", "i18n", "originalSourceMap", "routes", "m", "middleware", "r", "isRoot", "locales", "locale", "basePath", "rest", "parsedPage", "regexStr", "Error", "originalSource", "get", "regexp", "getMiddlewareConfig", "config", "result", "regions", "unstable_allowDynamic", "unstable_allowDynamicGlobs", "glob", "apiRouteWarnings", "max", "warnAboutExperimentalEdge", "apiRoute", "process", "env", "NODE_ENV", "NEXT_PRIVATE_BUILD_WORKER", "warnedUnsupportedValueMap", "warnAboutUnsupportedValue", "page", "path", "isDynamicMetadataRoute", "fileContent", "test", "exportsInfo", "getPageStaticInfo", "params", "isDev", "pageType", "rscInfo", "rsc", "e", "extraConfig", "prop", "includes", "stringify", "warnOnce", "resolvedRuntime", "nodejs", "options", "join", "requiresServerRuntime", "isAnAPIRoute", "replace", "experimentalEdge", "edge", "middlewareConfig", "amp"], "mappings": "AAGA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,OAAOC,cAAc,+BAA8B;AACnD,SAASC,OAAO,QAAQ,gCAA+B;AAEvD,SACEC,yBAAyB,EACzBC,qBAAqB,QAChB,wBAAuB;AAC9B,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,YAAYC,SAAS,gBAAe;AACpC,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,iBAAiB,QAAQ,+BAA8B;AAChE,SAASC,cAAc,QAAQ,8BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAwB;AACnD,SAASC,aAAa,QAAQ,4BAA2B;AACzD,SAASC,gBAAgB,QAAQ,6BAA4B;AAG7D,qCAAqC;AACrC,4DAA4D;AAC5D,MAAMC,gCAAgC;IAAC;CAAc;AA4BrD,MAAMC,sBACJ;AAEF,MAAMC,sBACJ;AAEF,MAAMC,mBAAmB;AACzB,MAAMC,0BAA0B;AAGhC,OAAO,SAASC,wBACdC,MAAc,EACdC,kBAA2B;QAiBRC;IAfnB,MAAMC,cAAcH,OAAOI,KAAK,CAACR;IACjC,MAAMS,UAAUF,cACXG,OAAOC,MAAM,CAACC,KAAKC,KAAK,CAACN,WAAW,CAAC,EAAE,KACxCO;IACJ,MAAMR,kBAAkBF,OAAOI,KAAK,CAACT;IACrC,MAAMgB,cAAc,CAAC,CAACT;IAEtB,IAAI,CAACD,oBAAoB;QACvB,OAAO;YACLW,MAAMnB,iBAAiBoB,MAAM;YAC7BR;YACAM;QACF;IACF;IAEA,MAAMG,aAAaZ,oCAAAA,oBAAAA,eAAiB,CAAC,EAAE,qBAApBA,kBAAsBa,KAAK,CAAC;IAC/C,MAAMC,kBAAkBd,mCAAAA,eAAiB,CAAC,EAAE;IAE5C,MAAMU,OAAOE,aAAarB,iBAAiBoB,MAAM,GAAGpB,iBAAiBwB,MAAM;IAE3E,OAAO;QACLL;QACAP;QACAS;QACAE;QACAL;IACF;AACF;AAEA,MAAMO,wBAAwB;IAC5BC,SAAS,IAAIC;IACbC,iBAAiB,IAAID;AACvB;AACA,SAASE,iBACPC,YAAoB,EACpBC,GAAuC,EACvCC,OAAe;IAEf,IAAIP,qBAAqB,CAACM,IAAI,CAACE,GAAG,CAACH,eAAe;IAElDpC,IAAIwC,IAAI,CACN,CAAC,uCAAuC,EAAEH,IAAI,aAAa,EAAED,aAAa,KAAK,EAAEE,QAAQ,CAAC,CAAC,GACzF,OACA;IAGJP,qBAAqB,CAACM,IAAI,CAACI,GAAG,CAACL,cAAc;AAC/C;AACA;;;;;;CAMC,GACD,SAASM,aACPC,MAAW,EACXP,YAAoB;IAYpB,MAAMQ,aAAa,IAAIC,IAAY;QACjC;QACA;QACA;QACA;QACA;KACD;IACD,IAAIC,MAAMC,OAAO,CAACJ,0BAAAA,OAAQK,IAAI,GAAG;QAC/B,IAAI;YACF,IAAIhB;YACJ,IAAIE;YACJ,IAAIe,MAAe;YACnB,IAAIC,MAAe;YACnB,IAAIC,wBAAiC;YACrC,IAAIC,mBAA4B;YAChC,IAAIC,uBAAuB;YAC3B,IAAIC,kBAAkB,IAAIT;YAC1B,IAAIU,aAAa,IAAIV;YACrB,IAAIW,6BAA6B;YAEjC,KAAK,MAAMC,QAAQd,OAAOK,IAAI,CAAE;oBAoB5BS,mBA2BAA,oBACeA,8BAYfA;gBA3DF,iEAAiE;gBACjE,IACEA,KAAKhC,IAAI,KAAK,yBACdgC,KAAKC,UAAU,CAACjC,IAAI,KAAK,iBACzB;oBACA,IAAI,CAAC+B,4BAA4B;wBAC/B,MAAMG,YAAYF,KAAKC,UAAU,CAACE,KAAK;wBACvC,IAAIlD,qBAAqBiD,WAAW;4BAClCJ,WAAWM,GAAG,CAAC;wBACjB;wBACA,IAAIlD,4BAA4BgD,WAAW;4BACzCJ,WAAWM,GAAG,CAAC;wBACjB;oBACF;gBACF,OAAO;oBACLL,6BAA6B;gBAC/B;gBACA,IACEC,KAAKhC,IAAI,KAAK,uBACdgC,EAAAA,oBAAAA,KAAKK,WAAW,qBAAhBL,kBAAkBhC,IAAI,MAAK,uBAC3B;wBAC0BgC;oBAA1B,KAAK,MAAMK,gBAAeL,qBAAAA,KAAKK,WAAW,qBAAhBL,mBAAkBM,YAAY,CAAE;wBACxD,IAAID,YAAYE,EAAE,CAACJ,KAAK,KAAK,WAAW;4BACtC5B,UAAU8B,YAAYG,IAAI,CAACL,KAAK;wBAClC,OAAO,IAAIE,YAAYE,EAAE,CAACJ,KAAK,KAAK,mBAAmB;4BACrD,IAAIE,YAAYG,IAAI,CAACxC,IAAI,KAAK,mBAAmB;gCAC/C,MAAMyC,WAAqB,EAAE;gCAC7B,KAAK,MAAMC,WAAWL,YAAYG,IAAI,CAACC,QAAQ,CAAE;oCAC/C,MAAM,EAAER,UAAU,EAAE,GAAGS;oCACvB,IAAIT,WAAWjC,IAAI,KAAK,iBAAiB;wCACvC;oCACF;oCACAyC,SAASE,IAAI,CAACV,WAAWE,KAAK;gCAChC;gCACA1B,kBAAkBgC;4BACpB,OAAO;gCACLhC,kBAAkB4B,YAAYG,IAAI,CAACL,KAAK;4BAC1C;wBACF,OAAO;4BACLN,gBAAgBO,GAAG,CAACC,YAAYE,EAAE,CAACJ,KAAK;wBAC1C;oBACF;gBACF;gBAEA,IACEH,KAAKhC,IAAI,KAAK,uBACdgC,EAAAA,qBAAAA,KAAKK,WAAW,qBAAhBL,mBAAkBhC,IAAI,MAAK,yBAC3BmB,WAAWL,GAAG,EAACkB,+BAAAA,KAAKK,WAAW,CAACO,UAAU,qBAA3BZ,6BAA6BG,KAAK,GACjD;oBACA,MAAMI,KAAKP,KAAKK,WAAW,CAACO,UAAU,CAACT,KAAK;oBAC5CV,MAAMc,OAAO;oBACbf,MAAMe,OAAO;oBACbb,wBAAwBa,OAAO;oBAC/BZ,mBAAmBY,OAAO;oBAC1BX,uBAAuBW,OAAO;gBAChC;gBAEA,IACEP,KAAKhC,IAAI,KAAK,uBACdgC,EAAAA,qBAAAA,KAAKK,WAAW,qBAAhBL,mBAAkBhC,IAAI,MAAK,uBAC3B;wBACWgC,iCAAAA;oBAAX,MAAMO,MAAKP,qBAAAA,KAAKK,WAAW,sBAAhBL,kCAAAA,mBAAkBM,YAAY,CAAC,EAAE,qBAAjCN,gCAAmCO,EAAE,CAACJ,KAAK;oBACtD,IAAIhB,WAAWL,GAAG,CAACyB,KAAK;wBACtBd,MAAMc,OAAO;wBACbf,MAAMe,OAAO;wBACbb,wBAAwBa,OAAO;wBAC/BZ,mBAAmBY,OAAO;wBAC1BX,uBAAuBW,OAAO;oBAChC;gBACF;gBAEA,IAAIP,KAAKhC,IAAI,KAAK,0BAA0B;oBAC1C,MAAML,SAASqC,KAAKa,UAAU,CAACC,GAAG,CAChC,CAACC;4BAECA,iBACAA;+BAFAA,UAAU/C,IAAI,KAAK,qBACnB+C,EAAAA,kBAAAA,UAAUC,IAAI,qBAAdD,gBAAgB/C,IAAI,MAAK,kBACzB+C,mBAAAA,UAAUC,IAAI,qBAAdD,iBAAgBZ,KAAK;;oBAGzB,KAAK,MAAMA,SAASxC,OAAQ;wBAC1B,IAAI,CAAC8B,OAAOU,UAAU,kBAAkBV,MAAM;wBAC9C,IAAI,CAACD,OAAOW,UAAU,sBAAsBX,MAAM;wBAClD,IAAI,CAACE,yBAAyBS,UAAU,yBACtCT,wBAAwB;wBAC1B,IAAI,CAACC,oBAAoBQ,UAAU,oBACjCR,mBAAmB;wBACrB,IAAI,CAACC,wBAAwBO,UAAU,wBACrCP,uBAAuB;wBACzB,IAAI,CAACrB,WAAW4B,UAAU,WACxBzB,iBACEC,cACA,WACA;wBAEJ,IAAI,CAACF,mBAAmB0B,UAAU,mBAChCzB,iBACEC,cACA,mBACA;oBAEN;gBACF;YACF;YAEA,OAAO;gBACLa;gBACAC;gBACAlB;gBACAE;gBACAiB;gBACAC;gBACAC;gBACAC;gBACAC;YACF;QACF,EAAE,OAAOmB,KAAK,CAAC;IACjB;IAEA,OAAO;QACLxB,KAAK;QACLD,KAAK;QACLjB,SAAST;QACTW,iBAAiBX;QACjB4B,uBAAuB;QACvBC,kBAAkB;QAClBC,sBAAsB;QACtBC,iBAAiB/B;QACjBgC,YAAYhC;IACd;AACF;AAEA,eAAeoD,cAAcC,QAAgB,EAAEC,WAAoB;IACjE,IAAI;QACF,OAAO,MAAMnF,GAAGoF,QAAQ,CAACF,UAAU;YACjCG,UAAU;QACZ;IACF,EAAE,OAAOC,OAAY;QACnB,IAAIH,aAAa;YACfG,MAAM1C,OAAO,GAAG,CAAC,mCAAmC,EAAEsC,SAAS,GAAG,EAAEI,MAAM1C,OAAO,CAAC,CAAC;YACnF,MAAM0C;QACR;IACF;AACF;AAEA,OAAO,SAASC,sBACdC,iBAA0B,EAC1BC,UAAsB;IAEtB,IAAIC,WAAsB,EAAE;IAC5B,IAAItC,MAAMC,OAAO,CAACmC,oBAAoB;QACpCE,WAAWF;IACb,OAAO;QACLE,SAAShB,IAAI,CAACc;IAChB;IACA,MAAM,EAAEG,IAAI,EAAE,GAAGF;IAEjB,MAAMG,oBAAoB,IAAIrD;IAC9B,IAAIsD,SAASH,SAASb,GAAG,CAAC,CAACiB;QACzB,IAAIC,aAAc,OAAOD,MAAM,WAAW;YAAE3E,QAAQ2E;QAAE,IAAIA;QAC1D,IAAIC,YAAY;YACdH,kBAAkB7C,GAAG,CAACgD,YAAYA,WAAW5E,MAAM;QACrD;QACA,OAAO4E;IACT;IAEA,yDAAyD;IACzD,uBAAuB;IACvBvF,kBAAkBqF,QAAQ;IAE1BA,SAASA,OAAOhB,GAAG,CAAC,CAACmB;QACnB,IAAI,EAAE7E,MAAM,EAAE,GAAG6E;QAEjB,MAAMC,SAAS9E,WAAW;QAE1B,IAAIwE,CAAAA,wBAAAA,KAAMO,OAAO,KAAIF,EAAEG,MAAM,KAAK,OAAO;YACvChF,SAAS,CAAC,yCAAyC,EACjD8E,SAAS,KAAK9E,OACf,CAAC;QACJ;QAEAA,SAAS,CAAC,gCAAgC,EAAEA,OAAO,EACjD8E,SACI,CAAC,CAAC,EAAER,WAAWE,IAAI,GAAG,cAAc,GAAG,wBAAwB,CAAC,GAChE,WACL,CAAC;QAEF,IAAIF,WAAWW,QAAQ,EAAE;YACvBjF,SAAS,CAAC,EAAEsE,WAAWW,QAAQ,CAAC,EAAEjF,OAAO,CAAC;QAC5C;QAEA6E,EAAE7E,MAAM,GAAGA;QACX,OAAO6E;IACT;IAEAxF,kBAAkBqF,QAAQ;IAE1B,OAAOA,OAAOhB,GAAG,CAAC,CAACmB;QACjB,MAAM,EAAE7E,MAAM,EAAE,GAAGkF,MAAM,GAAGL;QAC5B,MAAMM,aAAa7F,eAAeU;QAElC,IAAImF,WAAWhB,KAAK,IAAI,CAACgB,WAAWC,QAAQ,EAAE;YAC5C,MAAM,IAAIC,MAAM,CAAC,gBAAgB,EAAErF,OAAO,CAAC;QAC7C;QAEA,MAAMsF,iBAAiBb,kBAAkBc,GAAG,CAACV;QAE7C,OAAO;YACL,GAAGK,IAAI;YACPM,QAAQL,WAAWC,QAAQ;YAC3BE,gBAAgBA,kBAAkBtF;QACpC;IACF;AACF;AAEA,SAASyF,oBACPlE,YAAoB,EACpBmE,MAAW,EACXpB,UAAsB;IAEtB,MAAMqB,SAAoC,CAAC;IAE3C,IAAID,OAAO3G,OAAO,EAAE;QAClB4G,OAAOpB,QAAQ,GAAGH,sBAAsBsB,OAAO3G,OAAO,EAAEuF;IAC1D;IAEA,IAAI,OAAOoB,OAAOE,OAAO,KAAK,YAAY3D,MAAMC,OAAO,CAACwD,OAAOE,OAAO,GAAG;QACvED,OAAOC,OAAO,GAAGF,OAAOE,OAAO;IACjC,OAAO,IAAI,OAAOF,OAAOE,OAAO,KAAK,aAAa;QAChDzG,IAAIwC,IAAI,CACN,CAAC,4FAA4F,EAAEJ,aAAa,CAAC,CAAC;IAElH;IAEA,IAAImE,OAAOG,qBAAqB,EAAE;QAChCF,OAAOG,0BAA0B,GAAG7D,MAAMC,OAAO,CAC/CwD,OAAOG,qBAAqB,IAE1BH,OAAOG,qBAAqB,GAC5B;YAACH,OAAOG,qBAAqB;SAAC;QAClC,KAAK,MAAME,QAAQJ,OAAOG,0BAA0B,IAAI,EAAE,CAAE;YAC1D,IAAI;gBACF/G,QAAQgH;YACV,EAAE,OAAOlC,KAAK;gBACZ,MAAM,IAAIwB,MACR,CAAC,EAAE9D,aAAa,mEAAmE,EAAEwE,KAAK,GAAG,EAC3F,AAAClC,IAAcpC,OAAO,CACvB,CAAC;YAEN;QACF;IACF;IAEA,OAAOkE;AACT;AAEA,MAAMK,mBAAmB,IAAIlH,SAAS;IAAEmH,KAAK;AAAI;AACjD,SAASC,0BAA0BC,QAAuB;IACxD,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBF,QAAQC,GAAG,CAACE,yBAAyB,KAAK,KAC1C;QACA;IACF;IACA,IAAIP,iBAAiBtE,GAAG,CAACyE,WAAW;QAClC;IACF;IACAhH,IAAIwC,IAAI,CACNwE,WACI,CAAC,EAAEA,SAAS,2EAA2E,CAAC,GACxF,CAAC,iEAAiE,CAAC;IAEzEH,iBAAiBpE,GAAG,CAACuE,UAAU;AACjC;AAEA,MAAMK,4BAA4B,IAAI1H,SAA0B;IAAEmH,KAAK;AAAI;AAE3E,SAASQ,0BACPlF,YAAoB,EACpBmF,IAAwB,EACxBvC,KAA4B;IAE5B,IAAIqC,0BAA0B9E,GAAG,CAACH,eAAe;QAC/C;IACF;IAEApC,IAAIwC,IAAI,CACN,CAAC,yDAAyD,CAAC,GACxD+E,CAAAA,OAAO,CAAC,OAAO,EAAEA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEnF,aAAa,CAAC,CAAC,AAAD,IAC9C,QACA4C,MAAM1C,OAAO,GACZ0C,CAAAA,MAAMwC,IAAI,GAAG,CAAC,KAAK,EAAExC,MAAMwC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAC,IACvC,QACA,+CACA;IAGJH,0BAA0B5E,GAAG,CAACL,cAAc;AAC9C;AAEA,iEAAiE;AACjE,sDAAsD;AACtD,OAAO,eAAeqF,uBACpBrF,YAAoB;IAEpB,MAAMsF,cAAc,AAAC,MAAM/C,cAAcvC,cAAc,SAAU;IACjE,IAAI,CAAC,yCAAyCuF,IAAI,CAACD,cAAc,OAAO;IAExE,MAAM/E,SAAS,MAAM5C,YAAYqC,cAAcsF;IAC/C,MAAME,cAAclF,aAAaC,QAAQP;IAEzC,OAAO,CAACwF,YAAYzE,qBAAqB,IAAI,CAACyE,YAAYxE,gBAAgB;AAC5E;AAEA;;;;;;CAMC,GACD,OAAO,eAAeyE,kBAAkBC,MAMvC;IACC,MAAM,EAAEC,KAAK,EAAE3F,YAAY,EAAE+C,UAAU,EAAEoC,IAAI,EAAES,QAAQ,EAAE,GAAGF;IAE5D,MAAMJ,cAAc,AAAC,MAAM/C,cAAcvC,cAAc,CAAC2F,UAAW;IACnE,IACE,8FAA8FJ,IAAI,CAChGD,cAEF;QACA,MAAM/E,SAAS,MAAM5C,YAAYqC,cAAcsF;QAC/C,MAAM,EACJxE,GAAG,EACHD,GAAG,EACHjB,OAAO,EACPE,eAAe,EACfmB,oBAAoB,EACpBC,eAAe,EACfC,UAAU,EACX,GAAGb,aAAaC,QAAQP;QACzB,MAAM6F,UAAUrH,wBAAwB8G,aAAa;QACrD,MAAMQ,MAAMD,QAAQxG,IAAI;QAExB,sCAAsC;QACtC,IAAI8E;QACJ,IAAI;YACFA,SAAS1G,0BAA0B8C,QAAQ;QAC7C,EAAE,OAAOwF,GAAG;YACV,IAAIA,aAAarI,uBAAuB;gBACtCwH,0BAA0BlF,cAAcmF,MAAMY;YAChD;QACA,mFAAmF;QACrF;QAEA,MAAMC,cAAmC,CAAC;QAE1C,IAAI9E,mBAAmB0E,aAAa,OAAO;YACzC,KAAK,MAAMK,QAAQ/E,gBAAiB;gBAClC,IAAI,CAAC/C,8BAA8B+H,QAAQ,CAACD,OAAO;gBACnD,IAAI;oBACFD,WAAW,CAACC,KAAK,GAAGxI,0BAA0B8C,QAAQ0F;gBACxD,EAAE,OAAOF,GAAG;oBACV,IAAIA,aAAarI,uBAAuB;wBACtCwH,0BAA0BlF,cAAcmF,MAAMY;oBAChD;gBACF;YACF;QACF,OAAO,IAAIH,aAAa,SAAS;YAC/B,IAAK,MAAM3F,OAAOkE,OAAQ;gBACxB,IAAI,CAAChG,8BAA8B+H,QAAQ,CAACjG,MAAM;gBAClD+F,WAAW,CAAC/F,IAAI,GAAGkE,MAAM,CAAClE,IAAI;YAChC;QACF;QAEA,IAAI2F,aAAa,OAAO;YACtB,IAAIzB,QAAQ;gBACV,IAAIjE,UAAU,CAAC,eAAe,EAAEF,aAAa,qEAAqE,CAAC;gBAEnH,IAAImE,OAAOvE,OAAO,EAAE;oBAClBM,WAAW,CAAC,+BAA+B,EAAEjB,KAAKkH,SAAS,CACzDhC,OAAOvE,OAAO,EACd,EAAE,CAAC;gBACP;gBAEA,IAAIuE,OAAOE,OAAO,EAAE;oBAClBnE,WAAW,CAAC,uCAAuC,EAAEjB,KAAKkH,SAAS,CACjEhC,OAAOE,OAAO,EACd,EAAE,CAAC;gBACP;gBAEAnE,WAAW,CAAC,6GAA6G,CAAC;gBAE1H,IAAIyF,OAAO;oBACT/H,IAAIwI,QAAQ,CAAClG;gBACf,OAAO;oBACL,MAAM,IAAI4D,MAAM5D;gBAClB;gBACAiE,SAAS,CAAC;YACZ;QACF;QACA,IAAI,CAACA,QAAQA,SAAS,CAAC;QAEvB,4FAA4F;QAC5F,4EAA4E;QAC5E,iGAAiG;QACjG,yBAAyB;QACzB,IAAIkC;QACJ,IAAIT,aAAa,OAAO;YACtBS,kBAAkBzG;QACpB,OAAO;YACLyG,kBAAkBzG,WAAWuE,OAAOvE,OAAO;QAC7C;QAEA,IACE,OAAOyG,oBAAoB,eAC3BA,oBAAoBxI,eAAeyI,MAAM,IACzC,CAACrI,cAAcoI,kBACf;YACA,MAAME,UAAUxH,OAAOC,MAAM,CAACnB,gBAAgB2I,IAAI,CAAC;YACnD,MAAMtG,UACJ,OAAOmG,oBAAoB,WACvB,CAAC,iFAAiF,EAAEE,QAAQ,CAAC,GAC7F,CAAC,kBAAkB,EAAEF,gBAAgB,4DAA4D,EAAEE,QAAQ,CAAC;YAClH,IAAIZ,OAAO;gBACT/H,IAAIgF,KAAK,CAAC1C;YACZ,OAAO;gBACL,MAAM,IAAI4D,MAAM5D;YAClB;QACF;QAEA,MAAMuG,wBAAwB5F,OAAOC,OAAO8E,aAAa;QAEzD,MAAMc,eAAe1I,WAAWmH,wBAAAA,KAAMwB,OAAO,CAAC,wBAAwB;QAEtEN,kBACEpI,cAAcoI,oBAAoBI,wBAC9BJ,kBACAlH;QAEN,IAAIkH,oBAAoBxI,eAAe+I,gBAAgB,EAAE;YACvDjC,0BAA0B+B,eAAevB,OAAQ;QACnD;QAEA,IACEkB,oBAAoBxI,eAAegJ,IAAI,IACvCjB,aAAa,WACbT,QACA,CAACuB,cACD;YACA,MAAMxG,UAAU,CAAC,KAAK,EAAEiF,KAAK,4HAA4H,CAAC;YAC1J,IAAIQ,OAAO;gBACT/H,IAAIgF,KAAK,CAAC1C;YACZ,OAAO;gBACL,MAAM,IAAI4D,MAAM5D;YAClB;QACF;QAEA,MAAM4G,mBAAmB5C,oBACvBiB,QAAQ,6BACRhB,QACApB;QAGF,IACE6C,aAAa,UACbzE,8BAAAA,WAAYhB,GAAG,CAAC,cAChBc,sBACA;YACA,MAAM,IAAI6C,MACR,CAAC,MAAM,EAAEqB,KAAK,4EAA4E,CAAC;QAE/F;QAEA,OAAO;YACLtE;YACAC;YACAgF;YACA7E;YACA8F,KAAK5C,OAAO4C,GAAG,IAAI;YACnB,GAAID,oBAAoB;gBAAEzD,YAAYyD;YAAiB,CAAC;YACxD,GAAIT,mBAAmB;gBAAEzG,SAASyG;YAAgB,CAAC;YACnDvG;YACAkG;QACF;IACF;IAEA,OAAO;QACLnF,KAAK;QACLC,KAAK;QACLgF,KAAK5H,iBAAiBwB,MAAM;QAC5BuB,sBAAsB;QACtB8F,KAAK;QACLnH,SAAST;IACX;AACF"}