export interface CruiseTimeData {
  quotes: number;
  manualTime: string;
  oceanmindTime: string;
}

export interface CruiseLine {
  id: string;
  name: string;
  count: number;
  color?: string;
}

export interface CabinProcessingTime {
  singleCabin: {
    manual: string;
    oceanmind: string;
    quotes: number;
  };
  multipleCabin: {
    manual: string;
    oceanmind: string;
    quotes: number;
  };
}

export interface Agency {
  id: string;
  name: string;
  quotes: number;
  processingTimes: CabinProcessingTime;
  color: string;
}

// Color palette for visualization
export const COLORS = {
  royal: '#0088FE',
  celebrity: '#00C49F',
  princess: '#FFBB28',
  norwegian: '#FF8042',
  carnival: '#8884D8',
  msc: '#4BC0C0',
  virgin: '#C27D90',
  oceania: '#7353BA',
  explora: '#FFCE56',
  seabourn: '#36A2EB',
  holland: '#FF6384',
  cunard: '#A3D6D0',
  azamara: '#FF9E80',
  regent: '#B388FF',
  viking: '#82B1FF',

  portalA: '#2A9D8F',
  portalB: '#E76F51'
};

// All Cruise Lines Data
const cruiseLines: CruiseLine[] = [
  {
    id: 'royal',
    name: 'Royal Caribbean',
    count: 111 + 120,
    color: COLORS.royal
  },
  {
    id: 'celebrity',
    name: 'Celebrity Cruises',
    count: 100 + 12,
    color: COLORS.celebrity
  },
  {
    id: 'princess',
    name: 'Princess Cruises',
    count: 100,
    color: COLORS.princess
  },
  {
    id: 'norwegian',
    name: 'Norwegian Cruise Line',
    count: 10,
    color: COLORS.norwegian
  },
  {
    id: 'explora',
    name: 'Explora Journeys',
    count: 37,
    color: COLORS.explora
  },
  {
    id: 'msc',
    name: 'MSC Cruises',
    count: 31,
    color: COLORS.msc
  },
  {
    id: 'oceania',
    name: 'Oceania Cruises',
    count: 96,
    color: COLORS.oceania
  },
  {
    id: 'seabourn',
    name: 'Seabourn',
    count: 25,
    color: COLORS.seabourn
  },
  {
    id: 'virgin',
    name: 'Virgin Voyages',
    count: 14,
    color: COLORS.virgin
  },
  {
    id: 'windstar',
    name: 'Windstar Cruises',
    count: 10,
    color: COLORS.holland
  },
  {
    id: 'carnival',
    name: 'Carnival Cruise Line',
    count: 64,
    color: COLORS.carnival
  },
  {
    id: 'costa',
    name: 'Costa Cruise Line',
    count: 42,
    color: COLORS.cunard
  }
];

// Booking Agency Data with detailed processing times
const agencies: Agency[] = [
  {
    id: 'portalA',
    name: 'Portal A',
    quotes: 609, // Total quotes from both tables
    processingTimes: {
      singleCabin: {
        manual: '00:03:31', // Average of 3:48 and 3:14
        oceanmind: '00:02:15', // Average of 2:26 and 2:05
        quotes: 467 // Total single cabin quotes
      },
      multipleCabin: {
        manual: '00:04:15', // Average of 4:40 and 3:51
        oceanmind: '00:02:08', // Average of 2:20 and 1:57
        quotes: 125 // Total multiple cabin quotes
      }
    },
    color: COLORS.portalA
  },
  {
    id: 'portalB',
    name: 'Portal B',
    quotes: 142,
    processingTimes: {
      singleCabin: {
        manual: '00:03:29',
        oceanmind: '00:01:22',
        quotes: 86
      },
      multipleCabin: {
        manual: '00:03:25',
        oceanmind: '00:01:15',
        quotes: 56
      }
    },
    color: COLORS.portalB
  }
];

// Get top N cruise lines
export function getTopCruiseLines(count: number = 5): CruiseLine[] {
  return [...cruiseLines]
    .sort((a, b) => b.count - a.count)
    .slice(0, count);
}

// Get all cruise lines
export function getAllCruiseLines(): CruiseLine[] {
  return cruiseLines;
}

// Get all agencies
export const getAgencies = (): Agency[] => {
  return agencies;
};

// Convert time string (00:00:00) to minutes
export function timeToMinutes(timeString: string): number {
  if (!timeString || timeString === "00:00:00") return 0;

  const [hours, minutes, seconds] = timeString.split(':').map(Number);
  return hours * 60 + minutes + seconds / 60;
}

// Calculate time savings percentage
export function calculateTimeSavings(manualTime: string, oceanmindTime: string): number {
  const manualMinutes = timeToMinutes(manualTime);
  const oceanmindMinutes = timeToMinutes(oceanmindTime);

  if (manualMinutes === 0) return 0;

  return Math.round(((manualMinutes - oceanmindMinutes) / manualMinutes) * 100);
}

// Calculate time saving percentage between agencies
export const calculateTimeSavingPercentage = (agency1Time: number, agency2Time: number): number => {
  if (agency1Time <= 0 || agency2Time <= 0) return 0;
  const difference = agency2Time - agency1Time;
  return Math.round((difference / agency2Time) * 100);
};

export default {
  getAllCruiseLines,
  getTopCruiseLines,
  getAgencies,
  calculateTimeSavings,
  calculateTimeSavingPercentage,
  timeToMinutes
};