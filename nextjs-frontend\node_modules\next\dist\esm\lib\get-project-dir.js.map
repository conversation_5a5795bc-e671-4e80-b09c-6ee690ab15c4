{"version": 3, "sources": ["../../src/lib/get-project-dir.ts"], "names": ["path", "commands", "Log", "detectTypo", "realpathSync", "getProjectDir", "dir", "resolvedDir", "resolve", "realDir", "toLowerCase", "warn", "err", "code", "detectedTypo", "Object", "keys", "error", "process", "exit"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,QAAQ,QAAQ,aAAY;AACrC,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,UAAU,QAAQ,gBAAe;AAC1C,SAASC,YAAY,QAAQ,aAAY;AAEzC,OAAO,SAASC,cAAcC,GAAY;IACxC,IAAI;QACF,MAAMC,cAAcP,KAAKQ,OAAO,CAACF,OAAO;QACxC,MAAMG,UAAUL,aAAaG;QAE7B,IACEA,gBAAgBE,WAChBF,YAAYG,WAAW,OAAOD,QAAQC,WAAW,IACjD;YACAR,IAAIS,IAAI,CACN,CAAC,kDAAkD,EAAEJ,YAAY,aAAa,EAAEE,QAAQ,gFAAgF,CAAC;QAE7K;QAEA,OAAOA;IACT,EAAE,OAAOG,KAAU;QACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;YACzB,IAAI,OAAOP,QAAQ,UAAU;gBAC3B,MAAMQ,eAAeX,WAAWG,KAAKS,OAAOC,IAAI,CAACf;gBAEjD,IAAIa,cAAc;oBAChBZ,IAAIe,KAAK,CACP,CAAC,MAAM,EAAEX,IAAI,qCAAqC,EAAEQ,aAAa,EAAE,CAAC;oBAEtEI,QAAQC,IAAI,CAAC;gBACf;YACF;YAEAjB,IAAIe,KAAK,CACP,CAAC,uDAAuD,EAAEjB,KAAKQ,OAAO,CACpEF,OAAO,KACP,CAAC;YAELY,QAAQC,IAAI,CAAC;QACf;QACA,MAAMP;IACR;AACF"}