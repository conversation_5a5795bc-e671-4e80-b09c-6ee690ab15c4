"""
Redis Service Layer

Provides caching, session management, pub/sub, and other Redis functionality
for the OceanMind cruise booking application.
"""

import json
import os
import redis.asyncio as redis
import logging
from typing import Optional, Any, Dict, List
from datetime import timedelta
from dotenv import load_dotenv

logger = logging.getLogger("redis_service")
load_dotenv()
class RedisService:
    def __init__(self):
        ENVIRONMENT = os.getenv('ENVIRONMENT')
        if ENVIRONMENT == 'production':
            self.redis_url = os.getenv("REDIS_URL_PROD")
        else:
            self.redis_url = os.getenv("REDIS_URL_DEV")
        self.redis_db = int(os.getenv("REDIS_DB", 0))
        self.redis = None
        self.connection_pool = None
    
    async def initialize(self):
        """Initialize Redis connection pool"""
        try:
            self.connection_pool = redis.ConnectionPool.from_url(
                self.redis_url,
                db=self.redis_db,
                max_connections=20,
                decode_responses=True
            )
            self.redis = redis.Redis(connection_pool=self.connection_pool)
            
            # Test connection
            await self.redis.ping()
            logger.info(f"✅ Redis connected successfully to {self.redis_url}")
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to Redis: {e}")
            raise
    
    async def get_redis(self) -> redis.Redis:
        """Get Redis client"""
        if not self.redis:
            await self.initialize()
        return self.redis
    
    async def close(self):
        """Close Redis connections"""
        try:
            if self.redis:
                await self.redis.close()
            if self.connection_pool:
                await self.connection_pool.disconnect()
            logger.info("Redis connection pool closed")
        except Exception as e:
            logger.error(f"Error closing Redis connections: {e}")
    
    # Basic caching operations
    async def cache_set(self, key: str, value: Any, ttl: int = 3600):
        """Set cache with TTL"""
        try:
            redis = await self.get_redis()
            serialized_value = json.dumps(value, default=str)
            await redis.setex(key, ttl, serialized_value)
            logger.debug(f"Cached key: {key} with TTL: {ttl}")
        except Exception as e:
            logger.error(f"Error setting cache for key {key}: {e}")
            raise
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """Get from cache"""
        try:
            redis = await self.get_redis()
            data = await redis.get(key)
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"Error getting cache for key {key}: {e}")
            return None
    
    async def cache_delete(self, key: str):
        """Delete from cache"""
        try:
            redis = await self.get_redis()
            await redis.delete(key)
            logger.debug(f"Deleted cache key: {key}")
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
    
    async def cache_exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            redis = await self.get_redis()
            return bool(await redis.exists(key))
        except Exception as e:
            logger.error(f"Error checking cache existence for key {key}: {e}")
            return False
    
    # Session management
    async def set_session(self, session_id: str, session_data: Dict, ttl: int = 3600):
        """Store session data with TTL"""
        key = f"session:{session_id}"
        await self.cache_set(key, session_data, ttl)
    
    async def get_session(self, session_id: str) -> Optional[Dict]:
        """Retrieve session data"""
        key = f"session:{session_id}"
        return await self.cache_get(key)
    
    async def delete_session(self, session_id: str):
        """Delete session data"""
        key = f"session:{session_id}"
        await self.cache_delete(key)
    
    # Pub/Sub for real-time communication
    async def publish(self, channel: str, message: Dict):
        """Publish message to channel"""
        try:
            redis = await self.get_redis()
            serialized_message = json.dumps(message, default=str)
            await redis.publish(channel, serialized_message)
            logger.debug(f"Published to channel {channel}")
        except Exception as e:
            logger.error(f"Error publishing to channel {channel}: {e}")
    
    async def subscribe(self, channels: List[str]):
        """Subscribe to channels"""
        try:
            redis = await self.get_redis()
            pubsub = redis.pubsub()
            await pubsub.subscribe(*channels)
            logger.info(f"Subscribed to channels: {channels}")
            return pubsub
        except Exception as e:
            logger.error(f"Error subscribing to channels {channels}: {e}")
            raise
    
    # Rate limiting
    async def rate_limit_check(self, key: str, limit: int, window: int) -> bool:
        """Check if request is within rate limit"""
        try:
            redis = await self.get_redis()
            current = await redis.incr(key)
            if current == 1:
                await redis.expire(key, window)
            return current <= limit
        except Exception as e:
            logger.error(f"Error checking rate limit for key {key}: {e}")
            return True  # Allow on error
    
    # Request deduplication
    async def is_duplicate_request(self, request_id: str, ttl: int = 1800) -> bool:
        """Check if request is duplicate"""
        try:
            redis = await self.get_redis()
            key = f"request_dedup:{request_id}"
            exists = await redis.exists(key)
            if not exists:
                await redis.setex(key, ttl, "processing")
            return bool(exists)
        except Exception as e:
            logger.error(f"Error checking duplicate request {request_id}: {e}")
            return False  # Allow on error
    
    # Health check
    async def health_check(self) -> Dict[str, Any]:
        """Check Redis health"""
        try:
            redis_client = await self.get_redis()
            await redis_client.ping()
            
            info = await redis_client.info()
            
            return {
                "status": "healthy",
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_human": info.get("used_memory_human", "unknown"),
                "uptime_in_seconds": info.get("uptime_in_seconds", 0)
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }

# Create global instance
redis_service = RedisService() 