import React from 'react';
import { UserTrackingData } from '../../services/api';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { Bar, Pie, Line } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

// Helper function to get provider stats
export function getProviderStats(data: UserTrackingData[]) {
  const providers: Record<string, {
    name: string,
    total: number,
    validated: number,
    reprocessed: number,
    reset: number,
    correctFlow: number,
    incorrectFlow: number
  }> = {};
  
  // Initialize providers map
  data.forEach(item => {
    if (!providers[item.provider]) {
      providers[item.provider] = {
        name: item.provider,
        total: 0,
        validated: 0,
        reprocessed: 0,
        reset: 0,
        correctFlow: 0,
        incorrectFlow: 0
      };
    }
    
    const provider = providers[item.provider];
    
    provider.total++;
    
    // Check validation status with flexible comparisons
    let isValidated = false;
    let isReset = false;
    if (typeof item.val_status === 'boolean') {
      isValidated = item.val_status === true;
    } else if (typeof item.val_status === 'number') {
      isValidated = item.val_status === 1;
    } else if (typeof item.val_status === 'string') {
      const status = item.val_status.toLowerCase();
      isValidated = ['1', 'true', 'validated'].includes(status);
      isReset = status === 'reset';
    }
    
    if (isValidated) {
      provider.validated++;
    } else if (isReset) {
      // Reset bookings are counted separately, not as reprocessed
      provider.reset = (provider.reset || 0) + 1;
      
      // Check val_mark with flexible comparisons
      let isCorrectFlow = false;
      if (typeof item.val_mark === 'boolean') isCorrectFlow = item.val_mark === true;
      else if (typeof item.val_mark === 'number') isCorrectFlow = item.val_mark === 1;
      else if (typeof item.val_mark === 'string') {
        const mark = item.val_mark as string;
        isCorrectFlow = ['1', 'true', 'correct'].includes(mark.toLowerCase());
      }
      
      let isIncorrectFlow = false;
      if (typeof item.val_mark === 'boolean') isIncorrectFlow = item.val_mark === false;
      else if (typeof item.val_mark === 'number') isIncorrectFlow = item.val_mark === 0;
      else if (typeof item.val_mark === 'string') {
        const mark = item.val_mark as string;
        isIncorrectFlow = ['0', 'false', 'incorrect'].includes(mark.toLowerCase());
      }
      
      if (isCorrectFlow) {
        provider.correctFlow++;
      } else if (isIncorrectFlow) {
        provider.incorrectFlow++;
      }
    } else {
      provider.reprocessed++;
    }
  });
  
  return Object.values(providers);
}

// Stat Card Component
export function StatCard({ 
  title, 
  value, 
  percentage = null, 
  bgColor, 
  textColor 
}: { 
  title: string, 
  value: number | string, 
  percentage?: number | null, 
  bgColor: string, 
  textColor: string 
}) {
  return (
    <div className={`${bgColor} rounded-xl p-6 shadow-sm`}>
      <h4 className={`text-sm font-medium ${textColor}`}>{title}</h4>
      <div className="flex items-center justify-between mt-2">
        {percentage !== null && (
          <div className="text-2xl px-2 py-1 rounded-full font-medium">
            {percentage}%
          </div>
        )}
        <div className={`text-2xl font-bold ${textColor}`}>{value}</div>
      </div>
    </div>
  );
}

// Progress Bar Component
export function ProgressBar({ 
  label, 
  count, 
  total, 
  color 
}: { 
  label: string, 
  count: number, 
  total: number, 
  color: string 
}) {
  const percentage = total > 0 ? Math.round((count / total) * 100) : 0;
  
  return (
    <>
      <div className="flex justify-between mb-1">
        <span className="text-sm font-medium">{label}</span>
        <span className="text-sm font-medium">{count} ({percentage}%)</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2.5">
        <div className={`${color} h-2.5 rounded-full`} style={{ width: `${percentage}%` }}></div>
      </div>
    </>
  );
}

// Main analytics component
export function ProcessedRequestAnalytics({ data, isLoading }: { data: UserTrackingData[], isLoading: boolean }) {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <svg className="animate-spin h-8 w-8 text-teal-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span className="ml-3 text-lg text-gray-600">Loading analytics data...</span>
      </div>
    );
  }

  if (!data.length) {
    return (
      <div className="bg-white rounded-xl p-8 text-center">
        <svg className="h-12 w-12 mx-auto text-gray-400 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <h3 className="text-lg font-medium text-gray-700">No data to analyze</h3>
        <p className="text-gray-500 mt-1">Try adjusting your filters or refreshing the data.</p>
      </div>
    );
  }

  // 1. Total requests
  const totalRequests = data.length;
  
  // 2. Validated vs Reprocessed vs Reset
  // Check each record and determine its validation status
  const validatedRequests = data.filter(item => {
    if (typeof item.val_status === 'boolean') return item.val_status === true;
    if (typeof item.val_status === 'number') return item.val_status === 1;
    if (typeof item.val_status === 'string') {
      const status = item.val_status.toLowerCase();
      return ['1', 'true', 'validated'].includes(status);
    }
    return false;
  }).length;
  
  const resetRequests = data.filter(item => {
    if (typeof item.val_status === 'string') {
      return item.val_status.toLowerCase() === 'reset';
    }
    return false;
  }).length;
  
  const reprocessedRequests = data.filter(item => {
    if (typeof item.val_status === 'boolean') return item.val_status === false;
    if (typeof item.val_status === 'number') return item.val_status === 0;
    if (typeof item.val_status === 'string') {
      const status = item.val_status.toLowerCase();
      return ['0', 'false', 'reprocessed'].includes(status);
    }
    return false;
  }).length;
  
  const undefinedStatus = data.filter(item => {
    return item.val_status === undefined || 
           item.val_status === null || 
           (typeof item.val_status === 'string' && item.val_status === '');
  }).length;
  
  const validatedPercentage = Math.round((validatedRequests / totalRequests) * 100);
  const resetPercentage = Math.round((resetRequests / totalRequests) * 100);
  const reprocessedPercentage = Math.round((reprocessedRequests / totalRequests) * 100);
  const undefinedPercentage = Math.round((undefinedStatus / totalRequests) * 100);
  
  // 3. For validated: Correct vs Incorrect "as per flow"
  const validatedData = data.filter(item => {
    if (typeof item.val_status === 'boolean') return item.val_status === true;
    if (typeof item.val_status === 'number') return item.val_status === 1;
    if (typeof item.val_status === 'string') {
      const status = item.val_status as string;
      return ['1', 'true', 'validated'].includes(status.toLowerCase());
    }
    return false;
  });
  
  const correctAsPerFlow = validatedData.filter(item => {
    if (typeof item.val_mark === 'string') {
      return ['1', 'true', 'correct'].includes(item.val_mark.toLowerCase());
    }
    if (typeof item.val_mark === 'boolean') return item.val_mark === true;
    if (typeof item.val_mark === 'number') return item.val_mark === 1;
    return false;
  }).length;
  
  const incorrectAsPerFlow = validatedData.filter(item => {
    if (typeof item.val_mark === 'string') {
      return ['0', 'false', 'incorrect'].includes(item.val_mark.toLowerCase());
    }
    if (typeof item.val_mark === 'boolean') return item.val_mark === false;
    if (typeof item.val_mark === 'number') return item.val_mark === 0;
    return false;
  }).length;
  
  const undefinedAsPerFlow = validatedData.filter(item => 
    item.val_mark === undefined || 
    item.val_mark === null ||
    (typeof item.val_mark === 'string' && item.val_mark === '')
  ).length;
  
  const correctFlowPercentage = validatedRequests > 0 ? Math.round((correctAsPerFlow / validatedRequests) * 100) : 0;
  const incorrectFlowPercentage = validatedRequests > 0 ? Math.round((incorrectAsPerFlow / validatedRequests) * 100) : 0;
  const undefinedFlowPercentage = validatedRequests > 0 ? Math.round((undefinedAsPerFlow / validatedRequests) * 100) : 0;
  
  // 4. For incorrect "as per flow": Correct vs Incorrect "as per feature"
  const incorrectFlowData = validatedData.filter(item => {
    if (typeof item.val_mark === 'string') {
      return ['0', 'false', 'incorrect'].includes(item.val_mark.toLowerCase());
    }
    if (typeof item.val_mark === 'boolean') return item.val_mark === false;
    if (typeof item.val_mark === 'number') return item.val_mark === 0;
    return false;
  });
  
  const correctAsPerFeature = incorrectFlowData.filter(item => {
    if (typeof item.as_per_feature === 'string') {
      return ['1', 'true', 'correct'].includes(item.as_per_feature.toLowerCase());
    }
    if (typeof item.as_per_feature === 'boolean') return item.as_per_feature === true;
    if (typeof item.as_per_feature === 'number') return item.as_per_feature === 1;
    return false;
  }).length;
  
  const incorrectAsPerFeature = incorrectFlowData.filter(item => {
    if (typeof item.as_per_feature === 'string') {
      return ['0', 'false', 'incorrect'].includes(item.as_per_feature.toLowerCase());
    }
    if (typeof item.as_per_feature === 'boolean') return item.as_per_feature === false;
    if (typeof item.as_per_feature === 'number') return item.as_per_feature === 0;
    return false;
  }).length;
  
  const undefinedAsPerFeature = incorrectFlowData.filter(item => 
    item.as_per_feature === undefined || 
    item.as_per_feature === null ||
    (typeof item.as_per_feature === 'string' && item.as_per_feature === '')
  ).length;
  
  const incorrectFlowCount = incorrectFlowData.length;
  const correctFeaturePercentage = incorrectFlowCount > 0 ? Math.round((correctAsPerFeature / incorrectFlowCount) * 100) : 0;
  const incorrectFeaturePercentage = incorrectFlowCount > 0 ? Math.round((incorrectAsPerFeature / incorrectFlowCount) * 100) : 0;
  const undefinedFeaturePercentage = incorrectFlowCount > 0 ? Math.round((undefinedAsPerFeature / incorrectFlowCount) * 100) : 0;
  
  // 5. For both incorrect: Comments analysis
  const bothIncorrectData = incorrectFlowData.filter(item => {
    if (typeof item.as_per_feature === 'string') {
      return ['0', 'false', 'incorrect'].includes(item.as_per_feature.toLowerCase());
    }
    if (typeof item.as_per_feature === 'boolean') return item.as_per_feature === false;
    if (typeof item.as_per_feature === 'number') return item.as_per_feature === 0;
    return false;
  });
  
  // 6. Calculate average execution time
  const requestsWithExecutionTime = data.filter(item => item.execution_time && item.execution_time > 0);
  const averageExecutionTime = requestsWithExecutionTime.length > 0 
    ? requestsWithExecutionTime.reduce((sum, item) => sum + (item.execution_time || 0), 0) / requestsWithExecutionTime.length 
    : 0;
  
  // 7. Overall correct requests calculation
  const overallCorrectRequests = correctAsPerFlow + correctAsPerFeature;

  // Prepare chart data
  const validationStatusChartData = {
    labels: ['Validated', 'Reset', 'Reprocessed'],
    datasets: [
      {
        label: 'Validation Status',
        data: [validatedRequests, resetRequests, reprocessedRequests],
        backgroundColor: [
          'rgba(75, 192, 192, 0.6)',   // Green for validated
          'rgba(255, 127, 80, 0.6)',   // Orange for reset
          'rgba(255, 159, 64, 0.6)',   // Yellow for reprocessed
        ],
        borderColor: [
          'rgb(75, 192, 192)',
          'rgb(255, 127, 80)',
          'rgb(255, 159, 64)',
        ],
        borderWidth: 1,
      },
    ],
  };

  const flowAnalysisChartData = {
    labels: ['Correct', 'Incorrect'],
    datasets: [
      {
        label: 'Flow Analysis',
        data: [correctAsPerFlow, incorrectAsPerFlow],
        backgroundColor: [
          'rgba(75, 192, 192, 0.6)',
          'rgba(255, 99, 132, 0.6)',
        ],
        borderColor: [
          'rgb(75, 192, 192)',
          'rgb(255, 99, 132)',
        ],
        borderWidth: 1,
      },
    ],
  };
  
  // Complete validation flow analysis chart data
  const completeFlowAnalysisChartData = {
    labels: [
      'Flow Correct', 
      'Flow Incorrect + Feature Correct', 
      'Flow Incorrect + Feature Incorrect'
    ],
    datasets: [
      {
        label: 'Validation Flow Analysis',
        data: [
          correctAsPerFlow, 
          correctAsPerFeature, 
          incorrectAsPerFeature
        ],
        backgroundColor: [
          'rgba(75, 192, 192, 0.6)',  // Flow Correct - Teal
          'rgba(54, 162, 235, 0.6)',  // Flow Incorrect + Feature Correct - Blue
          'rgba(255, 99, 132, 0.6)'  // Flow Incorrect + Feature Incorrect - Red
         
        ],
        borderColor: [
          'rgb(75, 192, 192)',
          'rgb(54, 162, 235)',
          'rgb(255, 99, 132)' 
        ],
        borderWidth: 1,
      },
    ],
  };

  // Provider chart data
  const providerStats = getProviderStats(data);
  const providerChartData = {
    labels: providerStats.map(p => p.name),
    datasets: [
      {
        label: 'Total Requests',
        data: providerStats.map(p => p.total),
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgb(54, 162, 235)',
        borderWidth: 1,
      },
      {
        label: 'Validated',
        data: providerStats.map(p => p.validated),
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgb(75, 192, 192)',
        borderWidth: 1,
      },
      {
        label: 'Reprocessed',
        data: providerStats.map(p => p.reprocessed),
        backgroundColor: 'rgba(255, 159, 64, 0.6)',
        borderColor: 'rgb(255, 159, 64)',
        borderWidth: 1,
      },
    ],
  };

  // Time-based analysis (group by day)
  const timeBasedData = React.useMemo(() => {
    const dateMap = new Map();
    
    data.forEach(item => {
      const date = new Date(item.timestamp).toLocaleDateString();
      if (!dateMap.has(date)) {
        dateMap.set(date, { 
          total: 0, 
          validated: 0, 
          reprocessed: 0,
          avgExecutionTime: 0,
          executionTimeCount: 0,
          totalExecutionTime: 0
        });
      }
      
      const dateStats = dateMap.get(date);
      dateStats.total++;
      
      let isValidated = false;
      if (typeof item.val_status === 'boolean') isValidated = item.val_status === true;
      else if (typeof item.val_status === 'number') isValidated = item.val_status === 1;
      else if (typeof item.val_status === 'string') {
        const status = item.val_status as string;
        isValidated = ['1', 'true', 'validated'].includes(status.toLowerCase());
      }
      
      if (isValidated) {
        dateStats.validated++;
      } else {
        dateStats.reprocessed++;
      }
      
      // Track execution time
      if (item.execution_time && item.execution_time > 0) {
        dateStats.executionTimeCount++;
        dateStats.totalExecutionTime += item.execution_time;
      }
    });
    
    // Calculate average execution time for each day
    dateMap.forEach(stats => {
      if (stats.executionTimeCount > 0) {
        stats.avgExecutionTime = stats.totalExecutionTime / stats.executionTimeCount;
      }
    });
    
    // Sort by date
    return Array.from(dateMap.entries()).sort((a, b) => 
      new Date(a[0]).getTime() - new Date(b[0]).getTime()
    );
  }, [data]);
  
  const timeChartData = {
    labels: timeBasedData.map(([date]) => date),
    datasets: [
      {
        label: 'Total',
        data: timeBasedData.map(([_, stats]) => stats.total),
        borderColor: 'rgb(54, 162, 235)',
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
        tension: 0.1,
      },
      {
        label: 'Validated',
        data: timeBasedData.map(([_, stats]) => stats.validated),
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        tension: 0.1,
      },
      {
        label: 'Reprocessed',
        data: timeBasedData.map(([_, stats]) => stats.reprocessed),
        borderColor: 'rgb(255, 159, 64)',
        backgroundColor: 'rgba(255, 159, 64, 0.5)',
        tension: 0.1,
      },
    ],
  };
  
  // Execution time chart data
  const executionTimeChartData = {
    labels: timeBasedData.map(([date]) => date),
    datasets: [
      {
        label: 'Avg. Execution Time (seconds)',
        data: timeBasedData.map(([_, stats]) => stats.avgExecutionTime),
        borderColor: 'rgb(153, 102, 255)',
        backgroundColor: 'rgba(153, 102, 255, 0.5)',
        tension: 0.1,
        yAxisID: 'y',
      },
    ],
  };
  
  // Separate chart data for request count
  const requestCountChartData = {
    labels: timeBasedData.map(([date]) => date),
    datasets: [
      {
        label: 'Request Count',
        data: timeBasedData.map(([_, stats]) => stats.executionTimeCount),
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        tension: 0.1,
      },
    ],
  };

  return (
    <div className="space-y-6">
      {/* Summary statistics cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard 
          title="Total Requests" 
          value={totalRequests} 
          bgColor="bg-teal-50" 
          textColor="text-teal-700" 
        />
        <StatCard 
          title="Validated Requests" 
          value={validatedRequests} 
          percentage={validatedPercentage} 
          bgColor="bg-green-50" 
          textColor="text-green-700" 
        />
        <StatCard 
          title="Reset Requests" 
          value={resetRequests} 
          percentage={resetPercentage} 
          bgColor="bg-orange-50" 
          textColor="text-orange-700" 
        />
        <StatCard 
          title="Reprocessed Requests" 
          value={reprocessedRequests} 
          percentage={reprocessedPercentage} 
          bgColor="bg-yellow-50" 
          textColor="text-yellow-700" 
        />
      </div>
      
      {/* Second row of statistics cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <StatCard 
          title="Avg. Processing Time" 
          value={`${averageExecutionTime.toFixed(2)}s`}
          bgColor="bg-purple-50" 
          textColor="text-purple-700" 
        />
        <StatCard 
          title="Overall Accuracy - As per Flow"
          value={overallCorrectRequests}
          percentage={Math.round((overallCorrectRequests / validatedRequests) * 100)}
          bgColor="bg-emerald-50" 
          textColor="text-emerald-700" 
        />
        <StatCard 
          title="Incorrect - As per Flow & Feature" 
          value={bothIncorrectData.length}
          percentage={Math.round((bothIncorrectData.length / validatedRequests) * 100)} 
          bgColor="bg-rose-50" 
          textColor="text-rose-700" 
        />
      </div>

      {/* Section: As per Flow Analysis */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Validation Flow Analysis</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-md font-medium text-gray-700 mb-2">As per Flow (From Validated Requests)</h4>
            <div className="bg-gray-50 p-4 rounded-lg space-y-4">
              <ProgressBar
                label="Correct"
                count={correctAsPerFlow}
                total={validatedRequests}
                color="bg-green-500"
              />
              
              <ProgressBar
                label="Incorrect"
                count={incorrectAsPerFlow}
                total={validatedRequests}
                color="bg-red-500"
              />
            </div>
          </div>
          
          <div>
            <h4 className="text-md font-medium text-gray-700 mb-2">As per Feature (From Incorrect Flow)</h4>
            <div className="bg-gray-50 p-4 rounded-lg space-y-4">
              <ProgressBar
                label="Correct"
                count={correctAsPerFeature}
                total={incorrectFlowCount}
                color="bg-green-500"
              />
              
              <ProgressBar
                label="Incorrect"
                count={incorrectAsPerFeature}
                total={incorrectFlowCount}
                color="bg-red-500"
              />
            </div>
            
            <div className="mt-4 text-sm text-gray-600">
              <p>
                Requests with both incorrect flow and feature: <span className="font-semibold">{bothIncorrectData.length}</span>
                {incorrectAsPerFeature > 0 && (
                  <span> ({Math.round((bothIncorrectData.length / incorrectAsPerFeature) * 100)}% of incorrect features)</span>
                )}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Validation Status Chart */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Validation Status Distribution</h3>
          <div className="h-64">
            <Pie 
              data={validationStatusChartData} 
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom',
                  },
                  title: {
                    display: false,
                  },
                },
              }}
            />
          </div>
        </div>

        {/* Flow Analysis Chart */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Flow Analysis Distribution</h3>
          <div className="h-64">
            <Pie 
              data={flowAnalysisChartData} 
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom',
                  },
                  title: {
                    display: false,
                  },
                },
              }}
            />
          </div>
        </div>
      </div>
      
      {/* Complete Validation Flow Analysis Chart */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Complete Validation Flow Analysis</h3>
        <div className="h-72">
          <Pie 
            data={completeFlowAnalysisChartData} 
            options={{
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'bottom',
                },
                title: {
                  display: false,
                },
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      const label = context.label || '';
                      const value = context.raw as number;
                      const percentage = Math.round((value / validatedRequests) * 100);
                      return `${label}: ${value} (${percentage}%)`;
                    }
                  }
                }
              },
            }}
          />
        </div>
      </div>

      {/* Time-based Analysis */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Request Trends Over Time</h3>
        <div className="h-80">
          <Line 
            data={timeChartData} 
            options={{
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'top',
                },
                title: {
                  display: false,
                },
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Number of Requests'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Date'
                  }
                }
              }
            }}
          />
        </div>
      </div>

      
      {/* Execution Time Analysis */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Execution Time Analysis</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-80">
            <h4 className="text-md font-medium text-gray-700 mb-2">Average Execution Time</h4>
            <Line 
              data={executionTimeChartData} 
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'top',
                  },
                  title: {
                    display: false,
                  },
                  tooltip: {
                    callbacks: {
                      label: function(context) {
                        const label = context.dataset.label || '';
                        const value = context.parsed.y;
                        return `${label}: ${value.toFixed(2)}s`;
                      }
                    }
                  }
                },
                scales: {
                  y: {
                    beginAtZero: true,
                    title: {
                      display: true,
                      text: 'Execution Time (seconds)'
                    }
                  },
                  x: {
                    title: {
                      display: true,
                      text: 'Date'
                    }
                  }
                }
              }}
            />
          </div>
          <div className="h-80">
            <h4 className="text-md font-medium text-gray-700 mb-2">Request Count with Execution Time</h4>
            <Bar
              data={requestCountChartData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'top',
                  },
                  title: {
                    display: false,
                  }
                },
                scales: {
                  y: {
                    beginAtZero: true,
                    title: {
                      display: true,
                      text: 'Request Count'
                    }
                  },
                  x: {
                    title: {
                      display: true,
                      text: 'Date'
                    }
                  }
                }
              }}
            />
          </div>
        </div>
      </div>

      {/* Provider Analysis Chart */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Provider Analysis</h3>
        <div className="h-80">
          <Bar 
            data={providerChartData} 
            options={{
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'top',
                },
                title: {
                  display: false,
                },
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Number of Requests'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Provider'
                  }
                }
              }
            }}
          />
        </div>
      </div>  

      {/* Section: Provider Breakdown */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Provider Analysis Table</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Provider</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validated</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reprocessed</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Correct Flow</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Incorrect Flow</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {getProviderStats(data).map(provider => (
                <tr key={provider.name} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap font-medium">{provider.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap">{provider.total}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {provider.validated} ({Math.round((provider.validated / provider.total) * 100)}%)
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {provider.reprocessed} ({Math.round((provider.reprocessed / provider.total) * 100)}%)
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {provider.correctFlow} ({provider.validated > 0 ? Math.round((provider.correctFlow / provider.validated) * 100) : 0}%)
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {provider.incorrectFlow} ({provider.validated > 0 ? Math.round((provider.incorrectFlow / provider.validated) * 100) : 0}%)
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default ProcessedRequestAnalytics;