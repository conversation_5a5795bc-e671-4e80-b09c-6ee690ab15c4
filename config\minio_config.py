import os
import logging
import aioboto3
import asyncio
from botocore.exceptions import Client<PERSON><PERSON>r
from datetime import timed<PERSON>ta, datetime
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

logger = logging.getLogger(__name__)

class MinIOClientPool:
    
    def __init__(self, endpoint_url: str, aws_access_key_id: str, aws_secret_access_key: str, 
                 min_size: int = 1, max_size: int = 10):
        self.endpoint_url = endpoint_url
        self.aws_access_key_id = aws_access_key_id
        self.aws_secret_access_key = aws_secret_access_key
        self.min_size = min_size
        self.max_size = max_size
        self.session = aioboto3.Session(
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
        )
        self._pool = []
        self._in_use = []
        self._semaphore = asyncio.Semaphore(max_size)
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        async with self._lock:
            for _ in range(self.min_size):
                client = await self._create_client()
                self._pool.append(client)
        logger.info(f"Initialized MinIO client pool: min={self.min_size}, max={self.max_size}")
        
    async def _create_client(self):
        """Create a new S3 client"""
        client = await self.session.client(
            's3',
            endpoint_url=self.endpoint_url,
            region_name='us-east-1'  
        ).__aenter__()
        return client
    
    async def acquire(self):
        await self._semaphore.acquire()
        
        async with self._lock:
            if self._pool:
                client = self._pool.pop(0)
            else:
                client = await self._create_client()
            
            self._in_use.append(client)
            return client
    
    async def release(self, client):
        async with self._lock:
            if client in self._in_use:
                self._in_use.remove(client)
                self._pool.append(client)
        
        self._semaphore.release()
    
    async def close(self):
        async with self._lock:
            for client in self._pool:
                await client.__aexit__(None, None, None)
            
            for client in self._in_use:
                await client.__aexit__(None, None, None)
                
            self._pool = []
            self._in_use = []
            
        logger.info("MinIO client pool closed")

class MinIOConfig:
    
    def __init__(self):
        # Load environment configuration
        self.environment = os.getenv('ENVIRONMENT')  # 'production' or 'development'
        
        # MinIO connection settings
        self.endpoint = os.getenv('MINIO_ENDPOINT', '10.10.10.24:9000')
        self.access_key = os.getenv('MINIO_ACCESS_KEY', 'oceanmind_admin')
        self.secret_key = os.getenv('MINIO_SECRET_KEY', 'OceanMind@MinIO2024')
        self.secure = os.getenv('MINIO_SECURE', 'false').lower() == 'true'
        
        # Environment-based bucket configuration
        if self.environment == 'development':
            self.buckets = {
                'screenshots': os.getenv('MINIO_SCREENSHOTS_BUCKET_TEST'),
                'videos': os.getenv('MINIO_VIDEOS_BUCKET_TEST'),
                'backups': os.getenv('MINIO_BACKUPS_BUCKET_TEST')
            }
            logger.info(f"Using development MinIO buckets: {self.buckets}")
        else:
            self.buckets = {
                'screenshots': os.getenv('MINIO_SCREENSHOTS_BUCKET'),
                'videos': os.getenv('MINIO_VIDEOS_BUCKET'),
                'backups': os.getenv('MINIO_BACKUPS_BUCKET')
            }
            logger.info(f"Using PRODUCTION MinIO buckets: {self.buckets}")
        
        # File settings
        self.max_file_size = {
            'screenshot': 10 * 1024 * 1024,  # 10MB
            'video': 1000 * 1024 * 1024,      # 1000MB
        }
        
        # Initialize session
        self.session = aioboto3.Session(
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
        )
        
        # Initialize client pool
        min_clients = int(os.getenv('MINIO_POOL_MIN_CLIENTS', '1'))
        max_clients = int(os.getenv('MINIO_POOL_MAX_CLIENTS', '10'))
        self._client_pool = MinIOClientPool(
            endpoint_url=self.get_endpoint_url(),
            aws_access_key_id=self.access_key, 
            aws_secret_access_key=self.secret_key,
            min_size=min_clients,
            max_size=max_clients
        )
        
    def get_endpoint_url(self) -> str:
        """Get endpoint URL with proper protocol"""
        protocol = 'https' if self.secure else 'http'
        return f"{protocol}://{self.endpoint}"
    
    async def initialize_pool(self):
        """Initialize the client pool"""
        await self._client_pool.initialize()
        
    async def close_pool(self):
        """Close the client pool"""
        await self._client_pool.close()
    
    @asynccontextmanager
    async def client(self):
        """Get a client from the pool as a context manager"""
        client = await self._client_pool.acquire()
        try:
            yield client
        finally:
            await self._client_pool.release(client)
    
    async def _ensure_buckets_exist(self):
        """Ensure all required buckets exist"""
        async with self.client() as s3_client:
            for bucket_type, bucket_name in self.buckets.items():
                try:
                    # Check if bucket exists
                    try:
                        await s3_client.head_bucket(Bucket=bucket_name)
                        logger.debug(f"Bucket exists: {bucket_name}")
                    except ClientError as e:
                        error_code = int(e.response['Error']['Code'])
                        if error_code == 404:
                            # Create bucket if it doesn't exist
                            await s3_client.create_bucket(Bucket=bucket_name)
                            logger.info(f"Created bucket: {bucket_name}")
                        else:
                            raise
                except Exception as e:
                    logger.error(f"Error with bucket {bucket_name}: {e}")
    
    async def initialize_buckets(self):
        """Initialize and ensure buckets exist"""
        # Initialize the client pool first
        await self.initialize_pool()
        # Now ensure buckets exist
        await self._ensure_buckets_exist()
    
    def get_client_session(self):
        """Get aioboto3 session for client creation"""
        return self.session
    
    def get_bucket_name(self, media_type: str) -> str:
        """Get bucket name for media type"""
        return self.buckets.get(media_type, self.buckets['screenshots'])
    
    def generate_object_key(self, provider: str, session_id: str, media_type: str, 
                           file_extension: str, cabin_id: Optional[int] = None, 
                           sub_type: Optional[str] = None) -> str:
        """
        Generate a consistent object key for MinIO storage
        
        Args:
            provider: Provider name (ncl, cruising_power, studio)
            session_id: Session identifier
            media_type: Type of media (screenshots, videos)
            file_extension: File extension (png, webm, mp4)
            cabin_id: Optional cabin identifier
            sub_type: Optional sub-type (screenshot_type or video_type)
        
        Returns:
            Object key string
        """
        # Normalize provider name
        provider = provider.lower().replace(' ', '_')
        
        # Build path components
        path_parts = [provider, session_id, media_type]
        
        if cabin_id is not None:
            path_parts.append(f"cabin_{cabin_id}")
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename_parts = [session_id, media_type]
        
        if cabin_id is not None:
            filename_parts.append(f"cabin_{cabin_id}")
        
        if sub_type:
            filename_parts.append(sub_type)
        
        filename = f"{'_'.join(filename_parts)}_{timestamp}.{file_extension}"
        
        # Combine path and filename
        return f"{'/'.join(path_parts)}/{filename}"
    
    async def get_presigned_url(self, bucket_name: str, object_key: str, 
                         expires: timedelta = timedelta(hours=1)) -> str:
        """Generate presigned URL for object access"""
        try:
            async with self.client() as s3_client:
                url = await s3_client.generate_presigned_url(
                    'get_object',
                    Params={
                        'Bucket': bucket_name, 
                        'Key': object_key
                    },
                    ExpiresIn=int(expires.total_seconds())
                )
                return url
        except Exception as e:
            logger.error(f"Error generating presigned URL: {e}")
            return None

# Global MinIO configuration instance
minio_config = MinIOConfig() 