{"version": 3, "sources": ["../../../../src/server/web/spec-extension/fetch-event.ts"], "names": ["waitUntilSymbol", "NextFetchEvent", "responseSymbol", "Symbol", "passThroughSymbol", "FetchEvent", "constructor", "_request", "respondWith", "response", "Promise", "resolve", "passThroughOnException", "waitUntil", "promise", "push", "params", "request", "sourcePage", "page", "PageSignatureError"], "mappings": ";;;;;;;;;;;;;;;IAKaA,eAAe;eAAfA;;IAyBAC,cAAc;eAAdA;;;uBA9BsB;AAGnC,MAAMC,iBAAiBC,OAAO;AAC9B,MAAMC,oBAAoBD,OAAO;AAC1B,MAAMH,kBAAkBG,OAAO;AAEtC,MAAME;IAKJ,qEAAqE;IACrEC,YAAYC,QAAiB,CAAE;YALtB,CAACP,gBAAgB,GAAmB,EAAE;YAE/C,CAACI,kBAAkB,GAAG;IAGU;IAEhCI,YAAYC,QAAsC,EAAQ;QACxD,IAAI,CAAC,IAAI,CAACP,eAAe,EAAE;YACzB,IAAI,CAACA,eAAe,GAAGQ,QAAQC,OAAO,CAACF;QACzC;IACF;IAEAG,yBAA+B;QAC7B,IAAI,CAACR,kBAAkB,GAAG;IAC5B;IAEAS,UAAUC,OAAqB,EAAQ;QACrC,IAAI,CAACd,gBAAgB,CAACe,IAAI,CAACD;IAC7B;AACF;AAEO,MAAMb,uBAAuBI;IAGlCC,YAAYU,MAA8C,CAAE;QAC1D,KAAK,CAACA,OAAOC,OAAO;QACpB,IAAI,CAACC,UAAU,GAAGF,OAAOG,IAAI;IAC/B;IAEA;;;;GAIC,GACD,IAAIF,UAAU;QACZ,MAAM,IAAIG,yBAAkB,CAAC;YAC3BD,MAAM,IAAI,CAACD,UAAU;QACvB;IACF;IAEA;;;;GAIC,GACDV,cAAc;QACZ,MAAM,IAAIY,yBAAkB,CAAC;YAC3BD,MAAM,IAAI,CAACD,UAAU;QACvB;IACF;AACF"}