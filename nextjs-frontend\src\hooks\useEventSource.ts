import { useState, useEffect } from 'react';

interface EventSourceOptions {
  onMessage?: (data: any) => void;
  onOpen?: () => void;
  onError?: (error: Event) => void;
  withCredentials?: boolean;
}

/**
 * Custom hook for consuming Server-Sent Events (SSE) 
 * 
 * @param url The URL to connect to
 * @param options Configuration options for the EventSource
 * @returns An object containing the connection state and last received data
 */
export function useEventSource(url: string, options: EventSourceOptions = {}) {
  const [data, setData] = useState<any>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<Event | null>(null);

  useEffect(() => {
    // Don't connect if no URL provided
    if (!url) return;
    
    // Create the EventSource connection
    const eventSource = new EventSource(url, {
      withCredentials: options.withCredentials || false
    });

    // Handle connection open
    eventSource.onopen = () => {
      setIsConnected(true);
      setError(null);
      if (options.onOpen) options.onOpen();
    };

    // Handle incoming messages 
    eventSource.onmessage = (event) => {
      try {
        const parsedData = JSON.parse(event.data);
        setData(parsedData);
        if (options.onMessage) options.onMessage(parsedData);
      } catch (err) {
        console.error('Error parsing SSE data:', err);
      }
    };

    // Handle connection error
    eventSource.onerror = (event) => {
      setError(event);
      setIsConnected(false);
      if (options.onError) options.onError(event);
    };

    // Clean up on unmount
    return () => {
      eventSource.close();
      setIsConnected(false);
    };
  }, [url, options.withCredentials]);

  return { data, isConnected, error };
} 