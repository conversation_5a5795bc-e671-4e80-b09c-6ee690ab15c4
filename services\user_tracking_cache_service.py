"""
User Tracking Redis Cache Service

This service handles caching for the /api/user-tracking/data endpoint
to solve the N+1 query problem and improve performance from ~2000ms to ~50ms.
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from services.redis_service import redis_service
from db_service import db_connection
import logging

logger = logging.getLogger("user_tracking_cache_service")

class UserTrackingCacheService:
    """Redis caching service for user tracking data"""
    
    # Cache configuration
    CACHE_PREFIX = "user_tracking"
    DEFAULT_TTL = 3600  # 1 hour
    PROVIDER_DATA_TTL = 3600  # 1 hour for provider-specific data
    
    @staticmethod
    def _generate_cache_key(provider: Optional[str] = None, username: Optional[str] = None, 
                          limit: int = 10000, offset: int = 0) -> str:
        """Generate cache key based on query parameters"""
        key_parts = [
            UserTrackingCacheService.CACHE_PREFIX,
            f"provider:{provider or 'all'}",
            f"username:{username or 'all'}",
            f"limit:{limit}",
            f"offset:{offset}"
        ]
        
        # Create a hash for very long keys
        key_string = ":".join(key_parts)
        if len(key_string) > 10000:
            key_hash = hashlib.md5(key_string.encode()).hexdigest()
            return f"{UserTrackingCacheService.CACHE_PREFIX}:hash:{key_hash}"
        
        return key_string
    
    @staticmethod
    def _generate_provider_cache_key(provider: str) -> str:
        """Generate cache key for provider-specific booking data"""
        return f"{UserTrackingCacheService.CACHE_PREFIX}:provider_bookings:{provider}"
    
    @staticmethod
    async def get_cached_tracking_data(provider: Optional[str] = None, username: Optional[str] = None,
                                     limit: int = 10000, offset: int = 0) -> Optional[Dict[str, Any]]:
        """Get cached user tracking data"""
        try:
            # Check if Redis is available
            if not hasattr(redis_service, 'redis') or redis_service.redis is None:
                logger.debug("Redis not available, skipping cache check")
                return None
                
            cache_key = UserTrackingCacheService._generate_cache_key(provider, username, limit, offset)
            cached_data = await redis_service.cache_get(cache_key)
            
            if cached_data:
                logger.info(f"📊 User tracking data cache HIT for key: {cache_key}")
                cached_data["from_cache"] = True
                cached_data["cache_hit"] = True
                return cached_data
            
            logger.info(f"🔄 User tracking data cache MISS for key: {cache_key}")
            return None
            
        except Exception as e:
            logger.warning(f"Redis cache unavailable, continuing without cache: {e}")
            return None
    
    @staticmethod
    async def cache_tracking_data(data: Dict[str, Any], provider: Optional[str] = None, 
                                username: Optional[str] = None, limit: int = 10000, 
                                offset: int = 0, ttl: Optional[int] = None) -> None:
        """Cache user tracking data"""
        try:
            # Check if Redis is available
            if not hasattr(redis_service, 'redis') or redis_service.redis is None:
                logger.debug("Redis not available, skipping cache storage")
                return
                
            cache_key = UserTrackingCacheService._generate_cache_key(provider, username, limit, offset)
            cache_ttl = ttl or UserTrackingCacheService.DEFAULT_TTL
            
            # Add cache metadata
            data["from_cache"] = False
            data["cache_hit"] = False
            data["cached_at"] = datetime.now().isoformat()
            data["cache_ttl"] = cache_ttl
            
            await redis_service.cache_set(cache_key, data, cache_ttl)
            logger.info(f"💾 User tracking data cached for {cache_ttl} seconds with key: {cache_key}")
            
        except Exception as e:
            logger.warning(f"Failed to cache tracking data, continuing without cache: {e}")
    
    @staticmethod
    async def get_cached_provider_bookings(provider: str) -> Optional[Dict[str, Any]]:
        """Get cached booking data for a specific provider to solve N+1 problem"""
        try:
            cache_key = UserTrackingCacheService._generate_provider_cache_key(provider)
            cached_data = await redis_service.cache_get(cache_key)
            
            if cached_data:
                logger.info(f"📊 Provider booking data cache HIT for: {provider}")
                return cached_data
            
            logger.info(f"🔄 Provider booking data cache MISS for: {provider}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached provider bookings: {e}")
            return None
    
    @staticmethod
    async def cache_provider_bookings(provider: str, booking_data: Dict[str, Any], 
                                    ttl: Optional[int] = None) -> None:
        """Cache booking data for a specific provider"""
        try:
            cache_key = UserTrackingCacheService._generate_provider_cache_key(provider)
            cache_ttl = ttl or UserTrackingCacheService.PROVIDER_DATA_TTL
            
            await redis_service.cache_set(cache_key, booking_data, cache_ttl)
            logger.info(f"💾 Provider booking data cached for {provider} ({cache_ttl}s)")
            
        except Exception as e:
            logger.error(f"Error caching provider bookings: {e}")
    
    @staticmethod
    async def invalidate_tracking_cache(provider: Optional[str] = None, username: Optional[str] = None):
        """Invalidate tracking cache when new bookings are processed"""
        try:
            # Get Redis client
            redis = await redis_service.get_redis()
            
            # Pattern to match relevant cache keys
            if provider and username:
                pattern = f"{UserTrackingCacheService.CACHE_PREFIX}:*provider:{provider}*username:{username}*"
            elif provider:
                pattern = f"{UserTrackingCacheService.CACHE_PREFIX}:*provider:{provider}*"
            elif username:
                pattern = f"{UserTrackingCacheService.CACHE_PREFIX}:*username:{username}*"
            else:
                pattern = f"{UserTrackingCacheService.CACHE_PREFIX}:*"
            
            # Find and delete matching keys
            cursor = 0
            deleted_count = 0
            
            while True:
                cursor, keys = await redis.scan(cursor, match=pattern, count=10000)
                if keys:
                    await redis.delete(*keys)
                    deleted_count += len(keys)
                
                if cursor == 0:
                    break
            
            logger.info(f"🗑️ Invalidated {deleted_count} tracking cache entries for pattern: {pattern}")
            
            # Also invalidate provider-specific cache if provider specified
            if provider:
                provider_cache_key = UserTrackingCacheService._generate_provider_cache_key(provider)
                await redis_service.cache_delete(provider_cache_key)
                logger.info(f"🗑️ Invalidated provider cache for: {provider}")
                
        except Exception as e:
            logger.error(f"Error invalidating tracking cache: {e}")
    
    @staticmethod
    async def fetch_and_cache_provider_bookings(provider: str, session_ids: List[str]) -> Dict[str, Any]:
        """Fetch and cache booking data for multiple sessions at once (solves N+1 problem)"""
        try:
            # Check cache first
            cached_data = await UserTrackingCacheService.get_cached_provider_bookings(provider)
            if cached_data:
                # Filter cached data for requested session_ids
                filtered_data = {
                    session_id: cached_data.get(session_id)
                    for session_id in session_ids
                    if session_id in cached_data
                }
                
                # If we have all needed data in cache, return it
                if len(filtered_data) == len(session_ids):
                    return filtered_data
            
            # Cache miss or incomplete data - fetch from database
            logger.info(f"Fetching booking data for {len(session_ids)} sessions from {provider}")
            booking_data = {}
            
            if session_ids:
                async with db_connection() as conn:
                    booking_table = f"{provider.lower()}_bookings"
                    
                    # Single query to get all booking data at once
                    placeholders = ','.join(f'${i+1}' for i in range(len(session_ids)))
                    query = f"""
                        SELECT session_id, id, timestamp, execution_time
                        FROM {booking_table}
                        WHERE session_id IN ({placeholders})
                    """
                    
                    try:
                        booking_results = await conn.fetch(query, *session_ids)
                        
                        for row in booking_results:
                            booking_data[row['session_id']] = {
                                'booking_id': row['id'],
                                'booking_timestamp': row['timestamp'],
                                'execution_time': row.get('execution_time')
                            }
                        
                        logger.info(f"Fetched {len(booking_results)} booking records for {provider}")
                        
                    except Exception as e:
                        logger.warning(f"Error fetching from {booking_table}: {e}")
                        # Table might not exist or have different schema
                        pass
            
            # Cache the results (including empty results for efficiency)
            await UserTrackingCacheService.cache_provider_bookings(provider, booking_data)
            
            return booking_data
            
        except Exception as e:
            logger.error(f"Error fetching provider bookings for {provider}: {e}")
            return {}
    
    @staticmethod
    async def get_cache_statistics() -> Dict[str, Any]:
        """Get cache statistics for monitoring"""
        try:
            redis = await redis_service.get_redis()
            
            # Count cache keys
            cursor = 0
            total_keys = 0
            tracking_keys = 0
            provider_keys = 0
            
            while True:
                cursor, keys = await redis.scan(cursor, match=f"{UserTrackingCacheService.CACHE_PREFIX}:*", count=10000)
                
                for key in keys:
                    total_keys += 1
                    if ":provider_bookings:" in key:
                        provider_keys += 1
                    else:
                        tracking_keys += 1
                
                if cursor == 0:
                    break
            
            return {
                "total_cache_keys": total_keys,
                "tracking_data_keys": tracking_keys,
                "provider_booking_keys": provider_keys,
                "cache_prefix": UserTrackingCacheService.CACHE_PREFIX,
                "default_ttl": UserTrackingCacheService.DEFAULT_TTL,
                "provider_data_ttl": UserTrackingCacheService.PROVIDER_DATA_TTL
            }
            
        except Exception as e:
            logger.error(f"Error getting cache statistics: {e}")
            return {"error": str(e)} 