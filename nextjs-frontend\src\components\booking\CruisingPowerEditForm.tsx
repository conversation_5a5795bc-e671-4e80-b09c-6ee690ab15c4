import React, { useState, useEffect } from 'react';

interface CruisingPowerEditFormProps {
  details: any;
  requestId: string;
  onSave: (updatedDetails: any) => void;
  onCancel: () => void;
}

const CruisingPowerEditForm: React.FC<CruisingPowerEditFormProps> = ({
  details,
  requestId,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState({
    ship_name: '',
    ship_brand: '',
    departure_date: '',
    residency: '',
    adults: 0,
    children: 0,
    seniors: 0,
    total_passengers: 0,
    total_cabins: 1,
    cabins_with_passengers: [] as any[],
    has_senior: 'No',
    request_id: '',
    original_text: ''
  });

  // Initialize form data from the provided details
  useEffect(() => {
    if (details) {
      // Deep copy to avoid modifying the original
      const initialFormData = { ...details };

      // Ensure cabins_with_passengers exists
      if (!initialFormData.cabins_with_passengers || !Array.isArray(initialFormData.cabins_with_passengers)) {
        initialFormData.cabins_with_passengers = [
          {
            type: 'INSIDE',
            normalized: 'inside',
            assigned_adults: 2,
            assigned_children: 0,
            assigned_seniors: 0
          }
        ];
      }

      // Normalize cabin types to uppercase so that they match the <select> option values
      initialFormData.cabins_with_passengers = initialFormData.cabins_with_passengers.map((cabin: any) => ({
        ...cabin,
        type: (cabin.type || 'INSIDE').toUpperCase(),
        normalized: (cabin.type || 'INSIDE').toLowerCase()
      }));

      // Ensure passenger totals exist; if missing in extracted data, compute them once.
      if (initialFormData.total_passengers === undefined || initialFormData.total_passengers === null) {
        let adults = 0;
        let children = 0;
        let seniors = 0;
        initialFormData.cabins_with_passengers.forEach((cabin: any) => {
          adults += cabin.assigned_adults || 0;
          children += cabin.assigned_children || 0;
          seniors += cabin.assigned_seniors || 0;
        });
        initialFormData.adults = adults;
        initialFormData.children = children;
        initialFormData.seniors = seniors;
        initialFormData.total_passengers = adults + children + seniors;
        initialFormData.has_senior = seniors > 0 ? 'Yes' : 'No';
      }

      // Set request ID
      initialFormData.request_id = requestId;

      // Set form data
      setFormData(initialFormData);
    }
  }, [details, requestId]);

  // Handler for basic field changes
  const handleBasicChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  // Handler for number of cabins change
  const handleCabinsChange = (numCabins: number) => {
    const newFormData = { ...formData, total_cabins: numCabins };
    const newCabinsWithPassengers = [...formData.cabins_with_passengers];

    // Add cabins if needed
    while (newCabinsWithPassengers.length < numCabins) {
      newCabinsWithPassengers.push({
        type: 'INSIDE',
        normalized: 'inside',
        assigned_adults: 2,
        assigned_children: 0,
        assigned_seniors: 0
      });
    }

    // Remove cabins if needed
    if (newCabinsWithPassengers.length > numCabins) {
      newCabinsWithPassengers.length = numCabins;
    }

    newFormData.cabins_with_passengers = newCabinsWithPassengers;

    // Recalculate totals
    calculateTotals(newFormData);

    setFormData(newFormData);
  };

  // Handler for cabin type change
  const handleCabinTypeChange = (index: number, type: string) => {
    const newCabinsWithPassengers = [...formData.cabins_with_passengers];

    newCabinsWithPassengers[index] = {
      ...newCabinsWithPassengers[index],
      type: type,
      normalized: type.toLowerCase()
    };

    const newFormData = {
      ...formData,
      cabins_with_passengers: newCabinsWithPassengers
    };

    setFormData(newFormData);
  };

  // Handler for passenger count changes in a cabin
  const handleCabinPassengersChange = (
    index: number,
    field: 'assigned_adults' | 'assigned_children' | 'assigned_seniors',
    value: number
  ) => {
    const newCabinsWithPassengers = [...formData.cabins_with_passengers];

    newCabinsWithPassengers[index] = {
      ...newCabinsWithPassengers[index],
      [field]: value
    };

    const newFormData = {
      ...formData,
      cabins_with_passengers: newCabinsWithPassengers
    };

    // Calculate new totals
    calculateTotals(newFormData);

    setFormData(newFormData);
  };

  // Helper to calculate passenger totals
  const calculateTotals = (data: typeof formData) => {
    let totalAdults = 0;
    let totalChildren = 0;
    let totalSeniors = 0;

    data.cabins_with_passengers.forEach(cabin => {
      totalAdults += cabin.assigned_adults || 0;
      totalChildren += cabin.assigned_children || 0;
      totalSeniors += cabin.assigned_seniors || 0;
    });

    data.adults = totalAdults;
    data.children = totalChildren;
    data.seniors = totalSeniors;
    data.total_passengers = totalAdults + totalChildren + totalSeniors;
    data.has_senior = totalSeniors > 0 ? 'Yes' : 'No';
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Ensure the data is ready for the backend
    const updatedDetails = {
      ...formData,
      is_edited: true
    };

    onSave(updatedDetails);
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="mb-6">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 mr-2 text-teal-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <h3 className="text-2xl font-bold text-gray-800">Edit Cruising Power Details</h3>
          </div>
          <div className="mt-2 relative">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-20 h-0.5 bg-gray-800"></div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-6 mt-5">
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ship Name
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
              value={formData.ship_name}
              onChange={(e) => handleBasicChange('ship_name', e.target.value)}
              required
            />
          </div>

          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ship Brand
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
              value={formData.ship_brand}
              onChange={(e) => handleBasicChange('ship_brand', e.target.value)}
              required
            />
          </div>

          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Departure Date
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
              value={formData.departure_date}
              onChange={(e) => handleBasicChange('departure_date', e.target.value)}
              required
            />
          </div>


          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Residency
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
              value={formData.residency}
              onChange={(e) => handleBasicChange('residency', e.target.value)}
              required
            />
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Number of Cabins
          </label>
          <input
            type="number"
            className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
            min="1"
            max="10"
            value={formData.total_cabins}
            onChange={(e) => handleCabinsChange(parseInt(e.target.value) || 1)}
            required
          />
        </div>

        <div className="mb-4 mt-8">
          <div className="flex items-center">
            <div className="w-4 h-4 rounded-full bg-teal-500 mr-2"></div>
            <h4 className="text-xl font-bold text-gray-800">Cabin Details</h4>
          </div>
          <div className="mt-2 relative">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
          </div>
        </div>

    <div className="space-y-5 my-5">
        {formData.cabins_with_passengers.map((cabin, index) => (
           <div
              key={index}
              className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 rounded-full bg-teal-700 text-white flex items-center justify-center mr-2 shadow-sm">
                  {index + 1}
                </div>
                <h5 className="font-semibold text-teal-800 text-lg">Cabin {index + 1}</h5>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cabin Type
                  </label>
                  <select
                    className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
                    value={(cabin.type || 'INSIDE').toUpperCase()}
                    onChange={(e) => handleCabinTypeChange(index, e.target.value)}
                    required
                  >
                  <option value="INSIDE">INSIDE</option>
                  <option value="OUTSIDE">OUTSIDE</option>
                  <option value="OCEANVIEW">OCEANVIEW</option>
                  <option value="BALCONY">BALCONY</option>
                  <option value="JUNIOR SUITE">JUNIOR SUITE</option>
                  <option value="SUITE">SUITE</option>
                  <option value="DELUXE">DELUXE</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Adults
                </label>
                <input
                  type="number"
                  className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
                  min="0"
                  max="6"
                  value={cabin.assigned_adults || 0}
                  onChange={(e) => handleCabinPassengersChange(
                    index,
                    'assigned_adults',
                    parseInt(e.target.value) || 0
                  )}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Children
                </label>
                <input
                  type="number"
                  className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
                  min="0"
                  max="6"
                  value={cabin.assigned_children || 0}
                  onChange={(e) => handleCabinPassengersChange(
                    index,
                    'assigned_children',
                    parseInt(e.target.value) || 0
                  )}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Seniors
                </label>
                <input
                  type="number"
                  className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
                  min="0"
                  max="6"
                  value={cabin.assigned_seniors || 0}
                  onChange={(e) => handleCabinPassengersChange(
                    index,
                    'assigned_seniors',
                    parseInt(e.target.value) || 0
                  )}
                />
              </div>
            </div>
          </div>
        ))}
        </div>

        <div className="bg-gradient-to-r from-blue-100 to-teal-100 p-4 rounded-lg border border-blue-200 shadow-sm mb-5">
          <div className="font-semibold text-teal-800 text-lg mb-2">Passenger Summary</div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-3 rounded shadow-sm border border-gray-100">
              <span className="text-gray-600 text-sm">Total Passengers</span>
              <p className="text-teal-700 font-bold text-xl">{formData.total_passengers}</p>
            </div>
            <div className="bg-white p-3 rounded shadow-sm border border-gray-100">
              <span className="text-gray-600 text-sm">Adults</span>
              <p className="text-teal-700 font-bold text-xl">{formData.adults}</p>
            </div>
            <div className="bg-white p-3 rounded shadow-sm border border-gray-100">
              <span className="text-gray-600 text-sm">Children/Seniors</span>
              <p className="text-teal-700 font-bold text-xl">{formData.children + formData.seniors}</p>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            className="px-5 py-2.5 text-white rounded-lg shadow-md bg-gradient-to-r from-gray-700 to-gray-900 hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200"
            onClick={onCancel}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-5 py-2.5 text-white rounded-lg shadow-md bg-gradient-to-r from-teal-700 to-slate-800 hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200"
          >
            Save Changes
          </button>
        </div>
      </form>
    </div>
  );
};

export default CruisingPowerEditForm; 