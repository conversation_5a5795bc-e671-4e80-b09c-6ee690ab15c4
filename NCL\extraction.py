import re
from datetime import datetime
from loguru import logger


class CruiseDetailsExtractor:

    @staticmethod
    def convert_date_format(date_string):

        cleaned_date = re.sub(r'(\d+)(st|nd|rd|th)', r'\1', date_string)

        try:
            date_obj = datetime.strptime(cleaned_date, '%B %d, %Y')
            return date_obj.strftime('%m/%d/%Y')
        except ValueError as e:
            try:
                cleaned_date = re.sub(r'\s+', ' ', cleaned_date).strip()
                date_obj = datetime.strptime(cleaned_date, '%B %d, %Y')
                return date_obj.strftime('%m/%d/%Y')
            except ValueError:
                logger.error(f"Error parsing date '{date_string}': {e}")
                return date_string

    @staticmethod
    def extract_cruise_details(input_text):
        logger.info("Extracting cruise details from NCL input text")
        cruise_details = {
            'travel_date': '', 'nights': 0, 'ship_name': '', 'total_passengers': 0,
            'adults': 0, 'children': 0, 'infants': 0, 'cabins': 0, 'cabin_categories':
            [], 'original_cabin_count': 0
        }

        ship_pattern = r'Ship:\s*(?:NCL\s*)?([^,]+),\s*([^\n]+)'
        sails_pattern = r'Sails:\s*(.+?)\s+for\s*(\d+)\s*nights'
        passengers_pattern = r'Passengers:\s*(\d+)\s*total\s*\(\s*(?:(\d+)\s*adults?)?(?:\s*(?:,|and)\s*)?(?:(\d+)\s*seniors?)?(?:\s*(?:,|and)\s*)?(?:(\d+)\s*(?:child|children))?(?:\s*(?:,|and)\s*)?(?:(\d+)\s*infants?)?\s*\)'
        cabins_pattern = r'Cabins:\s*(\d+)\s*total\s*\(\s*(.*?)\)'

        ship_match = re.search(ship_pattern, input_text, re.IGNORECASE)
        if ship_match:
            cruise_details['ship_name'] = ship_match.group(1).strip()
            logger.debug(f"Extracted ship name: {cruise_details['ship_name']}")

        sails_match = re.search(sails_pattern, input_text, re.IGNORECASE)
        if sails_match:
            travel_date = sails_match.group(1).strip()
            cruise_details['travel_date'] = CruiseDetailsExtractor.convert_date_format(
                travel_date
            )
            cruise_details['nights'] = int(sails_match.group(2))
            logger.debug(
                f"Extracted travel date: {cruise_details['travel_date']}, nights: {cruise_details['nights']}"
            )

        passengers_match = re.search(passengers_pattern, input_text, re.IGNORECASE)
        if passengers_match:
            cruise_details['total_passengers'] = int(passengers_match.group(1))

            adults_count = int(passengers_match.group(2) or 0)

            seniors_count = int(passengers_match.group(3) or 0)

            cruise_details['adults'] = adults_count + seniors_count

            cruise_details['children'] = int(passengers_match.group(4) or 0)

            cruise_details['infants'] = int(passengers_match.group(5) or 0)

            logger.debug(
                f"Extracted passenger details: {cruise_details['total_passengers']} total ({cruise_details['adults']} adults, {cruise_details['children']} children, {cruise_details['infants']} infants)"
            )

            if cruise_details['adults'] == 0 and cruise_details[
                    'children'] == 0 and cruise_details['infants'] == 0:
                cruise_details['adults'] = cruise_details['total_passengers']
                logger.debug(
                    f"No age breakdown found, setting all {cruise_details['total_passengers']} passengers as adults"
                )

        cabins_match = re.search(cabins_pattern, input_text, re.IGNORECASE)
        if cabins_match:
            initial_cabin_count = int(cabins_match.group(1))
            cruise_details['cabins'] = initial_cabin_count
            cruise_details['original_cabin_count'] = initial_cabin_count
            logger.debug(f"Extracted cabin count: {initial_cabin_count}")

            cabin_categories_text = cabins_match.group(2).strip()

            cabin_categories = []

            if " or " in cabin_categories_text.lower():
                or_categories = re.split(
                    r'\s+or\s+', cabin_categories_text, flags=re.IGNORECASE
                )

                cabin_categories = [
                    cat.strip().lower() for cat in or_categories if cat.strip()
                ]

                cruise_details['cabins'] = len(cabin_categories)

                logger.info(
                    f"'OR' cabin scenario detected: Original count {initial_cabin_count}, new count {len(cabin_categories)}"
                )
            else:
                # Updated regex to handle multi-word categories like "JR Suite"
                # Only treat as single type if there's no comma (to avoid matching "1 Inside, 1 JR Suite" as single type)
                single_type_match = re.match(r'^(\d+)\s*(.+)$', cabin_categories_text)
                if single_type_match and ',' not in cabin_categories_text:
                    count = int(single_type_match.group(1))
                    category = single_type_match.group(2).strip().lower()
                    cabin_categories = [category] * count
                    logger.debug(f"Extracted single cabin type: {count} {category}")

                else:
                    category_parts = cabin_categories_text.split(',')
                    for part in category_parts:
                        part = part.strip()

                        # Updated regex to handle multi-word categories like "JR Suite"
                        count_match = re.match(r'^(\d+)\s*(.+)$', part)
                        if count_match:
                            count = int(count_match.group(1))
                            category = count_match.group(2).strip().lower()
                            cabin_categories.extend([category] * count)
                            logger.debug(
                                f"Extracted cabin category: {count} {category}"
                            )
                        else:
                            cabin_categories.append(part.lower())
                            logger.debug(
                                f"Extracted unstructured cabin category: {part}"
                            )

            cruise_details['cabin_categories'] = cabin_categories
            logger.debug(f"Final cabin categories: {cabin_categories}")

            if cabin_categories and len(cabin_categories) != cruise_details['cabins']:
                cruise_details['cabins'] = len(cabin_categories)
                logger.debug(
                    f"Adjusted cabin count to match categories: {cruise_details['cabins']}"
                )

        logger.info("Completed extraction of NCL cruise details")
        return cruise_details