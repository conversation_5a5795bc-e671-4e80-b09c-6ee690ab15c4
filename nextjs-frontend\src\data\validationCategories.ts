export interface ValidationIssue {
    code: string;
    label: string;
    help: string;
  }
  
  export interface ValidationCategory {
    category: string;
    issues: ValidationIssue[];
  }
  
  // Static catalogue derived from the reviewed CSV.
  // In a later iteration this can be fetched from an API endpoint.
  const validationCategories: ValidationCategory[] = [
    {
      category: 'Cabin Category',
      issues: [
        {
          code: 'WRONG_CABIN_TYPE',
          label: 'Wrong Cabin Type',
          help: '<PERSON>l selected a cheaper or different cabin type than requested (e.g. inside instead of balcony, balcony vs suite, etc.)',
        },
        {
          code: 'WAITLIST_OR_UNAVAILABLE',
          label: 'Returned Waitlist/CLS/Unavailable',
          help: 'Tool returned a price for a cabin or category that is on waitlist (WLT), canceled (CLS), or otherwise not actually available',
        },
      ],
    },
    {
      category: 'Price Issues',
      issues: [
        {
          code: 'PRICE_MISSING_ALL',
          label: 'Price Not Provided for All Cabins',
          help: 'Tool did not provide any price for the requested category/cabin, or missed entire categories',
        },
        {
          code: 'PRICE_PARTIAL',
          label: 'Price for Partial Cabins Request',
          help: 'Price given for only some of the requested cabins, passenger groups, or rate codes—others are missing',
        },
        {
          code: 'PRICE_MISMATCH',
          label: 'Price Mismatch with Manual/Expected',
          help: 'Tool’s price does not match manually calculated or expected value, including incorrect higher or lower prices',
        },
      ],
    },
    {
      category: 'Comment Ignored',
      issues: [
        { code: 'IGNORED_LOCATION', label: 'Instructions Ignored – Location', help: 'Comments about cabin location (e.g. midship, aft, near elevator, high deck, etc.) were not followed' },
        { code: 'IGNORED_ADJACENCY', label: 'Instructions Ignored – Adjacency', help: 'Requests for connecting, adjoining, or side-by-side cabins were not honored' },
        { code: 'IGNORED_REFUNDABILITY', label: 'Instructions Ignored – Refundability/Perks', help: 'Instructions about refundable fares, perks (drinks/wifi), or “all-included” not followed' },
        { code: 'IGNORED_ACCESSIBILITY', label: 'Instructions Ignored – Accessibility', help: 'Requests for handicapped or wheelchair-accessible cabins not honored' },
        { code: 'IGNORED_SPECIAL_CATEGORY', label: 'Instructions Ignored – Special Category', help: 'Any other custom or non-standard request in comments (e.g. “family”, “starboard”, “aft-facing”, etc.) ignored' },
        { code: 'IGNORED_PASSENGER_DISTRIBUTION', label: 'Instructions Ignored – Passenger Distribution', help: 'Comment instructions about splitting passengers into specific cabins or numbers not followed' },
        { code: 'IGNORED_PERK', label: 'Instructions Ignored – Perk', help: 'Requests for special perks or benefits (e.g. drinks, excursions) ignored or not reflected in quote' },
      ],
    },
    {
      category: 'Passenger Handling',
      issues: [
        { code: 'WRONG_PAX_DISTRIBUTION', label: 'Wrong Pax Distribution', help: 'Passenger split is incorrect (e.g. requested 2+2, but tool quoted all in one cabin, or omitted one of the passenger counts)' },
        { code: 'INCORRECT_AGE', label: 'Incorrect Age', help: 'Tool ignored child/adult distinction, max capacity per age, or age-restricted offers/rules' },
      ],
    },
    {
      category: 'Rate Code Issue',
      issues: [
        { code: 'RATE_CODE_NOT_APPLIED', label: 'Rate Code Not Applied', help: 'Tool did not apply the requested rate code (e.g. Usail, Value Sail, All Included, etc.)' },
        { code: 'WRONG_RATE_CODE', label: 'Incorrect Rate Code Applied', help: 'Wrong rate code used in the quote, does not match what was requested or expected' },
      ],
    },
    {
      category: 'Screenshot Error',
      issues: [
        { code: 'MISSING_SCREENSHOT', label: 'Missing Screenshot', help: 'No screenshot of the quote or pricing page was captured' },
        { code: 'PARTIAL_SCREENSHOT', label: 'Partial/Incomplete Screenshot', help: 'Only part of the relevant page or quote was captured' },
        { code: 'WRONG_SCREENSHOT', label: 'Wrong Screenshot', help: 'Screenshot does not correspond to the actual request or contains irrelevant data' },
      ],
    },
    {
      category: 'OBC Calculation',
      issues: [
        { code: 'FITOBC_NOT_APPLIED', label: 'FITOBC Not Applied', help: 'FITOBC (Future Cruise Credit Onboard Credit) was missing, not included, or not reflected correctly' },
        { code: 'NATOBC_NOT_APPLIED', label: 'NATOBC Not Applied', help: 'NATOBC (National Onboard Credit) was missing or not reflected' },
        { code: 'STAROBC_NOT_APPLIED', label: 'STAROBC Not Applied', help: 'STAROBC (Star Onboard Credit) was missing or not reflected' },
        { code: 'OBC_CALC_ERROR', label: 'OBC Calculation Error', help: 'General onboard credit (OBC) calculation is wrong due to a logic or eligibility error, or not included at all' },
        { code: 'OBC_WRONG_CATEGORY', label: 'OBC Not Applied to Correct Category', help: 'OBC was not applied to the appropriate cabin or category, or distributed incorrectly' },
        { code: 'OBC_ROUNDING_ERROR', label: 'Rounding Error', help: 'OBC value is present but contains rounding mistakes' },
        { code: 'OBC_WRONG_PERCENTAGE', label: 'Wrong Percentage Applied', help: 'OBC is calculated/applied, but the percentage used is incorrect (not matching user config or input)' },
      ],
    },
    {
      category: 'Miscellaneous',
      issues: [
        { code: 'SPECIAL_COMMENT', label: 'Special Comment', help: 'Any other issue, feedback, or comment not covered above; use this when the error is unique, unclear, or non-standard' },
      ],
    },
  ];
  
  export default validationCategories; 