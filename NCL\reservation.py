from loguru import logger
from NCL.utils import NCLUtils
from NCL.login import NCLLogin
import asyncio


class Reservation:

    @staticmethod
    async def click_new_reservation_and_continue(page):
        try:
            logger.info("Looking for New Reservation button")
            new_res_link = await page.wait_for_selector(
                "//a[text()='New Reservation']", state="visible", timeout=10000
            )

            logger.info("Clicking New Reservation button")
            await new_res_link.click()

            try:
                logger.info("Checking for Continue button on agency page")
                continue_button = await page.wait_for_selector(
                    "#SWXMLForm_SelectAgency_action_DoContinue",
                    state="visible",
                    timeout=8000
                )

                logger.info("Clicking Continue button")
                await continue_button.click()
            except:
                logger.info("No Continue button needed or already on search form")

            await page.wait_for_selector("#SWXMLForm_SearchVacation_From", timeout=10000)
            logger.success("Successfully navigated to search form")
            return True
        except Exception as e:
            logger.error(f"Navigation error: {str(e)}")
            return False

    @staticmethod
    async def fill_search_form(page):
        try:
            date_input = input("Enter travel date (MM/DD/YYYY): ")
            ship_input = input("Enter ship name: ")
            adults = input("Number of adults: ")
            children = input("Number of children (enter 0 if none): ")
            infants = input("Number of infants (enter 0 if none): ")

            logger.info(f"Setting date: {date_input}")
            date_field = await page.wait_for_selector(
                "#SWXMLForm_SearchVacation_From", state="visible", timeout=8000
            )
            await date_field.fill("")
            await date_field.fill(date_input)
            await date_field.press("Tab")

            logger.info(f"Setting ship: {ship_input}")
            ship_field = await page.wait_for_selector(
                "#SWXMLForm_SearchVacation_Ship", state="visible", timeout=8000
            )
            await ship_field.fill("")
            await ship_field.fill(ship_input)
            await ship_field.press("ArrowDown")
            await ship_field.press("Enter")

            if adults:
                logger.info(f"Setting adults: {adults}")
                adults_field = page.locator("#SWXMLForm_SearchVacation_age_ADULT")
                await adults_field.fill("")
                await adults_field.fill(adults)

            if children and children != "0":
                logger.info(f"Setting children: {children}")
                children_field = page.locator("#SWXMLForm_SearchVacation_age_CHILD")
                await children_field.fill("")
                await children_field.fill(children)

            if infants and infants != "0":
                logger.info(f"Setting infants: {infants}")
                infants_field = page.locator("#SWXMLForm_SearchVacation_age_INFANT")
                await infants_field.fill("")
                await infants_field.fill(infants)

            logger.success("Search form filled successfully")
            return True
        except Exception as e:
            logger.error(f"Form fill error: {str(e)}")
            return False

    @staticmethod
    async def start_search(page):
        try:
            logger.info("Clicking Start Search button")
            search_button = await page.wait_for_selector(
                "#SWXMLForm_SearchVacation_action_DoSearchVacation",
                state="visible",
                timeout=8000
            )
            await search_button.click()

            current_url = page.url
            await page.wait_for_url(lambda url: url != current_url, timeout=15000)
            logger.success("Search initiated successfully")
            return True
        except Exception as e:
            logger.error(f"Search error: {str(e)}")
            return False

    @classmethod
    async def make_reservation(cls):
        NCL_Login = NCLLogin()
        creds = NCL_Login.get_credentials()

        page = await NCL_Login.setup_driver(headless=True)

        try:
            logger.info("Starting login process")
            if not await NCL_Login.login(page, creds["url"], creds["username"],
                                   creds["password"]):
                logger.error("Login failed. Aborting.")
                if hasattr(page, '_context'):
                    await page._context.close()
                if hasattr(page, '_browser'):
                    await page._browser.close()
                if hasattr(page, '_playwright'):
                    await page._playwright.stop()
                return

            logger.info("Navigating to search form")
            if not await cls.click_new_reservation_and_continue(page):
                logger.error("Navigation failed. Aborting.")
                if hasattr(page, '_context'):
                    await page._context.close()
                if hasattr(page, '_browser'):
                    await page._browser.close()
                if hasattr(page, '_playwright'):
                    await page._playwright.stop()
                return

            logger.info("Filling search form")
            if not await cls.fill_search_form(page):
                logger.error("Form filling failed. Aborting.")
                if hasattr(page, '_context'):
                    await page._context.close()
                if hasattr(page, '_browser'):
                    await page._browser.close()
                if hasattr(page, '_playwright'):
                    await page._playwright.stop()
                return

            logger.info("Starting search process")
            if not await cls.start_search(page):
                logger.error("Search failed. Aborting.")
                if hasattr(page, '_context'):
                    await page._context.close()
                if hasattr(page, '_browser'):
                    await page._browser.close()
                if hasattr(page, '_playwright'):
                    await page._playwright.stop()
                return

            NCL_Utils = NCLUtils()
            logger.info("Taking screenshot of final results page")
            await NCL_Utils.save_screenshot(page, "final_results.png")
            logger.success("Screenshot saved")

            logger.success("Reservation process completed successfully!")
            input("Press Enter to close the browser...")

        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            await NCL_Utils.save_screenshot(page, "final_state.png")
            logger.info("Error state screenshot saved")
        finally:
            if hasattr(page, '_context'):
                await page._context.close()
            if hasattr(page, '_browser'):
                await page._browser.close()
            if hasattr(page, '_playwright'):
                await page._playwright.stop()