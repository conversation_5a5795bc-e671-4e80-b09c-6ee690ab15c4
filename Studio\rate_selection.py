from loguru import logger
from Core.ui_logger import ui_log
from Studio.optimal_rate_code import OptimalRateSelector


class RateSelector:
    """
    Handles rate selection functionality during the cruise booking process.
    
    This class provides methods to extract available rate options from the booking page,
    select the optimal rate based on a specified strategy, and proceed with the selection
    by clicking the continue button.
    """

    async def extract_rate_data(self, rates_wrapper, cabin_number, timestamp):
        """
        Extract available rate options from the rate selection table.
        
        This method parses the rate selection table to extract details about each available
        rate option, including rate name, price, promotions, and selection elements.
        
        Args:
            rates_wrapper (Locator): Playwright locator for the container with rate options
            cabin_number (int): The cabin number being processed (for logging)
            timestamp (str): Timestamp string for logging and screenshots
            
        Returns:
            list: A list of dictionaries containing rate information including rate name,
                 price, promotions, and UI elements for selection
        """
        # Locate the table and all rows with rate information
        table = rates_wrapper.locator('table').first
        rate_rows = await table.locator('tr[data-category-row]').all()

        # ui_log(f"Found {len(rate_rows)} available rates", 
        #        session_id=self.session_id, 
        #        cabin_id=cabin_number, 
        #        step="rates_found")
        
        logger.info(
            f"Processing {len(rate_rows)} available rates for cabin {cabin_number}"
        )

        table_data = []

        # Process each rate row to extract its details
        for idx, row in enumerate(rate_rows, 1):
            try:
                # Get the category row ID for identifying this rate later
                category_row_id = await row.get_attribute('data-category-row')

                # Extract the rate name from the rate column
                rate_name = await row.locator("td.grouped-category-column-rate"
                                        ).text_content()
                rate_name = rate_name.strip()

                # Extract the price from the price column
                price = await row.locator("td.grouped-category-column-price"
                                    ).text_content()
                price = price.strip()

                # Get the select button element for this rate
                select_button = row.locator("label.button[for^='category_']").first

                # Default empty promotion details
                promo_details = ""
                # Check if there are any promotions associated with this rate
                promo_cell = row.locator("td.grouped-category-column-promotions").first

                # Extract promotion details if available (without opening modal)
                if await promo_cell.count() and 'has-promotions' in await promo_cell.get_attribute(
                        'class'):
                    promo_button = promo_cell.locator("button.info-button").first

                    # Get promotion title or use generic message
                    promo_details = await promo_cell.get_attribute(
                        'title'
                    ) or "Promotions available"

                # Store all rate information in a structured format
                rate_info = {
                    'rate': rate_name, 'price': price, 'promotions': promo_details,
                    'button': select_button, 'row': row, 'row_id': category_row_id
                }

                table_data.append(rate_info)
                #logger.debug(f"Rate {idx}: {rate_name} - Price: {price}")

            except Exception as e:
                logger.error(
                    f"Error processing rate {idx} for cabin {cabin_number}: {str(e)}"
                )
                continue

        #ui_log(f"Processed {len(table_data)} rate options", 
        #       session_id=self.session_id, 
        #       cabin_id=cabin_number, 
        #       step="rates_processed")
               
        logger.info(f"Extracted {len(table_data)} rate options")
        return table_data

    async def handle_mobile_dialog(self, cabin_number, timestamp):
        """
        Handle the mobile dialog that sometimes appears after clicking the select button.
        
        This method checks for the presence of a mobile dialog with class '.is-mobile-dialog.is-expanded',
        extracts the discount text if present, and clicks the continue button.
        
        Args:
            cabin_number (int): The cabin number being processed (for logging)
            timestamp (str): Timestamp string for logging
            
        Returns:
            str: The extracted discount text or "NA" if no dialog appears
        """
        try:
            # Wait a short time for the dialog to potentially appear
            await self.page.wait_for_timeout(2000)
            
            # Check if the mobile dialog is present and expanded
            mobile_dialog_selector = ".is-mobile-dialog.is-expanded"
            dialog_count = await self.page.locator(mobile_dialog_selector).count()
            
            if dialog_count > 0:
                logger.info(f"Mobile dialog detected for cabin {cabin_number}")
                ui_log(f"Mobile dialog appeared - extracting discount text", 
                       session_id=self.session_id, 
                       cabin_id=cabin_number, 
                       step="mobile_dialog_detected", 
                       module="Studio")
                
                # Get the dialog element
                dialog = self.page.locator(mobile_dialog_selector).first
                
                # Wait for dialog content to be fully loaded
                await self.page.wait_for_timeout(2000)  # Increased delay for complete text extraction
                
                # Extract text from specific promotional content areas
                discount_text_parts = []
                processed_texts = set()  # Track processed content to avoid duplicates
                
                # Helper function to clean promotional text
                def clean_promotional_text(text):
                    if not text:
                        return ""
                    
                    # Split into lines and clean each line
                    lines = text.split('\n')
                    cleaned_lines = []
                    
                    for line in lines:
                        line = line.strip()
                        if not line:
                            continue
                            
                        # Skip only very specific technical metadata lines
                        if any(skip_phrase in line.lower() for skip_phrase in [
                            'type auto applied', 'id 282', 'details select', 'global.', 'bis_skin_checked'
                        ]):
                            continue
                            
                        # Skip standalone words that are metadata
                        if line.lower() in ['type', 'details', 'select', 'auto applied', 'id']:
                            continue
                            
                        # Skip lines that are just numbers
                        if line.isdigit() and len(line) < 15:
                            continue
                            
                        cleaned_lines.append(line)
                    
                    # Join lines and normalize whitespace
                    cleaned_text = ' '.join(cleaned_lines)
                    cleaned_text = ' '.join(cleaned_text.split())  # Normalize whitespace
                    
                    # Remove standalone "ID" strings at the end
                    cleaned_text = cleaned_text.strip()
                    while cleaned_text.endswith(' ID'):
                        cleaned_text = cleaned_text[:-3].strip()
                    
                    return cleaned_text
                
                # Target promotion wrapper and selected promotion items - ordered by specificity
                promotion_selectors = [
                    f"{mobile_dialog_selector} .promotion-item-content",
                    f"{mobile_dialog_selector} .promotion-wrapper",
                    f"{mobile_dialog_selector} .promotion-item.is-active",
                    f"{mobile_dialog_selector} [bis_skin_checked='1']",
                    f"{mobile_dialog_selector} .promotion-item"
                ]
                
                for selector in promotion_selectors:
                    try:
                        elements = await self.page.locator(selector).all()
                        for element in elements:
                            if await element.is_visible():
                                element_text = await element.text_content()
                                if element_text and element_text.strip():
                                    # Clean the promotional text
                                    cleaned_text = clean_promotional_text(element_text)
                                    
                                    if not cleaned_text or len(cleaned_text) < 30:
                                        continue
                                    
                                    # Create a normalized version for duplicate checking (more lenient)
                                    normalized_text = cleaned_text.lower().replace(' ', '').replace('\n', '')
                                    
                                    # Check if this is genuinely different content
                                    is_duplicate = False
                                    for existing in discount_text_parts:
                                        existing_normalized = existing.lower().replace(' ', '').replace('\n', '')
                                        # Only consider it a duplicate if there's significant overlap (80%+)
                                        if len(normalized_text) > 0 and len(existing_normalized) > 0:
                                            overlap = len(set(normalized_text) & set(existing_normalized))
                                            similarity = overlap / max(len(set(normalized_text)), len(set(existing_normalized)))
                                            if similarity > 0.8:
                                                is_duplicate = True
                                                break
                                    
                                    if not is_duplicate and normalized_text not in processed_texts:
                                        discount_text_parts.append(cleaned_text)
                                        processed_texts.add(normalized_text)
                                        logger.debug(f"Found promotional content from {selector}: {len(cleaned_text)} characters - '{cleaned_text[:50]}...'")
                    except Exception as e:
                        logger.debug(f"Error with selector {selector}: {e}")
                        continue
                
                # If no specific promotional content found, fallback to dialog text
                if not discount_text_parts:
                    # Try to extract from the entire dialog
                    full_dialog_text = await dialog.text_content()
                    cleaned_full_text = clean_promotional_text(full_dialog_text)
                    
                    if cleaned_full_text and len(cleaned_full_text) > 30:
                        discount_text = cleaned_full_text
                    else:
                        discount_text = "NA"
                else:
                    # Join all found promotional content with line breaks
                    discount_text = "\n".join(discount_text_parts)
                
                # Additional fallback: if we only got short content, try broader extraction
                if discount_text_parts and all(len(part) < 100 for part in discount_text_parts):
                    logger.debug("Only found short promotional content, trying broader extraction...")
                    
                    # Try alternative selectors for longer content
                    broader_selectors = [
                        f"{mobile_dialog_selector} .promotion-content",
                        f"{mobile_dialog_selector} .modal-body",
                        f"{mobile_dialog_selector} .promo-text",
                        mobile_dialog_selector  # The dialog itself as last resort
                    ]
                    
                    for broad_selector in broader_selectors:
                        try:
                            broad_elements = await self.page.locator(broad_selector).all()
                            for broad_element in broad_elements:
                                if await broad_element.is_visible():
                                    broad_text = await broad_element.text_content()
                                    if broad_text:
                                        cleaned_broad_text = clean_promotional_text(broad_text)
                                        if cleaned_broad_text and len(cleaned_broad_text) > 200:
                                            # Check if this contains significantly more content
                                            if not any(existing in cleaned_broad_text for existing in discount_text_parts):
                                                discount_text_parts.append(cleaned_broad_text)
                                                logger.debug(f"Found longer promotional content via broader extraction: {len(cleaned_broad_text)} characters")
                                                break
                        except Exception as e:
                            logger.debug(f"Error with broader selector {broad_selector}: {e}")
                            continue
                        
                        if any(len(part) > 200 for part in discount_text_parts):
                            break
                    
                    # Update final discount text
                    discount_text = "\n".join(discount_text_parts)
                
                logger.info(f"Extracted discount text for cabin {cabin_number}: {discount_text[:100]}...")
                ui_log(f"Extracted discount text: {discount_text[:50]}...", 
                       session_id=self.session_id, 
                       cabin_id=cabin_number, 
                       step="discount_text_extracted", 
                       module="Studio")
                
                # Look for and click the continue button in the dialog
                continue_button_selectors = [
                    f"{mobile_dialog_selector} button:has-text('Continue')",
                    f"{mobile_dialog_selector} button.continue",
                    f"{mobile_dialog_selector} button[type='submit']",
                    f"{mobile_dialog_selector} .button.is-primary",
                    f"{mobile_dialog_selector} button"
                ]
                
                continue_button_clicked = False
                for selector in continue_button_selectors:
                    try:
                        button = self.page.locator(selector).first
                        if await button.count() > 0 and await button.is_visible():
                            await button.click()
                            continue_button_clicked = True
                            logger.info(f"Clicked continue button in mobile dialog for cabin {cabin_number}")
                            ui_log(f"Clicked continue button in mobile dialog", 
                                   session_id=self.session_id, 
                                   cabin_id=cabin_number, 
                                   step="mobile_dialog_continue_clicked", 
                                   module="Studio")
                            break
                    except Exception as e:
                        logger.debug(f"Continue button selector {selector} failed: {e}")
                        continue
                
                if not continue_button_clicked:
                    logger.warning(f"Could not find or click continue button in mobile dialog for cabin {cabin_number}")
                    ui_log(f"Continue button not found in mobile dialog", 
                           session_id=self.session_id, 
                           cabin_id=cabin_number, 
                           step="mobile_dialog_continue_not_found", 
                           module="Studio")
                
                # Wait for dialog to close
                await self.page.wait_for_timeout(1000)
                
                return discount_text
                
            else:
                logger.info(f"No mobile dialog detected for cabin {cabin_number}")
                ui_log(f"No mobile dialog appeared - proceeding normally", 
                       session_id=self.session_id, 
                       cabin_id=cabin_number, 
                       step="no_mobile_dialog", 
                       module="Studio")
                return "NA"
                
        except Exception as e:
            logger.error(f"Error handling mobile dialog for cabin {cabin_number}: {str(e)}")
            ui_log(f"Error handling mobile dialog: {str(e)}", 
                   session_id=self.session_id, 
                   cabin_id=cabin_number, 
                   step="mobile_dialog_error", 
                   module="Studio")
            return "NA"

    async def select_rate(self, table_data, cabin_number, rate_selection_strategy="Cheapest"):
        """
        Select the optimal rate based on the specified strategy.
        
        This method uses the OptimalRateSelector to determine the best rate based on
        the given strategy, then selects that rate by clicking its button.
        
        Args:
            table_data (list): List of dictionaries containing rate information
            cabin_number (int): The cabin number being processed (for logging)
            rate_selection_strategy (str): Strategy to use for selecting the rate
                                          (e.g., "Cheapest", "Value Sails", etc.)
            
        Returns:
            tuple: (selected_rate_info, discount_text) - The selected rate information and any discount text from modal
            
        Raises:
            Exception: If no rate options are available
        """
        # Ensure we have at least one rate option
        if not table_data:
            error_msg = f"No rate options found for cabin {cabin_number}"
            #ui_log(error_msg, 
            #       session_id=self.session_id, 
            #       cabin_id=cabin_number, 
            #       step="rates_error")
            raise Exception(error_msg)

        # ui_log(f"Selecting best rate using {rate_selection_strategy} strategy", 
        #        session_id=self.session_id, 
        #        cabin_id=cabin_number, 
        #        step="rate_strategy")

        # Use the OptimalRateSelector to determine which rate to select based on strategy
        selected_rate_index = OptimalRateSelector.select_rate_index(
            table_data, rate_selection_strategy
        )
        selected_rate = table_data[selected_rate_index]

        # Clean the rate name to show only the main name (before global.learn_more)
        # This improves readability in logs
        clean_rate_name = selected_rate['rate'].split('global.learn_more')[0].strip()
        clean_price = selected_rate['price'].strip()

        ui_log(f"Chosen rate code: {clean_rate_name}", 
               session_id=self.session_id, 
               cabin_id=cabin_number, 
               step="rate_selected", module="Studio")

        logger.info(
            f"Selected rate for cabin {cabin_number}: {clean_rate_name} ({rate_selection_strategy})"
        )

        # Get the button element for the selected rate
        select_button = selected_rate['button']
        # Ensure the button is visible in the viewport
        await select_button.scroll_into_view_if_needed()

        # Click the button to select this rate
        # ui_log("Clicking to select rate", 
        #        session_id=self.session_id, 
        #        cabin_id=cabin_number, 
        #        step="rate_clicking")
        await select_button.click()

        # Handle any mobile dialog that might appear after clicking select
        discount_text = await self.handle_mobile_dialog(cabin_number, "")

        return selected_rate, discount_text

    async def click_continue_button(self, selected_rate, timestamp):
        """
        Find and click the continue button after rate selection.
        
        This method locates and clicks the appropriate continue button to proceed
        after selecting a rate. It includes multiple fallback mechanisms and retry
        logic to handle potential click failures.
        
        Args:
            selected_rate (dict): The selected rate information
            timestamp (str): Timestamp string for logging
            
        Returns:
            bool: True if the continue button was successfully clicked or if already
                 on the cabin selection page
                 
        Raises:
            Exception: If the continue button cannot be found and we're not already
                      on the cabin selection page
        """
        # First check if we've already navigated to the cabin selection page
        # This prevents unnecessary button clicks
        cabin_table_selector = "table.table.has-sticky-header th.cruise-column-cabin"
        if await self.page.locator(cabin_table_selector).count() > 0:
            #ui_log("Already on cabin selection page", 
            #       session_id=self.session_id, 
            #       step="cabin_selection_page")
            logger.info(
                "Already on cabin selection page, skipping continue button"
            )
            return True

        # ui_log("Looking for continue button", 
        #        session_id=self.session_id, 
        #        step="continue_search")

        # Wait for body to ensure page is loaded
        await self.page.wait_for_selector("body", state="visible")
        continue_button = None

        # Try multiple button selectors in order of specificity
        # This increases robustness in case page structure varies
        button_selectors = [
            # Most specific selector using the rate's row ID
            f"button[data-rate-selected='category_{selected_rate['row_id']}']",
            # Primary button styles
            "button.button.is-primary[type='submit']",
            # Button with role and type attributes
            "button[role='button'][type='submit']",
            # Button with specific text
            "button:has-text('Continue')",
            # Any submit button
            "button[type='submit']",
            # Submit button in a form
            "form button[type='submit']"
        ]

        # Try each selector until we find a visible button
        for selector in button_selectors:
            try:
                logger.debug(f"Trying selector: {selector}")
                button = await self.page.wait_for_selector(
                    selector, state="visible", timeout=15000
                )

                if button and await button.is_visible():
                    continue_button = button
                    # ui_log("Found continue button", 
                    #        session_id=self.session_id, 
                    #        step="continue_found")
                    logger.info(f"Found continue button")
                    break
            except Exception as e:
                logger.debug(f"Selector {selector} not found: {e}")
                continue

        # If we couldn't find a button using specific selectors, try a more generic approach
        if not continue_button:
            #ui_log("Trying alternative button search", 
            #       session_id=self.session_id, 
            #       step="continue_alt_search")
            try:
                # Look for any visible button that could be a continue button
                buttons = await self.page.query_selector_all("button:visible")
                for button in buttons:
                    button_text = await button.text_content()
                    if "continue" in button_text.lower():
                        continue_button = button
                        #ui_log("Found continue button by text", 
                        #       session_id=self.session_id, 
                        #       step="continue_text_found")
                        logger.info(f"Found continue button by text: {button_text}")
                        break
            except Exception as e:
                logger.error(f"Error finding continue button by text: {e}")

        # If we still don't have a button, check if we have already advanced to the cabin page
        if not continue_button:
            #ui_log("Continue button not found, checking current page", 
            #       session_id=self.session_id, 
            #       step="continue_check_page")
            # Check if we've already moved to cabin selection somehow
            try:
                cabin_content = await self.page.query_selector("div.cabin-selection-summary")
                if cabin_content:
                    #ui_log("Already on next page, continuing", 
                    #       session_id=self.session_id, 
                    #       step="continue_already_done")
                    logger.info("Already on next page, no need for continue button")
                    return True
            except Exception as e:
                logger.debug(f"Error checking if on cabin page: {e}")

        # If we found a button, click it and wait for navigation
        if continue_button:
            #ui_log("Clicking continue button", 
            #       session_id=self.session_id, 
            #       step="continue_clicking")
            try:
                await continue_button.click()
                #ui_log("Continue button clicked", 
                #       session_id=self.session_id, 
                #       step="continue_clicked")
                logger.info("Clicked continue button, waiting for page load")
                return True
            except Exception as e:
                #ui_log("Error clicking continue button", 
                #       session_id=self.session_id, 
                #       step="continue_click_error")
                logger.error(f"Error clicking continue button: {e}")
                raise Exception(f"Error clicking continue button: {e}")
        else:
            error_msg = "Could not continue to cabin selection. Retrying"
            ui_log(error_msg, 
                   session_id=self.session_id, 
                   step="continue_not_found", module="Studio")
            logger.error(error_msg)
            raise Exception(error_msg)
