'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { User, login as authLogin, logout as authLogout, getCurrentUser, isAuthenticated, getToken as authGetToken } from '../services/auth';
import { ProviderType } from '../components/booking/ProviderSelector';
import { fetchProviders, fetchAgencies } from '../services/api';
import { Provider } from '../services/auth';

// Define the context shape
interface AuthContextType {
  user: User | null;
  isLoggedIn: boolean;
  isAdmin: boolean;
  isSubAdmin: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
  getCurrentUser: () => User | null;
  getToken: () => string | null;
  hasProviderAccess: (provider: ProviderType) => boolean;
  getAccessibleAgencies: () => string[];
  canManageUser: (targetUser: User) => boolean;
  canAccessAgency: (agency: string) => boolean;
  isSubAdminOnly: boolean;
}

// Create the Auth Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider Component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [accessibleProviders, setAccessibleProviders] = useState<Provider[]>([]);
  const [allAgencies, setAllAgencies] = useState<string[]>([]);
  const [accessibleAgencies, setAccessibleAgencies] = useState<string[]>([]);
  const router = useRouter();

  // Check if user is logged in when the app loads
  useEffect(() => {
    const checkAuth = () => {
      try {
        if (isAuthenticated()) {
          const userData = getCurrentUser();
          setUser(userData);
        }
      } catch (error) {
        console.error('Auth check error:', error);
        // If we can't get user data, log them out
        handleLogout();
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Fetch providers user has access to
  useEffect(() => {
    const loadProviders = async () => {
      if (user) {
        try {
          const providers = await fetchProviders();
          setAccessibleProviders(providers);
        } catch (e) {
          console.error('Error loading accessible providers:', e);
          setAccessibleProviders([]);
        }
      } else {
        setAccessibleProviders([]);
      }
    };
    loadProviders();
  }, [user]);

  // Derive unique accessible agencies from providers
  useEffect(() => {
    const agencies = Array.from(new Set((accessibleProviders || []).map(p => p.agency)));
    setAccessibleAgencies(agencies);
  }, [accessibleProviders]);

  // Fetch all agencies for admin
  useEffect(() => {
    const loadAgencies = async () => {
      try {
        const agencies = await fetchAgencies();
        setAllAgencies(agencies);
      } catch (e) {
        console.error('Error loading all agencies:', e);
        setAllAgencies([]);
      }
    };
    loadAgencies();
  }, []);

  const handleLogin = async (username: string, password: string) => {
    setLoading(true);
    try {
      const response = await authLogin(username, password);
      setUser(response.user);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await authLogout();
    setUser(null);
    router.push('/');
  };

  // Check if user has access to a specific provider
  const hasProviderAccess = (provider: ProviderType): boolean => {
    if (!user) return false;
    
    // Previously admin could access all providers, now we respect their assigned access too
    // Check if user has this provider in their provider_access array
    return Array.isArray(user.provider_access) && user.provider_access.includes(provider);
  };

  // Get all agencies the user has access to based on their provider access
  const getAccessibleAgencies = (): string[] => {
    if (!user) return [];
    
    // Admin has access to all agencies
    if (user.role === 'admin') return allAgencies;
    // Return unique agencies derived from API-fetched providers
    return accessibleAgencies;
  };

  // Check if user can manage another user
  const canManageUser = (targetUser: User): boolean => {
    if (!user) return false;
    
    // Admins can manage all users
    if (user.role === 'admin') return true;
    
    // Sub-admins cannot manage admins
    if (user.role === 'sub_admin' && targetUser.role === 'admin') return false;
    
    // Sub-admins can only manage users within their agency
    if (user.role === 'sub_admin') {
      const userAgencies = getAccessibleAgencies();
      const targetUserAgency = targetUser.agency;
      return targetUserAgency ? userAgencies.includes(targetUserAgency) : false;
    }
    
    return false;
  };

  // Check if user can access a specific agency
  const canAccessAgency = (agency: string): boolean => {
    if (!user) return false;
    
    // Admins can access all agencies
    if (user.role === 'admin') return true;
    
    // Sub-admins can only access their assigned agencies
    if (user.role === 'sub_admin') {
      const accessibleAgencies = getAccessibleAgencies();
      return accessibleAgencies.includes(agency);
    }
    
    return false;
  };

  // Derived state
  const isLoggedIn = user !== null;
  const isAdmin = user?.role === 'admin';
  const isSubAdmin = user?.role === 'admin' || user?.role === 'sub_admin';
  const isSubAdminOnly = user?.role === 'sub_admin';

  // Create the context value
  const contextValue: AuthContextType = {
    user,
    isLoggedIn,
    isAdmin,
    isSubAdmin,
    login: handleLogin,
    logout: handleLogout,
    loading,
    getCurrentUser,
    getToken: authGetToken,
    hasProviderAccess,
    getAccessibleAgencies,
    canManageUser,
    canAccessAgency,
    isSubAdminOnly
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 