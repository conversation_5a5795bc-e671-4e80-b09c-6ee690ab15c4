'use client';

import React, { useState, useEffect } from 'react';
import LoginModal from '../LoginModal';
import { useAuth } from '../../context/AuthContext';
import { useRouter } from 'next/navigation';

interface HeaderProps {
  activeTab: 'Home' | 'Guide' | 'Features' | 'Booking';
  onTabChange: (tab: 'Home' | 'Guide' | 'Features' | 'Booking') => void;
}

export default function Header({ activeTab, onTabChange }: HeaderProps) {
  const [scrolled, setScrolled] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const router = useRouter();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Use the auth context
  const { isLoggedIn, logout, user, isAdmin, isSubAdmin } = useAuth();

  // Handle scroll event to track when page is scrolled
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    // Check scroll position immediately on component mount
    handleScroll();

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleLogout = () => {
    logout();
    onTabChange('Home');
  };

  const goToAdmin = () => {
    router.push('/admin');
  };

  // Check user access to booking based on status
  const canAccessBooking = isLoggedIn && (!user?.status || user.status === 'approved');

  // Prevent booking access if not approved
  const handleBookingClick = () => {
    if (canAccessBooking) {
      onTabChange('Booking');
    } else if (isLoggedIn && user?.status === 'pending') {
      alert('Your account is pending approval. Please wait for an administrator to approve your account.');
    } else {
      setIsLoginModalOpen(true);
    }
  };

  return (
    <>
      <header className={`sticky top-0 z-20 transition-all duration-300 shadow-lg border-b border-gray-300/30 ${scrolled ? 'bg-gray-200/85 backdrop-blur-sm' : 'bg-transparent'}`} style={{ fontFamily: 'var(--font-geist-sans, sans-serif)' }}>
        <div className="container mx-auto px-2 md:px-4 py-1">
          <nav className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="relative w-16 h-16 md:w-20 lg:w-24 md:h-20 mr-2 md:mr-3 ml-1 md:ml-2">
                <img
                  src="/PVlogo-1024x780.webp"
                  alt="ProcessVenue Logo"
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="hidden md:block">
                <div className="text-sm md:text-lg lg:text-xl font-semibold text-blue-900">Human Augmented Intelligence</div>
                <div className="flex items-center mt-0 text-sm md:text-lg lg:text-xl" style={{ fontFamily: 'Times New Roman' }}>Powered by<span className="ml-1 text-blue-900 font-bold" style={{ fontFamily: 'Times New Roman' }}> ProcessVenue</span></div>
                <div className="text-xs text-blue-900 font-semibold opacity-100 bg-gradient-to-r from-sky-200 to-green-300 rounded-full px-2 py-0.5 align-middle inline-block">ALPHA VERSION</div>
              </div>
              <div className="md:hidden">
                <div className="text-sm font-semibold text-blue-900">HAI</div>
                <div className="text-xs text-blue-900 font-semibold opacity-100 bg-gradient-to-r from-sky-200 to-green-300 rounded-full px-1 py-0.5 align-middle inline-block">ALPHA</div>
              </div>
            </div>
            <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-4 lg:space-x-8">
              <button
                onClick={() => onTabChange('Home')}
                className={`nav-link text-sky-900 hover:text-purple-800 transition font-semibold text-xs sm:text-sm md:text-base lg:text-lg ${activeTab === 'Home' ? 'active' : ''}`}
              >
                Home
              </button>
              <button
                onClick={() => onTabChange('Features')}
                className={`nav-link text-sky-900 hover:text-purple-800 transition font-semibold text-xs sm:text-sm md:text-base lg:text-lg ${activeTab === 'Features' ? 'active' : ''}`}
              >
                Features
              </button>
              <button
                onClick={() => onTabChange('Guide')}
                className={`nav-link text-sky-900 hover:text-purple-800 transition font-semibold text-xs sm:text-sm md:text-base lg:text-lg ${activeTab === 'Guide' ? 'active' : ''} hidden sm:block`}
              >
                User Guide
              </button>

              {isLoggedIn ? (
                <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-4">

                  <button
                    onClick={handleBookingClick}
                    className="bg-gradient-to-r from-blue-100 to-green-100 text-black px-2 sm:px-3 md:px-4 py-1.5 rounded-full shadow-lg hover:from-blue-200 hover:to-green-200 transition-all duration-300 text-xs sm:text-sm md:text-base w-fit flex items-center"
                  >
                    Launch
                  </button>
                  {!isSubAdmin && (
                    <button
                      onClick={handleLogout}
                      className="bg-gradient-to-r from-gray-300 to-green-200 text-black px-2 sm:px-3 md:px-4 py-1.5 rounded-full shadow-lg hover:from-blue-200 hover:to-green-200 transition-all duration-300 text-xs sm:text-sm md:text-base w-fit flex items-center">
                      Logout
                    </button>
                  )}
 

                  {isSubAdmin && (
                    <div className="relative">
                      <button
                        onClick={() => setIsDropdownOpen(prev => !prev)}
                        className="bg-purple-100 text-purple-800 px-2 sm:px-3 md:px-5 py-1.5 md:py-2 shadow-gray-400 shadow-md rounded-full hover:bg-purple-200 transition-all duration-300 text-xs sm:text-sm md:text-base"
                      >
                        Dashboard
                      </button>
                      {isDropdownOpen && (
                        <div className="absolute right-0 mt-2 w-40 sm:w-48 bg-slate-100 border border-gray-200 rounded-lg shadow-md shadow-gray-700 z-50">
                          <button
                            onClick={() => {
                              setIsDropdownOpen(false);
                              goToAdmin();
                            }}
                            className="block w-full text-left px-3 sm:px-4 py-2 text-gray-800 hover:bg-gray-300 rounded-t-lg text-xs sm:text-sm"
                          >
                            Go to Dashboard
                          </button>
                          <button
                            onClick={() => {
                              setIsDropdownOpen(false);
                              handleLogout();
                            }}
                            className="block w-full text-left px-3 sm:px-4 py-2 text-gray-800 hover:bg-gray-300 rounded-b-lg text-xs sm:text-sm"
                          >
                            Logout
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <button
                  onClick={() => setIsLoginModalOpen(true)}
                  className="bg-gradient-to-r from-blue-100 to-green-100 text-black px-2 sm:px-3 md:px-4 py-1.5 rounded-full shadow-lg hover:from-blue-200 hover:to-green-200 transition-all duration-300 text-xs sm:text-sm md:text-base w-fit flex items-center"
                >
                  Login
                </button>
              )}
            </div>
          </nav>
        </div>
      </header>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        onTabChange={onTabChange}
      />
    </>
  );
}
