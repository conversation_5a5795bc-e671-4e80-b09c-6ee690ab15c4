import logging
from datetime import datetime
from db_service import get_db_connection, db_connection
from db.user_auth import hash_password
import asyncio

logger = logging.getLogger("db.seed_admin")
logging.basicConfig(level=logging.INFO)

async def seed_admin():
    async with db_connection() as conn:

        # Dummy admin user data
        user_id = 'admin'
        username = 'admin'
        # Hash the default password for storage
        password = hash_password('admin')
        email = '<EMAIL>'
        full_name = 'Administrator'
        role = 'admin'
        agency = 'STUDIO'
        portal_access = '[]'
        status = 'approved'
        provider_access = '["Studio.Sales.CabinCloseOut", "Studio.Res.CabinCloseOut", "NCL", "Cruising Power", "OneSource"]'
        created_at = datetime.utcnow().isoformat()

        insert_query = """
        INSERT INTO users (user_id, username, password, email, full_name, role, agency, portal_access, created_at, status, provider_access)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        ON CONFLICT (user_id) DO NOTHING;
        """

        try:
            await conn.execute(insert_query, 
                user_id, username, password, email,
                full_name, role, agency, portal_access,
                created_at, status, provider_access
            )
            logger.info("Admin user seeded (or already exists).")
        except Exception as e:
            logger.error(f"Error seeding admin user: {e}")

if __name__ == "__main__":
    asyncio.run(seed_admin()) 