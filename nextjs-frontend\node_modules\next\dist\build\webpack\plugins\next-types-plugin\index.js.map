{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/next-types-plugin/index.ts"], "names": ["NextTypesPlugin", "PLUGIN_NAME", "createTypeGuardFile", "fullPath", "relativePath", "options", "type", "HTTP_METHODS", "map", "method", "join", "slots", "slot", "collectNamedSlots", "<PERSON><PERSON><PERSON>", "layoutDir", "path", "dirname", "items", "fs", "readdir", "withFileTypes", "item", "isDirectory", "name", "startsWith", "push", "slice", "pluginState", "getProxiedPluginState", "routeTypes", "edge", "static", "dynamic", "node", "extra", "formatRouteToRouteType", "route", "isDynamic", "isDynamicRoute", "split", "part", "endsWith", "routeType", "redirectsRewritesTypesProcessed", "addRedirectsRewritesRouteTypes", "rewrites", "redirects", "addExtraRoute", "source", "tokens", "parse", "Array", "isArray", "possibleNormalizedRoutes", "slugCnt", "append", "suffix", "i", "length", "fork", "<PERSON><PERSON><PERSON><PERSON>", "token", "slug", "modifier", "prefix", "pattern", "test", "normalizedRoute", "rewrite", "beforeFiles", "afterFiles", "fallback", "redirect", "createRouteDefinitions", "staticRouteTypes", "dynamicRouteTypes", "routeTypesFallback", "appTypesBasePath", "constructor", "dir", "distDir", "appDir", "dev", "isEdgeServer", "pageExtensions", "pagesDir", "typedRoutes", "distDirAbsolutePath", "originalRewrites", "originalRedirects", "getRelativePathFromAppTypesDir", "moduleRelativePathToAppDir", "moduleAbsolutePath", "moduleInAppTypesAbsolutePath", "relative", "collectPage", "filePath", "isApp", "sep", "isPages", "normalizeAppPath", "denormalizePagePath", "ensureLeadingSlash", "getPageFromPath", "apply", "compiler", "assetDirRelative", "handleModule", "mod", "assets", "resource", "layer", "WEBPACK_LAYERS", "reactServerComponents", "appRouteHandler", "IS_LAYOUT", "IS_PAGE", "IS_ROUTE", "relativePathToApp", "typePath", "replace", "relativeImportPath", "normalizePathSep", "assetPath", "sources", "RawSource", "hooks", "compilation", "tap", "processAssets", "tapAsync", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "callback", "promises", "chunkGroups", "for<PERSON>ach", "chunkGroup", "chunks", "chunk", "chunkModules", "chunkGraph", "getChunkModulesIterable", "anyModule", "modules", "concatenatedMod", "Promise", "all", "packageJsonAssetPath", "devPageFiles", "file", "linkAssetPath"], "mappings": ";;;;+BAofaA;;;eAAAA;;;iEAjfE;yBACkB;8BACX;6DACL;2BAEc;qCACK;oCACD;kCACF;sBACJ;uBACE;0BACE;yBACD;wBACH;8BACS;;;;;;AAEtC,MAAMC,cAAc;AAoBpB,SAASC,oBACPC,QAAgB,EAChBC,YAAoB,EACpBC,OAGC;IAED,OAAO,CAAC,SAAS,EAAEF,SAAS;wBACN,EAAEC,aAAa;AACvC,EACEC,QAAQC,IAAI,KAAK,UACb,CAAC,iDAAiD,CAAC,GACnD,CAAC,8GAA8G,CAAC,CACrH;;6BAE4B,EAAEF,aAAa;;;;EAI1C,EACEC,QAAQC,IAAI,KAAK,UACbC,kBAAY,CAACC,GAAG,CAAC,CAACC,SAAW,CAAC,EAAEA,OAAO,WAAW,CAAC,EAAEC,IAAI,CAAC,UAC1D,oBACL;;;;;;;;;;EAUD,EACEL,QAAQC,IAAI,KAAK,UACb,KACA,CAAC;;;;;EAKP,CAAC,CACA;;;AAGH,EACED,QAAQC,IAAI,KAAK,UACbC,kBAAY,CAACC,GAAG,CACd,CAACC,SAAW,CAAC;KAChB,EAAEA,OAAO;;;;;kBAKI,EAAEA,OAAO;;qDAE0B,EAAEA,OAAO;;OAEvD,EAAEA,OAAO;;;;;;;kBAOE,EAAEA,OAAO;;sDAE2B,EAAEA,OAAO;;OAExD,EAAEA,OAAO;;;EAGd,EACE,GAID;;;;kBAIe,EAAEA,OAAO;;;;kBAIT,EAAEA,OAAO;wDAC6B,EAAEA,OAAO;;OAE1D,EAAEA,OAAO;;;;AAIhB,CAAC,EACOC,IAAI,CAAC,MACP,CAAC;iBACU,EACTL,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;;mBAIY,EACfD,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;;;;mBAMgB,EACfD,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;AAGH,CAAC,CACA;;;;;;;;;;;;;;AAcD,EACED,QAAQM,KAAK,GACTN,QAAQM,KAAK,CAACH,GAAG,CAAC,CAACI,OAAS,CAAC,EAAE,EAAEA,KAAK,iBAAiB,CAAC,EAAEF,IAAI,CAAC,QAC/D,GACL;;;;;;;;;;;;;;;;AAgBD,EACEL,QAAQC,IAAI,KAAK,UACb,CAAC;;;;CAIN,CAAC,GACI,GACL;;;;;;;;;AASD,CAAC;AACD;AAEA,eAAeO,kBAAkBC,UAAkB;IACjD,MAAMC,YAAYC,aAAI,CAACC,OAAO,CAACH;IAC/B,MAAMI,QAAQ,MAAMC,iBAAE,CAACC,OAAO,CAACL,WAAW;QAAEM,eAAe;IAAK;IAChE,MAAMV,QAAQ,EAAE;IAChB,KAAK,MAAMW,QAAQJ,MAAO;QACxB,IAAII,KAAKC,WAAW,MAAMD,KAAKE,IAAI,CAACC,UAAU,CAAC,MAAM;YACnDd,MAAMe,IAAI,CAACJ,KAAKE,IAAI,CAACG,KAAK,CAAC;QAC7B;IACF;IACA,OAAOhB;AACT;AAEA,oEAAoE;AACpE,0EAA0E;AAC1E,8DAA8D;AAE9D,MAAMiB,cAAcC,IAAAA,mCAAqB,EAAC;IACxCC,YAAY;QACVC,MAAM;YACJC,QAAQ;YACRC,SAAS;QACX;QACAC,MAAM;YACJF,QAAQ;YACRC,SAAS;QACX;QACAE,OAAO;YACLH,QAAQ;YACRC,SAAS;QACX;IACF;AACF;AAEA,SAASG,uBAAuBC,KAAa;IAC3C,MAAMC,YAAYC,IAAAA,qBAAc,EAACF;IACjC,IAAIC,WAAW;QACbD,QAAQA,MACLG,KAAK,CAAC,KACNhC,GAAG,CAAC,CAACiC;YACJ,IAAIA,KAAKhB,UAAU,CAAC,QAAQgB,KAAKC,QAAQ,CAAC,MAAM;gBAC9C,IAAID,KAAKhB,UAAU,CAAC,SAAS;oBAC3B,aAAa;oBACb,OAAO,CAAC,mBAAmB,CAAC;gBAC9B,OAAO,IAAIgB,KAAKhB,UAAU,CAAC,YAAYgB,KAAKC,QAAQ,CAAC,OAAO;oBAC1D,eAAe;oBACf,OAAO,CAAC,2BAA2B,CAAC;gBACtC;gBACA,UAAU;gBACV,OAAO,CAAC,eAAe,CAAC;YAC1B;YACA,OAAOD;QACT,GACC/B,IAAI,CAAC;IACV;IAEA,OAAO;QACL4B;QACAK,WAAW,CAAC,UAAU,EAAEN,MAAM,EAAE,CAAC;IACnC;AACF;AAEA,6EAA6E;AAC7E,IAAIO,kCAAkC;AAEtC,kDAAkD;AAClD,SAASC,+BACPC,QAA8B,EAC9BC,SAAiC;IAEjC,SAASC,cAAcC,MAAc;QACnC,IAAIC;QACJ,IAAI;YACFA,SAASC,IAAAA,mBAAK,EAACF;QACjB,EAAE,OAAM;QACN,gEAAgE;QAClE;QAEA,IAAIG,MAAMC,OAAO,CAACH,SAAS;YACzB,MAAMI,2BAA2B;gBAAC;aAAG;YACrC,IAAIC,UAAU;YAEd,SAASC,OAAOC,MAAc;gBAC5B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,yBAAyBK,MAAM,EAAED,IAAK;oBACxDJ,wBAAwB,CAACI,EAAE,IAAID;gBACjC;YACF;YAEA,SAASG,KAAKH,MAAc;gBAC1B,MAAMI,gBAAgBP,yBAAyBK,MAAM;gBACrD,IAAK,IAAID,IAAI,GAAGA,IAAIG,eAAeH,IAAK;oBACtCJ,yBAAyB5B,IAAI,CAAC4B,wBAAwB,CAACI,EAAE,GAAGD;gBAC9D;YACF;YAEA,KAAK,MAAMK,SAASZ,OAAQ;gBAC1B,IAAI,OAAOY,UAAU,UAAU;oBAC7B,sCAAsC;oBACtC,MAAMC,OACJD,MAAMtC,IAAI,IAAK+B,CAAAA,cAAc,IAAI,SAAS,CAAC,IAAI,EAAEA,QAAQ,CAAC,AAAD;oBAE3D,IAAIO,MAAME,QAAQ,KAAK,KAAK;wBAC1BR,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,KAAK,EAAEF,KAAK,EAAE,CAAC;oBACxC,OAAO,IAAID,MAAME,QAAQ,KAAK,KAAK;wBACjCR,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC;oBACtC,OAAO,IAAID,MAAME,QAAQ,KAAK,IAAI;wBAChC,IAAIF,MAAMI,OAAO,KAAK,gBAAgB;4BACpC,cAAc;4BACdV,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC;wBACnC,OAAO,IAAID,MAAMI,OAAO,KAAK,MAAM;4BACjC,6BAA6B;4BAC7BV,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,KAAK,EAAEF,KAAK,EAAE,CAAC;wBACxC,OAAO,IAAID,MAAMI,OAAO,KAAK,MAAM;4BACjC,mBAAmB;4BACnBV,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC;wBACtC,OAAO;4BACL,2DAA2D;4BAC3D;wBACF;oBACF,OAAO,IAAID,MAAME,QAAQ,KAAK,KAAK;wBACjC,IAAI,mBAAmBG,IAAI,CAACL,MAAMI,OAAO,GAAG;4BAC1C,yDAAyD;4BACzDV,OAAOM,MAAMG,MAAM;4BACnBL,KAAKE,MAAMI,OAAO;wBACpB,OAAO;4BACL,8DAA8D;4BAC9D;wBACF;oBACF;gBACF,OAAO,IAAI,OAAOJ,UAAU,UAAU;oBACpCN,OAAOM;gBACT;YACF;YAEA,KAAK,MAAMM,mBAAmBd,yBAA0B;gBACtD,MAAM,EAAEhB,SAAS,EAAEK,SAAS,EAAE,GAAGP,uBAAuBgC;gBACxDxC,YAAYE,UAAU,CAACK,KAAK,CAACG,YAAY,YAAY,SAAS,IAC5DK;YACJ;QACF;IACF;IAEA,IAAIG,UAAU;QACZ,KAAK,MAAMuB,WAAWvB,SAASwB,WAAW,CAAE;YAC1CtB,cAAcqB,QAAQpB,MAAM;QAC9B;QACA,KAAK,MAAMoB,WAAWvB,SAASyB,UAAU,CAAE;YACzCvB,cAAcqB,QAAQpB,MAAM;QAC9B;QACA,KAAK,MAAMoB,WAAWvB,SAAS0B,QAAQ,CAAE;YACvCxB,cAAcqB,QAAQpB,MAAM;QAC9B;IACF;IAEA,IAAIF,WAAW;QACb,KAAK,MAAM0B,YAAY1B,UAAW;YAChC,0BAA0B;YAC1B,wIAAwI;YACxI,IAAI,CAAE,CAAA,cAAc0B,QAAO,GAAI;gBAC7BzB,cAAcyB,SAASxB,MAAM;YAC/B;QACF;IACF;AACF;AAEA,SAASyB;IACP,IAAIC,mBAAmB;IACvB,IAAIC,oBAAoB;IAExB,KAAK,MAAMtE,QAAQ;QAAC;QAAQ;QAAQ;KAAQ,CAAW;QACrDqE,oBAAoB/C,YAAYE,UAAU,CAACxB,KAAK,CAAC0B,MAAM;QACvD4C,qBAAqBhD,YAAYE,UAAU,CAACxB,KAAK,CAAC2B,OAAO;IAC3D;IAEA,+EAA+E;IAC/E,MAAM4C,qBACJ,CAACF,oBAAoB,CAACC,oBAAoB,WAAW;IAEvD,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBA8BY,EAAED,oBAAoB,QAAQ;kDACF,EAC9CC,qBAAqB,QACtB;;sBAEmB,EAClBC,sBACA,CAAC;IACD,EACE,uDAAuD;IACvD,iBACD;;;;;IAKD,CAAC,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DH,CAAC;AACD;AAEA,MAAMC,mBAAmB9D,aAAI,CAACN,IAAI,CAAC,SAAS;AAErC,MAAMV;IAWX+E,YAAY1E,OAAgB,CAAE;QAC5B,IAAI,CAAC2E,GAAG,GAAG3E,QAAQ2E,GAAG;QACtB,IAAI,CAACC,OAAO,GAAG5E,QAAQ4E,OAAO;QAC9B,IAAI,CAACC,MAAM,GAAG7E,QAAQ6E,MAAM;QAC5B,IAAI,CAACC,GAAG,GAAG9E,QAAQ8E,GAAG;QACtB,IAAI,CAACC,YAAY,GAAG/E,QAAQ+E,YAAY;QACxC,IAAI,CAACC,cAAc,GAAGhF,QAAQgF,cAAc;QAC5C,IAAI,CAACC,QAAQ,GAAGtE,aAAI,CAACN,IAAI,CAAC,IAAI,CAACwE,MAAM,EAAE,MAAM;QAC7C,IAAI,CAACK,WAAW,GAAGlF,QAAQkF,WAAW;QACtC,IAAI,CAACC,mBAAmB,GAAGxE,aAAI,CAACN,IAAI,CAAC,IAAI,CAACsE,GAAG,EAAE,IAAI,CAACC,OAAO;QAC3D,IAAI,IAAI,CAACM,WAAW,IAAI,CAAC3C,iCAAiC;YACxDA,kCAAkC;YAClCC,+BACExC,QAAQoF,gBAAgB,EACxBpF,QAAQqF,iBAAiB;QAE7B;IACF;IAEAC,+BAA+BC,0BAAkC,EAAE;QACjE,MAAMC,qBAAqB7E,aAAI,CAACN,IAAI,CAClC,IAAI,CAACwE,MAAM,EACXU;QAGF,MAAME,+BAA+B9E,aAAI,CAACN,IAAI,CAC5C,IAAI,CAAC8E,mBAAmB,EACxBV,kBACAc;QAGF,OAAO5E,aAAI,CAAC+E,QAAQ,CAClBD,+BAA+B,OAC/BD;IAEJ;IAEAG,YAAYC,QAAgB,EAAE;QAC5B,IAAI,CAAC,IAAI,CAACV,WAAW,EAAE;QAEvB,MAAMW,QAAQD,SAASxE,UAAU,CAAC,IAAI,CAACyD,MAAM,GAAGlE,aAAI,CAACmF,GAAG;QACxD,MAAMC,UAAU,CAACF,SAASD,SAASxE,UAAU,CAAC,IAAI,CAAC6D,QAAQ,GAAGtE,aAAI,CAACmF,GAAG;QAEtE,IAAI,CAACD,SAAS,CAACE,SAAS;YACtB;QACF;QAEA,qDAAqD;QACrD,IAAIF,SAAS,CAAC,8BAA8B/B,IAAI,CAAC8B,WAAW;YAC1D;QACF;QAEA,yCAAyC;QACzC,IACEG,WACA,iDAAiDjC,IAAI,CAAC8B,WACtD;YACA;QACF;QAEA,IAAI5D,QAAQ,AAAC6D,CAAAA,QAAQG,0BAAgB,GAAGC,wCAAmB,AAAD,EACxDC,IAAAA,sCAAkB,EAChBC,IAAAA,wBAAe,EACbxF,aAAI,CAAC+E,QAAQ,CAACG,QAAQ,IAAI,CAAChB,MAAM,GAAG,IAAI,CAACI,QAAQ,EAAEW,WACnD,IAAI,CAACZ,cAAc;QAKzB,MAAM,EAAE/C,SAAS,EAAEK,SAAS,EAAE,GAAGP,uBAAuBC;QAExDT,YAAYE,UAAU,CAAC,IAAI,CAACsD,YAAY,GAAG,SAAS,OAAO,CACzD9C,YAAY,YAAY,SACzB,IAAIK;IACP;IAEA8D,MAAMC,QAA0B,EAAE;QAChC,+BAA+B;QAC/B,MAAMC,mBAAmB,IAAI,CAACxB,GAAG,GAC7B,OACA,IAAI,CAACC,YAAY,GACjB,OACA;QAEJ,MAAMwB,eAAe,OAAOC,KAA2BC;YACrD,IAAI,CAACD,IAAIE,QAAQ,EAAE;YAEnB,IAAI,CAAC,yBAAyB5C,IAAI,CAAC0C,IAAIE,QAAQ,GAAG;YAElD,IAAI,CAACF,IAAIE,QAAQ,CAACtF,UAAU,CAAC,IAAI,CAACyD,MAAM,GAAGlE,aAAI,CAACmF,GAAG,GAAG;gBACpD,IAAI,CAAC,IAAI,CAAChB,GAAG,EAAE;oBACb,IAAI0B,IAAIE,QAAQ,CAACtF,UAAU,CAAC,IAAI,CAAC6D,QAAQ,GAAGtE,aAAI,CAACmF,GAAG,GAAG;wBACrD,IAAI,CAACH,WAAW,CAACa,IAAIE,QAAQ;oBAC/B;gBACF;gBACA;YACF;YACA,IACEF,IAAIG,KAAK,KAAKC,yBAAc,CAACC,qBAAqB,IAClDL,IAAIG,KAAK,KAAKC,yBAAc,CAACE,eAAe,EAE5C;YAEF,MAAMC,YAAY,yBAAyBjD,IAAI,CAAC0C,IAAIE,QAAQ;YAC5D,MAAMM,UAAU,CAACD,aAAa,oBAAoBjD,IAAI,CAAC0C,IAAIE,QAAQ;YACnE,MAAMO,WAAW,CAACD,WAAW,qBAAqBlD,IAAI,CAAC0C,IAAIE,QAAQ;YACnE,MAAMQ,oBAAoBvG,aAAI,CAAC+E,QAAQ,CAAC,IAAI,CAACb,MAAM,EAAE2B,IAAIE,QAAQ;YAEjE,IAAI,CAAC,IAAI,CAAC5B,GAAG,EAAE;gBACb,IAAIkC,WAAWC,UAAU;oBACvB,IAAI,CAACtB,WAAW,CAACa,IAAIE,QAAQ;gBAC/B;YACF;YAEA,MAAMS,WAAWxG,aAAI,CAACN,IAAI,CACxBoE,kBACAyC,kBAAkBE,OAAO,CAAC,0BAA0B;YAEtD,MAAMC,qBAAqBC,IAAAA,kCAAgB,EACzC3G,aAAI,CACDN,IAAI,CAAC,IAAI,CAACiF,8BAA8B,CAAC4B,oBACzCE,OAAO,CAAC,0BAA0B;YAGvC,MAAMG,YAAY5G,aAAI,CAACN,IAAI,CAACiG,kBAAkBa;YAE9C,IAAIJ,WAAW;gBACb,MAAMzG,QAAQ,MAAME,kBAAkBgG,IAAIE,QAAQ;gBAClDD,MAAM,CAACc,UAAU,GAAG,IAAIC,gBAAO,CAACC,SAAS,CACvC5H,oBAAoB2G,IAAIE,QAAQ,EAAEW,oBAAoB;oBACpDpH,MAAM;oBACNK;gBACF;YAEJ,OAAO,IAAI0G,SAAS;gBAClBP,MAAM,CAACc,UAAU,GAAG,IAAIC,gBAAO,CAACC,SAAS,CACvC5H,oBAAoB2G,IAAIE,QAAQ,EAAEW,oBAAoB;oBACpDpH,MAAM;gBACR;YAEJ,OAAO,IAAIgH,UAAU;gBACnBR,MAAM,CAACc,UAAU,GAAG,IAAIC,gBAAO,CAACC,SAAS,CACvC5H,oBAAoB2G,IAAIE,QAAQ,EAAEW,oBAAoB;oBACpDpH,MAAM;gBACR;YAEJ;QACF;QAEAoG,SAASqB,KAAK,CAACC,WAAW,CAACC,GAAG,CAAChI,aAAa,CAAC+H;YAC3CA,YAAYD,KAAK,CAACG,aAAa,CAACC,QAAQ,CACtC;gBACE3G,MAAMvB;gBACNmI,OAAOC,gBAAO,CAACC,WAAW,CAACC,kCAAkC;YAC/D,GACA,OAAOzB,QAAQ0B;gBACb,MAAMC,WAA2B,EAAE;gBAEnC,eAAe;gBACf,IAAI,IAAI,CAACrD,YAAY,EAAE;oBACrBxD,YAAYE,UAAU,CAACC,IAAI,CAACE,OAAO,GAAG;oBACtCL,YAAYE,UAAU,CAACC,IAAI,CAACC,MAAM,GAAG;gBACvC,OAAO;oBACLJ,YAAYE,UAAU,CAACI,IAAI,CAACD,OAAO,GAAG;oBACtCL,YAAYE,UAAU,CAACI,IAAI,CAACF,MAAM,GAAG;gBACvC;gBAEAgG,YAAYU,WAAW,CAACC,OAAO,CAAC,CAACC;oBAC/BA,WAAWC,MAAM,CAACF,OAAO,CAAC,CAACG;wBACzB,IAAI,CAACA,MAAMtH,IAAI,EAAE;wBAEjB,4CAA4C;wBAC5C,IACE,CAACsH,MAAMtH,IAAI,CAACC,UAAU,CAAC,aACvB,CACEqH,CAAAA,MAAMtH,IAAI,CAACC,UAAU,CAAC,WACrBqH,CAAAA,MAAMtH,IAAI,CAACkB,QAAQ,CAAC,YACnBoG,MAAMtH,IAAI,CAACkB,QAAQ,CAAC,SAAQ,CAAC,GAEjC;4BACA;wBACF;wBAEA,MAAMqG,eACJf,YAAYgB,UAAU,CAACC,uBAAuB,CAC5CH;wBAEJ,KAAK,MAAMjC,OAAOkC,aAAc;4BAC9BN,SAAS/G,IAAI,CAACkF,aAAaC,KAAKC;4BAEhC,oEAAoE;4BACpE,MAAMoC,YAAYrC;4BAGlB,IAAIqC,UAAUC,OAAO,EAAE;gCACrBD,UAAUC,OAAO,CAACR,OAAO,CAAC,CAACS;oCACzBX,SAAS/G,IAAI,CAACkF,aAAawC,iBAAiBtC;gCAC9C;4BACF;wBACF;oBACF;gBACF;gBAEA,MAAMuC,QAAQC,GAAG,CAACb;gBAElB,8EAA8E;gBAE9E,MAAMc,uBAAuBvI,aAAI,CAACN,IAAI,CACpCiG,kBACA;gBAGFG,MAAM,CAACyC,qBAAqB,GAAG,IAAI1B,gBAAO,CAACC,SAAS,CAClD;gBAGF,IAAI,IAAI,CAACvC,WAAW,EAAE;oBACpB,IAAI,IAAI,CAACJ,GAAG,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;wBAClCoE,oBAAY,CAACb,OAAO,CAAC,CAACc;4BACpB,IAAI,CAACzD,WAAW,CAACyD;wBACnB;oBACF;oBAEA,MAAMC,gBAAgB1I,aAAI,CAACN,IAAI,CAACiG,kBAAkB;oBAElDG,MAAM,CAAC4C,cAAc,GAAG,IAAI7B,gBAAO,CAACC,SAAS,CAC3CpD;gBAEJ;gBAEA8D;YACF;QAEJ;IACF;AACF"}