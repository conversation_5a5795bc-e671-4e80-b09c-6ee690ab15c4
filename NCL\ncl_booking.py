import os
import sys
from loguru import logger
import time
import asyncio

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)


class NCLBooking:

    @staticmethod
    def setup_logging():
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_file = os.path.join(log_dir, "ncl_booking_{time}.log")

        logger.remove()
        logger.add(sys.stderr, level="INFO")
        logger.add(log_file, rotation="10 MB", level="DEBUG")

        return logger

    async def run(self):
        start_time = time.time()

        self.setup_logging()

        logger.info("\n")
        logger.info("=" * 50)
        logger.info("  NCL CRUISE RESERVATION SYSTEM")
        logger.info("=" * 50)

        logger.info("\nEnter the cruise details (press Enter twice to finish):")
        input_text = []
        while True:
            line = input()
            if line == "":
                break
            input_text.append(line)

        input_text = '\n'.join(input_text)
        try:
            from extraction import extract_cruise_details
            cruise_details = extract_cruise_details(input_text)

            if not cruise_details['travel_date'] or not cruise_details['ship_name']:
                logger.error("Failed to extract required cruise details. Aborting.")
                return

            if not cruise_details['cabins'] or cruise_details['cabins'] < 1:
                cruise_details['cabins'] = 1

            num_cabins = cruise_details['cabins']
            cabin_plural = "cabin" if num_cabins == 1 else "cabins"
            logger.info(
                f"\nProcessing {num_cabins} {cabin_plural} using the unified booking process..."
            )

            try:
                from parallel_booking import ParallelBooking
                await ParallelBooking.parallel_cruise_reservation(cruise_details)
            except ImportError as e:
                logger.error(f"Failed to import parallel booking module: {e}")
                logger.error(f"Error: Could not load the parallel booking module: {e}")
                logger.error(
                    "Please ensure parallel_booking.py is available in the same directory."
                )
                return

        except ImportError as e:
            logger.error(f"Failed to import required module: {e}")
            logger.error(f"Error: Could not load the necessary module: {e}")
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            logger.error(f"An error occurred: {e}")
            logger.error("Please check the logs for more details.")
        finally:
            logger.info("Booking process completed.")

            end_time = time.time()
            elapsed_time = end_time - start_time
            minutes, seconds = divmod(elapsed_time, 60)
            hours, minutes = divmod(minutes, 60)

            time_str = ""
            if hours > 0:
                time_str += f"{int(hours)} hours, "
            if minutes > 0 or hours > 0:
                time_str += f"{int(minutes)} minutes, "
            time_str += f"{int(seconds)} seconds"

            logger.info(f"\nTotal execution time: {time_str}")