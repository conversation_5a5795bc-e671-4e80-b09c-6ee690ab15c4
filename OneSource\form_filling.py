import asyncio
import logging
from typing import Dict, Any, Optional
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError
from OneSource.screenshot_utils import OneSourceScreenshotManager

logger = logging.getLogger(__name__)

PP_INPUT_SELECTORS = [
    "#CMRDLT > table > tbody > tr:nth-child(2) > td > table > tbody > tr:nth-child(3) > td > table > tbody > tr:nth-child(4) > td > table > tbody > tr > td > table > tbody > tr > td:nth-child(1) > table > tbody > tr:nth-child(2) > td.arial11_13h_333333_boxcontent > input",
    "xpath=//*[@id=\"CMRDLT\"]/table/tbody/tr[2]/td/table/tbody/tr[3]/td/table/tbody/tr[4]/td/table/tbody/tr/td/table/tbody/tr/td[1]/table/tbody/tr:nth-child(2)/td[2]/input",
    "input[name='F190170010_CMRDLT-CSTID']"
]

PP_SEARCH_BTN_SELECTORS = [
    "#CMRDLT > table > tbody > tr:nth-child(2) > td > table > tbody > tr:nth-child(3) > td > table > tbody > tr:nth-child(4) > td > table > tbody > tr > td > table > tbody > tr > td:nth-child(1) > table > tbody > tr:nth-child(2) > td:nth-child(4) > a",
    "xpath=//*[@id='CMRDLT']/table/tbody/tr[2]/td/table/tbody/tr[3]/td/table/tbody/tr[4]/td/table/tbody/tr/td/table/tbody/tr/td[1]/table/tbody/tr[2]/td[4]/a",
    "a:has-text('Search')"
]

PP_DROPDOWN_SELECTOR = "#F080010001_CMRDTY-SELECT"
PP_SELECT_BTN_SELECTORS = [
    "#PaxSelRow > td > table > tbody > tr > td:nth-child(1) > a",
    "xpath=//*[@id='PaxSelRow']/td/table/tbody/tr/td[1]/a",
    "a:has-text('Select Passengers')"
]

class OneSourceFormFilling:
    """Handles form filling after clicking Create Booking in OneSource"""
    
    def __init__(self, page: Page):
        """Initialize the form filling handler"""
        self.page = page
        self.screenshot_manager = None
        self.request_id = None
        self.cabin_id = None
        self.session_id = None

    def set_screenshot_config(self, request_id: str, cabin_id: int = None, session_id: str = None):
        """Set screenshot configuration"""
        self.request_id = request_id
        self.cabin_id = cabin_id
        self.session_id = session_id
        self.screenshot_manager = OneSourceScreenshotManager(
            self.page, request_id, cabin_id, session_id
        )
        
    async def fill_booking_form(self, booking_data: Dict[str, Any]) -> bool:
        """
        Fill the OneSource booking form with extracted data
        
        Args:
            booking_data: Dictionary containing:
                - airport_code: Home city airport code
                - departure_date: Cruise departure date (MM/DD/YYYY format)
                - ship_name: Name of the ship
                - occupancy: Number of passengers
                - nights: Number of nights (numeric value only)
        
        Returns:
            bool: True if form filling was successful, False otherwise
        """
        try:
            logger.info("Starting OneSource booking form filling")
            
            # Set page timeout to avoid default timeouts
            self.page.set_default_timeout(60000)  # 60 seconds
            
            # Wait for the page to load and stabilize
            await self.page.wait_for_load_state('domcontentloaded', timeout=10000)
            
            # Brief wait for dynamic content
            await asyncio.sleep(3)
            
            # Verify form elements are present
            try:
                await self.page.wait_for_selector('#CMRDLT', timeout=10000)
                logger.info("Form container found, proceeding with form filling")
            except Exception as e:
                logger.error(f"Form container not found: {e}")
                return False
            
            # Image blocking removed
            
            # First, handle past passenger number if provided
            await self._handle_past_passenger(booking_data.get('past_passenger_number', ''))

            # Determine final search action based on cruise line
            cruise_line = booking_data.get('cruise_line', '').lower()
            if 'holland' in cruise_line:
                search_step = ("Search By Date", self._click_search_by_date, None)
            else:
                search_step = ("Search by Category", self._click_search_by_category, None)

            # Fill form fields in sequence
            form_steps = [
                ("Home City (Airport Code)", self._fill_home_city, booking_data.get('airport_code', '')),
                ("Departure Date", self._fill_departure_date, booking_data.get('departure_date', '')),
                ("Ship Name", self._select_ship, booking_data.get('ship_name', '')),
                ("Occupancy", self._select_occupancy, booking_data.get('occupancy', '')),
                ("Number of Nights", self._fill_nights, booking_data.get('nights', '')),
                search_step
            ]
            
            for step_name, method, value in form_steps:
                try:
                    if value is None:
                        success = await method()
                    else:
                        success = await method(str(value))
                    
                    if success:
                        logger.info(f"✅ {step_name} completed successfully")
                    else:
                        logger.warning(f"⚠️ {step_name} failed, but continuing...")
                        
                    # Brief pause between form steps
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"❌ Error in {step_name}: {e}")
                    continue
            
            logger.info("Form filling process completed")
            return True
            
        except PlaywrightTimeoutError as e:
            logger.error(f"Timeout error during form filling: {e}")
            return False
        except Exception as e:
            logger.error(f"Error filling OneSource booking form: {e}")
            return False
    
    async def _fill_home_city(self, airport_code: str) -> bool:
        """Fill the home city field with airport code"""
        if not airport_code:
            logger.error("Airport code is required for home city")
            return False
            
        try:
            # Try multiple approaches for home city field
            home_city_selectors = [
                # Original complex selectors
                "#CMRDLT > table > tbody > tr:nth-child(3) > td > table > tbody > tr:nth-child(1) > td:nth-child(1) > table > tbody > tr:nth-child(8) > td > table > tbody > tr > td > table > tbody > tr:nth-child(7) > td:nth-child(2) > table > tbody > tr > td:nth-child(1) > input",
                "xpath=//*[@id=\"CMRDLT\"]/table/tbody/tr[3]/td/table/tbody/tr[1]/td[1]/table/tbody/tr[6]/td/table/tbody/tr/td/table/tbody/tr[5]/td[2]/table/tbody/tr/td[1]/input",
                # Simpler fallbacks
                "#CMRDLT input[type='text']",  # Any text input in the form
                "#CMRDLT input",  # Any input in the form
                "input[type='text']",  # Any text input on page
                "input"  # Any input on page
            ]
            
            for i, selector in enumerate(home_city_selectors):
                try:
                    await self.page.wait_for_selector(selector, timeout=3000)
                    await self.page.fill(selector, airport_code)
                    logger.info(f"Home city filled with airport code: {airport_code}")
                    return True
                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    continue
                    
            logger.error("Could not find home city input field")
            return False
            
        except Exception as e:
            logger.error(f"Error filling home city: {str(e)}")
            return False
    
    async def _fill_departure_date(self, departure_date: str) -> bool:
        """Fill the departure date field"""
        if not departure_date:
            logger.error("Departure date is required")
            return False
            
        try:
            # Try CSS selector first
            date_selectors = [
                "#CMRDLT > table > tbody > tr:nth-child(3) > td > table > tbody > tr:nth-child(1) > td:nth-child(1) > table > tbody > tr:nth-child(8) > td > table > tbody > tr > td > table > tbody > tr:nth-child(16) > td:nth-child(2) > input",
                "xpath=//*[@id=\"CMRDLT\"]/table/tbody/tr[3]/td/table/tbody/tr[1]/td[1]/table/tbody/tr[6]/td/table/tbody/tr/td/table/tbody/tr[11]/td[2]/input"
            ]
            
            for selector in date_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=5000)
                    await self.page.fill(selector, departure_date)
                    logger.info(f"Departure date filled: {departure_date}")
                    return True
                except PlaywrightTimeoutError:
                    continue
                    
            logger.error("Could not find departure date input field")
            return False
            
        except Exception as e:
            logger.error(f"Error filling departure date: {str(e)}")
            return False
    
    async def _select_ship(self, ship_name: str) -> bool:
        """Select ship from dropdown"""
        if not ship_name:
            logger.error("Ship name is required")
            return False
            
        try:
            # Try CSS selector first
            ship_selectors = [
                "#SHIPLAND_1",
                "xpath=//*[@id=\"SHIPLAND_1\"]"
            ]
            
            for selector in ship_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=5000)
                    
                    # Click to open dropdown
                    await self.page.click(selector)
                    await asyncio.sleep(0.5)
                    
                    # Select the ship by text
                    await self.page.select_option(selector, label=ship_name)
                    logger.info(f"Ship selected: {ship_name}")
                    return True
                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    # Try selecting by value if label doesn't work
                    try:
                        await self.page.select_option(selector, value=ship_name)
                        logger.info(f"Ship selected: {ship_name}")
                        return True
                    except:
                        continue
                        
            logger.error("Could not find or select ship dropdown")
            return False
            
        except Exception as e:
            logger.error(f"Error selecting ship: {str(e)}")
            return False
    
    async def _select_occupancy(self, occupancy: str) -> bool:
        """Select occupancy from dropdown"""
        if not occupancy:
            logger.error("Occupancy is required")
            return False
            
        try:
            # Try CSS selector first
            occupancy_selectors = [
                "#BERTHS_1",
                "xpath=//*[@id=\"BERTHS_1\"]"
            ]
            
            for selector in occupancy_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=5000)
                    
                    # Click to open dropdown
                    await self.page.click(selector)
                    await asyncio.sleep(0.5)
                    
                    # Select the occupancy
                    await self.page.select_option(selector, value=str(occupancy))
                    logger.info(f"Occupancy selected: {occupancy}")
                    return True
                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    # Try selecting by label if value doesn't work
                    try:
                        await self.page.select_option(selector, label=str(occupancy))
                        logger.info(f"Occupancy selected: {occupancy}")
                        return True
                    except:
                        continue
                        
            logger.error("Could not find or select occupancy dropdown")
            return False
            
        except Exception as e:
            logger.error(f"Error selecting occupancy: {str(e)}")
            return False
    
    async def _fill_nights(self, nights: str) -> bool:
        """Fill the number of nights field"""
        if not nights:
            logger.error("Number of nights is required")
            return False
            
        try:
            # Extract only numeric value
            nights_numeric = ''.join(filter(str.isdigit, str(nights)))
            if not nights_numeric:
                logger.error("Could not extract numeric value from nights")
                return False
            
            # Try CSS selector first
            nights_selectors = [
                "#CMRDLT > table > tbody > tr:nth-child(3) > td > table > tbody > tr:nth-child(1) > td:nth-child(1) > table > tbody > tr:nth-child(8) > td > table > tbody > tr > td > table > tbody > tr:nth-child(21) > td:nth-child(2) > input",
                "xpath=//*[@id=\"CMRDLT\"]/table/tbody/tr[3]/td/table/tbody/tr[1]/td[1]/table/tbody/tr[6]/td/table/tbody/tr/td/table/tbody/tr[16]/td[2]/input"
            ]
            
            for selector in nights_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=5000)
                    await self.page.fill(selector, nights_numeric)
                    logger.info(f"Number of nights filled: {nights_numeric}")
                    return True
                except PlaywrightTimeoutError:
                    continue
                    
            logger.error("Could not find nights input field")
            return False
            
        except Exception as e:
            logger.error(f"Error filling nights: {str(e)}")
            return False
    
    async def _click_search_by_category(self) -> bool:
        """Click the Search by Category List button"""
        try:
            # Take screenshot before clicking search button
            if self.screenshot_manager:
                await self.screenshot_manager.take_form_filling_screenshot()
            
            # Try CSS selector first
            search_button_selectors = [
                "#CMRDLT > table > tbody > tr:nth-child(3) > td > table > tbody > tr:nth-child(1) > td:nth-child(1) > table > tbody > tr:nth-child(8) > td > table > tbody > tr > td > table > tbody > tr:nth-child(26) > td:nth-child(2) > table > tbody > tr > td.noWrap > a",
                "xpath=//*[@id=\"CMRDLT\"]/table/tbody/tr[3]/td/table/tbody/tr[1]/td[1]/table/tbody/tr[6]/td/table/tbody/tr/td/table/tbody/tr[20]/td[2]/table/tbody/tr/td[1]/a"
            ]
            
            for selector in search_button_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=5000)
                    await self.page.click(selector)
                    logger.info("Search by Category List button clicked successfully")
                    
                    return True
                except PlaywrightTimeoutError:
                    continue
                except Exception as click_error:
                    logger.debug(f"Error with selector {selector}: {click_error}")
                    continue
                    
            logger.error("Could not find Search by Category List button")
            return False
            
        except Exception as e:
            logger.error(f"Error clicking Search by Category List button: {str(e)}")
            return False 

    async def _click_search_by_date(self) -> bool:
        """Click the Search By Date button (for Holland America)."""
        try:
            # Take screenshot before clicking search button
            if self.screenshot_manager:
                await self.screenshot_manager.take_form_filling_screenshot()
            
            selectors = [
                "#CMRDLT > table > tbody > tr:nth-child(3) > td > table > tbody > tr:nth-child(1) > td:nth-child(1) > table > tbody > tr:nth-child(8) > td > table > tbody > tr > td > table > tbody > tr:nth-child(23) > td:nth-child(2) > table > tbody > tr > td.noWrap > a",
                "xpath=//*[@id=\"CMRDLT\"]/table/tbody/tr[3]/td/table/tbody/tr[1]/td[1]/table/tbody/tr[6]/td/table/tbody/tr/td/table/tbody/tr[18]/td[2]/table/tbody/tr/td[1]/a",
                "a:has-text('Search By Date')"
            ]
            for sel in selectors:
                try:
                    await self.page.wait_for_selector(sel, timeout=5000)
                    await self.page.click(sel)
                    logger.info("Search By Date button clicked successfully")
                    return True
                except PlaywrightTimeoutError:
                    continue
            logger.error("Could not find Search By Date button")
            return False
        except Exception as e:
            logger.error(f"Error clicking Search By Date button: {e}")
            return False

    async def _handle_past_passenger(self, pp_number: str) -> bool:
        """Enter past passenger number, search, and select passenger 1."""
        if not pp_number:
            logger.debug("No past passenger number provided – skipping")
            return True  # Nothing to do but not an error
        try:
            # Fill number
            input_found = False
            for sel in PP_INPUT_SELECTORS:
                try:
                    await self.page.wait_for_selector(sel, timeout=3000)
                    await self.page.fill(sel, pp_number)
                    input_found = True
                    break
                except Exception:
                    continue
            if not input_found:
                logger.warning("Past passenger input field not found")
                return False

            # Click search button
            for sel in PP_SEARCH_BTN_SELECTORS:
                try:
                    await self.page.wait_for_selector(sel, timeout=3000)
                    await self.page.click(sel, force=True)
                    break
                except Exception:
                    continue

            # Wait for dropdown and select '1'
            try:
                await self.page.wait_for_selector(PP_DROPDOWN_SELECTOR, timeout=5000)
                await self.page.select_option(PP_DROPDOWN_SELECTOR, value="1")
            except Exception as de:
                logger.warning(f"Dropdown selection failed: {de}")
                return False

            # Click Select Passengers button
            for sel in PP_SELECT_BTN_SELECTORS:
                try:
                    await self.page.wait_for_selector(sel, timeout=3000)
                    await self.page.click(sel, force=True)
                    logger.info("Past passenger selection completed")
                    return True
                except Exception:
                    continue

            logger.warning("Select Passengers button not found")
            return False
        except Exception as e:
            logger.error(f"Error handling past passenger: {e}")
            return False 