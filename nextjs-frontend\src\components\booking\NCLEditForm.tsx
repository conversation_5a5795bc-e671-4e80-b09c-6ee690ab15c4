import React, { useState, useEffect } from 'react';

interface NCLEditFormProps {
  details: any;
  requestId: string;
  onSave: (updatedDetails: any) => void;
  onCancel: () => void;
}

const NCLEditForm: React.FC<NCLEditFormProps> = ({ details, requestId, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    travel_date: '',
    nights: 7,
    ship_name: '',
    adults: 2,
    children: 0,
    infants: 0,
    cabins: 1,
    cabin_categories: [] as string[],
    cabin_passengers: [] as { adults: number; children: number; infants: number }[],
    total_passengers: 2,
    request_id: '',
    cabin_passengers_dict: {} as Record<string, any>,
    original_cabin_count: 1,
    is_edited: true
  });

  // Distributes passengers across cabins using the same logic as backend
  const distributeCabinPassengers = (details: any) => {
    const numCabins = details.cabins || 1;
    const totalAdults = details.adults || 0;
    const totalChildren = details.children || 0;
    const totalInfants = details.infants || 0;

    let cabinDistribution = [];

    // Check if we have explicit cabin_passengers_dict
    if (details.cabin_passengers_dict && Object.keys(details.cabin_passengers_dict).length > 0) {
      // Use the explicit cabin passenger distribution
      for (let i = 1; i <= numCabins; i++) {
        if (details.cabin_passengers_dict[i]) {
          cabinDistribution.push({
            adults: details.cabin_passengers_dict[i].adults || 0,
            children: details.cabin_passengers_dict[i].children || 0,
            infants: details.cabin_passengers_dict[i].infants || 0
          });
        }
      }
    }
    // Check if we have cabin_passengers array
    else if (details.cabin_passengers && Array.isArray(details.cabin_passengers) &&
      details.cabin_passengers.length > 0) {
      cabinDistribution = details.cabin_passengers;
    }
    // Otherwise distribute passengers ourselves
    else {
      const isORCabinScenario =
        numCabins > 1 &&
        details.cabin_categories &&
        details.cabin_categories.length === numCabins &&
        details.cabins !== details.original_cabin_count;

      // For single cabin or OR scenario, assign all passengers to each cabin
      if (numCabins === 1 || isORCabinScenario) {
        for (let i = 0; i < numCabins; i++) {
          cabinDistribution.push({
            adults: totalAdults,
            children: totalChildren,
            infants: totalInfants
          });
        }
      }
      // Otherwise, distribute passengers evenly across cabins
      else {
        // Distribute adults
        const baseAdults = Math.floor(totalAdults / numCabins);
        const extraAdults = totalAdults % numCabins;

        // Distribute children
        const baseChildren = Math.floor(totalChildren / numCabins);
        const extraChildren = totalChildren % numCabins;

        // Distribute infants
        const baseInfants = Math.floor(totalInfants / numCabins);
        const extraInfants = totalInfants % numCabins;

        for (let i = 0; i < numCabins; i++) {
          cabinDistribution.push({
            adults: baseAdults + (i < extraAdults ? 1 : 0),
            children: baseChildren + (i < extraChildren ? 1 : 0),
            infants: baseInfants + (i < extraInfants ? 1 : 0)
          });
        }
      }
    }

    return cabinDistribution;
  };

  // Initialize form data from the provided details
  useEffect(() => {
    if (details) {
      // Make a deep copy to avoid modifying the original
      const initialFormData = { ...details };

      // Initialize cabin categories
      if (!initialFormData.cabin_categories) {
        initialFormData.cabin_categories = [];

        // Create default categories
        for (let i = 0; i < initialFormData.cabins; i++) {
          initialFormData.cabin_categories.push('INSIDE');
        }
      }

      // Normalise existing cabin categories to match select option values
      if (Array.isArray(initialFormData.cabin_categories)) {
        initialFormData.cabin_categories = initialFormData.cabin_categories.map((cat: string) =>
          (cat || 'INSIDE').replace(/\s+/g, '').toUpperCase()
        );
      }

      // Initialize cabin_passengers based on extracted data
      initialFormData.cabin_passengers = distributeCabinPassengers(initialFormData);

      // Ensure we have the correct number of passenger entries
      while (initialFormData.cabin_passengers.length < initialFormData.cabins) {
        // Default to the overall averages if we need to add extra entries
        const avgAdults = Math.ceil(initialFormData.adults / initialFormData.cabins);
        const avgChildren = Math.ceil(initialFormData.children / initialFormData.cabins);
        const avgInfants = Math.ceil(initialFormData.infants / initialFormData.cabins);

        initialFormData.cabin_passengers.push({
          adults: avgAdults,
          children: avgChildren,
          infants: avgInfants
        });
      }

      // Trim if we have too many
      if (initialFormData.cabin_passengers.length > initialFormData.cabins) {
        initialFormData.cabin_passengers = initialFormData.cabin_passengers.slice(0, initialFormData.cabins);
      }

      // Set the request ID
      initialFormData.request_id = requestId;
      setFormData(initialFormData);
    }
  }, [details, requestId]);

  // Handler for basic detail changes
  const handleBasicChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  // Handler for number of cabins change
  const handleCabinsChange = (numCabins: number) => {
    // Create a new form data object to update
    const newFormData = { ...formData, cabins: numCabins };

    // Adjust cabin categories array
    while (newFormData.cabin_categories.length < numCabins) {
      newFormData.cabin_categories.push('INSIDE');
    }

    // Trim if needed
    if (newFormData.cabin_categories.length > numCabins) {
      newFormData.cabin_categories = newFormData.cabin_categories.slice(0, numCabins);
    }

    // Adjust cabin passengers array - preserve existing passenger counts where possible
    while (newFormData.cabin_passengers.length < numCabins) {
      // Default to the overall averages if we need to add extra entries
      const avgAdults = Math.ceil(newFormData.adults / numCabins);
      const avgChildren = Math.ceil(newFormData.children / numCabins);
      const avgInfants = Math.ceil(newFormData.infants / numCabins);

      newFormData.cabin_passengers.push({
        adults: avgAdults,
        children: avgChildren,
        infants: avgInfants
      });
    }

    // Trim if needed
    if (newFormData.cabin_passengers.length > numCabins) {
      newFormData.cabin_passengers = newFormData.cabin_passengers.slice(0, numCabins);
    }

    // Update the form state
    setFormData(newFormData);
  };

  // Handler for cabin category change
  const handleCategoryChange = (index: number, category: string) => {
    const newCategories = [...formData.cabin_categories];
    newCategories[index] = category;
    setFormData({
      ...formData,
      cabin_categories: newCategories
    });
  };

  // Handler for passenger counts in a cabin
  const handleCabinPassengersChange = (index: number, field: 'adults' | 'children' | 'infants', value: number) => {
    const newCabinPassengers = [...formData.cabin_passengers];
    newCabinPassengers[index] = {
      ...newCabinPassengers[index],
      [field]: value
    };

    // Update total passengers
    const totalAdults = newCabinPassengers.reduce((sum, cabin) => sum + cabin.adults, 0);
    const totalChildren = newCabinPassengers.reduce((sum, cabin) => sum + cabin.children, 0);
    const totalInfants = newCabinPassengers.reduce((sum, cabin) => sum + cabin.infants, 0);

    setFormData({
      ...formData,
      cabin_passengers: newCabinPassengers,
      adults: totalAdults,
      children: totalChildren,
      infants: totalInfants,
      total_passengers: totalAdults + totalChildren + totalInfants
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Create the cabin_passengers_dict for backend processing
    const cabin_passengers_dict: Record<string, any> = {};

    formData.cabin_passengers.forEach((cabin, index) => {
      const cabinId = index + 1;
      cabin_passengers_dict[cabinId] = {
        adults: cabin.adults,
        children: cabin.children,
        infants: cabin.infants,
        category: formData.cabin_categories[index],
        is_or_option: false
      };
    });

    // Prepare the final data object
    const updatedDetails = {
      ...formData,
      cabin_passengers_dict,
      is_edited: true
    };

    onSave(updatedDetails);
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="mb-6">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 mr-2 text-teal-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <h3 className="text-2xl font-bold text-gray-800">Edit NCL Cruise Details</h3>
          </div>

          <div className="mt-2 relative">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-20 h-0.5 bg-gray-800"></div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-6 mt-5">
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sailing Date
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
              value={formData.travel_date}
              onChange={(e) => handleBasicChange('travel_date', e.target.value)}
              required
            />
          </div>

          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (nights)
            </label>
            <input
              type="number"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
              min="1"
              max="30"
              value={formData.nights}
              onChange={(e) => handleBasicChange('nights', parseInt(e.target.value) || 1)}
              required
            />
          </div>

          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ship Name
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
              value={formData.ship_name}
              onChange={(e) => handleBasicChange('ship_name', e.target.value)}
              required
            />
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Number of Cabins
          </label>
          <input
            type="number"
            className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
            min="1"
            max="10"
            value={formData.cabins}
            onChange={(e) => handleCabinsChange(parseInt(e.target.value) || 1)}
            required
          />
        </div>

        <div className="mb-4 mt-8">
          <div className="flex items-center">
            <div className="w-4 h-4 rounded-full bg-teal-500 mr-2"></div>
            <h4 className="text-xl font-bold text-gray-800">Cabin Details</h4>
          </div>
          <div className="mt-2 relative">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
          </div>
        </div>

        <div className="space-y-5 my-5">
          {Array.from({ length: formData.cabins }).map((_, index) => (
             <div
              key={index}
              className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 rounded-full bg-teal-700 text-white flex items-center justify-center mr-2 shadow-sm">
                  {index + 1}
                </div>
                <h5 className="font-semibold text-teal-800 text-lg">Cabin {index + 1}</h5>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
                    value={(formData.cabin_categories[index] || 'INSIDE').toUpperCase()}
                    onChange={(e) => handleCategoryChange(index, e.target.value)}
                    required
                  >
                    <option value="INSIDE">INSIDE</option>
                    <option value="OCEANVIEW">OCEANVIEW</option>
                    <option value="OUTSIDE">OUTSIDE</option>
                    <option value="BALCONY">BALCONY</option>
                    <option value="SUITE">SUITE</option>
                    <option value="HAVENSTUDIO">HAVEN STUDIO</option>
                    <option value="HAVENSUITE">HAVEN SUITE</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Adults
                  </label>
                  <input
                    type="number"
                    className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
                    min="0"
                    max="6"
                    value={formData.cabin_passengers[index]?.adults || 2}
                    onChange={(e) => handleCabinPassengersChange(
                      index,
                      'adults',
                      parseInt(e.target.value) || 0
                    )}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Children
                  </label>
                  <input
                    type="number"
                    className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
                    min="0"
                    max="6"
                    value={formData.cabin_passengers[index]?.children || 0}
                    onChange={(e) => handleCabinPassengersChange(
                      index,
                      'children',
                      parseInt(e.target.value) || 0
                    )}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Infants
                  </label>
                  <input
                    type="number"
                    className="w-full p-3 border border-gray-300 rounded-lg shadow-sm bg-white"
                    min="0"
                    max="6"
                    value={formData.cabin_passengers[index]?.infants || 0}
                    onChange={(e) => handleCabinPassengersChange(
                      index,
                      'infants',
                      parseInt(e.target.value) || 0
                    )}
                  />
                </div>
              </div>
            </div>
          ))}
          </div>

          <div className="bg-gradient-to-r from-blue-100 to-teal-100 p-4 rounded-lg border border-blue-200 shadow-sm mb-5">
          <div className="font-semibold text-teal-800 text-lg mb-2">Passenger Summary</div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-3 rounded shadow-sm border border-gray-100">
              <span className="text-gray-600 text-sm">Total Passengers</span>
              <p className="text-teal-700 font-bold text-xl">{formData.total_passengers}</p>
            </div>
            <div className="bg-white p-3 rounded shadow-sm border border-gray-100">
              <span className="text-gray-600 text-sm">Adults</span>
              <p className="text-teal-700 font-bold text-xl">{formData.adults}</p>
            </div>
            <div className="bg-white p-3 rounded shadow-sm border border-gray-100">
              <span className="text-gray-600 text-sm">Children/Infants</span>
              <p className="text-teal-700 font-bold text-xl">{formData.children + formData.infants}</p>
            </div>
          </div>
        </div>

          <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            className="px-5 py-2.5 text-white rounded-lg shadow-md bg-gradient-to-r from-gray-700 to-gray-900 hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200"
            onClick={onCancel}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-5 py-2.5 text-white rounded-lg shadow-md bg-gradient-to-r from-teal-700 to-slate-800 hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200"
          >
            Save Changes
          </button>
        </div>
      </form>
    </div>
  );
};

export default NCLEditForm; 