# database/video_manager.py
import os
import logging
from datetime import datetime
import tempfile
import asyncio
import time
import aiofiles
from db_service import get_db_connection, db_connection
from .session_manager import SessionManager
from services.media_service import media_service

logger = logging.getLogger("db.video_manager")

class VideoCleanupQueue:
    _instance = None
    _lock = None  # Will be initialized in _initialize
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(VideoCleanupQueue, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance
    
    def _initialize(self):
        self.queue = []
        self.max_retries = 3
        self.retry_delay = 5  # seconds
        self.processing = False
        self._lock = asyncio.Lock()
        self._task = None
        self.start_processing()
    
    def start_processing(self):
        if not self.processing:
            self.processing = True
            # Create an asyncio task instead of a thread
            loop = asyncio.get_event_loop()
            self._task = asyncio.create_task(self._process_queue_loop())
            logger.info("Started async video cleanup task")
    
    async def add_to_cleanup(self, file_path):
        async with self._lock:
            self.queue.append({
                'path': file_path,
                'retry_count': 0,
                'last_attempt': None
            })
            logger.info(f"Added file to cleanup queue: {file_path}")
    
    async def _process_queue_loop(self):
        while self.processing:
            await self.process_queue()
            await asyncio.sleep(1)  # Check queue every second
    
    async def process_queue(self):
        async with self._lock:
            items_to_remove = []
            for item in self.queue:
                if await self._should_retry(item):
                    try:
                        if await self._try_delete(item['path']):
                            items_to_remove.append(item)
                            logger.info(f"Successfully deleted file: {item['path']}")
                        else:
                            item['retry_count'] += 1
                            item['last_attempt'] = datetime.now()
                            logger.warning(f"Failed to delete file (attempt {item['retry_count']}): {item['path']}")
                    except Exception as e:
                        logger.error(f"Error processing {item['path']}: {e}")
                        item['retry_count'] += 1
                        item['last_attempt'] = datetime.now()
            
            # Remove processed items
            for item in items_to_remove:
                self.queue.remove(item)
    
    async def _should_retry(self, item):
        return (item['retry_count'] < self.max_retries and 
                (not item['last_attempt'] or 
                 (datetime.now() - item['last_attempt']).seconds > self.retry_delay))
    
    async def _try_delete(self, file_path):
        try:
            if os.path.exists(file_path):
                # Use asyncio.to_thread to make file deletion non-blocking
                await asyncio.to_thread(os.remove, file_path)
                return True
            return True  # File doesn't exist, consider it deleted
        except PermissionError:
            return False
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            return False

# Initialize the cleanup queue
_cleanup_queue = None

async def get_cleanup_queue():
    """Get the singleton cleanup queue instance"""
    global _cleanup_queue
    if _cleanup_queue is None:
        _cleanup_queue = VideoCleanupQueue()
    return _cleanup_queue

async def save_video_to_db(video_data_or_path, request_id, provider, 
                     video_type="booking_process", file_name=None, 
                     cabin_id=None, session_id=None, format="webm",
                     should_compress=True, delete_source=False):
    """
    Save a video to MinIO and store metadata in the database
    
    Args:
        video_data_or_path: Binary video data or path to video file
        request_id: The booking request ID
        provider: The provider (Studio, NCL, or Cruising Power)
        video_type: Type of video (e.g., booking_process, verification)
        file_name: Original file name of the video
        cabin_id: ID of the cabin this video is related to (if applicable)
        session_id: Session ID from the current processing run
        format: Video format (default: webm)
        should_compress: Whether to compress the video before storage (default: True)
        delete_source: Whether to delete the source file after storing (default: False)
    """
    try:
        # Get the global session manager
        session_manager = SessionManager.get_instance()
        
        conn = None  # Initialize to None to avoid potential reference before assignment
        
        # If no session_id was provided, try to get it from the session manager
        if not session_id:
            session_id = session_manager.get_current_session()
        
            # If still no session_id, try to get one for this request
            if not session_id:
                # First try to get session_id from session_tracking table
                async with db_connection() as conn:
                
                    session_result = await conn.fetchrow("""
                        SELECT session_id FROM session_tracking 
                        WHERE request_id = $1 AND provider = $2
                        ORDER BY updated_at DESC LIMIT 1
                    """, request_id, provider.lower())

                    if session_result and session_result['session_id']:
                        session_id = session_result['session_id']
                        logger.info(f"Using most recent session ID from session_tracking: {session_id}")
                    else:
                        # Fall back to booking table
                        booking_table = f"{provider.lower().replace(' ', '_')}_bookings"
                        result = await conn.fetchrow(f"""
                            SELECT session_id FROM {booking_table} 
                            WHERE request_id = $1
                            ORDER BY timestamp DESC LIMIT 1
                        """, request_id)

                        if result and result['session_id']:
                            session_id = result['session_id']
                            logger.info(f"Using most recent session ID from booking table: {session_id}")
                        else:
                            # Last resort: Create a new session ID
                            from .utils import register_session
                            session_id = await register_session(request_id, provider)
                            logger.info(f"Created new session ID for video: {session_id}")


                    # Set this session as the current one in the session manager
                    session_manager.set_current_session(session_id)

        # Handle video data from path or direct binary data
        video_data = None
        is_path = isinstance(video_data_or_path, str) and os.path.exists(video_data_or_path)
        
        if is_path:
            # Process file path
            source_path = video_data_or_path
            if not file_name:
                file_name = os.path.basename(source_path)
                
            if should_compress:
                # Compress the video before storing
                video_data = await compress_video_file(source_path)
                logger.info(f"Compressed video from {os.path.getsize(source_path) // 1024} KB")
            else:
                # Read the raw data
                async with aiofiles.open(source_path, 'rb') as f:
                    video_data = await f.read()
                    
            # Get video duration
            duration = await get_video_duration(source_path)
            
            # Add to cleanup queue if deletion is requested
            if delete_source:
                cleanup_queue = await get_cleanup_queue()
                await cleanup_queue.add_to_cleanup(source_path)
                    
        else:
            # Direct binary data
            video_data = video_data_or_path
            duration = 0  # Can't determine duration from binary data
            
            if should_compress:
                # Save to temp file, compress, then read back
                with tempfile.NamedTemporaryFile(suffix=f'.{format}', delete=False) as temp:
                    temp_path = temp.name
                
                async with aiofiles.open(temp_path, 'wb') as temp:
                    await temp.write(video_data)
                
                video_data = await compress_video_file(temp_path)
                # Add temp file to cleanup queue
                cleanup_queue = await get_cleanup_queue()
                await cleanup_queue.add_to_cleanup(temp_path)
                
        if not video_data:
            logger.error("No video data to store")
            return False
            
        # Calculate size in KB
        size_kb = len(video_data) // 1024
        
        # Upload to MinIO - await the async method and properly unpack the tuple
        upload_success, upload_result = await media_service.upload_video(
            video_data, provider, session_id, video_type, cabin_id, file_name, format, duration
        )
        
        if not upload_success:
            logger.error(f"Failed to upload video to MinIO: {upload_result.get('error', 'Unknown error')}")
            # Fall back to database storage for critical videos
            return await _save_video_to_db_fallback(video_data, request_id, provider, video_type, 
                                            file_name, cabin_id, session_id, format, duration, size_kb)
        
        # Extract MinIO metadata
        minio_url = upload_result.get('url')
        minio_bucket = upload_result.get('bucket')
        minio_object_key = upload_result.get('object_key')
        file_size_kb = upload_result.get('file_size_kb', size_kb)
        
        # Now proceed with the database metadata storage
        async with db_connection() as conn:
        
            # Find the booking ID matching this request_id and session_id
            booking_table = f"{provider.lower().replace(' ', '_')}_bookings"
            result = await conn.fetchrow(f"""
            SELECT id FROM {booking_table} 
            WHERE request_id = $1 AND session_id = $2
            """, request_id, session_id)

            # Set up booking-related fields
            booking_id = None
            booking_table_id = None
        
            # If a booking record exists with this session_id, use it
            if result:
                booking_id = result['id']
                booking_table_id = result['id']
                logger.info(f"Found matching {provider} booking (ID: {booking_id}) for request_id: {request_id} with session_id: {session_id}")
            else:
                # Store the video with NULL booking_id but the correct session_id
                booking_id = None
                booking_table_id = None
                logger.info(f"No booking with matching session_id found yet for request_id: {request_id}. Storing video with session_id: {session_id}")

            # Generate a filename if not provided
            if not file_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"{provider}_{request_id}_{video_type}_{timestamp}.{format}"
        
            # Insert the video metadata into the centralized table
            result = await conn.execute("""
            INSERT INTO centralized_videos
            (booking_id, request_id, session_id, provider, cabin_id, video_type, timestamp, 
             minio_url, minio_bucket, minio_object_key, file_name, duration, format, file_size_kb, booking_table, booking_table_id)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
            RETURNING id
            """,
                booking_id,
                request_id,
                session_id,
                provider.lower(),
                cabin_id,
                video_type,
                datetime.now().isoformat(),
                minio_url,
                minio_bucket,
                minio_object_key,
                file_name,
                duration,
                format,
                file_size_kb,
                booking_table,
                booking_table_id
            )
        
            # Get the ID of the inserted row
            video_id = None
            if result and hasattr(result, 'split'):
                # Parse the ID from the result string (e.g., "INSERT 0 1 [5]" -> 5)
                try:
                    video_id = int(result.split('[')[1].split(']')[0])
                except (IndexError, ValueError):
                    pass
                

            logger.info(f"Saved video metadata (ID: {video_id}, Size: {file_size_kb}KB) to database and MinIO with session_id: {session_id}")
            return True

    except Exception as e:
        logger.error(f"Error saving video: {e}")
        import traceback
        logger.error(traceback.format_exc())

        return False

async def _save_video_to_db_fallback(video_data: bytes, request_id: str, provider: str, video_type: str,
                             file_name: str, cabin_id: int, session_id: str, format: str, duration: int, size_kb: int):
    """Fallback method to save video directly to database if MinIO fails"""
    try:
        async with db_connection() as conn:
        
            booking_table = f"{provider.lower().replace(' ', '_')}_bookings"

            if not file_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"{provider}_{request_id}_{video_type}_{timestamp}.{format}"

            # Store in database with legacy video_data column
            await conn.execute("""
            INSERT INTO centralized_videos
            (booking_id, request_id, session_id, provider, cabin_id, video_type, timestamp, 
             video_data, file_name, duration, format, file_size_kb, booking_table, booking_table_id)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            """,
                None,
                request_id,
                session_id,
                provider.lower(),
                cabin_id,
                video_type,
                datetime.now().isoformat(),
                video_data,
                file_name,
                duration,
                format,
                size_kb,
                booking_table,
                None
            )

            logger.warning(f"Saved video to database fallback storage for session_id: {session_id}")
            return True
    except Exception as e:
        logger.error(f"Error in video fallback storage: {e}")

        return False

async def get_videos_from_db(request_id: str, provider: str = None, session_id: str = None):
    """
    Retrieve videos from the database for a given request ID
    
    Args:
        request_id: The booking request ID
        provider: Optional provider filter
        session_id: Optional session ID filter
    
    Returns:
        List of video dictionaries with metadata
    """
    try:
        async with db_connection() as conn:
        
            # Build query with optional filters
            query = """
            SELECT id, booking_id, request_id, session_id, provider, cabin_id, 
                   video_type, timestamp, file_name, duration, format, file_size_kb,
                   minio_url, minio_bucket, minio_object_key,
                   CASE 
                     WHEN minio_url IS NOT NULL THEN 'minio'
                     WHEN video_data IS NOT NULL THEN 'database'
                     ELSE 'missing'
                   END as storage_type
            FROM centralized_videos 
            WHERE request_id = $1
            """
            params = [request_id]
            param_idx = 2
            
            if provider:
                query += f" AND provider = ${param_idx}"
                params.append(provider)
                param_idx += 1

            if session_id:
                query += f" AND session_id = ${param_idx}"
                params.append(session_id)

            query += " ORDER BY timestamp DESC"

            videos = await conn.fetch(query, *params)
        
            # Convert to list of dictionaries
            result = []
            minio_count = 0
            db_count = 0
            missing_count = 0

            for video in videos:
                video_dict = dict(video)

                # Count storage types for logging
                storage_type = video_dict.get('storage_type', 'missing')
                if storage_type == 'minio':
                    minio_count += 1
                elif storage_type == 'database':
                    db_count += 1
                else:
                    missing_count += 1

                # Remove binary data to save memory in list operations
                video_dict.pop('video_data', None)
                result.append(video_dict)
        
            # Log storage distribution
            total_videos = len(result)
            if total_videos > 0:
                logger.info(f"Retrieved {total_videos} videos for request_id {request_id}: "
                           f"{minio_count} from MinIO, {db_count} from PostgreSQL, {missing_count} missing data")

            return result
        
    except Exception as e:
        logger.error(f"Error retrieving videos from database: {e}")
        import traceback
        logger.error(traceback.format_exc())

        return []

async def get_video_data(video_id):
    """
    Get the video data for a specific video (either MinIO URL or legacy binary data)
    
    Args:
        video_id: ID of the video
        
    Returns:
        Dict with video data and metadata
    """
    try:
        async with db_connection() as conn:
        
            video = await conn.fetchrow("""
            SELECT id, minio_url, minio_bucket, minio_object_key, video_data, file_name, format,
                   CASE 
                     WHEN minio_url IS NOT NULL THEN 'minio'
                     WHEN video_data IS NOT NULL THEN 'database'
                     ELSE 'missing'
                   END as storage_type
            FROM centralized_videos
            WHERE id = $1
            """, video_id)


            if video:
                video_dict = dict(video)

                # Log where the video is being loaded from
                if video_dict.get('minio_url'):
                    logger.info(f"Loading video {video_id} from MinIO: {video_dict['minio_url']}")
                    video_dict['storage_type'] = 'minio'
                    # Remove binary data to save memory
                    video_dict.pop('video_data', None)
                elif video_dict.get('video_data'):
                    logger.info(f"Loading video {video_id} from PostgreSQL database (legacy storage)")
                    video_dict['storage_type'] = 'database'
                    # Remove MinIO fields
                    video_dict.pop('minio_url', None)
                    video_dict.pop('minio_bucket', None)
                    video_dict.pop('minio_object_key', None)
                else:
                    logger.warning(f"Video {video_id} has no storage data (neither MinIO nor database)")
                    video_dict['storage_type'] = 'missing'

                return video_dict
            else:
                logger.warning(f"No video data found for video id: {video_id}")
                return None

    except Exception as e:
        logger.error(f"Error retrieving video data: {e}")
        import traceback
        logger.error(traceback.format_exc())

        return None

async def compress_video_file(input_path, output_path=None, target_size_kb=10240):
    """
    Compress a video file using ffmpeg (if available)
    
    Args:
        input_path: Path to the input video file
        output_path: Path for the output file (if None, a temp file is created)
        target_size_kb: Target size in KB (default: 10MB)
        
    Returns:
        bytes: Compressed video data or original data if compression fails
    """
    try:
        # If ffmpeg is not available, return the original video
        if not await is_ffmpeg_available():
            logger.warning("FFmpeg not available, using original video")
            async with aiofiles.open(input_path, 'rb') as f:
                return await f.read()
                
        # Use a temporary file if no output path specified
        if not output_path:
            output_path = tempfile.NamedTemporaryFile(suffix='.webm', delete=False).name
        
        # Get input file size in KB
        input_size_kb = os.path.getsize(input_path) // 1024
        
        # Skip compression if already smaller than target
        if input_size_kb <= target_size_kb:
            logger.info(f"Video already small enough ({input_size_kb}KB), skipping compression")
            async with aiofiles.open(input_path, 'rb') as f:
                return await f.read()
        
        # Calculate bitrate based on target size (kb * 8 / duration in seconds)
        duration = await get_video_duration(input_path)
        if duration <= 0:
            duration = 60  # Fallback to 60 seconds if duration can't be determined
            
        bitrate_kbps = int((target_size_kb * 8) / duration)
        if bitrate_kbps < 100:
            bitrate_kbps = 100  # Minimum 100 kbps
            
        # Compress using ffmpeg
        command = [
            'ffmpeg', '-i', input_path, 
            '-c:v', 'libvpx-vp9', 
            '-b:v', f'{bitrate_kbps}k',
            '-maxrate', f'{bitrate_kbps * 2}k',
            '-bufsize', f'{bitrate_kbps * 4}k',
            '-c:a', 'libopus', 
            '-b:a', '64k', 
            '-f', 'webm',
            '-y', output_path
        ]
        
        logger.info(f"Compressing video with target bitrate {bitrate_kbps}kbps")
        
        # Use asyncio subprocess instead of subprocess.run
        process = await asyncio.create_subprocess_exec(
            *command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # Wait for the process to finish
        await process.communicate()
        
        # Check if the output file exists and has content
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            async with aiofiles.open(output_path, 'rb') as f:
                compressed_data = await f.read()
                
            # Clean up the temp file
            if output_path != input_path:
                cleanup_queue = await get_cleanup_queue()
                await cleanup_queue.add_to_cleanup(output_path)
                
            output_size_kb = len(compressed_data) // 1024
            logger.info(f"Compressed video from {input_size_kb}KB to {output_size_kb}KB")
            
            return compressed_data
        else:
            logger.warning(f"Compression failed, using original video")
            async with aiofiles.open(input_path, 'rb') as f:
                return await f.read()
                
    except Exception as e:
        logger.error(f"Error compressing video: {e}")
        # Return original video on error
        try:
            async with aiofiles.open(input_path, 'rb') as f:
                return await f.read()
        except:
            return None

async def get_video_duration(file_path):
    """
    Get the duration of a video file in seconds
    
    Args:
        file_path: Path to the video file
        
    Returns:
        float: Duration in seconds or 0 if not available
    """
    try:
        if not await is_ffmpeg_available():
            return 0
            
        command = [
            'ffprobe', '-v', 'error', 
            '-show_entries', 'format=duration', 
            '-of', 'default=noprint_wrappers=1:nokey=1', 
            file_path
        ]
        
        # Use asyncio subprocess instead of subprocess.run
        process = await asyncio.create_subprocess_exec(
            *command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            text=True
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0 and stdout.strip():
            return float(stdout.strip())
        return 0
    except Exception as e:
        logger.error(f"Error getting video duration: {e}")
        return 0

async def is_ffmpeg_available():
    """Check if ffmpeg is available on the system"""
    try:
        process = await asyncio.create_subprocess_exec(
            'ffmpeg', '-version',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await process.communicate()
        return process.returncode == 0
    except:
        return False

# Legacy functions for backward compatibility
async def save_video_to_cabin(video_path, request_id, provider, cabin_id=None, session_id=None):
    """Legacy function - redirects to centralized storage"""
    return await save_video_to_db(video_path, request_id, provider, 
                           "cabin_video", os.path.basename(video_path), 
                           cabin_id, session_id)

async def get_video_from_cabin(cabin_id, provider):
    """Legacy function - first tries centralized storage, then falls back to cabin table"""
    try:
        # Try to get video from centralized storage
        async with db_connection() as conn:
        
            result = await conn.fetchrow("""
            SELECT video_data
            FROM centralized_videos
            WHERE provider = $1 AND cabin_id = $2
            ORDER BY timestamp DESC LIMIT 1
            """, provider.lower(), cabin_id)

            if result and result['video_data']:
                return result['video_data']

            # Fall back to cabin table
            provider_lower = provider.lower().replace(' ', '_')
            cabins_table = f"{provider_lower}_cabins"

            result = await conn.fetchrow(f"""
            SELECT video FROM {cabins_table}
            WHERE id = $1
            """, cabin_id)


            if result and result['video']:
                return result['video']
            else:
                logger.warning(f"No video found for cabin id: {cabin_id} in {cabins_table}")
                return None
    except Exception as e:
        logger.error(f"Error retrieving video from database: {e}")

        return None

async def get_videos_by_session_id(session_id):
    """
    Get all videos for a specific session
    
    Args:
        session_id: Session ID to get videos for
    
    Returns:
        List of video metadata (excluding the actual video data for performance)
    """
    try:
        async with db_connection() as conn:
        
            # Query videos but exclude the binary data for performance
            rows = await conn.fetch("""
                SELECT id, request_id, provider, session_id, cabin_id, timestamp
                FROM centralized_videos
                WHERE session_id = $1
                ORDER BY timestamp DESC
            """, session_id)

            # Convert to list of dictionaries
            videos = [dict(row) for row in rows]

            return videos

    except Exception as e:
        logger.error(f"Error retrieving videos by session ID: {e}")
        import traceback
        logger.error(traceback.format_exc())

        return []

async def get_video_by_id(video_id):
    """
    Get a single video by its ID, including the binary data
    
    Args:
        video_id: ID of the video to retrieve
    
    Returns:
        Dictionary with video metadata and binary data
    """
    try:
        async with db_connection() as conn:
        
            # Query the video including binary data
            row = await conn.fetchrow("""
                SELECT id, request_id, provider, session_id, cabin_id, timestamp, video_data
                FROM centralized_videos
                WHERE id = $1
            """, video_id)


            if not row:
                return None

            # Convert to dictionary
            video_dict = dict(row)

            return video_dict

    except Exception as e:
        logger.error(f"Error retrieving video by ID: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

async def get_videos_by_cabin_id(cabin_id, provider=None, session_id=None):
    """
    Get all videos for a specific cabin
    
    Args:
        cabin_id: ID of the cabin to get videos for
        provider: Optional provider filter
        session_id: Optional session ID filter
    
    Returns:
        List of video metadata (excluding the actual video data for performance)
    """
    try:
        async with db_connection() as conn:
        
            # Build query based on provided filters
            query = """
                SELECT id, request_id, provider, session_id, cabin_id, video_type, timestamp, file_name
                FROM centralized_videos
                WHERE cabin_id = $1
            """
            params = [cabin_id]
            param_idx = 2

            if provider:
                query += f" AND provider = ${param_idx}"
                params.append(provider.lower())
                param_idx += 1

            if session_id:
                query += f" AND session_id = ${param_idx}"
                params.append(session_id)

            query += " ORDER BY timestamp DESC"

            rows = await conn.fetch(query, *params)

            # Convert to list of dictionaries
            videos = [dict(row) for row in rows]

            return videos

    except Exception as e:
        logger.error(f"Error retrieving videos by cabin ID: {e}")
        import traceback
        logger.error(traceback.format_exc())

        return []
