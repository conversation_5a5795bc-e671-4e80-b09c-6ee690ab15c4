{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-edge-function-loader.ts"], "names": ["nextEdgeFunctionLoader", "absolutePagePath", "page", "rootDir", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "getOptions", "stringifiedPagePath", "stringifyRequest", "buildInfo", "getModuleBuildInfo", "_module", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "route", "nextEdgeApiFunction", "stringify"], "mappings": ";;;;+BA4DA;;;eAAA;;;oCA3DmC;kCACF;AAWjC,MAAMA,yBACJ,SAASA;IACP,MAAM,EACJC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,eAAe,EACfC,kBAAkBC,sBAAsB,EACzC,GAA8B,IAAI,CAACC,UAAU;IAC9C,MAAMC,sBAAsBC,IAAAA,kCAAgB,EAAC,IAAI,EAAER;IACnD,MAAMS,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IACjD,MAAMP,mBAAqCQ,KAAKC,KAAK,CACnDC,OAAOC,IAAI,CAACV,wBAAwB,UAAUW,QAAQ;IAExDP,UAAUQ,KAAK,GAAG;QAChBhB,MAAMA,QAAQ;QACdD;QACAG;QACAC;IACF;IACAK,UAAUS,mBAAmB,GAAG;QAC9BjB,MAAMA,QAAQ;IAChB;IACAQ,UAAUP,OAAO,GAAGA;IAEpB,OAAO,CAAC;;;;;4BAKgB,EAAEK,oBAAoB;;;mDAGC,EAAEN,KAAK;;;;;;;oBAOtC,EAAEW,KAAKO,SAAS,CAAClB,MAAM;;;;IAIvC,CAAC;AACH;MAEF,WAAeF"}