"""
Database operations for the Cruising Power provider.
"""
import json
import logging
from datetime import datetime
from typing import Dict, Tuple
from db_service import get_db_connection, db_connection
from .session_manager import SessionManager
from .utils import register_session

# Configure logging
logger = logging.getLogger("db.cruising_power_provider")

# Session tracking to prevent redundant saves
class CPResultsCache:
    """Track already saved Cruising Power booking results by session_id"""
    _processed_sessions = set()
    
    @classmethod
    def is_processed(cls, session_id):
        """Check if a session has already been processed"""
        return session_id in cls._processed_sessions
        
    @classmethod
    def mark_processed(cls, session_id):
        """Mark a session as processed"""
        cls._processed_sessions.add(session_id)
        
    @classmethod
    def reset(cls):
        """Reset the tracking mechanism"""
        cls._processed_sessions.clear()

async def save_cruising_power_results(data: Dict, request_id: str, session_id: str = None):
    """Save cruising power results to the database in one transaction."""
    # Ensure consistent request_id and session_id
    if data.get('request_id') and data.get('request_id') != request_id:
        logger.info(f"Overriding data request_id ({data.get('request_id')}) with provided request_id: {request_id}")
    data['request_id'] = request_id
    # Ensure session_id is set
    if not session_id and data.get('session_id'):
        session_id = data['session_id']
    elif not session_id:
        session_id = await register_session(request_id, 'cruising power')
        logger.info(f"Created new session ID for Cruising Power booking {request_id}: {session_id}")
    data['session_id'] = session_id
    
    try:
        # Prepare booking data
        overall_status = 1 if data.get('overall_status', False) else 0
        timestamp = data.get('timestamp', datetime.now().isoformat())
        execution_time = data.get('execution_time', 0.0)

        # Get a connection from the pool
        async with db_connection() as conn:
        # Start a transaction
            async with conn.transaction():
                # Insert booking record
                booking_id_row = await conn.fetchrow(
                    '''
                    INSERT INTO cruising_power_bookings
                        (request_id, timestamp, overall_status, execution_time, session_id)
                    VALUES ($1, $2, $3, $4, $5)
                    RETURNING id
                    ''',
                    request_id, timestamp, overall_status, execution_time, session_id
                )
                booking_id = booking_id_row['id']

                # Insert cabin records
                for cabin in data.get('cabins', []):
                    passengers = cabin.get('passengers', {}) or {}
                    adults = passengers.get('adults', 0)
                    children = passengers.get('children', 0)
                    seniors = passengers.get('seniors', 0)
                    total = passengers.get('total', 0)
                    await conn.execute(
                        '''
                        INSERT INTO cruising_power_cabins
                            (booking_id, cabin_number, cabin_type, normalized_type,
                            passengers_adults, passengers_children, passengers_seniors, passengers_total,
                            timestamp, success, cabin_allocation, total_price, onboard_credit, error)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                        ''',
                        booking_id,
                        cabin.get('cabin_number', 0),
                        cabin.get('cabin_type', ''),
                        cabin.get('normalized_type', ''),
                        adults, children, seniors, total,
                        cabin.get('timestamp', timestamp),
                        1 if cabin.get('total_price') else 0,
                        cabin.get('cabin_allocation', ''),
                        cabin.get('total_price', ''),
                        str(cabin.get('onboard_credit', '')),
                        cabin.get('error', '')
                    )

                # Update session tracking status
                session_metadata = json.dumps({'booking_id': booking_id, 'timestamp': timestamp})
                await conn.execute(
                    '''
                    UPDATE session_tracking
                    SET updated_at = $1, status = $2, metadata = $3
                    WHERE session_id = $4
                    ''',
                    timestamp, 'active', session_metadata, session_id
                )
        
        CPResultsCache.mark_processed(session_id)
        logger.info(f"Successfully saved Cruising Power booking data in database for request_id: {request_id}")
        return True, session_id
    except Exception as e:
        logger.error(f"Error saving Cruising Power results: {e}")
        return False, None

async def get_cruising_power_results(request_id: str, session_id: str = None) -> Tuple[Dict, Dict]:
    """Retrieve cruising power results from the database"""
    try:
        async with db_connection() as conn:
        
            logger.info(f"Searching for Cruising Power results - request_id: {request_id}, session_id: {session_id}")
        
            booking = None
        
            # If session_id is provided, use it directly to find the booking
            if session_id:
                logger.info(f"Searching by provided session_id: {session_id}")
                booking = await conn.fetchrow('''
                SELECT * FROM cruising_power_bookings 
                WHERE session_id = $1
                ''', session_id)
            
                if not booking:
                    logger.warning(f"No Cruising Power booking found with session_id: {session_id}, falling back to request_id search")
                    # Fall back to searching by request_id if no booking found with session_id
                    booking = await conn.fetchrow('''
                    SELECT * FROM cruising_power_bookings 
                    WHERE request_id = $1
                    ORDER BY timestamp DESC LIMIT 1
                    ''', request_id)
                
                    # If still not found, try a broader search
                    if not booking:
                        # Try a broader search with LIKE
                        logger.info(f"Trying broader search with LIKE %%{request_id}%%")
                        booking = await conn.fetchrow('''
                        SELECT * FROM cruising_power_bookings 
                        WHERE request_id LIKE $1 
                        ORDER BY timestamp DESC LIMIT 1
                        ''', f"%{request_id}%")
            else:
                # First check session_tracking to find the most recent session_id for this request_id
                logger.info(f"No session_id provided, checking session_tracking table for request_id: {request_id}")
                session_result = await conn.fetchrow('''
                SELECT session_id FROM session_tracking
                WHERE request_id = $1 AND provider = 'cruising power'
                ORDER BY updated_at DESC LIMIT 1
                ''', request_id)

                if session_result and session_result['session_id']:
                    # Use the session_id to get the booking
                    session_id = session_result['session_id']
                    logger.info(f"Found most recent session_id for {request_id}: {session_id}")

                    booking = await conn.fetchrow('''
                    SELECT * FROM cruising_power_bookings 
                    WHERE session_id = $1
                    ''', session_id)
                else:
                    # Get the most recent booking with this request_id
                    booking = await conn.fetchrow('''
                    SELECT * FROM cruising_power_bookings 
                    WHERE request_id = $1
                    ORDER BY timestamp DESC LIMIT 1
                    ''', request_id)

                    # If not found, try a broader search
                    if not booking:
                        # Try a broader search with LIKE
                        logger.info(f"Trying broader search with LIKE %%{request_id}%%")
                        booking = await conn.fetchrow('''
                        SELECT * FROM cruising_power_bookings 
                        WHERE request_id LIKE $1
                        ORDER BY timestamp DESC LIMIT 1
                        ''', f"%{request_id}%")

                        # As a last resort, get the most recent booking
                        if not booking:
                            logger.info("Last resort: getting most recent booking")
                            booking = await conn.fetchrow('''
                            SELECT * FROM cruising_power_bookings 
                            ORDER BY timestamp DESC LIMIT 1
                            ''')

                            if not booking:
                                logger.warning(f"No Cruising Power booking found for request_id: {request_id}")
                                return {}, {}
                            else:
                                logger.info(f"Using most recent Cruising Power booking as fallback")

            if not booking:
                logger.warning(f"No Cruising Power booking found for request_id: {request_id}")
                return {}, {}

            booking_dict = dict(booking)
            session_id = booking_dict.get('session_id')  # Extract session_id for screenshot retrieval

            logger.info(f"Found booking with ID: {booking_dict['id']}, session_id: {session_id}")

            # Get all cabins for this booking
            cabins_raw = await conn.fetch('''
            SELECT * FROM cruising_power_cabins 
            WHERE booking_id = $1
            ''', booking['id'])

            logger.info(f"Found {len(cabins_raw)} cabins for booking ID: {booking_dict['id']}")

            # Convert cabins to the format expected by the frontend
            cabins = []
            for cabin_raw in cabins_raw:
                cabin = dict(cabin_raw)

                # Reconstruct the passengers dictionary
                passengers = {
                    'adults': cabin['passengers_adults'],
                    'children': cabin['passengers_children'],
                    'seniors': cabin['passengers_seniors'],
                    'total': cabin['passengers_total']
                }

                cabin_dict = {
                    'cabin_number': cabin['cabin_number'],
                    'cabin_type': cabin['cabin_type'],
                    'normalized_type': cabin['normalized_type'],
                    'passengers': passengers,
                    'timestamp': cabin['timestamp'],
                    'success': bool(cabin['success']),
                    'cabin_allocation': cabin['cabin_allocation'],
                    'total_price': cabin['total_price'],
                    'onboard_credit': cabin['onboard_credit']
                }

                if cabin['error']:
                    cabin_dict['error'] = cabin['error']

                # Deserialize the JSON string back to a list if present
                try:
                    # Only attempt to parse current_promos if the key exists
                    if 'current_promos' in cabin and cabin['current_promos']:
                        try:
                            cabin_dict['current_promos'] = json.loads(cabin['current_promos'])
                        except (json.JSONDecodeError, TypeError):
                        # Handle any errors (e.g., if it's already a list or invalid JSON)
                            cabin_dict['current_promos'] = []
                    else:
                        # If key doesn't exist, set to empty list
                        cabin_dict['current_promos'] = []
                except Exception as e:
                    logger.warning(f"Error processing current_promos for cabin {cabin.get('cabin_number', 'unknown')}: {e}")
                    cabin_dict['current_promos'] = []

                cabins.append(cabin_dict)
        
            # Reconstruct the result in the format expected by the frontend
            result = {
                'cabins': cabins,
                'timestamp': booking_dict['timestamp'],
                'overall_status': bool(booking_dict['overall_status']),
                'execution_time': booking_dict['execution_time'],
                'request_id': booking_dict['request_id'],  # Use the original request_id
                'session_id': session_id  # Include session_id for screenshot retrieval
            }

            logger.info(f"Retrieved Cruising Power booking data for request_id: {booking_dict['request_id']}, found {len(cabins)} cabins, session_id: {session_id}")
            return booking_dict, result
    except Exception as e:
        logger.error(f"Error retrieving Cruising Power results: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {}, {} 