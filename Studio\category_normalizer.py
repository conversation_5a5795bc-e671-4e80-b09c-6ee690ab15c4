"""
Category Normalizer module for standardizing cruise cabin categories.

This module provides functionality to normalize and standardize cabin category names 
across different cruise lines. It maps various terminology used by different cruise
companies into standardized category types (INSIDE, OCEANVIEW, BALCONY, SUITE),
allowing for consistent handling of cabin types throughout the booking process.
"""

from loguru import logger


class CategoryNormalizer:
    """
    A class for normalizing and standardizing cruise cabin categories.
    
    This class provides methods to convert various cabin category names and codes
    into standardized formats. It maintains mappings between common variants of 
    category names and their standard equivalents, enabling consistent category 
    handling regardless of the cruise line's specific terminology.
    
    Attributes:
        CATEGORY_MAPPINGS (dict): Dictionary mapping standard category names to 
                                lists of variant names/abbreviations
    """

    # Define mappings from standard categories to their various representations
    # This allows the normalizer to recognize different terms for the same category type
    CATEGORY_MAPPINGS = {
        'INSIDE': ['INSIDE', 'INTERIOR', 'INT', 'INSI'], 
        'OCEANVIEW': ['OCEANVIEW', 'OCEAN VIEW', 'OUTSIDE', 'OV', 'WINDOW', 'OUTSI'], 
        'BALCONY': ['BALCONY', 'VERANDA', 'BAL', 'VER'], 
        'JR SUITE': ['JR SUITE', 'JUNIO<PERSON> SUITE', 'MINI SUITE', '<PERSON><PERSON>ITE'],
        'S<PERSON>TE': ['SUITE', 'STE', 'SUIT']
    }

    @classmethod
    def get_standard_categories(cls):
        """
        Get a list of all standardized category names.
        
        Returns:
            list: List of standard category names (INSIDE, OCEANVIEW, BALCONY, SUITE)
        """
        # Return keys from the CATEGORY_MAPPINGS dictionary
        # These are the canonical category names used throughout the system
        return list(cls.CATEGORY_MAPPINGS.keys())

    @classmethod
    def is_valid_category(cls, category):
        """
        Check if a category name can be normalized to a standard category.
        
        Args:
            category (str): The category name to validate
            
        Returns:
            bool: True if the category can be normalized to a standard category,
                 False otherwise
        """
        # Normalize the category and check if it matches a standard category
        normalized = cls.normalize_category(category)
        return normalized in cls.get_standard_categories()

    @classmethod
    def normalize_category(cls, category):
        """
        Normalize a category name to its standard form.
        
        This method takes any variant of a cabin category name and converts it
        to one of the standardized category names.
        
        Args:
            category (str): The category name to normalize
            
        Returns:
            str: The normalized category name, or the original category
                if no standard match is found
        """
        # Handle empty or None input
        if not category:
            return None

        # Convert to uppercase for case-insensitive comparison
        category = str(category).strip().upper()

        # Direct match: If it's already a standard category name
        for standard, variants in cls.CATEGORY_MAPPINGS.items():
            if category == standard:
                return standard

        # Substring match: If the category contains a variant name
        for standard, variants in cls.CATEGORY_MAPPINGS.items():
            if any(variant in category for variant in variants):
                return standard

        # Bidirectional substring match: If category contains variant or variant contains category
        for standard, variants in cls.CATEGORY_MAPPINGS.items():
            if any(variant in category or category in variant for variant in variants):
                return standard

        # If no match found, return the original category
        return category

    @classmethod
    def normalize_cabin_category(cls, category):
        """
        Normalize a specific cabin category code or name.
        
        This method handles both standard category names and specific cabin
        category codes (like "BB" or "4A"). It has special handling for
        one or two-character codes that might represent specific cabin types.
        
        Args:
            category (str): Cabin category code or name to normalize
            
        Returns:
            str: Normalized category name or the original code if it appears
                to be a specific cabin code rather than a category type
        """
        # Handle empty input
        if not category:
            return category

        category = category.strip()

        # Special handling for 1-2 character codes which are likely
        # specific cabin codes rather than general category types
        if len(category) <= 2 and len(category) >= 1:
            # Single letter code (e.g., "B" for balcony)
            if len(category) == 1 and category.isalpha():
                return category.upper()

            # Two-character codes: could be all letters (BB),
            # letter+number (B4), or number+letter (4B)
            elif len(category) == 2:
                if category.isalpha() or \
                (category[0].isalpha() and category[1].isdigit()) or \
                (category[0].isdigit() and category[1].isalpha()):
                    return category.upper()

        # For longer category names, try to match with standard categories
        upper_category = category.upper()

        # Direct match with standard category or known variant
        for standard, variants in cls.CATEGORY_MAPPINGS.items():
            if upper_category == standard or upper_category in variants:
                return standard

        # Handle special multi-word cases that need exact matching
        words = upper_category.split()
        special_multi_words = ['JR SUITE', 'JUNIOR SUITE', 'MINI SUITE', 'OCEAN VIEW']

        # If it's a multi-word category but not a special case, return as is
        if len(words) > 1 and upper_category not in special_multi_words:
            return category

        # Return the original category if no matches found
        return category
