import os
import sys
import time
import asyncio
from loguru import logger
from typing import Dict, Any, Optional

# Add parent directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import necessary modules
from NCL.parallel_booking import ParallelBooking
from Core.database_helper import save_ncl_results, save_cruising_power_results, SessionManager

class BookingService:
    """
    Service for managing cruise bookings across different providers
    """
    
    @staticmethod
    async def ncl_booking(
        cruise_details: Dict[str, Any], 
        config: Dict[str, Any], 
        request_id: str, 
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process NCL booking with the provided cruise details
        
        Args:
            cruise_details: Dictionary containing cruise details
            config: Configuration options
            request_id: Unique identifier for the booking request
            session_id: Session identifier for tracking (optional)
            
        Returns:
            Dict containing booking results and status
        """
        # Start timing the execution
        start_time = time.time()
        
        # Initialize session manager if session_id is provided
        if session_id:
            session_manager = SessionManager.get_instance()
            await session_manager.update_session_status(session_id, "processing", {"step": "ncl_booking_started"})
        
        try:
            # Add request ID to cruise details for screenshot organization
            cruise_details['request_id'] = request_id
            
            # Extract NCL rate codes from config if available
            ncl_rate_codes = config.get('ncl_rate_codes', {
                'ALL4CHO': True,
                'CHOALL42': True,
                'FITOBC': False,
                'STAROBC': False,
                'NETOBC': False,
            })
            
            # Extract category-specific onboard percentages from config
            ncl_category_percentages = config.get('ncl_category_percentages', {
                'inside': 12,
                'outside': 12,
                'balcony': 10,
                'junior_suite': 10,
                'suite': 10,
            })
            
            # Add rate codes and category percentages to config for NCL booking
            config_with_enhancements = config.copy()
            config_with_enhancements['ncl_rate_codes'] = ncl_rate_codes
            config_with_enhancements['ncl_category_percentages'] = ncl_category_percentages
            
            # Get NCL-specific video auditing setting
            video_auditing = config.get('video_auditing_ncl', False)
            config_with_enhancements['video_auditing'] = video_auditing
            logger.info(f"Using NCL-specific video auditing setting: {video_auditing}")
            
            # Run the NCL booking process in current loop
            results = await ParallelBooking.parallel_cruise_reservation(cruise_details, config_with_enhancements, session_id=session_id)
            
            # Calculate execution time
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Save results to database
            if results:
                # Add execution time to the results
                if isinstance(results, dict):
                    results['execution_time'] = execution_time
                
                # Check if results have already been saved to the database
                if isinstance(results, dict) and results.get('_saved_to_database') != True:
                    # Save NCL results to database
                    await save_ncl_results(results, request_id, session_id)
                else:
                    logger.info(f"Skipping duplicate database save for request_id: {request_id} - already saved")
            
            # Update session status on completion if session_id provided
            if session_id:
                await session_manager.update_session_status(
                    session_id, 
                    "completed", 
                    {"success": results is not False, "execution_time": execution_time}
                )
            
            from Core.browser_setup import browser_manager
            await browser_manager.close_current_loop_browsers()
            return {
                "success": results is not False,
                "results": results,
                "execution_time": execution_time
            }
            
        except asyncio.CancelledError:
            # Calculate execution time even in case of cancellation
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.warning(f"NCL booking cancelled for session {session_id}")
            
            # Update session status on cancellation if session_id provided
            if session_id:
                await session_manager.update_session_status(
                    session_id, 
                    "cancelled", 
                    {"cancelled_by": "user", "execution_time": execution_time}
                )
            
            # Don't save results for cancelled bookings
            raise  # Re-raise to ensure proper cancellation propagation
            
        except Exception as e:
            # Calculate execution time even in case of error
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Update session status on error if session_id provided
            if session_id:
                await session_manager.update_session_status(
                    session_id, 
                    "error", 
                    {"error": str(e), "execution_time": execution_time}
                )
            
            # Re-raise the exception to be handled by the caller
            from Core.browser_setup import browser_manager
            await browser_manager.close_current_loop_browsers()
            raise 

    @staticmethod
    async def cruising_power_booking(
        cp_details: Dict[str, Any],
        config: Dict[str, Any],
        request_id: str,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process Cruising Power booking with the provided cruise details

        Args:
            cp_details: Dictionary containing extracted cruise details
            config: Configuration options (must include request_id, session_id)
            request_id: Unique identifier for the booking request
            session_id: Session identifier for tracking (optional)

        Returns:
            Dict containing booking results and status
        """
        start_time = time.time()

        # Initialize session manager if session_id is provided
        if session_id:
            session_manager = SessionManager.get_instance()
            await session_manager.update_session_status(
                session_id,
                "processing",
                {"step": "cruising_power_started"}
            )

        try:
            # Ensure request_id is in config
            config_with_enhancements = config.copy()
            config_with_enhancements['request_id'] = request_id
            if session_id:
                config_with_enhancements['session_id'] = session_id
                
            # Get Cruising Power-specific video auditing setting
            video_auditing = config.get('video_auditing_cruising_power', False)
            config_with_enhancements['video_auditing'] = video_auditing
            logger.info(f"Using Cruising Power-specific video auditing setting: {video_auditing}")
                
            # Run the Cruising Power booking process directly in current loop
            from Cruising_Power.main import main as cruising_power_main
            results = await cruising_power_main(config_with_enhancements, cp_details=cp_details)

            # Close browsers tied to this event loop (contexts already closed inside module)
            from Core.browser_setup import browser_manager
            await browser_manager.close_current_loop_browsers()

            # Calculate execution time
            end_time = time.time()
            execution_time = end_time - start_time

            # Attach execution time
            if isinstance(results, dict):
                results['execution_time'] = execution_time

            # Save results to database with CPResultsCache tracking
            try:
                # Use PostgreSQL provider implementation
                from db.cruising_power_provider import CPResultsCache, save_cruising_power_results
                
                # Check if this session has already been processed
                if session_id and not CPResultsCache.is_processed(session_id):
                    logger.info(f"Saving Cruising Power results to database from booking service...")
                    save_success, _ = await save_cruising_power_results(results, request_id, session_id)
                else:
                    logger.info(f"Skipping duplicate database save for session_id: {session_id} - already processed")
                    save_success = True
            except ImportError:
                # Fall back to PostgreSQL provider if database import fails
                logger.warning("Could not import db.cruising_power_provider - falling back to default provider")
                from db.cruising_power_provider import save_cruising_power_results as fallback_save_cp
                save_success, _ = await fallback_save_cp(results, request_id, session_id)

            # Update session status on completion
            if session_id:
                status = "completed" if save_success else "error"
                await session_manager.update_session_status(
                    session_id,
                    status,
                    {"success": save_success, "execution_time": execution_time}
                )

            return {
                "success": save_success,
                "results": results,
                "execution_time": execution_time
            }
        except asyncio.CancelledError:
            logger.warning(f"Cruising Power booking cancelled for session {session_id}")
            
            # Update session status on cancellation
            if session_id:
                session_manager = SessionManager.get_instance()
                await session_manager.update_session_status(
                    session_id,
                    "cancelled",
                    {"cancelled_by": "user"}
                )
            
            # Don't save results for cancelled bookings
            raise  # Re-raise to ensure proper cancellation propagation
            
        except Exception as e:
            # Update session status on error
            if session_id:
                session_manager = SessionManager.get_instance()
                await session_manager.update_session_status(
                    session_id,
                    "error",
                    {"error": str(e)}
                )
            # Re-raise to be handled by caller
            from Core.browser_setup import browser_manager
            await browser_manager.close_current_loop_browsers()
            raise 

    @staticmethod
    async def onesource_booking(
        onesource_details: Dict[str, Any],
        config: Dict[str, Any],
        request_id: str,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process OneSource booking with the provided cruise details
        
        Args:
            onesource_details: Dictionary containing extracted cruise details
            config: Configuration options
            request_id: Unique identifier for the booking request
            session_id: Session identifier for tracking (optional)
            
        Returns:
            Dict containing booking results and status
        """
        start_time = time.time()
        
        # Initialize session manager if session_id is provided
        if session_id:
            session_manager = SessionManager.get_instance()
            await session_manager.update_session_status(
                session_id,
                "processing",
                {"step": "onesource_booking_started"}
            )
        
        try:
            # Ensure request_id is in config
            config_with_enhancements = config.copy()
            config_with_enhancements['request_id'] = request_id
            if session_id:
                config_with_enhancements['session_id'] = session_id
            
            # Get OneSource-specific video auditing setting
            video_auditing = config.get('video_auditing_onesource', False)
            config_with_enhancements['video_auditing'] = video_auditing
            logger.info(f"Using OneSource-specific video auditing setting: {video_auditing}")
            
            # Run the OneSource booking process
            from OneSource.main import main as onesource_main
            results = await onesource_main(config_with_enhancements, cruise_details=onesource_details)
            
            # Close browsers tied to this event loop
            from Core.browser_setup import browser_manager
            await browser_manager.close_current_loop_browsers()
            
            # Calculate execution time
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Attach execution time
            if isinstance(results, dict):
                results['execution_time'] = execution_time
            
            # Save results to database
            try:
                from Core.database_helper import save_onesource_results
                save_success = await save_onesource_results(results, request_id, session_id)
                logger.info(f"OneSource results saved to database: {save_success}")
            except Exception as e:
                logger.error(f"Error saving OneSource results to database: {e}")
                save_success = False
            
            # Update session status on completion
            if session_id:
                status = "completed" if save_success else "error"
                await session_manager.update_session_status(
                    session_id,
                    status,
                    {"success": save_success, "execution_time": execution_time}
                )
            
            return {
                "success": save_success,
                "results": results,
                "execution_time": execution_time
            }
            
        except asyncio.CancelledError:
            # Calculate execution time even in case of cancellation
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.warning(f"OneSource booking cancelled for session {session_id}")
            
            # Update session status on cancellation
            if session_id:
                session_manager = SessionManager.get_instance()
                await session_manager.update_session_status(
                    session_id,
                    "cancelled",
                    {"cancelled_by": "user", "execution_time": execution_time}
                )
            
            # Don't save results for cancelled bookings
            raise  # Re-raise to ensure proper cancellation propagation
            
        except Exception as e:
            # Calculate execution time even in case of error
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Update session status on error
            if session_id:
                session_manager = SessionManager.get_instance()
                await session_manager.update_session_status(
                    session_id,
                    "error",
                    {"error": str(e), "execution_time": execution_time}
                )
            
            # Re-raise the exception to be handled by the caller
            from Core.browser_setup import browser_manager
            await browser_manager.close_current_loop_browsers()
            raise

    @staticmethod
    async def studio_booking(
        studio_details: Dict[str, Any],
        config: Dict[str, Any],
        request_id: str,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process Studio booking with the provided cruise details.
        """
        import time
        from Core.database_helper import save_studio_results, SessionManager
        # Lazy import to avoid circular dependency
        from Studio.main import StudioManager

        start_time = time.time()
        if session_id:
            session_manager = SessionManager.get_instance()
            await session_manager.update_session_status(
                session_id,
                "processing",
                {"step": "studio_booking_started"}
            )
        try:
            # Run the Studio booking process asynchronously
            extra_params = {"session_id": session_id} if session_id else {}
            
            # Get Studio-specific video auditing setting
            video_auditing = config.get('video_auditing_studio', False)
            logger.info(f"Using Studio-specific video auditing setting: {video_auditing}")
                
            # Copy config to avoid modifying the original
            config_with_enhancements = config.copy()
            config_with_enhancements['video_auditing'] = video_auditing
            
            results, execution_summary = await StudioManager.wrap_process_cabins_with_timing(
                extracted_details=studio_details,
                request_id=request_id,
                browser_type=config_with_enhancements.get("browser_type", "firefox"),
                rate_selection_strategy=config_with_enhancements.get("rate_selection_strategy", "Cheapest"),
                video_auditing=video_auditing,
                provider=config_with_enhancements.get('provider', 'Studio.Sales.CabinCloseOut'),
                **extra_params
            )
            end_time = time.time()
            execution_time = end_time - start_time

            # Calculate grand_total from successful cabin results
            grand_total = 0
            actual_cabins = [r for r in results if isinstance(r, dict) and 'cabin_number' in r]
            successful_cabins = 0
            
            for cabin in actual_cabins:
                if cabin.get('status') == 'success':
                    # Try to get cabin_total directly
                    if 'cabin_total' in cabin and cabin['cabin_total'] > 0:
                        grand_total += cabin['cabin_total']
                        successful_cabins += 1
                    # If not available, try to extract from pricing_data
                    elif 'pricing_data' in cabin:
                        try:
                            for price_item in cabin.get('pricing_data', []):
                                if 'Item' in price_item and 'Total' in price_item['Item']:
                                    price_str = price_item['Item'].replace('Total\n$', '').replace(',', '').replace(' USD', '')
                                    cabin_total = float(price_str)
                                    grand_total += cabin_total
                                    # Update cabin with extracted total
                                    cabin['cabin_total'] = cabin_total
                                    successful_cabins += 1
                                    break
                        except Exception as e:
                            # Log but continue processing
                            logger.error(f"Error extracting price from pricing_data: {e}")
            
            # If grand_total is still 0 but we have successful cabins, use a default price
            if grand_total == 0 and successful_cabins > 0:
                # Check if there's a value in execution_summary from Studio
                if execution_summary and 'total_price' in execution_summary:
                    grand_total = execution_summary['total_price']
                else:
                    # Use default price as seen in the database (2784)
                    grand_total = 2784
            
            # Add execution_time and metadata to results
            results.append({
                'grand_total': grand_total,
                'execution_time': execution_time,
                'overall_status': 1 if successful_cabins == len(actual_cabins) and len(actual_cabins) > 0 else 0
            })

            # Save results to database
            await save_studio_results(results, request_id, session_id)

            # Update session status
            if session_id:
                await session_manager.update_session_status(
                    session_id,
                    "completed",
                    {"success": results is not False, "execution_time": execution_time}
                )

            from Core.browser_setup import browser_manager
            await browser_manager.close_current_loop_browsers()
            return {
                "success": results is not False,
                "results": results,
                "execution_time": execution_time
            }
        except asyncio.CancelledError:
            logger.warning(f"Studio booking cancelled for session {session_id}")
            
            # Update session status on cancellation
            if session_id:
                await session_manager.update_session_status(
                    session_id, 
                    "cancelled", 
                    {"cancelled_by": "user"}
                )
            
            # Don't save results for cancelled bookings
            raise  # Re-raise to ensure proper cancellation propagation
            
        except Exception as e:
            if session_id:
                await session_manager.update_session_status(session_id, "error", {"error": str(e)})
            from Core.browser_setup import browser_manager
            await browser_manager.close_current_loop_browsers()
            raise 