/// <reference types="react" />
import type { StackFrame } from 'next/dist/compiled/stacktrace-parser';
import type { VersionInfo } from '../../../../server/dev/parse-version-info';
import type { SupportedErrorEvent } from './container/Errors';
import type { ComponentStackFrame } from './helpers/parse-component-stack';
export declare const ACTION_BUILD_OK = "build-ok";
export declare const ACTION_BUILD_ERROR = "build-error";
export declare const ACTION_BEFORE_REFRESH = "before-fast-refresh";
export declare const ACTION_REFRESH = "fast-refresh";
export declare const ACTION_UNHANDLED_ERROR = "unhandled-error";
export declare const ACTION_UNHANDLED_REJECTION = "unhandled-rejection";
export declare const ACTION_VERSION_INFO = "version-info";
export declare const INITIAL_OVERLAY_STATE: OverlayState;
interface BuildOkAction {
    type: typeof ACTION_BUILD_OK;
}
interface BuildErrorAction {
    type: typeof ACTION_BUILD_ERROR;
    message: string;
}
interface BeforeFastRefreshAction {
    type: typeof ACTION_BEFORE_REFRESH;
}
interface FastRefreshAction {
    type: typeof ACTION_REFRESH;
}
export interface UnhandledErrorAction {
    type: typeof ACTION_UNHANDLED_ERROR;
    reason: Error;
    frames: StackFrame[];
    componentStackFrames?: ComponentStackFrame[];
}
export interface UnhandledRejectionAction {
    type: typeof ACTION_UNHANDLED_REJECTION;
    reason: Error;
    frames: StackFrame[];
}
interface VersionInfoAction {
    type: typeof ACTION_VERSION_INFO;
    versionInfo: VersionInfo;
}
export type FastRefreshState = {
    type: 'idle';
} | {
    type: 'pending';
    errors: SupportedErrorEvent[];
};
export interface OverlayState {
    nextId: number;
    buildError: string | null;
    errors: SupportedErrorEvent[];
    rootLayoutMissingTagsError?: {
        missingTags: string[];
    };
    refreshState: FastRefreshState;
    versionInfo: VersionInfo;
    notFound: boolean;
}
export declare const errorOverlayReducer: React.Reducer<Readonly<OverlayState>, Readonly<BuildOkAction | BuildErrorAction | BeforeFastRefreshAction | FastRefreshAction | UnhandledErrorAction | UnhandledRejectionAction | VersionInfoAction>>;
export {};
