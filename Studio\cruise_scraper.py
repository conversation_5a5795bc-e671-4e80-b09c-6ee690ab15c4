"""
Cruise Scraper module for automating the cruise booking workflow.

This module provides the main integration class that combines the functionality 
of multiple scraper components (base, category, rate selection, cabin selection)
to create a complete end-to-end cruise booking automation workflow.
"""

import time
import asyncio
from datetime import datetime

from loguru import logger
from playwright.async_api import TimeoutError

from Studio.base_scraper import BaseScraper
from Studio.category_normalizer import CategoryNormalizer
from Studio.category_scraper import CategoryScraper
from Studio.rate_selection import RateSelector
from Studio.select_cabin_with_price import CabinSelector


class CruiseScraper(BaseScraper, CategoryScraper, RateSelector, CabinSelector):
    """
    Main scraper class that integrates all booking components into a complete workflow.
    
    This class inherits from multiple specialized scrapers to combine their functionality
    into a single coordinated booking flow. It handles the entire process from loading
    the search results page to selecting a cabin category, choosing a rate, selecting
    a specific cabin, and extracting pricing information.
    
    Inheritance:
        BaseScraper: Provides core browser functionality and page navigation
        CategoryScraper: Handles category search and identification
        RateSelector: Handles rate selection based on strategy
        CabinSelector: Handles cabin selection and pricing extraction
    """

    def __init__(self, request_id, browser_type, session_id, provider='Studio.Sales.CabinCloseOut'):
        """
        Initialize the CruiseScraper with browser settings and configuration.
        
        Args:
            request_id (str): Unique identifier for this booking request
            browser_type (str): Type of browser to use (e.g., 'firefox', 'chromium')
            session_id (str): Unique identifier for this browser session
            provider (str): The provider for the cabin booking (e.g., 'Studio.Sales.CabinCloseOut', 'Studio.Res.CabinCloseOut')
        """
        # Initialize the parent BaseScraper class with provider
        super().__init__(request_id, browser_type, session_id, provider=provider)

        # Configure operation parameters
        self.max_wait_time = 30  # Maximum time to wait for elements (seconds)
        self.retry_attempts = 3  # Number of retry attempts for operations
        self.category_normalizer = CategoryNormalizer(
        )  # For standardizing category names
        self.rate_selection_strategy = 'Cheapest'  # Default rate selection strategy

    async def process_cabin_selection(
        self, selected_category_type, selected_category_code, cabin_number
    ):
        """
        Execute the complete cabin selection workflow from category to pricing.
        
        This method orchestrates the entire booking flow:
        1. Find and select the specified cabin category
        2. Select the appropriate rate based on strategy
        3. Select a specific cabin
        4. Extract and return pricing details
        
        Args:
            selected_category_type (str): Type of cabin category (e.g., "BALCONY", "INSIDE")
                                         or "UNKNOWN" if only code is provided
            selected_category_code (str): Specific category code (e.g., "BB", "4A")
            cabin_number (int): Reference cabin number for tracking
            
        Returns:
            dict: A dictionary containing success status, cabin details, and pricing information
            
        Raises:
            TimeoutError: If page elements cannot be found within timeout period
            Exception: For other errors during the booking process
        """
        try:
            # Log the start of cabin selection process
            logger.info(
                f"Processing cabin {cabin_number}: {selected_category_type} - {selected_category_code}"
            )
            # Generate timestamp for unique screenshot naming
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Wait for cabin category panels to appear
            await self.page.wait_for_selector(
                '.panel.is-itemized', state="visible", timeout=30000
            )
            panels = await self.page.locator('.panel.is-itemized').all()

            # Wait for category containers to appear
            await self.page.wait_for_selector(
                '.grouped-category-container-wrapper', state="visible", timeout=30000
            )
            category_containers = await self.page.locator(
                '.grouped-category-container-wrapper'
            ).all()

            # Verify category containers exist
            if not category_containers:
                raise Exception(f"No category containers found - cabin {cabin_number}")

            # Find the specific container that matches our selected category code
            target_container = None
            for container in category_containers:
                try:
                    code_element = container.locator('.room-category-block').first
                    if await code_element.text_content() == selected_category_code:
                        target_container = container
                        break
                except Exception:
                    continue

            # Verify target container was found
            if not target_container:
                raise Exception(
                    f"Container not found for code {selected_category_code}"
                )

            logger.info(
                f"Found category container for code: {selected_category_code}"
            )

            # If category type is unknown, try to determine it from panel titles
            if selected_category_type == "UNKNOWN":
                try:
                    for panel in panels:
                        try:
                            # Search each panel for the matching category code
                            category_codes = await panel.locator('.room-category-block').all()
                            for code_element in category_codes:
                                if await code_element.text_content() == selected_category_code:
                                    # Extract and normalize the panel title to get category type
                                    panel_title = await panel.locator(
                                        '.panel-title'
                                    ).first.text_content()
                                    panel_title = panel_title.strip().upper()
                                    selected_category_type = self.category_normalizer.normalize_category(
                                        panel_title
                                    )
                                    logger.info(
                                        f"Determined category type: {selected_category_type}"
                                    )
                                    break
                            if selected_category_type != "UNKNOWN":
                                break
                        except Exception as e:
                            logger.warning(f"Error checking panel: {e}")
                            continue

                    # If still unknown after checking all panels, log a warning
                    if selected_category_type == "UNKNOWN":
                        logger.warning(
                            f"Could not determine category type for {selected_category_code}"
                        )
                except Exception as category_error:
                    logger.warning(
                        f"Category type detection failed: {category_error}"
                    )

            logger.info(
                f"Using category: {selected_category_type} ({selected_category_code})"
            )

            # Find and click the button to view rates for this category
            view_rates_button = target_container.locator(
                'button.grouped-category-button.icon-expand.is-swappable'
            )

            # Verify rate button exists and click it
            if await view_rates_button.count() > 0:
                view_rates_button_element = view_rates_button.first
                await view_rates_button_element.scroll_into_view_if_needed()
                await view_rates_button_element.click()
                logger.info("Expanded rate options")

            else:
                raise Exception(
                    f"Rate button not found for category {selected_category_code}"
                )

            # Take screenshot of rate selection screen
            screenshot_rate_code = self.page.locator(".main-area-container").first
            await self.optimize_screenshot(
                screenshot_rate_code, f"rate_selection_{timestamp}.png", cabin_number, quality=50
            )

            # Get the rates wrapper element that contains all rate options
            rates_wrapper = target_container.locator(
                'div.grouped-category-items-wrapper'
            ).first

            # Extract rate data from the wrapper
            table_data = await self.extract_rate_data(rates_wrapper, cabin_number, timestamp)

            # Select the optimal rate based on strategy
            selected_rate, discount_text = await self.select_rate(
                table_data, cabin_number, self.rate_selection_strategy
            )

            # Wait for page to load after rate selection
            await self.wait_for_page_load(timeout=30)

            # Click continue button to proceed to cabin selection
            await self.click_continue_button(selected_rate, timestamp)

            # Wait for cabin selection page to load
            await self.wait_for_page_load(timeout=30)

            # Find available cabins and select one
            cabin_data = await self.find_cabins(cabin_number, timestamp)
            selected_cabin = await self.select_cabin(cabin_data, cabin_number)

            # Handle potential errors with cabin selection (like unavailable cabins)
            selected_cabin, cabin_changed = await self.handle_cabin_error(
                cabin_data, selected_cabin, timestamp, cabin_number
            )

            # Wait for pricing details to load
            await self.wait_for_page_load(timeout=30)

            # Extract pricing data for the selected cabin
            pricing_data = await self.extract_pricing_data(timestamp, cabin_number)

            # Return a comprehensive result with all booking details
            return {
                'status': 'success', 'cabin_number': cabin_number, 'category_type':
                selected_category_type, 'category_code': selected_category_code,
                'selected_rate': selected_rate['rate'], 'selected_cabin':
                selected_cabin['cabin_number'], 'pricing_data': pricing_data,
                'discount_text': discount_text
            }

        except TimeoutError as e:
            # Specific handling for timeout errors (when elements don't appear in time)
            logger.error(f"Timeout error: {str(e)}")
            raise

        except Exception as e:
            # General error handling for other exceptions
            logger.error(f"Error in cabin selection process: {str(e)}")
            raise
