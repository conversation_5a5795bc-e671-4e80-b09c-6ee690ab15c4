{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/GroupedStackFrames.tsx"], "names": ["GroupedStackFrames", "FrameworkGroup", "framework", "stackFrames", "all", "details", "data-nextjs-collapsed-call-stack-details", "summary", "tabIndex", "svg", "data-nextjs-call-stack-chevron-icon", "fill", "height", "width", "shapeRendering", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "viewBox", "path", "d", "FrameworkIcon", "map", "frame", "index", "CallStackFrame", "key", "groupedStackFrames", "stackFramesGroup", "groupIndex", "frameIndex"], "mappings": ";;;;+BA8CgBA;;;eAAAA;;;;gEA9CE;gCAEa;+BACD;AAE9B,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,SAAS,EACTC,WAAW,EACXC,GAAG,EAKJ,GARuB;IAStB,qBACE,0EACE,6BAACC;QAAQC,4CAAAA;qBACP,6BAACC;QACCC,UAAU;qBAEV,6BAACC;QACCC,uCAAAA;QACAC,MAAK;QACLC,QAAO;QACPC,OAAM;QACNC,gBAAe;QACfC,QAAO;QACPC,eAAc;QACdC,gBAAe;QACfC,aAAY;QACZC,SAAQ;qBAER,6BAACC;QAAKC,GAAE;uBAEV,6BAACC,4BAAa;QAACpB,WAAWA;QACzBA,cAAc,UAAU,UAAU,YAGpCC,YAAYoB,GAAG,CAAC,CAACC,OAAOC,sBACvB,6BAACC,8BAAc;YAACC,KAAK,AAAC,gBAAaF,QAAM,MAAGrB;YAAOoB,OAAOA;;AAKpE;AAEO,SAASxB,mBAAmB,KAMlC;IANkC,IAAA,EACjC4B,kBAAkB,EAClBxB,GAAG,EAIJ,GANkC;IAOjC,qBACE,4DACGwB,mBAAmBL,GAAG,CAAC,CAACM,kBAAkBC;QACzC,oCAAoC;QACpC,IAAID,iBAAiB3B,SAAS,EAAE;YAC9B,qBACE,6BAACD;gBACC0B,KAAK,AAAC,gCAA6BG,aAAW,MAAG1B;gBACjDF,WAAW2B,iBAAiB3B,SAAS;gBACrCC,aAAa0B,iBAAiB1B,WAAW;gBACzCC,KAAKA;;QAGX;QAEA,OACE,2CAA2C;QAC3CyB,iBAAiB1B,WAAW,CAACoB,GAAG,CAAC,CAACC,OAAOO,2BACvC,6BAACL,8BAAc;gBACbC,KAAK,AAAC,gBAAaG,aAAW,MAAGC,aAAW,MAAG3B;gBAC/CoB,OAAOA;;IAIf;AAGN"}