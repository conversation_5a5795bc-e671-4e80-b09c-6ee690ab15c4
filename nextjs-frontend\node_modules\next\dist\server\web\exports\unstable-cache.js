// This file is for modularized imports for next/server to get fully-treeshaking.
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _unstablecache.unstable_cache;
    }
});
const _unstablecache = require("../spec-extension/unstable-cache");

//# sourceMappingURL=unstable-cache.js.map