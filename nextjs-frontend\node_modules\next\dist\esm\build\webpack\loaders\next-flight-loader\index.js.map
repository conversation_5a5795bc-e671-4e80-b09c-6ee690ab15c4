{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/index.ts"], "names": ["RSC_MOD_REF_PROXY_ALIAS", "RSC_MODULE_TYPES", "warnOnce", "getRSCModuleInformation", "getModuleBuildInfo", "noopHeadPath", "require", "resolve", "MODULE_PROXY_PATH", "transformSource", "source", "sourceMap", "buildInfo", "Error", "_module", "rsc", "type", "client", "sourceType", "parser", "detectedClientEntryType", "clientEntryType", "clientRefs", "assumedSourceType", "length", "includes", "callback", "esmSource", "resourcePath", "cnt", "ref", "replace"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,4BAA2B;AACnE,SAASC,gBAAgB,QAAQ,mCAAkC;AACnE,SAASC,QAAQ,QAAQ,yCAAwC;AACjE,SAASC,uBAAuB,QAAQ,yCAAwC;AAChF,SAASC,kBAAkB,QAAQ,2BAA0B;AAE7D,MAAMC,eAAeC,QAAQC,OAAO,CAAC;AACrC,gEAAgE;AAChE,MAAMC,oBACJ;AAEF,eAAe,SAASC,gBAEtBC,MAAc,EACdC,SAAc;QAaVC,gBAiEAA;IA5EJ,8BAA8B;IAC9B,IAAI,OAAOF,WAAW,UAAU;QAC9B,MAAM,IAAIG,MAAM;IAClB;IAEA,gDAAgD;IAChD,mEAAmE;IACnE,MAAMD,YAAYR,mBAAmB,IAAI,CAACU,OAAO;IACjDF,UAAUG,GAAG,GAAGZ,wBAAwBO,QAAQ;IAEhD,qBAAqB;IACrB,IAAIE,EAAAA,iBAAAA,UAAUG,GAAG,qBAAbH,eAAeI,IAAI,MAAKf,iBAAiBgB,MAAM,EAAE;YAChC,sBAAA;QAAnB,MAAMC,cAAa,gBAAA,IAAI,CAACJ,OAAO,sBAAZ,uBAAA,cAAcK,MAAM,qBAApB,qBAAsBD,UAAU;QACnD,MAAME,0BAA0BR,UAAUG,GAAG,CAACM,eAAe;QAC7D,MAAMC,aAAaV,UAAUG,GAAG,CAACO,UAAU;QAE3C,4EAA4E;QAC5E,6EAA6E;QAC7E,4DAA4D;QAC5D,IAAIC,oBAAoBL;QACxB,IAAIK,sBAAsB,UAAUH,4BAA4B,QAAQ;YACtE,IACEE,WAAWE,MAAM,KAAK,KACrBF,WAAWE,MAAM,KAAK,KAAKF,UAAU,CAAC,EAAE,KAAK,IAC9C;gBACA,uEAAuE;gBACvE,yEAAyE;gBACzE,oBAAoB;gBACpBC,oBAAoB;YACtB,OAAO,IAAI,CAACD,WAAWG,QAAQ,CAAC,MAAM;gBACpC,2CAA2C;gBAC3CF,oBAAoB;YACtB;QACF;QAEA,IAAIA,sBAAsB,UAAU;YAClC,IAAID,WAAWG,QAAQ,CAAC,MAAM;gBAC5B,IAAI,CAACC,QAAQ,CACX,IAAIb,MACF,CAAC,oGAAoG,CAAC;gBAG1G;YACF;YAEA,IAAIc,YAAY,CAAC;6BACM,EAAEnB,kBAAkB;sCACX,EAAE,IAAI,CAACoB,YAAY,CAAC;;;;;;;;AAQ1D,CAAC;YACK,IAAIC,MAAM;YACV,KAAK,MAAMC,OAAOR,WAAY;gBAC5B,IAAIQ,QAAQ,IAAI;oBACdH,aAAa,CAAC,wCAAwC,EAAE,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC;gBAClF,OAAO,IAAIE,QAAQ,WAAW;oBAC5BH,aAAa,CAAC;;2BAEG,CAAC;gBACpB,OAAO;oBACLA,aAAa,CAAC;OACjB,EAAEE,IAAI,2BAA2B,EAAE,IAAI,CAACD,YAAY,CAAC,CAAC,EAAEE,IAAI;UACzD,EAAED,MAAM,IAAI,EAAEC,IAAI,GAAG,CAAC;gBACxB;YACF;YAEA,IAAI,CAACJ,QAAQ,CAAC,MAAMC,WAAWhB;YAC/B;QACF;IACF;IAEA,IAAIC,EAAAA,kBAAAA,UAAUG,GAAG,qBAAbH,gBAAeI,IAAI,MAAKf,iBAAiBgB,MAAM,EAAE;QACnD,IAAIZ,iBAAiB,IAAI,CAACuB,YAAY,EAAE;YACtC1B,SACE,CAAC,0OAA0O,CAAC;QAEhP;IACF;IAEA,IAAI,CAACwB,QAAQ,CACX,MACAhB,OAAOqB,OAAO,CAAC/B,yBAAyBQ,oBACxCG;AAEJ"}