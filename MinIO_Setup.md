I'll help you set up MinIO on your VM and then configure your application to store screenshots and videos there. Let's do this step by step.

## 🚀 **MinIO Setup on Your VM (***********)**

### **Step 1: Install MinIO Server**

```bash
# Connect to your VM
ssh user_name@***********

# Create MinIO user (for security)
sudo useradd -r -s /sbin/nologin -d /opt/minio minio

# Create directories
sudo mkdir -p /opt/minio/bin
sudo mkdir -p /opt/minio/data
sudo mkdir -p /var/log/minio

# Download MinIO binary
cd /tmp
wget https://dl.min.io/server/minio/release/linux-amd64/minio
chmod +x minio
sudo mv minio /opt/minio/bin/

# Set ownership
sudo chown -R minio:minio /opt/minio
sudo chown -R minio:minio /var/log/minio
```

### **Step 2: Create MinIO Configuration**

Create the MinIO configuration file:

```bash
# Create environment file
sudo nano /etc/default/minio
```

**File: `/etc/default/minio`**
```bash
# MinIO local volumes
MINIO_VOLUMES="/opt/minio/data"

# MinIO CLI options
MINIO_OPTS="--console-address :9001"

# MinIO root credentials
MINIO_ROOT_USER=oceanmind_admin
MINIO_ROOT_PASSWORD=OceanMind@MinIO2024

# MinIO server address
MINIO_SERVER_URL=http://***********:9000

# Log file
MINIO_LOG_FILE=/var/log/minio/minio.log
```

### **Step 3: Create SystemD Service**

```bash
sudo nano /etc/systemd/system/minio.service
```

**File: `/etc/systemd/system/minio.service`**
```ini
[Unit]
Description=MinIO Object Storage Server
Documentation=https://docs.min.io
Wants=network-online.target
After=network-online.target
AssertFileIsExecutable=/opt/minio/bin/minio

[Service]
WorkingDirectory=/opt/minio
User=minio
Group=minio
EnvironmentFile=/etc/default/minio
ExecStartPre=/bin/bash -c "if [ -z \"${MINIO_VOLUMES}\" ]; then echo \"Variable MINIO_VOLUMES not set in /etc/default/minio\"; exit 1; fi"
ExecStart=/opt/minio/bin/minio server $MINIO_OPTS $MINIO_VOLUMES
Restart=always
LimitNOFILE=65536
TasksMax=infinity
TimeoutStopSec=infinity
SendSIGKILL=no
StandardOutput=append:/var/log/minio/minio.log
StandardError=append:/var/log/minio/minio.log

[Install]
WantedBy=multi-user.target
```

### **Step 4: Start MinIO Service**

```bash
# Reload systemd and start MinIO
sudo systemctl daemon-reload
sudo systemctl enable minio
sudo systemctl start minio

# Check status
sudo systemctl status minio

# Check logs
sudo tail -f /var/log/minio/minio.log
```

### **Step 5: Configure Firewall (if needed)**

```bash
# Open MinIO ports
sudo ufw allow 9000/tcp  # MinIO API
sudo ufw allow 9001/tcp  # MinIO Console

# Or if using firewalld
sudo firewall-cmd --permanent --add-port=9000/tcp
sudo firewall-cmd --permanent --add-port=9001/tcp
sudo firewall-cmd --reload
```

### **Step 6: Install MinIO Client (mc)**

```bash
# Download MinIO client
cd /tmp
wget https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc
sudo mv mc /usr/local/bin/

# Configure mc alias
mc alias set local http://***********:9000 oceanmind_admin OceanMind@MinIO2024

# Test connection
mc admin info local
```

### **Step 7: Create Buckets for Your Application**

```bash
# Create buckets for different media types
mc mb local/oceanmind-screenshots
mc mb local/oceanmind-videos
mc mb local/oceanmind-backups

# Set bucket policies (public read for screenshots, private for videos)
mc anonymous set download local/oceanmind-screenshots
mc anonymous set none local/oceanmind-videos

# List buckets
mc ls local
```

## 🔧 **MinIO Configuration Files for Your Application**

Create these files in your OceanMind project:

### **File: `config/minio_config.py`**
### **File: `services/media_service.py`**
### **File: `requirements.txt` (Add MinIO dependency)**
Read file: requirements.txt
## 🔧 **Environment Configuration**

### **File: `.env` (Add MinIO settings)**

Add these lines to your `.env` file:

```bash
# MinIO Configuration
MINIO_ENDPOINT=***********:9000
MINIO_ACCESS_KEY=oceanmind_admin
MINIO_SECRET_KEY=OceanMind@MinIO2024
MINIO_SECURE=false
MINIO_SCREENSHOTS_BUCKET=oceanmind-screenshots
MINIO_VIDEOS_BUCKET=oceanmind-videos
MINIO_BACKUPS_BUCKET=oceanmind-backups

# Storage Configuration
USE_MINIO_STORAGE=true
STORAGE_FALLBACK_TO_DB=true
```

## 🧪 **MinIO Testing Script**

### **File: `scripts/test_minio.py`**
## 📋 **Installation Commands Summary**

Run these commands on your VM (***********):

```bash
# 1. Install MinIO Server
sudo useradd -r -s /sbin/nologin -d /opt/minio minio
sudo mkdir -p /opt/minio/{bin,data} /var/log/minio
cd /tmp && wget https://dl.min.io/server/minio/release/linux-amd64/minio
chmod +x minio && sudo mv minio /opt/minio/bin/
sudo chown -R minio:minio /opt/minio /var/log/minio

# 2. Create configuration files (use the content provided above)
sudo nano /etc/default/minio
sudo nano /etc/systemd/system/minio.service

# 3. Start MinIO service
sudo systemctl daemon-reload
sudo systemctl enable minio
sudo systemctl start minio
sudo systemctl status minio

# 4. Install MinIO Client
wget https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc && sudo mv mc /usr/local/bin/
mc alias set local http://***********:9000 oceanmind_admin OceanMind@MinIO2024

# 5. Create buckets
mc mb local/oceanmind-screenshots
mc mb local/oceanmind-videos  
mc mb local/oceanmind-backups
mc anonymous set download local/oceanmind-screenshots

# 6. Test connection
mc admin info local
```

## 🔧 **Application Setup**

In your OceanMind project directory:

```bash
# 1. Install Python dependencies
pip install minio>=7.2.0 psycopg2-binary

# 2. Create config directory
mkdir -p config

# 3. Update .env file with MinIO settings (provided above)

# 4. Test MinIO integration
python scripts/test_minio.py
```

## 🌐 **Access MinIO Console**

After setup, you can access:

- **MinIO Console**: http://***********:9001
- **MinIO API**: http://***********:9000

Login credentials:
- Username: `oceanmind_admin`
- Password: `OceanMind@MinIO2024`

## 🔄 **Next Steps**

Once MinIO is running and tested:

1. **Verify MinIO is working**: Run the test script
2. **Update database schema**: Add MinIO metadata columns
3. **Modify screenshot/video managers**: Integrate MinIO uploads
4. **Update API endpoints**: Return MinIO URLs instead of binary data
5. **Test with real booking**: Verify end-to-end flow

Let me know once you have MinIO running, and we'll proceed with integrating it into your screenshot and video storage workflows!