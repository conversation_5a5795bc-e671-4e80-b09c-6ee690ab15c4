"""
User authentication and role-based access control functionality.
"""
import json
import datetime
import hashlib
import os
import uuid
from typing import Dict, Any, List, Optional, Union, Tuple
import logging
from db_service import get_db_connection, db_connection

# Configure logging
logger = logging.getLogger("db.user_auth")

def hash_password(password: str) -> str:
    """Hash a password for storing."""
    salt = os.urandom(32)
    key = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt,
        100000
    )
    return salt.hex() + ':' + key.hex()

def verify_password(stored_password: str, provided_password: str) -> bool:
    """Verify a stored password against a provided password."""
    salt_hex, key_hex = stored_password.split(':')
    salt = bytes.fromhex(salt_hex)
    stored_key = bytes.fromhex(key_hex)
    new_key = hashlib.pbkdf2_hmac(
        'sha256',
        provided_password.encode('utf-8'),
        salt,
        100000
    )
    return stored_key == new_key

async def create_user(username: str, password: str, email: str, full_name: str, role: str, 
                agency: str, portal_access: List[str], status: str = "pending", provider_access: List[str] = None) -> bool:
    """
    Create a new user with specified role and permissions.
    
    Args:
        username: Unique username
        password: Password (will be hashed)
        email: User's email
        full_name: User's full name
        role: Main role (admin, sub_admin, user)
        agency: Agency (STUDIO, CNM)
        portal_access: List of portal IDs the user has access to
        status: User status (pending, approved, rejected)
        provider_access: List of provider names the user has access to
        
    Returns:
        bool: True if user was created successfully, False otherwise
    """
    try:
        async with db_connection() as conn:
        
            # Check if username already exists
            existing_user = await conn.fetchrow("SELECT username FROM users WHERE username = $1", username)
            if existing_user:
                logger.warning(f"Username {username} already exists")
                return False

            # Hash the password
            hashed_password = hash_password(password)

            # Convert portal_access list to JSON
            portal_access_json = json.dumps(portal_access)

            # Convert provider_access list to JSON (default to empty array)
            if provider_access is None:
                provider_access = []
            provider_access_json = json.dumps(provider_access)

            # Generate UUID for user_id
            user_id = str(uuid.uuid4())

            # Get current timestamp
            created_at = datetime.datetime.now().isoformat()

            # Insert new user
            await conn.execute("""
            INSERT INTO users 
            (user_id, username, password, email, full_name, role, agency, portal_access, provider_access, created_at, last_login, status)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NULL, $11)
            """, user_id, username, hashed_password, email, full_name, role, agency, portal_access_json, provider_access_json, created_at, status)

            logger.info(f"User {username} created successfully with status {status}")
            return True
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        return False

async def authenticate_user(username: str, password: str) -> Optional[Dict[str, Any]]:
    """
    Authenticate a user with username and password.
    
    Args:
        username: Username
        password: Password (plaintext)
        
    Returns:
        Optional[Dict]: User data if authentication successful, None otherwise
    """
    try:
        async with db_connection() as conn:
        
            user = await conn.fetchrow("""
            SELECT user_id, username, password, email, full_name, role, agency, portal_access, provider_access, created_at, last_login, status
            FROM users
            WHERE username = $1
            """, username)

            if not user:
                logger.warning(f"No user found with username {username}")
                return None

            # Convert to dict
            user_dict = dict(user)

            # Verify password
            if not verify_password(user_dict['password'], password):
                logger.warning(f"Invalid password for user {username}")
                return None

            # Update last login time
            last_login = datetime.datetime.now().isoformat()
            await conn.execute(
                "UPDATE users SET last_login = $1 WHERE user_id = $2",
                last_login, user_dict['user_id']
            )

            # Parse portal_access JSON string
            user_dict['portal_access'] = json.loads(user_dict['portal_access'])

            # Parse provider_access JSON string if available
            if user_dict.get('provider_access'):
                user_dict['provider_access'] = json.loads(user_dict['provider_access'])
            else:
                user_dict['provider_access'] = []

            # Remove password hash before returning
            user_dict.pop('password', None)

            return user_dict
    except Exception as e:
        logger.error(f"Error authenticating user: {e}")
        return None

async def get_user(user_id: str) -> Optional[Dict[str, Any]]:
    """Get user by ID."""
    try:
        async with db_connection() as conn:
        
            user = await conn.fetchrow("""
            SELECT user_id, username, email, full_name, role, agency, portal_access, provider_access, created_at, last_login, status
            FROM users
            WHERE user_id = $1
            """, user_id)

            if not user:
                return None

            # Convert to dict
            user_dict = dict(user)

            # Parse portal_access JSON string
            user_dict['portal_access'] = json.loads(user_dict['portal_access'])
        
            # Parse provider_access JSON string if available
            if user_dict.get('provider_access'):
                user_dict['provider_access'] = json.loads(user_dict['provider_access'])
            else:
                user_dict['provider_access'] = []
        
            return user_dict
    except Exception as e:
        logger.error(f"Error getting user: {e}")
        return None

async def update_user(user_id: str, data: Dict[str, Any]) -> bool:
    """
    Update user information.
    
    Args:
        user_id: User ID
        data: Dictionary of fields to update
        
    Returns:
        bool: True if update was successful, False otherwise
    """
    try:
        async with db_connection() as conn:
        
            # Start building the query
            query_parts = []
            params = []
            param_index = 1

            # Handle special fields
            if 'password' in data:
                query_parts.append(f"password = ${param_index}")
                params.append(hash_password(data['password']))
                param_index += 1
                data.pop('password')

            if 'portal_access' in data:
                query_parts.append(f"portal_access = ${param_index}")
                params.append(json.dumps(data['portal_access']))
                param_index += 1
                data.pop('portal_access')

            if 'provider_access' in data:
                query_parts.append(f"provider_access = ${param_index}")
                params.append(json.dumps(data['provider_access']))
                param_index += 1
                data.pop('provider_access')

            # Add all other fields
            for key, value in data.items():
                # Skip user_id and created_at (they shouldn't be updated)
                if key in ['user_id', 'created_at']:
                    continue

                query_parts.append(f"{key} = ${param_index}")
                params.append(value)
                param_index += 1

            if not query_parts:
                logger.warning("No fields to update")
                return False

            # Build the final query
            query = f"UPDATE users SET {', '.join(query_parts)} WHERE user_id = ${param_index}"
            params.append(user_id)

            # Execute the query
            result = await conn.execute(query, *params)

            # Check if any rows were affected (asyncpg doesn't have rowcount)
            if result == "UPDATE 0":
                logger.warning(f"No user found with ID {user_id}")
                return False

            logger.info(f"User {user_id} updated successfully")
            return True
    except Exception as e:
        logger.error(f"Error updating user: {e}")
        return False

async def delete_user(user_id: str) -> bool:
    """Delete a user."""
    try:
        async with db_connection() as conn:
        
            result = await conn.execute("DELETE FROM users WHERE user_id = $1", user_id)

            if result == "DELETE 0":
                logger.warning(f"No user found with ID {user_id}")
                return False

            logger.info(f"User {user_id} deleted successfully")
            return True
    except Exception as e:
        logger.error(f"Error deleting user: {e}")
        return False

async def list_users(role: Optional[str] = None, agency: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    List all users, optionally filtered by role, agency, and/or status.
    
    Args:
        role: Filter by role (admin, sub_admin, user)
        agency: Filter by agency (STUDIO, CNM)
        status: Filter by status (pending, approved, rejected)
        
    Returns:
        List[Dict]: List of user dictionaries (without password)
    """
    try:
        async with db_connection() as conn:
        
            query = """
            SELECT user_id, username, email, full_name, role, agency, portal_access, provider_access, created_at, last_login, status
            FROM users
            """
            params = []
            param_index = 1

            # Add filters
            conditions = []
            if role:
                conditions.append(f"role = ${param_index}")
                params.append(role)
                param_index += 1
            if agency:
                conditions.append(f"agency = ${param_index}")
                params.append(agency)
                param_index += 1
            if status:
                conditions.append(f"status = ${param_index}")
                params.append(status)
                param_index += 1

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            users = await conn.fetch(query, *params)

            # Convert to list of dicts
            result = []
            for user in users:
                user_dict = dict(user)
                # Parse portal_access JSON string
                user_dict['portal_access'] = json.loads(user_dict['portal_access'])

                # Parse provider_access JSON string if available
                if user_dict.get('provider_access'):
                    user_dict['provider_access'] = json.loads(user_dict['provider_access'])
                else:
                    user_dict['provider_access'] = []

                result.append(user_dict)

            return result
    except Exception as e:
        logger.error(f"Error listing users: {e}")
        return []

async def update_user_role(user_id: str, role: str) -> bool:
    """
    Update a user's role.
    
    Args:
        user_id: User ID
        role: New role (admin, sub_admin, user)
        
    Returns:
        bool: True if update was successful, False otherwise
    """
    try:
        async with db_connection() as conn:
        
            result = await conn.execute("UPDATE users SET role = $1 WHERE user_id = $2", role, user_id)

            if result == "UPDATE 0":
                logger.warning(f"No user found with ID {user_id}")
                return False

            logger.info(f"Role updated for user {user_id}")
            return True
    except Exception as e:
        logger.error(f"Error updating user role: {e}")
        return False

async def get_provider_list(agency: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get list of all providers, optionally filtered by agency.
    
    Args:
        agency: Filter by agency (STUDIO, CNM)
        
    Returns:
        List[Dict]: List of provider dictionaries
    """
    try:
        async with db_connection() as conn:
        
            query = "SELECT provider_id, name, agency, description, status FROM providers"
            params = []

            if agency:
                query += " WHERE agency = $1"
                params.append(agency)

            query += " ORDER BY agency, name"

            providers = await conn.fetch(query, *params)

            # Convert to list of dicts
            result = [dict(provider) for provider in providers]

            return result
    except Exception as e:
        logger.error(f"Error getting provider list: {e}")
        return []

async def get_user_accessible_providers(user_id: str) -> List[Dict[str, Any]]:
    """
    Get list of providers that a specific user has access to.
    This function performs a JOIN between users and providers tables to ensure
    only providers the user is authorized to access are returned.
    
    Args:
        user_id: User ID to get accessible providers for
        
    Returns:
        List[Dict]: List of provider dictionaries the user can access
    """
    try:
        async with db_connection() as conn:
        
            # Get user data first
            user_data = await conn.fetchrow("""
            SELECT provider_access, role, agency
            FROM users 
            WHERE user_id = $1 AND status = 'approved'
            """, user_id)

            if not user_data:
                logger.warning(f"User {user_id} not found or not approved")
                return []

            user_dict = dict(user_data)

            # Parse provider_access JSON
            if user_dict.get('provider_access'):
                provider_access = json.loads(user_dict['provider_access'])
            else:
                provider_access = []

            # If user has no provider access, return empty list
            if not provider_access:
                logger.info(f"User {user_id} has no provider access configured")
                return []
        
            # Build query to get providers the user has access to
            # Use ANY() to check if provider name is in the user's provider_access array
            query = """
            SELECT p.provider_id, p.name, p.agency, p.description, p.status
            FROM providers p
            WHERE p.name = ANY($1::text[])
            ORDER BY p.agency, p.name
            """

            providers = await conn.fetch(query, provider_access)

            # Convert to list of dicts
            result = [dict(provider) for provider in providers]

            logger.info(f"User {user_id} has access to {len(result)} providers: {[p['name'] for p in result]}")

            return result

    except Exception as e:
        logger.error(f"Error getting user accessible providers for user {user_id}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return []

async def add_provider(name: str, agency: str, description: str, status: str = "active") -> Optional[int]:
    """
    Add a new provider.
    
    Args:
        name: Provider name
        agency: Agency (STUDIO, CNM)
        description: Provider description
        status: Provider status (active, coming_soon, maintenance)
        
    Returns:
        Optional[int]: Provider ID if created successfully, None otherwise
    """
    try:
        async with db_connection() as conn:
        
            logger.info(f"Attempting to create provider: name={name}, agency={agency}, description={description}, status={status}")
            result = await conn.fetchrow(
                """
                INSERT INTO providers (provider_id, name, agency, description, status)
                VALUES (nextval('providers_provider_id_seq'), $1, $2, $3, $4)
                RETURNING provider_id
                """,
                name, agency, description, status
            )

            if result:
                new_provider_id = result['provider_id']
                logger.info(f"Provider {name} created successfully with ID: {new_provider_id}")
                return new_provider_id
            else:
                logger.error("No result returned from INSERT statement")
                return None

    except Exception as e:
        logger.error(f"Error adding provider: {e}")
        return None


async def update_provider(provider_id: str, data: Dict[str, Any]) -> bool:
    """
    Update provider information.
    
    Args:
        provider_id: Provider ID
        data: Dictionary of fields to update
        
    Returns:
        bool: True if update was successful, False otherwise
    """
    try:
        async with db_connection() as conn:
        
            # Build SET clauses excluding provider_id
            query_parts = []
            params = []
            param_index = 1

            for key, value in data.items():
                if key == 'provider_id':
                    continue
                query_parts.append(f"{key} = ${param_index}")
                params.append(value)
                param_index += 1

            if not query_parts:
                logger.warning("No fields to update")
                return False

            query = f"UPDATE providers SET {', '.join(query_parts)} WHERE provider_id = ${param_index}"
            params.append(provider_id)

            result = await conn.execute(query, *params)

            if result == "UPDATE 0":
                logger.warning(f"No provider found with ID {provider_id}")
                return False

            logger.info(f"Provider {provider_id} updated successfully")
            return True
    except Exception as e:
        logger.error(f"Error updating provider: {e}")
        return False

async def delete_provider(provider_id: str) -> bool:
    """
    Delete a provider.
    
    Args:
        provider_id: Provider ID
    
    Returns:
        bool: True if deletion was successful, False otherwise
    """
    try:
        async with db_connection() as conn:
        
            result = await conn.execute("DELETE FROM providers WHERE provider_id = $1", provider_id)

            if result == "DELETE 0":
                logger.warning(f"No provider found with ID {provider_id}")
                return False

            logger.info(f"Provider {provider_id} deleted successfully")
            return True
    except Exception as e:
        logger.error(f"Error deleting provider: {e}")
        return False

async def get_agency_list() -> List[str]:
    """
    Get list of distinct agencies from providers table only.
    """
    try:
        async with db_connection() as conn:
        
            # Get distinct agencies from providers table
            rows = await conn.fetch("""
            SELECT DISTINCT agency
              FROM providers
             ORDER BY agency
            """)

            # Convert to list of strings
            result = [row['agency'] for row in rows]

            return result
    except Exception as e:
        logger.error(f"Error getting agency list from providers: {e}")
        return []  # Return empty list on error

async def get_providers_by_agencies(agencies: List[str]) -> List[Dict[str, Any]]:
    try:
        if not agencies:
            return []
        async with db_connection() as conn:
            query = """
            SELECT provider_id, name, agency, description, status
            FROM providers 
            WHERE agency = ANY($1::text[])
            ORDER BY agency, name
            """
            providers = await conn.fetch(query, agencies)
            result = [dict(provider) for provider in providers]
            return result
    except Exception as e:
        logger.error(f"Error getting providers by agencies: {e}")
        return []

async def update_provider_status(provider_id: int, status: str) -> bool:
    """
    Update provider status in the database.
    """
    try:
        valid = ['active', 'coming_soon', 'maintenance', 'deprecated']
        if status not in valid:
            logger.error(f"Invalid status '{status}'")
            return False
        async with db_connection() as conn:
            result = await conn.execute(
                "UPDATE providers SET status = $1 WHERE provider_id = $2",
                status, provider_id
            )
            if result == "UPDATE 0":
                return False
            return True
    except Exception as e:
        logger.error(f"Error updating provider status: {e}")
        return False

# def create_default_admin():
#     """Create a default admin user if no users exist."""
#     try:
#         conn = get_db_connection()
#         cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
#         # Check if any users exist
#         cursor.execute("SELECT COUNT(*) FROM users")
#         count = cursor.fetchone()[0]
        
#         if count == 0:
#             logger.info("Creating default admin user")
#             # Create a default admin user
#             create_user(
#                 username="admin",
#                 password="admin123",  # This should be changed immediately
#                 email="<EMAIL>",
#                 full_name="Admin User",
#                 role="admin",
#                 agency="STUDIO",  # Default agency
#                 portal_access=[],  # Admin has access to all portals
#                 status="approved",  # Admin is automatically approved
#                 provider_access=["Studio.Sales.CabinCloseOut", "Studio.Res.CabinCloseOut", "NCL", "Cruising Power", "OneSource"]  # All providers
#             )
#             logger.warning("Default admin user created. Please change the password immediately.")
#         else:
#             # Ensure admin user exists and is approved
#             ensure_admin_account()
        
#         conn.close()
#     except Exception as e:
#         logger.error(f"Error creating default admin: {e}")

# def ensure_admin_account():
#     """Ensure that admin account exists and is properly set up."""
#     try:
#         conn = get_db_connection()
#         cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
#         # Check if admin user exists
#         cursor.execute("SELECT user_id, status FROM users WHERE username = 'admin'")
#         admin = cursor.fetchone()
        
#         if admin:
#             # If admin exists but status is not approved, update it
#             if admin['status'] != 'approved':
#                 logger.info("Updating admin status to 'approved'")
#                 cursor.execute(
#                     "UPDATE users SET status = 'approved' WHERE user_id = %s",
#                     (admin['user_id'],)
#                 )
#                 conn.commit()
#         else:
#             # If no admin user exists but other users do, create one
#             logger.info("No admin user found, creating default admin")
#             create_user(
#                 username="admin",
#                 password="admin123",  # This should be changed immediately
#                 email="<EMAIL>",
#                 full_name="Admin User",
#                 role="admin",
#                 agency="STUDIO",
#                 portal_access=[],
#                 status="approved",
#                 provider_access=["Studio.Sales.CabinCloseOut", "Studio.Res.CabinCloseOut", "NCL", "Cruising Power", "OneSource"]  # All providers
#             )
#             logger.warning("Default admin user created. Please change the password immediately.")
        
#         conn.close()
#     except Exception as e:
#         logger.error(f"Error ensuring admin account: {e}") 