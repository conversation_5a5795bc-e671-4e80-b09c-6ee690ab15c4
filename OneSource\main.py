"""
OneSource Main Module

This module provides the main functionality for OneSource cruise booking automation.
It orchestrates the entire booking flow including login, search, cabin selection,
and result processing with support for multiple cabins processed asynchronously.

The module integrates with the broader booking system and provides endpoints
for the "Start Booking" button functionality.
"""

import os
import sys
import asyncio
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from loguru import logger

# Add parent directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from OneSource.login import OneSourceLogin, login_to_onesource
from OneSource.extraction import OneSourceExtractor
from OneSource.booking import OneSourceBooking, process_booking
from OneSource.form_filling import OneSourceFormFilling
from OneSource.category_selector import OneSourceCategorySelector
from OneSource.price_details import OneSourcePriceDetails
from Core.browser_setup import AsyncBrowserSetup, browser_manager
from Core.ui_logger import ui_log
from Core.database_helper import save_onesource_results


class CabinProcessor:
    """
    Class to process individual cabin bookings for OneSource
    """
    
    def __init__(self, page, context, cruise_details, cabin_info, cabin_index=0, 
                 session_id=None, request_id=None, video_auditing=False, config=None):
        """
        Initialize the cabin processor
        
        Args:
            page: Playwright Page instance
            context: BrowserContext instance
            cruise_details: Dictionary containing cruise details
            cabin_info: Dictionary containing cabin and passenger information
            cabin_index: Index of the cabin for logging
            session_id: Session ID for tracking
            request_id: Request ID for tracking
            video_auditing: Whether video recording is enabled
            config: Configuration dictionary
        """
        self.page = page
        self.context = context
        self.cruise_details = cruise_details
        self.cabin_info = cabin_info
        self.cabin_index = cabin_index
        self.session_id = session_id
        self.request_id = request_id
        self.video_auditing = video_auditing
        self.config = config or {}
        
    async def process(self):
        """
        Process a single cabin booking
        
        Returns:
            dict: Result of the cabin booking process
        """
        self.cabin_result = {
            "cabin_number": self.cabin_index + 1,
            "cabin_type": self.cabin_info.get('cabin_type', 'Unknown'),
            "passengers": self.cabin_info.get('passengers', 0),
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "request_id": self.request_id,
            "provider": "OneSource",
            "session_id": self.session_id,
            "error": None
        }
        
        try:
            logger.info(f"Processing cabin {self.cabin_index + 1} for OneSource")
            ui_log(f"Processing cabin {self.cabin_index + 1}", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_index + 1,
                  step="processing_cabin", 
                  module="OneSource")
            
            # Execute OneSource booking flow (login → create booking)
            booking_result = await process_booking(
                page=self.page,
                cruise_details=self.cruise_details,
                session_id=self.session_id,
                cabin_id=self.cabin_index + 1
            )
            
            if booking_result.get("success", False):
                # Update page object if booking returned a new page (due to tab switching)
                if "page" in booking_result:
                    self.page = booking_result["page"]
                # Now fill the booking form
                form_filling_result = await self._fill_booking_form()
                
                if form_filling_result:
                    # Both booking and form filling completed successfully
                    self.cabin_result.update({
                        "success": True,
                        "booking_reference": f"OS{datetime.now().strftime('%Y%m%d')}{self.cabin_index + 1:03d}",
                        "booking_details": {
                            "cruise_line": self.cruise_details.get('cruise_line', 'Unknown'),
                            "ship_name": self.cruise_details.get('ship_name', 'Unknown'),
                            "sail_date": self.cruise_details.get('date', 'Unknown'),
                            "nights": self.cruise_details.get('nights', 0),
                            "passengers": self.cabin_info.get('passengers', 0)
                        },
                        "booking_flow_result": booking_result,
                        "form_filling_result": form_filling_result
                    })
                else:
                    # Booking succeeded but form filling failed
                    self.cabin_result.update({
                        "success": False,
                        "error": "Form filling failed after successful booking",
                        "booking_flow_result": booking_result
                    })
                    return self.cabin_result
            else:
                # Booking flow failed
                self.cabin_result.update({
                    "success": False,
                    "error": booking_result.get("error", "Booking flow failed")
                })
                return self.cabin_result
            
            logger.success(f"Cabin {self.cabin_index + 1} processed successfully")
            ui_log(f"Cabin {self.cabin_index + 1} booked successfully", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_index + 1,
                  step="cabin_booked", 
                  module="OneSource")
            
        except Exception as e:
            logger.error(f"Error processing cabin {self.cabin_index + 1}: {e}")
            self.cabin_result.update({
                "success": False,
                "error": str(e)
            })
            ui_log(f"Error processing cabin {self.cabin_index + 1}: {e}", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_index + 1,
                  step="cabin_error", 
                  module="OneSource")
        
        return self.cabin_result
    
    async def _fill_booking_form(self):
        """
        Fill the booking form with extracted cruise details
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Starting form filling for cabin {self.cabin_index + 1}")
            ui_log("Filling booking form", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_index + 1,
                  step="filling_form", 
                  module="OneSource")
            
            # Initialize form filling handler
            form_handler = OneSourceFormFilling(self.page)
            form_handler.set_screenshot_config(
                request_id=self.request_id,
                cabin_id=self.cabin_index + 1,
                session_id=self.session_id
            )
            
            # Prepare booking data from cruise details
            # Map extraction field names to form field names
            booking_data = {
                'airport_code': self.cruise_details.get('airport_code', ''),  # Extracted field name
                'departure_date': self.cruise_details.get('date', ''),       # Extracted field name
                'ship_name': self.cruise_details.get('ship_name', ''),
                'occupancy': self.cabin_info.get('passengers', self.cruise_details.get('passengers', '2')),  # From cabin or cruise details
                'nights': self.cruise_details.get('nights', ''),            # Extracted field name
                'past_passenger_number': self.cruise_details.get('past_passenger_number', '')
                , 'cruise_line': self.cruise_details.get('cruise_line', '')
            }
            
            logger.info(f"Form data prepared for cabin {self.cabin_index + 1}: {booking_data}")
            logger.debug(f"Raw cruise_details for debugging: {self.cruise_details}")
            logger.debug(f"Raw cabin_info for debugging: {self.cabin_info}")
            
            # Fill the form
            success = await form_handler.fill_booking_form(booking_data)
            
            if success:
                logger.success(f"Booking form filled successfully for cabin {self.cabin_index + 1}")
                ui_log("Booking form filled successfully", 
                      session_id=self.session_id, 
                      cabin_id=self.cabin_index + 1,
                      step="form_filled", 
                      module="OneSource")
                
                # Step 2: Handle category selection
                logger.info(f"Starting category selection for cabin {self.cabin_index + 1}")
                ui_log("Starting category selection", 
                      session_id=self.session_id, 
                      cabin_id=self.cabin_index + 1,
                      step="category_selection", 
                      module="OneSource")
                
                category_selector = OneSourceCategorySelector(
                    page=self.page,
                    session_id=self.session_id,
                    cabin_id=self.cabin_index + 1
                )
                category_selector.set_screenshot_config(
                    request_id=self.request_id,
                    cabin_id=self.cabin_index + 1,
                    session_id=self.session_id
                )
                
                # Extract category from cruise details
                extracted_category = self.cruise_details.get('category', '') or self.cabin_info.get('cabin_type', '')
                
                # Extract passenger count for filtering
                passenger_count = int(self.cabin_info.get('passengers', self.cruise_details.get('passengers', 2)))
                logger.info(f"Passenger count for cabin {self.cabin_index + 1}: {passenger_count}")
                
                category_success = await category_selector.process_category_selection(extracted_category, passenger_count, self.config)
                
                if category_success:
                    # Store selected category information
                    if hasattr(category_selector, 'selected_category_info'):
                        selected_category = category_selector.selected_category_info
                        self.cabin_result["selected_category_name"] = selected_category['name']
                        self.cabin_result["selected_rate_code"] = selected_category.get('promo', '')
                        logger.info(f"Stored category info: {selected_category['name']}, rate: {selected_category.get('promo', '')}")
                    
                    logger.success(f"Category selection completed successfully for cabin {self.cabin_index + 1}")
                    ui_log("Category selection completed successfully", 
                          session_id=self.session_id, 
                          cabin_id=self.cabin_index + 1,
                          step="category_selected", 
                          module="OneSource")
                    # After category page Save & Continue, handle price details page
                    pd_handler = OneSourcePriceDetails(self.page)
                    pd_handler.set_screenshot_config(
                        request_id=self.request_id,
                        cabin_id=self.cabin_index + 1,
                        session_id=self.session_id
                    )
                    # Get passenger information for age selection
                    passenger_count = self.cabin_info.get('passengers', 2)
                    has_senior = self.cruise_details.get('has_senior', False)
                    has_child = self.cruise_details.get('has_child', False)
                    
                    preferences_success = await pd_handler.apply_price_preferences(
                        passenger_count=passenger_count,
                        has_senior=has_senior,
                        has_child=has_child
                    )
                    if preferences_success:
                        # Determine commission percentage based on cruise line
                        commission_percentage = self._get_commission_percentage()
                        
                        # Now extract and calculate prices
                        from OneSource.price_extraction import OneSourcePriceExtractor
                        price_extractor = OneSourcePriceExtractor(self.page, commission_percentage)
                        price_results = await price_extractor.extract_and_calculate()
                        if price_results:
                            logger.info(f"Price calculation for cabin {self.cabin_index + 1}: Commission={price_results['commission']}, Gross Fare={price_results['gross_fare']}, Final Price={price_results['final_price']}")
                            ui_log(f"Calculated final price: {price_results['final_price']}", 
                                  session_id=self.session_id, 
                                  cabin_id=self.cabin_index + 1,
                                  step="price_calculated", 
                                  module="OneSource")
                            
                            # Store price results in cabin result
                            self.cabin_result["commission"] = price_results['commission']
                            self.cabin_result["gross_fare"] = price_results['gross_fare']
                            self.cabin_result["onboard_credit"] = price_results['onboard_credit']
                            self.cabin_result["cabin_total"] = price_results['final_price']
                        else:
                            logger.error(f"Failed to extract and calculate prices for cabin {self.cabin_index + 1}")
                    return preferences_success
                else:
                    logger.error(f"Failed to complete category selection for cabin {self.cabin_index + 1}")
                    ui_log("Failed to complete category selection", 
                          session_id=self.session_id, 
                          cabin_id=self.cabin_index + 1,
                          step="category_error", 
                          module="OneSource")
                    return False
                    
            else:
                logger.error(f"Failed to fill booking form for cabin {self.cabin_index + 1}")
                ui_log("Failed to fill booking form", 
                      session_id=self.session_id, 
                      cabin_id=self.cabin_index + 1,
                      step="form_error", 
                      module="OneSource")
                return False
            
        except Exception as e:
            logger.error(f"Error filling booking form for cabin {self.cabin_index + 1}: {e}")
            ui_log(f"Error filling booking form: {e}", 
                  session_id=self.session_id, 
                  cabin_id=self.cabin_index + 1,
                  step="form_error", 
                  module="OneSource")
            return False

    def _get_commission_percentage(self) -> float:
        """
        Determine commission percentage based on cruise line and config
        
        Returns:
            float: Commission percentage to use
        """
        try:
            # Get commission percentages from config
            commission_config = self.config.get('onesource_commission_percentages', {
                'holland': 12.0,
                'princess': 11.0
            })
            
            # Get cruise line from cruise details
            cruise_line = self.cruise_details.get('cruise_line', '').lower()
            logger.info(f"Processing commission percentage for cruise line: '{cruise_line}'")
            
            # Determine commission percentage based on cruise line
            if 'holland' in cruise_line or 'hal' in cruise_line or 'holland america' in cruise_line:
                commission_percentage = commission_config.get('holland', 12.0)
                logger.info(f"Detected Holland America cruise line '{cruise_line}' - using Holland commission percentage: {commission_percentage}%")
            elif 'princess' in cruise_line:
                commission_percentage = commission_config.get('princess', 11.0)
                logger.info(f"Detected Princess cruise line '{cruise_line}' - using Princess commission percentage: {commission_percentage}%")
            else:
                # Default to Princess percentage for unknown cruise lines
                commission_percentage = commission_config.get('princess', 11.0)
                logger.info(f"Unknown cruise line '{cruise_line}', defaulting to Princess commission percentage: {commission_percentage}%")
            
            return commission_percentage
            
        except Exception as e:
            logger.error(f"Error determining commission percentage: {e}")
            # Default to 11% if there's an error
            return 11.0


class OneSourceManager:
    """
    Main class for OneSource booking operations
    """
    
    def __init__(self, config=None, cruise_details=None):
        """
        Initialize the OneSource manager
        
        Args:
            config: Configuration options
            cruise_details: Pre-extracted cruise details
        """
        self.config = config or {}
        self.cruise_details = cruise_details or {}
        
        # Extract IDs from config
        self.request_id = self.config.get('request_id', f"os_{datetime.now().strftime('%Y%m%d%H%M%S')}")
        self.session_id = self.config.get('session_id')
        
        # Initialize results container
        self.results = {
            "cabins": [],
            "timestamp": None,
            "overall_status": False,
            "provider": "OneSource",
            "request_id": self.request_id,
            "session_id": self.session_id,
            "execution_time": 0,
            "error": None
        }
        
        self.start_time = None
        self.end_time = None
        
    def _prepare_cabins(self):
        """
        Prepare cabin information from cruise details
        
        Returns:
            list: List of cabin information dictionaries
        """
        cabins = []
        
        # Get cabin types from extracted details
        cabin_types = self.cruise_details.get('cabin_types', [])
        
        if cabin_types:
            for cabin_type_info in cabin_types:
                cabins.append({
                    'cabin_number': cabin_type_info.get('cabin_number', 1),
                    'cabin_type': cabin_type_info.get('cabin_type', 'Unknown'),
                    'passengers': cabin_type_info.get('passengers', 2)
                })
        else:
            # Fallback: create single cabin from basic info
            cabins.append({
                'cabin_number': 1,
                'cabin_type': 'Standard',
                'passengers': self.cruise_details.get('passengers', 2)
            })
        
        logger.info(f"Prepared {len(cabins)} cabins for processing")
        return cabins
    
    async def _process_cabin_async(self, cabin_info, cabin_index):
        """
        Process a single cabin asynchronously
        
        Args:
            cabin_info: Dictionary containing cabin information
            cabin_index: Index of the cabin
            
        Returns:
            dict: Result of the cabin processing
        """
        try:
            # Get shared browser for OneSource
            browser = await browser_manager.get_or_launch_browser('OneSource')
            logger.info(f"Retrieved shared OneSource browser for cabin {cabin_index + 1}")
            
            # Create login handler
            login_handler = OneSourceLogin(
                session_id=self.session_id,
                cabin_id=cabin_index + 1,
                video_auditing=self.config.get('video_auditing', False)
            )
            
            # Perform login
            page, login_success = await login_handler.login()
            
            if not login_success:
                logger.error(f"Login failed for cabin {cabin_index + 1}")
                return {
                    "cabin_number": cabin_index + 1,
                    "success": False,
                    "error": "Login failed",
                    "provider": "OneSource",
                    "session_id": self.session_id,
                    "request_id": self.request_id
                }
            
            # Create cabin processor
            processor = CabinProcessor(
                page=page,
                context=login_handler.context,
                cruise_details=self.cruise_details,
                cabin_info=cabin_info,
                cabin_index=cabin_index,
                session_id=self.session_id,
                request_id=self.request_id,
                video_auditing=self.config.get('video_auditing', False),
                config=self.config
            )
            
            # Process the cabin
            cabin_result = await processor.process()
            
            # Stop video recording if enabled
            if self.config.get('video_auditing', False):
                try:
                    await login_handler.stop_video_recording(
                        request_id=self.request_id,
                        provider="OneSource"
                    )
                except Exception as e:
                    logger.error(f"Error stopping video recording: {e}")
            
            # Cleanup
            await login_handler.cleanup()
            
            return cabin_result
            
        except Exception as e:
            logger.error(f"Error processing cabin {cabin_index + 1}: {e}")
            return {
                "cabin_number": cabin_index + 1,
                "success": False,
                "error": str(e),
                "provider": "OneSource",
                "session_id": self.session_id,
                "request_id": self.request_id
            }
    
    async def run(self):
        """
        Run the OneSource booking process
        
        Returns:
            dict: Processing results
        """
        self.start_time = datetime.now()
        logger.info("Starting OneSource booking process")
        ui_log("Starting OneSource booking process", 
              session_id=self.session_id, 
              step="booking_start", 
              module="OneSource")
        
        try:
            # Prepare cabins for processing
            cabins = self._prepare_cabins()
            
            if not cabins:
                raise ValueError("No cabins to process")
            
            logger.info(f"Processing {len(cabins)} cabins")
            ui_log(f"Processing {len(cabins)} cabins", 
                  session_id=self.session_id, 
                  step="cabins_processing", 
                  module="OneSource")
            
            # Process all cabins asynchronously
            tasks = [
                asyncio.create_task(self._process_cabin_async(cabin, index))
                for index, cabin in enumerate(cabins)
            ]
            
            # Wait for all tasks to complete
            cabin_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            successful_cabins = 0
            total_price = 0
            
            for result in cabin_results:
                if isinstance(result, Exception):
                    logger.error(f"Task failed with exception: {result}")
                    self.results["cabins"].append({
                        "success": False,
                        "error": str(result),
                        "provider": "OneSource"
                    })
                else:
                    self.results["cabins"].append(result)
                    if result.get("success", False):
                        successful_cabins += 1
                        total_price += result.get("cabin_total", 0)
            
            # Update overall results
            self.end_time = datetime.now()
            execution_time = (self.end_time - self.start_time).total_seconds()
            
            self.results.update({
                "timestamp": self.end_time.isoformat(),
                "overall_status": successful_cabins > 0,
                "execution_time": execution_time,
                "total_cabins": len(cabins),
                "successful_cabins": successful_cabins,
                "total_price": total_price,
                "is_or_case": self.cruise_details.get('is_or_case', False)
            })
            
            # Save results to database
            try:
                await save_onesource_results(self.results, self.request_id, self.session_id)
                logger.info("Results saved to database")
            except Exception as e:
                logger.error(f"Error saving results to database: {e}")
            
            if successful_cabins > 0:
                logger.success(f"OneSource booking completed successfully: {successful_cabins}/{len(cabins)} cabins")
                logger.info(f"End time: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
                logger.info(f"Total execution time: {execution_time:.2f} seconds")
                ui_log(f"Booking completed successfully: {successful_cabins}/{len(cabins)} cabins", 
                      session_id=self.session_id, 
                      step="booking_complete", 
                      module="OneSource")
                ui_log(f"Total execution time: {execution_time:.2f} seconds", 
                      session_id=self.session_id, 
                      step="execution_time", 
                      module="OneSource")
                # Add the specific trigger message for frontend validation window
                ui_log("OneSource booking completed successfully", 
                      session_id=self.session_id, 
                      step="booking_complete_trigger", 
                      module="OneSource")
            else:
                logger.error("OneSource booking failed: No cabins processed successfully")
                logger.info(f"End time: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
                logger.info(f"Total execution time: {execution_time:.2f} seconds")
                ui_log("Booking failed: No cabins processed successfully", 
                      session_id=self.session_id, 
                      step="booking_failed", 
                      module="OneSource")
                ui_log(f"Total execution time: {execution_time:.2f} seconds", 
                      session_id=self.session_id, 
                      step="execution_time", 
                      module="OneSource")
            
            return self.results
            
        except Exception as e:
            self.end_time = datetime.now()
            execution_time = (self.end_time - self.start_time).total_seconds() if self.start_time else 0
            
            logger.error(f"OneSource booking process failed: {e}")
            logger.info(f"End time: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"Total execution time: {execution_time:.2f} seconds")
            ui_log(f"Booking process failed: {e}", 
                  session_id=self.session_id, 
                  step="booking_error", 
                  module="OneSource")
            ui_log(f"Total execution time: {execution_time:.2f} seconds", 
                  session_id=self.session_id, 
                  step="execution_time", 
                  module="OneSource")
            
            self.results.update({
                "timestamp": self.end_time.isoformat(),
                "overall_status": False,
                "execution_time": execution_time,
                "error": str(e)
            })
            
            return self.results
        
        finally:
            # Clean up browser resources
            try:
                await browser_manager.close_current_loop_browsers()
            except Exception as e:
                logger.error(f"Error cleaning up browsers: {e}")


# Main entry point for the booking service
async def main(config=None, cruise_details=None):
    """
    Main entry point for OneSource booking process
    
    Args:
        config: Dictionary of configuration options
        cruise_details: Pre-extracted cruise details
        
    Returns:
        dict: Processing results
    """
    if config is None:
        config = {}
    
    # Set default values
    if 'video_auditing' not in config:
        config['video_auditing'] = False
    
    manager = OneSourceManager(config, cruise_details)
    try:
        return await manager.run()
    except asyncio.CancelledError:
        logger.warning(f"OneSource booking cancelled for session {config.get('session_id')}")
        # Perform any necessary cleanup
        # The contexts will be closed by the cancellation service
        raise  # Re-raise to ensure proper cancellation propagation


if __name__ == "__main__":
    # This module is designed to be imported and used by the booking service
    logger.info("OneSource main.py - use main() function for booking operations") 