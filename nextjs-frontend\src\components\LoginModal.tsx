'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import RegisterModal from './RegisterModal';
import { useAuth } from '../context/AuthContext';

// CSS for hover gradient
const hoverStyles = `
  .hover-gradient:hover {
    background-color: #394349 !important;
    background-image: linear-gradient(180deg, #394349 0%, #7cbbb5 100%) !important;
  }
`;

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTabChange?: (tab: 'Home' | 'Guide' | 'Features' | 'Booking') => void;
}

const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose, onTabChange }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  
  // Use the auth context
  const { login, loading: isLoading, getCurrentUser } = useAuth();

  if (!isOpen) return null;

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    try {
      // Call the login function from the auth context
      await login(username, password);
      
      // Get the current user and check status
      const currentUser = getCurrentUser();
      if (currentUser?.status === 'pending') {
        setError('Your account is pending administrator approval.');
        return;
      }
      
      // Change to the Booking tab if onTabChange is provided
      if (onTabChange) {
        onTabChange('Booking');
      } else {
        // Fallback to router navigation if onTabChange is not provided
        router.push('/booking');
      }

      onClose();
    } catch (error) {
      console.error('Login error:', error);
      // Check for specific error messages
      if (error instanceof Error) {
        if (error.message.includes('pending approval')) {
          setError('Your account is pending administrator approval.');
        } else {
          setError(error.message);
        }
      } else {
        setError('Login failed. Please try again.');
      }
    }
  };

  const handleForgotPassword = () => {
    router.push('/forgot-password');
    onClose();
  };

  const handleRegister = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsRegisterModalOpen(true);
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
        <style dangerouslySetInnerHTML={{ __html: hoverStyles }} />
        <div className="relative flex w-full max-w-3xl overflow-hidden rounded-lg shadow-xl" style={{ maxHeight: '90vh', minHeight: '500px' }}>
        {/* Left side - Teal gradient with illustration */}
        <div className="hidden md:flex md:w-1/3 p-6 text-white flex-col justify-between"
             style={{
               background: 'linear-gradient(180deg, #55d4e0 5%, #00262b 100%)'
             }}>
          <div className="flex-grow">
            <h2 className="text-3xl font-bold mb-2">OceanMind-o</h2>
            <h3 className="text-2xl font-semibold mb-4">HAI Agent</h3>
          </div>

          <div className="mb-4">
            {/* Login Icon */}
            <div className="flex justify-center items-center mb-3">
              <div className="bg-white/20 p-3 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
            <p className="text-sm opacity-80 text-center">Secure login with enhanced protection</p>
          </div>
        </div>

        {/* Right side - Login form */}
        <div className="w-full md:w-2/3 bg-white p-10 flex flex-col">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-[#013e5f]">Sign in to your account</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="flex-grow">
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}
            
            <form onSubmit={handleLogin} className="mb-8">
              <div className="mb-6">
                <label htmlFor="username" className="block text-base font-medium text-[#0F6095] mb-2">
                  Username
                </label>
                <input
                  type="text"
                  id="username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="w-full px-4 py-3 text-base bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="Enter your username"
                  required
                  disabled={isLoading}
                />
              </div>

              <div className="mb-6">
                <label htmlFor="password" className="block text-base font-medium text-[#0F6095] mb-2">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 text-base bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="••••••••"
                  required
                  disabled={isLoading}
                />
              </div>

              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                  <input
                    id="remember"
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="remember" className="ml-2 block text-sm text-[#0F6095]">
                    Remember me
                  </label>
                </div>
                <a href="#" onClick={handleForgotPassword} className="text-sm text-[#0F6095] hover:text-[#013e5f]">
                  Forgot password?
                </a>
              </div>

              <div className="mt-10">
                <button
                  type="submit"
                  className="w-full py-2 px-4 text-lg text-white font-semibold rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 hover-gradient"
                  style={{
                    backgroundColor: '#394349',
                    backgroundImage: 'linear-gradient(0deg, #394349 0%, #7cbbb5 100%)',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                  }}
                  disabled={isLoading}
                >
                  {isLoading ? 'Logging in...' : 'Login'}
                </button>
              </div>
            </form>

            <div className="text-center mt-2">
              <a href="#" onClick={handleRegister} className="text-lg text-[#0F6095] hover:text-[#013e5f]">
                Don't have an account?
              </a>
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Register Modal */}
      <RegisterModal isOpen={isRegisterModalOpen} onClose={() => setIsRegisterModalOpen(false)} />
    </>
  );
};

export default LoginModal;
