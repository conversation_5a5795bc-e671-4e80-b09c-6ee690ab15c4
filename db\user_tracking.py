import loguru
from datetime import datetime
from db_service import get_db_connection, db_connection
from .utils import execute_with_retry

# Configure logging
logger = loguru.logger.bind(module="database.user_tracking")

async def track_user_booking(username: str, provider: str, request_id: str, session_id: str):
    """
    Track which user processed a booking
    
    Args:
        username: The username of the user who processed the booking
        provider: The provider (Studio, NCL, or Cruising Power)
        request_id: The booking request ID
        session_id: Session ID for the booking
        
    Returns:
        True if tracking succeeded, False otherwise
    """
    async def do_tracking():
        try:
            async with db_connection() as conn:
            
                # Check if record already exists for this session_id
                result = await conn.fetchrow("""
                    SELECT session_id FROM user_booking_tracking
                    WHERE session_id = $1
                """, session_id)

                if result:
                    # Record already exists with this session_id, update it
                    logger.info(f"User tracking record already exists for session_id: {session_id}, updating to user: {username}")
                    await conn.execute("""
                        UPDATE user_booking_tracking
                        SET username = $1, provider = $2, request_id = $3, timestamp = $4
                        WHERE session_id = $5
                    """,
                        username,
                        provider.lower(),
                        request_id,
                        datetime.now().isoformat(),
                        session_id
                    )
                    return True

                # Insert new tracking record
                await conn.execute("""
                    INSERT INTO user_booking_tracking
                    (session_id, username, provider, request_id, timestamp)
                    VALUES ($1, $2, $3, $4, $5)
                """,
                    session_id,
                    username,
                    provider.lower(),
                    request_id,
                    datetime.now().isoformat()
                )

                logger.info(f"Tracked booking {request_id} for user {username} with provider {provider}, session_id: {session_id}")
                return True
        except Exception as e:
            logger.error(f"Error tracking user booking: {e}")
            import traceback
            logger.error(traceback.format_exc())

            return False
    
    # Use execute_with_retry to handle database locks
    return await execute_with_retry(do_tracking, max_retries=3)

async def get_user_bookings(username: str = None, provider: str = None):
    """
    Get bookings processed by a specific user
    
    Args:
        username: Optional filter by username
        provider: Optional filter by provider
        
    Returns:
        List of bookings processed by the user
    """
    try:
        async with db_connection() as conn:
        
            query = "SELECT * FROM user_booking_tracking"
            params = []
            conditions = []
            param_idx = 1

            if username:
                conditions.append(f"username = ${param_idx}")
                params.append(username)
                param_idx += 1

            if provider:
                conditions.append(f"provider = ${param_idx}")
                params.append(provider.lower())
                param_idx += 1

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY timestamp DESC"

            results = await conn.fetch(query, *params)
        
            # Convert to list of dictionaries
            bookings = [dict(row) for row in results]

            # For each booking, get additional information from booking tables
            for booking in bookings:
                session_id = booking['session_id']
                provider = booking['provider']
                request_id = booking['request_id']

                # Get screenshot count
                screenshot_result = await conn.fetchrow("""
                    SELECT COUNT(*) as screenshot_count
                    FROM centralized_screenshots
                    WHERE session_id = $1
                """, session_id)

                if screenshot_result:
                    booking['screenshot_count'] = screenshot_result['screenshot_count']

                # Get booking details based on provider
                booking_table = f"{provider}_bookings"
                try:
                    booking_result = await conn.fetchrow(f"""
                        SELECT id, timestamp, overall_status, execution_time
                        FROM {booking_table}
                        WHERE session_id = $1
                    """, session_id)

                    if booking_result:
                        booking['booking_id'] = booking_result['id']
                        booking['booking_timestamp'] = booking_result['timestamp']
                        if 'overall_status' in booking_result.keys():
                            booking['booking_status'] = booking_result['overall_status']
                        if 'execution_time' in booking_result.keys():
                            booking['execution_time'] = booking_result['execution_time']
                except:
                    # Table might not have some columns, silently continue
                    pass
                
            return bookings
    except Exception as e:
        logger.error(f"Error retrieving user bookings: {e}")
        import traceback
        logger.error(traceback.format_exc())

        return []

async def get_booking_users(request_id: str, provider: str = None):
    """
    Get users who processed a specific booking
    
    Args:
        request_id: The booking request ID
        provider: Optional filter by provider
        
    Returns:
        List of users who processed the booking
    """
    try:
        async with db_connection() as conn:
        
            query = "SELECT * FROM user_booking_tracking WHERE request_id = $1"
            params = [request_id]
            param_idx = 2

            if provider:
                query += f" AND provider = ${param_idx}"
                params.append(provider.lower())

            query += " ORDER BY timestamp DESC"

            results = await conn.fetch(query, *params)

            # Convert to list of dictionaries
            users = [dict(row) for row in results]

            return users
    except Exception as e:
        logger.error(f"Error retrieving booking users: {e}")
        import traceback
        logger.error(traceback.format_exc())

        return []

def integrate_user_tracking(provider_save_function):
    
    async def wrapper(*args, **kwargs):
        # Extract username, provider, request_id, and session_id from args/kwargs
        username = kwargs.pop('username', None)
        if not username:
            # If no username provided, just call the original function
            return await provider_save_function(*args, **kwargs)
        
        # Get the provider name - either from kwargs or try to determine from function name
        provider = kwargs.get('provider')
        if not provider:
            func_name = provider_save_function.__name__
            if 'studio' in func_name:
                provider = 'studio'
            elif 'ncl' in func_name:
                provider = 'ncl'
            elif 'cruising_power' in func_name:
                provider = 'cruising_power'
            else:
                # Try to get from the first arg if it's a dict with provider key
                if args and isinstance(args[0], dict) and 'provider' in args[0]:
                    provider = args[0]['provider']
        
        # Find request_id and session_id
        request_id = kwargs.get('request_id')
        if not request_id and len(args) > 1:
            # Assume second arg is request_id for typical save functions
            request_id = args[1]
        
        session_id = kwargs.get('session_id')
        
        # Call the original function first
        result = await provider_save_function(*args, **kwargs)
        
        # If successful and we have all required tracking info, track the user
        if result and username and provider and request_id and session_id:
            try:
                await track_user_booking(username, provider, request_id, session_id)
            except Exception as e:
                # Log but don't interrupt the flow
                logger.error(f"Error in user tracking integration: {e}")
        
        return result
    
    return wrapper 