'use client';

import React, { useState, useRef } from 'react';
import Header from '../components/ui/Header';
import Footer from '../components/ui/Footer';
import Config, { ConfigState } from '../components/ui/Config';
import ProviderSelector, { ProviderType } from '../components/booking/ProviderSelector';
import QuoteInput, { QuoteInputData } from '../components/booking/QuoteInput';
import CruiseDetails from '../components/booking/CruiseDetails';
import BookingProcess from '../components/booking/BookingProcess';
import { LogProvider, useLogContext } from '../contexts/LogContext';
import MultiCabinLogViewer from '../components/MultiCabinLogViewer';
import { extractDetails } from '../services/api';
import { initializeTokenExpiryCheck } from '../services/auth';
import HomeContent from '../components/home/<USER>';
import GuideContent from '../components/guide/GuideContent';
import FeaturesContent from '../components/features/FeaturesContent';
import { useAuth } from '../context/AuthContext';
import SessionTimer from '../components/ui/SessionTimer';

// Create a component to hold the Booking content with access to LogContext
const BookingContent = ({
  provider,
  setProvider,
  config,
  handleConfigChange,
  isExtracting,
  extractionComplete,
  cruiseDetails,
  setCruiseDetails,
  setExtractionComplete,
  handleExtractionSubmit,
  requestId,
  setRequestId,
  handleCruiseDetailsUpdate,
  handleReset
}: {
  provider: ProviderType;
  setProvider: React.Dispatch<React.SetStateAction<ProviderType>>;
  config: ConfigState;
  handleConfigChange: (newConfig: ConfigState) => void;
  isExtracting: boolean;
  extractionComplete: boolean;
  cruiseDetails: any;
  setCruiseDetails: React.Dispatch<React.SetStateAction<any>>;
  setExtractionComplete: React.Dispatch<React.SetStateAction<boolean>>;
  handleExtractionSubmit: (data: QuoteInputData) => void;
  requestId: string;
  setRequestId: React.Dispatch<React.SetStateAction<string>>;
  handleCruiseDetailsUpdate: (updatedDetails: any) => void;
  handleReset: () => void;
}) => {
  // Access the log context to clear logs
  const { clearLogs } = useLogContext();
  const { user, hasProviderAccess } = useAuth();

  // Initialize provider on first login only if no provider is selected
  React.useEffect(() => {
    if (user && user.provider_access && user.provider_access.length > 0) {
      // Check if current provider is valid for this user
      const isCurrentProviderValid = user.provider_access.includes(provider);
      
      // Only set to first provider if current selection is invalid
      if (!isCurrentProviderValid) {
        const validProvider = user.provider_access[0];
        setProvider(validProvider);
        // Reset extraction state when provider changes
        setExtractionComplete(false);
        setCruiseDetails(null);
        // Clear logs when changing providers
        clearLogs();
      }
    }
  }, [user, provider, setProvider, clearLogs, setExtractionComplete, setCruiseDetails]);

  // Handle provider change with log clearing
  const handleProviderChange = (newProvider: ProviderType) => {
    console.log("Provider changed to:", newProvider);

    // Only update if the provider is actually different
    if (newProvider !== provider) {
      setProvider(newProvider);

      // Reset extraction state when provider changes
      setExtractionComplete(false);
      setCruiseDetails(null);
      
      // Clear logs when changing providers
      clearLogs();
    }
  };

  return (
    <>
      {/* Responsive grid layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
        {/* Left Column - Configuration */}
        <div className="md:col-span-1 lg:col-span-1 order-2 md:order-1">
          <Config
            onConfigChange={handleConfigChange}
            provider={provider}
          />
        </div>

        {/* Middle Column - Quote Input and Results */}
        <div className="md:col-span-2 lg:col-span-2 space-y-4 md:space-y-6 lg:space-y-8 order-1 md:order-2">
          {/* Quote Input */}
          {!extractionComplete && (
            <div>
              <QuoteInput
                provider={provider}
                onSubmit={handleExtractionSubmit}
                isLoading={isExtracting}
              />
            </div>
          )}

          {/* Extraction Results */}
          {extractionComplete && cruiseDetails && (
            <>
              <div className="bg-white p-4 md:p-6 rounded-lg shadow-md">
                <h2 className="text-lg md:text-xl font-bold mb-2">Extracted Information</h2>
                <div className="mt-1 relative pb-2">
                  <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
                  <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-800"></div>
                </div>
                <p className="text-sm text-gray-600 mt-2 mb-2">This is the extracted information from the cruise booking website. Please review and validate the details.</p>
                <CruiseDetails
                  provider={provider}
                  details={cruiseDetails}
                  requestId={requestId}
                  onUpdate={handleCruiseDetailsUpdate}
                />
              </div>

              <div className="bg-white p-4 md:p-6 rounded-lg shadow-md">
                <BookingProcess
                  provider={provider}
                  cruiseDetails={cruiseDetails}
                  config={config}
                  requestId={requestId}
                  onReset={handleReset}
                />
              </div>

              {/* Log Viewer for real-time booking logs */}
              <div className="bg-white p-4 md:p-6 rounded-lg shadow-md mt-6">
                <h3 className="text-base md:text-lg font-semibold mb-2">Live Booking Logs</h3>
                <MultiCabinLogViewer 
                  maxHeight="200px" 
                  module={getModuleFromProvider(provider)}
                />
              </div>
            </>
          )}
        </div>
        
        {/* Right Column - Provider Selection */}
        <div className="md:col-span-1 lg:col-span-1 order-3">
          {/* Provider Selection */}
          <div>
            <ProviderSelector
              selectedProvider={provider}
              onProviderChange={handleProviderChange}
            />
          </div>
        </div>
      </div>

      <div className="mt-8">
        <Footer />
      </div>
    </>
  );
};

// Helper function to map provider types to module names
const getModuleFromProvider = (provider: ProviderType): string => {
  switch (provider) {
    case 'Studio.Sales.CabinCloseOut':
    case 'Studio.Res.CabinCloseOut':
      return 'Studio';
    case 'NCL':
      return 'NCL';
    case 'Cruising Power':
      return 'Cruising_Power';  // Match the backend module name format
    case 'OneSource':
      return 'OneSource';
    default:
      return 'Studio'; // fallback
  }
};

export default function Home() {
  // State for navigation
  const [activeTab, setActiveTab] = useState<'Home' | 'Guide' | 'Features' | 'Booking'>('Home');
 
  // Debug: Log when activeTab changes
  React.useEffect(() => {
    console.log('Active tab changed to:', activeTab);
  }, [activeTab]);
  
  // Initialize token expiry check on app startup
  React.useEffect(() => {
    initializeTokenExpiryCheck();
  }, []);
 
  // Application state
  const { user, hasProviderAccess } = useAuth();
  const [provider, setProvider] = useState<ProviderType>('Studio.Sales.CabinCloseOut');
  const [config, setConfig] = useState<ConfigState>({
    browser_type: 'chrome',
    headless: true,
    take_screenshots: true,
    debug_mode: false,
    video_auditing_ncl: false,
    video_auditing_studio: false,
    video_auditing_cruising_power: false,
    cruising_power_onboard_percentage: 12,
    ncl_category_percentages: {
      inside: 12,
      outside: 12,
      balcony: 10,
      junior_suite: 10,
      suite: 10,
    },
    rate_selection_strategy: 'Cheapest',
    ncl_rate_codes: {
      ALL4CHO: true,
      CHOALL42: true,
      FITOBC: false,
      STAROBC: false,
      NATOBC: false,
    },
    onesource_commission_percentages: {
      holland: 12,
      princess: 11,
    },
    onesource_perk: 'No',
  });
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractionComplete, setExtractionComplete] = useState(false);
  const [cruiseDetails, setCruiseDetails] = useState<any>(null);
  const [requestId, setRequestId] = useState<string>('');

  // Initialize provider on first render
  React.useEffect(() => {
    if (user && user.provider_access && user.provider_access.length > 0) {
      // Check if there's no selected provider yet or if current provider is invalid
      const isCurrentProviderValid = user.provider_access.includes(provider);
      if (!isCurrentProviderValid) {
        setProvider(user.provider_access[0]);
      }
    }
  }, [user, provider]);

  // API base URL
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL;

  // Handle config changes
  const handleConfigChange = (newConfig: ConfigState) => {
    setConfig(newConfig);
  };
 
  // Handle extraction submission
  const handleExtractionSubmit = async (data: QuoteInputData) => {
    try {
      setIsExtracting(true);
      setRequestId(data.requestId);
 
      // Call the API
      const response = await extractDetails(provider, data);
 
      if (response.success && response.details) {
        setCruiseDetails(response.details);
        setExtractionComplete(true);
      } else {
        console.error('Extraction failed:', response);
        alert('Failed to extract cruise details. Please try again.');
      }
    } catch (error) {
      console.error('Error during extraction:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsExtracting(false);
    }
  };
 
  // Handle updating cruise details
  const handleCruiseDetailsUpdate = (updatedDetails: any) => {
    setCruiseDetails(updatedDetails);
  };
 
  // Handle reset of the booking process
  const handleReset = () => {
    setExtractionComplete(false);
    setCruiseDetails(null);
    setRequestId('');
  };
 
  return (
    <main className=" bg-[#d7f2f2]">
      <Header activeTab={activeTab} onTabChange={setActiveTab} />
      <SessionTimer />
 
      <div className=" mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-4 md:py-8">
        {activeTab === 'Home' && (
          <>
            <HomeContent />
            <div className="mt-8">
              <Footer />
            </div>
          </>
        )}
 
        {activeTab === 'Guide' && (
          <>
            <GuideContent />
            <div className="mt-8">
              <Footer />
            </div>
          </>
        )}
 
        {activeTab === 'Features' && (
          <>
            <FeaturesContent />
            <div className="mt-8">
              <Footer />
            </div>
          </>
        )}
 
        {activeTab === 'Booking' && (
          <LogProvider apiBaseUrl={apiBaseUrl}>
            <BookingContent
              provider={provider}
              setProvider={setProvider}
              config={config}
              handleConfigChange={handleConfigChange}
              isExtracting={isExtracting}
              extractionComplete={extractionComplete}
              cruiseDetails={cruiseDetails}
              setCruiseDetails={setCruiseDetails}
              setExtractionComplete={setExtractionComplete}
              handleExtractionSubmit={handleExtractionSubmit}
              requestId={requestId}
              setRequestId={setRequestId}
              handleCruiseDetailsUpdate={handleCruiseDetailsUpdate}
              handleReset={handleReset}
            />
          </LogProvider>
        )}
      </div>
    </main>
  );
}
 
