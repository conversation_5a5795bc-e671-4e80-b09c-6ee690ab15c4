'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '../../../context/AuthContext';
import {
  User,
  // Provider types
  Provider,
  getUsers,
  updateUser,
  deleteUser,
  updateUserRole,
  getProviders,
  createProvider,
  deleteProvider,
  getPendingUsers,
  approveUser,
  rejectUser,
  suspendUser,
  unsuspendUser,
  fetchAgencies,
  updateProvider,
  updateProviderStatus
} from '../../../services/auth';
import { ProviderType } from '../../../components/booking/ProviderSelector';
import { getUserTrackingData, UserTrackingData } from '../../../services/api';
import { parseIssueCodes, getIssueLabel } from '../../../utils/issueHelpers';
import { ProcessedRequestAnalytics } from '../../admin/analytics';
import ResultsViewModal from '../../../components/booking/ResultsViewModal';

// Define the provider option type
type ProviderOption = {
  value: ProviderType;
  label: string;
  agency: string;
  disabled?: boolean;
};

// Types for subadmin panel state
type TabType = 'dashboard' | 'pending' | 'users' | 'providers' | 'processed';
type ApprovalData = {
  role: 'user' | 'sub_admin' | 'admin';
  agencies: string[];
  portalAccess: string[];
  providerAccess: ProviderType[];
};

// Create a custom multi-select dropdown component
interface MultiSelectDropdownProps {
  options: { value: string; label: string }[];
  selected: string[];
  onChange: (selected: string[]) => void;
}

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({ options, selected, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOption = (value: string) => {
    if (selected.includes(value)) {
      onChange(selected.filter(item => item !== value));
    } else {
      onChange([...selected, value]);
    }
  };

  return (
    <div className="relative">
      <div
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 cursor-pointer flex justify-between items-center"
      >
        <span className="text-gray-700">
          {selected.length === 0 ? 'Select agencies' : selected.join(', ')}
        </span>
        <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>
      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200">
          {options.map(option => (
            <div
              key={option.value}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
              onClick={() => toggleOption(option.value)}
            >
              <input
                type="checkbox"
                checked={selected.includes(option.value)}
                readOnly
                className="mr-2 h-4 w-4"
              />
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default function SubadminPanel() {
  const router = useRouter();
  const params = useParams();
  const agencyParam = params?.agency as string;
  const { isAdmin, isSubAdmin, isLoggedIn, loading, user: currentUser, canManageUser, canAccessAgency, isSubAdminOnly, getAccessibleAgencies } = useAuth();

  // Get the agency from URL parameter and validate it
  const currentAgency = agencyParam?.toUpperCase();
  
  // Main data states - filtered to current agency
  const [users, setUsers] = useState<User[]>([]);
  const [pendingUsers, setPendingUsers] = useState<User[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [agencies, setAgencies] = useState<string[]>(['STUDIO', 'CNM']);

  // Filter providers to only show those for the current agency
  const agencyProviders = useMemo(
    () => providers.filter(p => p.agency === currentAgency),
    [providers, currentAgency]
  );

  // Dynamically build providerOptions from agency-filtered providers
  const providerOptions: ProviderOption[] = useMemo(
    () => agencyProviders.map(p => ({
      value: p.name as ProviderType,
      label: p.description || p.name,
      agency: p.agency
    })),
    [agencyProviders]
  );

  // Filter users to only show those for the current agency
  const agencyUsers = useMemo(
    () => users.filter(u => u.agency === currentAgency),
    [users, currentAgency]
  );

  // Filter pending users to only show those for the current agency
  const agencyPendingUsers = useMemo(
    () => pendingUsers.filter(u => u.agency === currentAgency),
    [pendingUsers, currentAgency]
  );

  // UI states
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Form states - default to current agency
  const [newProvider, setNewProvider] = useState({
    name: '',
    agency: currentAgency || 'STUDIO',
    description: '',
    status: 'active' as 'active' | 'coming_soon' | 'maintenance'
  });
  const [customAgency, setCustomAgency] = useState('');
  const [showCustomAgency, setShowCustomAgency] = useState(false);
  
  // Provider editing states
  const [editingProvider, setEditingProvider] = useState<Provider | null>(null);
  const [editProvider, setEditProvider] = useState({
    name: '',
    agency: '',
    description: '',
    status: 'active' as 'active' | 'coming_soon' | 'maintenance'
  });
  const [editCustomAgency, setEditCustomAgency] = useState('');
  const [showEditCustomAgency, setShowEditCustomAgency] = useState(false);

  // User management states
  const [userApprovalData, setUserApprovalData] = useState<Record<string, ApprovalData>>({});
  const [userProviderAccess, setUserProviderAccess] = useState<Record<string, ProviderType[]>>({});

  // Tracking data states - filtered to current agency
  const [trackingData, setTrackingData] = useState<UserTrackingData[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<string>('all');
  const [availableUsers, setAvailableUsers] = useState<string[]>([]);
  const [searchUsername, setSearchUsername] = useState<string>('');
  const [isLoadingTracking, setIsLoadingTracking] = useState(false);
  const [trackingError, setTrackingError] = useState<string | null>(null);
  const [processedViewMode, setProcessedViewMode] = useState<'table' | 'analytics'>('table');
  const [numDays, setNumDays] = useState(0); // 0 for all data
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);
  const [customDays, setCustomDays] = useState<string>('');

  // Add state for managing user agencies selection in the edit mode
  const [userAgenciesEdit, setUserAgenciesEdit] = useState<Record<string, string[]>>({});
  
  // State for expanded comments and request IDs
  const [expandedComments, setExpandedComments] = useState<Record<string, boolean>>({});
  const [expandedRequestIds, setExpandedRequestIds] = useState<Record<string, boolean>>({});
  const [expandedIssues, setExpandedIssues] = useState<Record<string, boolean>>({});

  // Result view modal state
  const [resultModalOpen, setResultModalOpen] = useState(false);
  const [selectedResult, setSelectedResult] = useState<{ provider: ProviderType; requestId: string; sessionId: string } | null>(null);

  const handleViewResult = (provider: string, requestId: string, sessionId: string) => {
    setSelectedResult({ provider: provider as ProviderType, requestId, sessionId });
    setResultModalOpen(true);
  };

  const handleCloseResultModal = () => {
    setResultModalOpen(false);
    setSelectedResult(null);
  };

  // ====== Pagination state for processed tracking table ======
  const rowsPerPage = 100;
  const [currentPage, setCurrentPage] = useState(1);

  // Reset to first page whenever fresh data arrive or filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [trackingData]);

  const totalPages = Math.max(1, Math.ceil(trackingData.length / rowsPerPage));

  const paginatedTrackingData = useMemo(() => {
    const start = (currentPage - 1) * rowsPerPage;
    return trackingData.slice(start, start + rowsPerPage);
  }, [trackingData, currentPage]);

  const canPrev = currentPage > 1;
  const canNext = currentPage < totalPages;

  const gotoPrev = () => canPrev && setCurrentPage(p => p - 1);
  const gotoNext = () => canNext && setCurrentPage(p => p + 1);

  // Function to toggle comment expansion
  const toggleCommentExpansion = (sessionId: string) => {
    setExpandedComments(prev => ({
      ...prev,
      [sessionId]: !prev[sessionId]
    }));
  };

  // Function to toggle request ID expansion
  const toggleRequestIdExpansion = (sessionId: string) => {
    setExpandedRequestIds(prev => ({
      ...prev,
      [sessionId]: !prev[sessionId]
    }));
  };

  const toggleIssueExpansion = (sessionId: string) => {
    setExpandedIssues(prev => ({
      ...prev,
      [sessionId]: !prev[sessionId]
    }));
  };

  // Function to truncate comment text
  const truncateComment = (comment: string, maxLength: number = 50) => {
    if (!comment || comment.length <= maxLength) return comment;
    return comment.substring(0, maxLength) + '...';
  };

  // Function to parse session ID into request ID and timestamp
  const parseSessionId = (sessionId: string) => {
    // Session ID format: Quote_1_20250711114301
    // Split by underscore and find the last part which should be the timestamp
    const parts = sessionId.split('_');
    if (parts.length >= 2) {
      const lastPart = parts[parts.length - 1];
      // Check if last part is a timestamp (14 digits: YYYYMMDDHHMMSS)
      if (lastPart.length === 14 && /^\d{14}$/.test(lastPart)) {
        const rawTimestamp = lastPart;
        const requestId = parts.slice(0, -1).join('_');

        // Parse timestamp: YYYYMMDDHHMMSS
        const year = rawTimestamp.substring(0, 4);
        const month = rawTimestamp.substring(4, 6);
        const day = rawTimestamp.substring(6, 8);
        const hour = rawTimestamp.substring(8, 10);
        const minute = rawTimestamp.substring(10, 12);
        const second = rawTimestamp.substring(12, 14);

        // Create date object and format as IST
        const date = new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}`);

        // Format date and time separately for IST (Indian Standard Time)
        const istDate = date.toLocaleDateString('en-IN', {
          timeZone: 'Asia/Kolkata',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });

        const istTime = date.toLocaleTimeString('en-IN', {
          timeZone: 'Asia/Kolkata',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });

        return { requestId, date: istDate, time: istTime };
      }
    }
    // If parsing fails, return the original session ID as request ID
    return { requestId: sessionId, date: '', time: '' };
  };

  // Initialize the agencies edit state and provider access when users are loaded
  useEffect(() => {
    if (agencyUsers.length > 0) {
      const initialAgenciesEdit: Record<string, string[]> = {};
      const initialProviderAccess: Record<string, ProviderType[]> = {};

      // Get all available provider values for non-admin users (filtered to current agency)
      const nonAdminProviderValues = agencyProviders
        .filter(provider => provider.status !== 'coming_soon')
        .map(provider => provider.name as ProviderType);

      // Get ALL provider values for admin users (including coming soon, filtered to current agency)
      const allProviderValues = agencyProviders
        .map(provider => provider.name as ProviderType);

      agencyUsers.forEach(user => {
        // Initialize agencies edit state - for subadmin dashboard, always use current agency
        initialAgenciesEdit[user.user_id] = [currentAgency];

        // Initialize provider access based on user's current access and role
        if (user.role === 'admin') {
          // Admin users get all providers for the current agency
          initialProviderAccess[user.user_id] = allProviderValues;
        } else {
          // Non-admin users get their current provider access filtered to current agency
          const userProviders = Array.isArray(user.provider_access) ? user.provider_access : [];
          const agencyFilteredProviders = userProviders.filter(provider =>
            agencyProviders.some(p => p.name === provider)
          );
          initialProviderAccess[user.user_id] = agencyFilteredProviders;
        }
      });

      setUserAgenciesEdit(initialAgenciesEdit);
      setUserProviderAccess(initialProviderAccess);
    }
  }, [agencyUsers, agencyProviders, currentAgency]);

  // Validation: Check if user has access to this agency
  useEffect(() => {
    if (!loading && currentUser && currentAgency) {
      if (!canAccessAgency(currentAgency)) {
        router.push('/subadmin/select-agency');
        return;
      }
    }
  }, [currentUser, currentAgency, canAccessAgency, loading, router]);

  // Redirect if not subadmin
  useEffect(() => {
    if (!loading && !isSubAdmin) {
      router.push('/');
    }
  }, [isSubAdmin, loading, router]);

  // Load data when the component mounts
  useEffect(() => {
    if (isSubAdmin && currentAgency && canAccessAgency(currentAgency)) {
      fetchData();
    }
  }, [isSubAdmin, currentAgency]);

  // Generic error handler
  const handleError = (err: any, message: string) => {
    console.error(`${message}:`, err);
    setError(message);
    setIsLoading(false);
  };

  // Success message handler
  const showSuccess = (message: string) => {
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  // Fetch all required data for the subadmin panel (filtered to current agency)
  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const [usersData, pendingUsersData, providersData, agenciesData] = await Promise.all([
        getUsers(),
        getPendingUsers(),
        getProviders(),
        fetchAgencies()
      ]);

      // Filter data to current agency
      const filteredUsers = usersData.filter(u => u.agency === currentAgency);
      const filteredPendingUsers = pendingUsersData.filter(u => u.agency === currentAgency);
      const filteredProviders = providersData.filter(p => p.agency === currentAgency);

      setUsers(usersData); // Keep all users for management checks
      setPendingUsers(pendingUsersData); // Keep all pending users for management checks
      setProviders(providersData); // Keep all providers for filtering
      setAgencies(agenciesData);

      console.log(`Loaded data for ${currentAgency}:`, {
        users: filteredUsers.length,
        pendingUsers: filteredPendingUsers.length,
        providers: filteredProviders.length
      });
    } catch (err) {
      handleError(err, 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch tracking data (filtered to current agency providers)
  const fetchTrackingData = async () => {
    setIsLoadingTracking(true);
    setTrackingError(null);

    try {
      const data = await getUserTrackingData();

      // Filter tracking data to only include current agency providers
      const agencyProviderNames = agencyProviders.map(p => p.name);
      const filteredData = data.filter(item =>
        agencyProviderNames.includes(item.provider)
      );

      setTrackingData(filteredData);

      // Extract unique usernames for filtering (from agency-filtered data)
      const usernames = Array.from(new Set(filteredData.map(item => item.username).filter(Boolean)));
      setAvailableUsers(usernames);

      console.log(`Loaded tracking data for ${currentAgency}:`, filteredData.length, 'records');
    } catch (err) {
      console.error('Error fetching tracking data:', err);
      setTrackingError('Failed to load tracking data');
    } finally {
      setIsLoadingTracking(false);
    }
  };

  // Role change validation function
  const canChangeRole = (userId: string, currentRole: string, newRole: string, currentUser: User | null) => {
    if (!currentUser) return { canChange: false, reason: 'Not authenticated' };

    // Only admins can change roles in subadmin dashboard
    if (currentUser.role !== 'admin') {
      return { canChange: false, reason: 'Only admins can change user roles' };
    }

    // Cannot change admin roles
    if (currentRole === 'admin') {
      return { canChange: false, reason: 'Admin roles cannot be changed' };
    }

    // Cannot promote to admin in subadmin dashboard
    if (newRole === 'admin') {
      return { canChange: false, reason: 'Cannot promote to admin in agency dashboard' };
    }

    return { canChange: true };
  };

  // User management actions (filtered to current agency)
  const handleUpdateRole = async (userId: string, newRole: 'admin' | 'sub_admin' | 'user') => {
    try {
      setIsLoading(true);

      // Get the current user being updated
      const targetUser = agencyUsers.find(u => u.user_id === userId);
      if (!targetUser) {
        throw new Error('User not found in current agency');
      }

      // Check if role change is allowed
      const roleCheck = canChangeRole(userId, targetUser.role, newRole, currentUser);
      if (!roleCheck.canChange) {
        setError(roleCheck.reason || 'Role change not allowed');
        return;
      }

      // Update provider access for the new role (limited to current agency)
      let updatedProviderAccess = userProviderAccess[userId] || [];

      if (newRole === 'sub_admin') {
        // Sub-admin gets access to all active providers in current agency
        const agencyActiveProviders = agencyProviders
          .filter(provider => provider.status === 'active')
          .map(provider => provider.name as ProviderType);
        updatedProviderAccess = agencyActiveProviders;

        // Update the provider access state
        setUserProviderAccess(prev => ({
          ...prev,
          [userId]: updatedProviderAccess
        }));

        // Update user with new role and provider access
        await updateUser(userId, {
          role: newRole,
          provider_access: updatedProviderAccess,
          agency: currentAgency // Ensure user stays in current agency
        });
      } else {
        // Just update the role for regular users
        await updateUserRole(userId, newRole);
      }

      // Update the user in the list
      setUsers(users.map(user =>
        user.user_id === userId ? {
          ...user,
          role: newRole,
          provider_access: updatedProviderAccess,
          agency: currentAgency
        } : user
      ));

      showSuccess('User role updated successfully');
    } catch (err) {
      handleError(err, 'Failed to update user role');
    } finally {
      setIsLoading(false);
    }
  };

  // Delete user (only within current agency)
  const handleDeleteUser = async (userId: string) => {
    if (!window.confirm('Are you sure you want to delete this user?')) {
      return;
    }

    try {
      setIsLoading(true);

      // Check if current user can manage this user and if user is in current agency
      const targetUser = agencyUsers.find(u => u.user_id === userId);
      if (!targetUser) {
        setError('User not found in current agency');
        return;
      }

      if (!canManageUser(targetUser)) {
        setError('You can only delete users within your assigned agencies');
        return;
      }

      await deleteUser(userId);

      // Remove the user from the list
      setUsers(users.filter(user => user.user_id !== userId));
      showSuccess('User deleted successfully');
    } catch (err: any) {
      const errorMessage = err?.toString().includes('Admin accounts cannot be deleted')
        ? 'Admin accounts cannot be deleted'
        : 'Failed to delete user';
      handleError(err, errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Suspend/Unsuspend user (only within current agency)
  const handleSuspendUser = async (userId: string, suspend: boolean) => {
    try {
      setIsLoading(true);

      const targetUser = agencyUsers.find(u => u.user_id === userId);
      if (!targetUser) {
        setError('User not found in current agency');
        return;
      }

      if (!canManageUser(targetUser)) {
        setError('You can only manage users within your assigned agencies');
        return;
      }

      if (suspend) {
        await suspendUser(userId);
      } else {
        await unsuspendUser(userId);
      }

      // Update the user in the list
      setUsers(users.map(user =>
        user.user_id === userId ? { ...user, status: suspend ? 'suspended' : 'approved' } : user
      ));

      showSuccess(`User ${suspend ? 'suspended' : 'unsuspended'} successfully`);
    } catch (err) {
      handleError(err, `Failed to ${suspend ? 'suspend' : 'unsuspend'} user`);
    } finally {
      setIsLoading(false);
    }
  };

  // Provider management actions (only for current agency)
  const handleCreateProvider = async () => {
    if (!newProvider.name || !newProvider.description) {
      setError('Please fill in all required fields');
      return;
    }

    // Ensure provider is created for current agency
    const providerData = {
      ...newProvider,
      agency: currentAgency
    };

    try {
      setIsLoading(true);
      await createProvider(providerData);

      // Refresh providers data
      const providersData = await getProviders();
      setProviders(providersData);

      // Reset form
      setNewProvider({
        name: '',
        agency: currentAgency || 'STUDIO',
        description: '',
        status: 'active'
      });

      showSuccess('Provider created successfully');
    } catch (err) {
      handleError(err, 'Failed to create provider');
    } finally {
      setIsLoading(false);
    }
  };

  // Update provider (only for current agency)
  const handleUpdateProvider = async () => {
    if (!editingProvider || !editProvider.name || !editProvider.description) {
      setError('Please fill in all required fields');
      return;
    }

    // Ensure provider stays in current agency
    const providerData = {
      ...editProvider,
      agency: currentAgency
    };

    try {
      setIsLoading(true);
      await updateProvider(editingProvider.provider_id, providerData);

      // Refresh providers data
      const providersData = await getProviders();
      setProviders(providersData);

      // Reset editing state
      setEditingProvider(null);
      setEditProvider({ name: '', agency: '', description: '', status: 'active' });

      showSuccess('Provider updated successfully');
    } catch (err) {
      handleError(err, 'Failed to update provider');
    } finally {
      setIsLoading(false);
    }
  };

  // Delete provider (only for current agency)
  const handleDeleteProvider = async (providerId: number) => {
    if (!window.confirm('Are you sure you want to delete this provider?')) {
      return;
    }

    try {
      setIsLoading(true);
      await deleteProvider(providerId);

      // Refresh providers data
      const providersData = await getProviders();
      setProviders(providersData);

      showSuccess('Provider deleted successfully');
    } catch (err) {
      handleError(err, 'Failed to delete provider');
    } finally {
      setIsLoading(false);
    }
  };

  // Update provider status (only for current agency)
  const handleUpdateProviderStatus = async (providerId: number, status: 'active' | 'coming_soon' | 'maintenance') => {
    try {
      setIsLoading(true);
      await updateProviderStatus(providerId, status);

      // Update the provider in the list
      setProviders(providers.map(provider =>
        provider.provider_id === providerId ? { ...provider, status } : provider
      ));

      showSuccess('Provider status updated successfully');
    } catch (err) {
      handleError(err, 'Failed to update provider status');
    } finally {
      setIsLoading(false);
    }
  };

  // Pending user approval functions (only for current agency)
  const handleApproveUser = async (userId: string) => {
    try {
      setIsLoading(true);

      const targetUser = agencyPendingUsers.find(u => u.user_id === userId);
      if (!targetUser) {
        setError('Pending user not found in current agency');
        return;
      }

      const approvalData = userApprovalData[userId];
      if (!approvalData) {
        setError('Please configure user permissions before approving');
        return;
      }

      // Ensure user is approved for current agency only
      await approveUser(userId, {
        role: approvalData.role,
        agency: currentAgency,
        portal_access: approvalData.portalAccess,
        provider_access: approvalData.providerAccess
      });

      // Remove from pending and add to users
      setPendingUsers(pendingUsers.filter(user => user.user_id !== userId));

      // Refresh users data
      const usersData = await getUsers();
      setUsers(usersData);

      showSuccess('User approved successfully');
    } catch (err) {
      handleError(err, 'Failed to approve user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectUser = async (userId: string) => {
    if (!window.confirm('Are you sure you want to reject this user?')) {
      return;
    }

    try {
      setIsLoading(true);

      const targetUser = agencyPendingUsers.find(u => u.user_id === userId);
      if (!targetUser) {
        setError('Pending user not found in current agency');
        return;
      }

      await rejectUser(userId);

      // Remove from pending users
      setPendingUsers(pendingUsers.filter(user => user.user_id !== userId));

      showSuccess('User rejected successfully');
    } catch (err) {
      handleError(err, 'Failed to reject user');
    } finally {
      setIsLoading(false);
    }
  };

  // Update user approval data (restricted to current agency)
  const updateUserApprovalData = (userId: string, field: string, value: any) => {
    setUserApprovalData(prev => {
      const updatedData = {
        ...prev[userId],
        [field]: value
      };

      // For subadmin dashboard, always restrict to current agency
      if (field === 'agencies') {
        updatedData.agencies = [currentAgency];
      }

      // If role is being updated to sub_admin, automatically assign agency providers
      if (field === 'role' && value === 'sub_admin') {
        const agencyActiveProviders = agencyProviders
          .filter(provider => provider.status === 'active')
          .map(provider => provider.name as ProviderType);
        updatedData.providerAccess = agencyActiveProviders;
      }

      return {
        ...prev,
        [userId]: updatedData
      };
    });
  };

  // Agency-specific color scheme
  const getAgencyColors = (agency: string) => {
    switch (agency) {
      case 'STUDIO':
        return {
          primary: 'from-blue-50 to-indigo-50',
          border: 'border-blue-200',
          accent: 'text-blue-600',
          badge: 'bg-blue-100 text-blue-800'
        };
      case 'CNM':
        return {
          primary: 'from-purple-50 to-pink-50',
          border: 'border-purple-200',
          accent: 'text-purple-600',
          badge: 'bg-purple-100 text-purple-800'
        };
      default:
        return {
          primary: 'from-teal-50 to-blue-50',
          border: 'border-teal-200',
          accent: 'text-teal-600',
          badge: 'bg-teal-100 text-teal-800'
        };
    }
  };

  const agencyColors = getAgencyColors(currentAgency);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Access denied state
  if (!currentUser || !currentAgency || !canAccessAgency(currentAgency)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-4">You don't have access to this agency dashboard.</p>
          <button
            onClick={() => router.push('/subadmin/select-agency')}
            className="bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700"
          >
            Select Agency
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className={`bg-gradient-to-r ${agencyColors.primary} ${agencyColors.border} border-b`}>
        <div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                {currentAgency} Dashboard
              </h1>
              <p className="text-gray-600 mt-1">
                Subadmin Panel - Managing {currentAgency} operations
              </p>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="text-center">
                <div className={`text-2xl font-bold ${agencyColors.accent}`}>
                  {currentAgency}
                </div>
                <div className="text-sm text-gray-500">
                  Your Agency
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Success message */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 border-l-4 border-green-500 text-green-700 rounded-md flex items-center shadow-sm">
            <svg className="h-5 w-5 mr-3 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>{successMessage}</span>
          </div>
        )}

        {/* Error message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 text-red-700 rounded-md flex items-center shadow-sm">
            <svg className="h-5 w-5 mr-3 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <span>{error}</span>
          </div>
        )}

        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          {/* Tabs */}
          <div className="flex bg-white text-md rounded-xl shadow-md overflow-hidden border border-gray-200 mb-4 md:mb-0">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`px-9 py-2 font-medium transition-colors relative ${activeTab === 'dashboard'
                ? 'text-teal-600 bg-teal-50'
                : 'text-gray-600 hover:bg-slate-200'
                }`}
            >
              Dashboard
              {activeTab === 'dashboard' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500 to-blue-500"></div>
              )}
            </button>
            <button
              onClick={() => setActiveTab('pending')}
              className={`px-9 py-2 font-medium transition-colors relative ${activeTab === 'pending'
                ? 'text-teal-600 bg-teal-50'
                : 'text-gray-600 hover:bg-slate-200'
                }`}
            >
              Pending Approvals
              {agencyPendingUsers.length > 0 && (
                <span className="ml-2 px-2 py-0.5 text-xs font-semibold rounded-full bg-amber-100 text-amber-700">{agencyPendingUsers.length}</span>
              )}
              {activeTab === 'pending' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500 to-blue-500"></div>
              )}
            </button>
            <button
              onClick={() => setActiveTab('users')}
              className={`px-9 py-3 font-medium transition-colors relative ${activeTab === 'users'
                ? 'text-teal-600 bg-teal-50'
                : 'text-gray-600 hover:bg-slate-200'
                }`}
            >
              Users
              {activeTab === 'users' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500 to-blue-500"></div>
              )}
            </button>
            <button
              onClick={() => setActiveTab('providers')}
              className={`px-9 py-3 font-medium transition-colors relative ${activeTab === 'providers'
                ? 'text-teal-600 bg-teal-50'
                : 'text-gray-600 hover:bg-slate-200'
                }`}
            >
              Providers
              {activeTab === 'providers' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500 to-blue-500"></div>
              )}
            </button>
            <button
              onClick={() => setActiveTab('processed')}
              className={`px-9 py-3 font-medium transition-colors relative ${activeTab === 'processed'
                ? 'text-teal-600 bg-teal-50'
                : 'text-gray-600 hover:bg-slate-200'
                }`}
            >
              Processed Request
              {activeTab === 'processed' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500 to-blue-500"></div>
              )}
            </button>
          </div>

          <div>
            {activeTab === 'processed' ? (
              // Separate refresh button for processed tab
              <button
                onClick={fetchTrackingData}
                disabled={isLoadingTracking}
                style={{ background: "linear-gradient(to right, #3498db, #2c3e50)" }}
                className="text-white px-5 py-2.5 rounded-lg shadow-sm flex items-center gap-2 hover:shadow-md transition-all disabled:opacity-70 disabled:cursor-not-allowed"
              >
                {isLoadingTracking ? (
                  <>
                    <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Refreshing...
                  </>
                ) : (
                  <>
                    <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Refresh
                  </>
                )}
              </button>
            ) : (
              // Common refresh button for other tabs
              <button
                onClick={fetchData}
                disabled={isLoading}
                style={{ background: "linear-gradient(to right, #3498db, #2c3e50)" }}
                className="text-white px-5 py-2.5 rounded-lg shadow-md flex items-center gap-2 hover:shadow-gray-500 transition-all disabled:opacity-70 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Refreshing...
                  </>
                ) : (
                  <>
                    <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Refresh
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Tab content */}
      <div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8">
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            {/* Welcome Section */}
            <div className={`bg-gradient-to-r ${agencyColors.primary} rounded-xl p-6 ${agencyColors.border} border`}>
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-800">
                    Welcome, {currentUser?.full_name}
                  </h2>
                  <p className="text-gray-600 mt-1">
                    Managing {currentAgency} operations
                  </p>
                </div>
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${agencyColors.accent}`}>
                      {currentAgency}
                    </div>
                    <div className="text-sm text-gray-500">
                      Your Agency
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Users Under Management */}
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Agency Users</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {agencyUsers.length}
                    </p>
                  </div>
                  <div className="bg-blue-100 rounded-lg p-3">
                    <svg className="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a1.5 1.5 0 01-3 0V5.196a1.5 1.5 0 013 0v3.093z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <div className="text-sm text-gray-500">
                    in {currentAgency} agency
                  </div>
                </div>
              </div>

              {/* Pending Approvals */}
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {agencyPendingUsers.length}
                    </p>
                  </div>
                  <div className="bg-amber-100 rounded-lg p-3">
                    <svg className="h-6 w-6 text-amber-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <div className="text-sm text-gray-500">
                    awaiting review
                  </div>
                </div>
              </div>

              {/* Agency Providers */}
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Agency Providers</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {agencyProviders.length}
                    </p>
                  </div>
                  <div className="bg-green-100 rounded-lg p-3">
                    <svg className="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <div className="text-sm text-gray-500">
                    active: {agencyProviders.filter(p => p.status === 'active').length}
                  </div>
                </div>
              </div>

              {/* Active Providers */}
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Providers</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {agencyProviders.filter(p => p.status === 'active').length}
                    </p>
                  </div>
                  <div className="bg-teal-100 rounded-lg p-3">
                    <svg className="h-6 w-6 text-teal-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <div className="text-sm text-gray-500">
                    of {agencyProviders.length} total
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <div className={`w-2 h-2 rounded-full ${agencyColors.accent.replace('text-', 'bg-')} mr-2`}></div>
                Quick Actions
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button
                  onClick={() => setActiveTab('pending')}
                  className="bg-amber-50 hover:bg-amber-100 border border-amber-200 rounded-lg p-4 text-left transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-amber-700">Review Pending</div>
                      <div className="text-sm text-amber-600">{agencyPendingUsers.length} awaiting</div>
                    </div>
                    <svg className="h-5 w-5 text-amber-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>

                <button
                  onClick={() => setActiveTab('users')}
                  className="bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg p-4 text-left transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-blue-700">Manage Users</div>
                      <div className="text-sm text-blue-600">{agencyUsers.length} in agency</div>
                    </div>
                    <svg className="h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>

                <button
                  onClick={() => setActiveTab('providers')}
                  className="bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg p-4 text-left transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-green-700">Manage Providers</div>
                      <div className="text-sm text-green-600">{agencyProviders.length} available</div>
                    </div>
                    <svg className="h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>

                <button
                  onClick={() => setActiveTab('processed')}
                  className="bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg p-4 text-left transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-purple-700">View Analytics</div>
                      <div className="text-sm text-purple-600">Request data</div>
                    </div>
                    <svg className="h-5 w-5 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
              </div>
            </div>

            {/* Agency Information */}
            <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <div className={`w-2 h-2 rounded-full ${agencyColors.accent.replace('text-', 'bg-')} mr-2`}></div>
                {currentAgency} Agency Overview
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Agency Details */}
                <div>
                  <h4 className="font-medium text-gray-800 mb-3">Agency Details</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Agency Name:</span>
                      <span className={`font-medium ${agencyColors.accent}`}>{currentAgency}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Users:</span>
                      <span className="font-medium">{agencyUsers.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Active Providers:</span>
                      <span className="font-medium">{agencyProviders.filter(p => p.status === 'active').length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Pending Approvals:</span>
                      <span className="font-medium">{agencyPendingUsers.length}</span>
                    </div>
                  </div>
                </div>

                {/* Provider List */}
                <div>
                  <h4 className="font-medium text-gray-800 mb-3">Available Providers</h4>
                  <div className="space-y-2">
                    {agencyProviders.map(provider => (
                      <div key={provider.provider_id} className="flex justify-between items-center">
                        <span className="text-gray-600">{provider.name}</span>
                        <span className={`px-2 py-1 text-xs font-medium rounded ${
                          provider.status === 'active' ? 'bg-green-100 text-green-800' :
                          provider.status === 'coming_soon' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {provider.status}
                        </span>
                      </div>
                    ))}
                    {agencyProviders.length === 0 && (
                      <p className="text-gray-500 text-sm">No providers configured for this agency</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Pending Approvals Tab */}
        {activeTab === 'pending' && (
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-md border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800">
                  Pending User Approvals - {currentAgency}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  Review and approve user registrations for {currentAgency} agency
                </p>
              </div>

              <div className="p-6">
                {agencyPendingUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <svg className="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No pending approvals</h3>
                    <p className="mt-1 text-sm text-gray-500">All user registrations for {currentAgency} have been processed.</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {agencyPendingUsers.map((user) => (
                      <div key={user.user_id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <h4 className="text-lg font-medium text-gray-900">{user.full_name}</h4>
                              <span className={`px-2 py-1 text-xs font-medium rounded ${agencyColors.badge}`}>
                                {user.agency}
                              </span>
                            </div>
                            <div className="mt-2 space-y-1">
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Username:</span> {user.username}
                              </p>
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Email:</span> {user.email}
                              </p>
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Requested Role:</span> {user.role}
                              </p>
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Registration Date:</span> {new Date(user.created_at).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Approval Configuration */}
                        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                          {/* Role Selection */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Assign Role</label>
                            <select
                              value={userApprovalData[user.user_id]?.role || 'user'}
                              onChange={(e) => updateUserApprovalData(user.user_id, 'role', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                              <option value="user">User</option>
                              <option value="sub_admin">Sub Admin</option>
                            </select>
                          </div>

                          {/* Portal Access */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Portal Access</label>
                            <select
                              value={userApprovalData[user.user_id]?.portalAccess?.[0] || 'portalA'}
                              onChange={(e) => updateUserApprovalData(user.user_id, 'portalAccess', [e.target.value])}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                              <option value="portalA">Portal A</option>
                              <option value="portalB">Portal B</option>
                            </select>
                          </div>

                          {/* Provider Access */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Provider Access</label>
                            <select
                              multiple
                              value={userApprovalData[user.user_id]?.providerAccess || []}
                              onChange={(e) => {
                                const selectedProviders = Array.from(e.target.selectedOptions, option => option.value);
                                updateUserApprovalData(user.user_id, 'providerAccess', selectedProviders);
                              }}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                              size={Math.min(agencyProviders.length, 4)}
                            >
                              {agencyProviders.map(provider => (
                                <option key={provider.provider_id} value={provider.name}>
                                  {provider.description || provider.name}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="mt-6 flex space-x-3">
                          <button
                            onClick={() => handleApproveUser(user.user_id)}
                            disabled={isLoading}
                            className="bg-green-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-green-700 transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
                          >
                            Approve User
                          </button>
                          <button
                            onClick={() => handleRejectUser(user.user_id)}
                            disabled={isLoading}
                            className="bg-red-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-red-700 transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
                          >
                            Reject User
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Users Management Tab */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-md border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800">
                  User Management - {currentAgency}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  Manage users in {currentAgency} agency
                </p>
              </div>

              <div className="p-6">
                {agencyUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <svg className="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a1.5 1.5 0 01-3 0V5.196a1.5 1.5 0 013 0v3.093z" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No users found</h3>
                    <p className="mt-1 text-sm text-gray-500">No users are currently registered for {currentAgency} agency.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {agencyUsers.map((user) => (
                      <div key={user.user_id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <h4 className="text-lg font-medium text-gray-900">{user.full_name}</h4>
                              <span className={`px-2 py-1 text-xs font-medium rounded ${agencyColors.badge}`}>
                                {user.agency}
                              </span>
                              <span className={`px-2 py-1 text-xs font-medium rounded ${
                                user.role === 'admin' ? 'bg-red-100 text-red-800' :
                                user.role === 'sub_admin' ? 'bg-purple-100 text-purple-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {user.role}
                              </span>
                              <span className={`px-2 py-1 text-xs font-medium rounded ${
                                user.status === 'approved' ? 'bg-green-100 text-green-800' :
                                user.status === 'suspended' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {user.status}
                              </span>
                            </div>
                            <div className="mt-2 space-y-1">
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Username:</span> {user.username}
                              </p>
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Email:</span> {user.email}
                              </p>
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Last Login:</span> {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* User Management Actions */}
                        <div className="mt-6 flex flex-wrap gap-3">
                          {/* Role Change */}
                          <select
                            value={user.role}
                            onChange={(e) => {
                              const newRole = e.target.value as 'admin' | 'sub_admin' | 'user';
                              const roleCheck = canChangeRole(user.user_id, user.role, newRole, currentUser);
                              if (!roleCheck.canChange) {
                                setError(roleCheck.reason || 'Role change not allowed');
                                return;
                              }
                              handleUpdateRole(user.user_id, newRole);
                            }}
                            disabled={isLoading || user.role === 'admin'}
                            className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <option value="user">User</option>
                            <option value="sub_admin">Sub Admin</option>
                          </select>

                          {/* Suspend/Unsuspend */}
                          {user.status !== 'suspended' ? (
                            <button
                              onClick={() => handleSuspendUser(user.user_id, true)}
                              disabled={isLoading || user.role === 'admin'}
                              className="bg-yellow-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-yellow-700 transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
                            >
                              Suspend
                            </button>
                          ) : (
                            <button
                              onClick={() => handleSuspendUser(user.user_id, false)}
                              disabled={isLoading}
                              className="bg-green-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-green-700 transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
                            >
                              Unsuspend
                            </button>
                          )}

                          {/* Delete User */}
                          <button
                            onClick={() => handleDeleteUser(user.user_id)}
                            disabled={isLoading || user.role === 'admin'}
                            title={user.role === 'admin' ? 'Admin accounts cannot be deleted' : 'Delete user'}
                            className={`text-white px-4 py-2 rounded-md shadow-sm transition-colors ${
                              user.role === 'admin' ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700'
                            } disabled:opacity-70 disabled:cursor-not-allowed`}
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Providers Management Tab */}
        {activeTab === 'providers' && (
          <div className="space-y-6">
            {/* Add New Provider */}
            <div className="bg-white rounded-xl shadow-md border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800">
                  Add New Provider - {currentAgency}
                </h3>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Provider Name</label>
                    <input
                      type="text"
                      value={newProvider.name}
                      onChange={(e) => setNewProvider({ ...newProvider, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      placeholder="Enter provider name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <input
                      type="text"
                      value={newProvider.description}
                      onChange={(e) => setNewProvider({ ...newProvider, description: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      placeholder="Enter description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      value={newProvider.status}
                      onChange={(e) => setNewProvider({ ...newProvider, status: e.target.value as 'active' | 'coming_soon' | 'maintenance' })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    >
                      <option value="active">Active</option>
                      <option value="coming_soon">Coming Soon</option>
                      <option value="maintenance">Maintenance</option>
                    </select>
                  </div>

                  <div className="flex items-end">
                    <button
                      onClick={handleCreateProvider}
                      disabled={isLoading}
                      className="w-full bg-teal-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-teal-700 transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
                    >
                      Add Provider
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Existing Providers */}
            <div className="bg-white rounded-xl shadow-md border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800">
                  {currentAgency} Providers
                </h3>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agency</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {agencyProviders.map((provider) => (
                      <tr key={provider.provider_id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-800">{provider.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-medium rounded ${agencyColors.badge}`}>
                            {provider.agency}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-600">{provider.description}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            value={provider.status}
                            onChange={(e) => handleUpdateProviderStatus(provider.provider_id, e.target.value as 'active' | 'coming_soon' | 'maintenance')}
                            disabled={isLoading}
                            className={`px-2 py-1 text-xs font-medium rounded border-0 focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                              provider.status === 'active' ? 'bg-green-100 text-green-800' :
                              provider.status === 'coming_soon' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}
                          >
                            <option value="active">Active</option>
                            <option value="coming_soon">Coming Soon</option>
                            <option value="maintenance">Maintenance</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleDeleteProvider(provider.provider_id)}
                            disabled={isLoading}
                            className="text-red-600 hover:text-red-900 disabled:opacity-70 disabled:cursor-not-allowed"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {agencyProviders.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No providers found for {currentAgency} agency</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Processed Requests Tab */}
        {activeTab === 'processed' && (
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-md border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800">
                  Processed Requests - {currentAgency}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  View analytics and processed request data for {currentAgency} providers
                </p>
              </div>

              <div className="p-6">
                {trackingData.length === 0 ? (
                  <div className="text-center py-8">
                    <svg className="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No processed requests</h3>
                    <p className="mt-1 text-sm text-gray-500">No processed request data available for {currentAgency} providers.</p>
                    <button
                      onClick={fetchTrackingData}
                      className="mt-4 bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700"
                    >
                      Refresh Data
                    </button>
                  </div>
                ) : (
                  <ProcessedRequestAnalytics
                    data={trackingData}
                    isLoading={isLoadingTracking}
                  />
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Result View Modal */}
      {resultModalOpen && selectedResult && (
        <ResultsViewModal
          open={resultModalOpen}
          onClose={handleCloseResultModal}
          provider={selectedResult.provider}
          requestId={selectedResult.requestId}
          sessionId={selectedResult.sessionId}
        />
      )}
    </div>
  );
}
