'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '../../../context/AuthContext';
import { User, Provider } from '../../../services/auth';

// Types for subadmin dashboard
type TabType = 'dashboard' | 'pending' | 'users' | 'providers' | 'analytics';

interface SubadminDashboardData {
  users: User[];
  providers: Provider[];
  pending_users: User[];
  stats: {
    total_users: number;
    total_providers: number;
    pending_approvals: number;
    active_users: number;
  };
}

interface AnalyticsData {
  agency: string;
  providers: string[];
  booking_stats: Record<string, any>;
  performance_metrics: Record<string, any>;
}

export default function AgencyDashboard() {
  const router = useRouter();
  const params = useParams();
  const agency = params?.agency as string;
  const { user, isSubAdminOnly, canAccessAgency, loading } = useAuth();

  // State management
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [dashboardData, setDashboardData] = useState<SubadminDashboardData | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Validate agency access
  useEffect(() => {
    if (!loading) {
      if (!isSubAdminOnly) {
        router.push('/admin');
        return;
      }

      if (!agency || !canAccessAgency(agency.toUpperCase())) {
        router.push('/subadmin');
        return;
      }
    }
  }, [loading, isSubAdminOnly, agency, canAccessAgency, router]);

  // Fetch dashboard data
  const fetchDashboardData = useCallback(async () => {
    if (!agency) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/subadmin/dashboard/${agency.toUpperCase()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }

      const result = await response.json();
      if (result.success) {
        setDashboardData(result.data);
      } else {
        throw new Error(result.message || 'Failed to fetch dashboard data');
      }
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  }, [agency]);

  // Fetch analytics data
  const fetchAnalyticsData = useCallback(async () => {
    if (!agency) return;

    try {
      const response = await fetch(`/api/subadmin/analytics/${agency.toUpperCase()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const result = await response.json();
      if (result.success) {
        setAnalyticsData(result.data);
      }
    } catch (err) {
      console.error('Error fetching analytics data:', err);
    }
  }, [agency]);

  // Load data on mount and when agency changes
  useEffect(() => {
    if (agency && isSubAdminOnly && canAccessAgency(agency.toUpperCase())) {
      fetchDashboardData();
      if (activeTab === 'analytics') {
        fetchAnalyticsData();
      }
    }
  }, [agency, isSubAdminOnly, canAccessAgency, fetchDashboardData, fetchAnalyticsData, activeTab]);

  // Agency-specific styling
  const getAgencyTheme = useMemo(() => {
    switch (agency?.toUpperCase()) {
      case 'STUDIO':
        return {
          primary: 'blue',
          gradient: 'from-blue-50 to-indigo-50',
          border: 'border-blue-200',
          text: 'text-blue-600',
          bg: 'bg-blue-50',
          button: 'bg-blue-600 hover:bg-blue-700',
        };
      case 'CNM':
        return {
          primary: 'purple',
          gradient: 'from-purple-50 to-violet-50',
          border: 'border-purple-200',
          text: 'text-purple-600',
          bg: 'bg-purple-50',
          button: 'bg-purple-600 hover:bg-purple-700',
        };
      case 'HP':
        return {
          primary: 'orange',
          gradient: 'from-orange-50 to-amber-50',
          border: 'border-orange-200',
          text: 'text-orange-600',
          bg: 'bg-orange-50',
          button: 'bg-orange-600 hover:bg-orange-700',
        };
      default:
        return {
          primary: 'teal',
          gradient: 'from-teal-50 to-blue-50',
          border: 'border-teal-200',
          text: 'text-teal-600',
          bg: 'bg-teal-50',
          button: 'bg-teal-600 hover:bg-teal-700',
        };
    }
  }, [agency]);

  // Error and success message handlers
  const showSuccess = (message: string) => {
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  const handleError = (err: any, message: string) => {
    console.error(`${message}:`, err);
    setError(message);
    setTimeout(() => setError(null), 5000);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isSubAdminOnly || !agency || !canAccessAgency(agency.toUpperCase())) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {agency?.toUpperCase()} Dashboard
              </h1>
              <p className="text-gray-600">Agency-specific management portal</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/subadmin')}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                ← Back to Agency Selection
              </button>
              <div className="text-right">
                <p className="text-sm text-gray-500">Welcome,</p>
                <p className="font-medium text-gray-900">{user?.full_name}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Error and Success Messages */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-800">{successMessage}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6">
        <div className="flex bg-white text-md rounded-xl shadow-md overflow-hidden border border-gray-200 mb-6">
          {[
            { id: 'dashboard', label: 'Dashboard' },
            { id: 'pending', label: 'Pending Approvals', count: dashboardData?.pending_users?.length },
            { id: 'users', label: 'Users' },
            { id: 'providers', label: 'Providers' },
            { id: 'analytics', label: 'Analytics' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as TabType)}
              className={`px-6 py-3 font-medium transition-colors relative ${
                activeTab === tab.id
                  ? `${getAgencyTheme.text} ${getAgencyTheme.bg}`
                  : 'text-gray-600 hover:bg-slate-200'
              }`}
            >
              {tab.label}
              {tab.count && tab.count > 0 && (
                <span className="ml-2 px-2 py-0.5 text-xs font-semibold rounded-full bg-amber-100 text-amber-700">
                  {tab.count}
                </span>
              )}
              {activeTab === tab.id && (
                <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-${getAgencyTheme.primary}-500 to-${getAgencyTheme.primary}-600`}></div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        {/* Content will be added in the next step */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <p className="text-gray-600">
            {activeTab === 'dashboard' && 'Dashboard content will be implemented here'}
            {activeTab === 'pending' && 'Pending approvals content will be implemented here'}
            {activeTab === 'users' && 'Users management content will be implemented here'}
            {activeTab === 'providers' && 'Providers management content will be implemented here'}
            {activeTab === 'analytics' && 'Analytics content will be implemented here'}
          </p>
        </div>
      </div>
    </div>
  );
}
