{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts"], "names": ["CacheStates", "fetchServerResponse", "createHrefFromUrl", "invalidateCacheBelowFlightSegmentPath", "fillCacheWithDataProperty", "applyRouterStatePatchToTree", "shouldHardNavigate", "isNavigatingToNewRootLayout", "PrefetchKind", "handleMutable", "applyFlightData", "PrefetchCacheEntryStatus", "getPrefetchEntryCacheStatus", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "prefetchQueue", "createEmptyCacheNode", "handleExternalUrl", "state", "mutable", "url", "pendingPush", "mpaNavigation", "canonicalUrl", "scrollableSegments", "undefined", "generateSegmentsFromPatch", "flightRouterPatch", "segments", "segment", "parallelRoutes", "Object", "keys", "length", "parallelRouteKey", "parallelRoute", "entries", "childSegment", "push", "addRefetchToLeafSegments", "newCache", "currentCache", "flightSegmentPath", "treePatch", "data", "appliedPatch", "status", "READY", "subTreeData", "Map", "segmentPathsToFill", "map", "segmentPaths", "navigateReducer", "action", "isExternalUrl", "navigateType", "shouldScroll", "hash", "href", "prefetchCache", "preserveCustomHistoryState", "toString", "prefetchValues", "get", "tree", "nextUrl", "buildId", "process", "env", "NODE_ENV", "AUTO", "newPrefetchValue", "kind", "TEMPORARY", "prefetchTime", "Date", "now", "treeAtTimeOfPrefetch", "lastUsedTime", "set", "prefetchEntryCacheStatus", "bump", "then", "flightData", "canonicalUrlOverride", "postponed", "currentTree", "cache", "flightDataPath", "slice", "flightSegmentPathWithLeadingEmpty", "newTree", "applied", "reusable", "stale", "hardNavigate", "subSegment", "scrollableSegmentPath", "patchedTree", "hashFragment"], "mappings": "AAAA,SAASA,WAAW,QAAQ,2DAA0D;AAMtF,SAASC,mBAAmB,QAAQ,2BAA0B;AAE9D,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,qCAAqC,QAAQ,+CAA8C;AACpG,SAASC,yBAAyB,QAAQ,mCAAkC;AAC5E,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,YAAY,QAAQ,0BAAyB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SACEC,wBAAwB,EACxBC,2BAA2B,QACtB,qCAAoC;AAC3C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,oBAAoB,QAAQ,mBAAkB;AAEvD,OAAO,SAASC,kBACdC,KAA2B,EAC3BC,OAAgB,EAChBC,GAAW,EACXC,WAAoB;IAEpBF,QAAQG,aAAa,GAAG;IACxBH,QAAQI,YAAY,GAAGH;IACvBD,QAAQE,WAAW,GAAGA;IACtBF,QAAQK,kBAAkB,GAAGC;IAE7B,OAAOf,cAAcQ,OAAOC;AAC9B;AAEA,SAASO,0BACPC,iBAAoC;IAEpC,MAAMC,WAAgC,EAAE;IACxC,MAAM,CAACC,SAASC,eAAe,GAAGH;IAElC,IAAII,OAAOC,IAAI,CAACF,gBAAgBG,MAAM,KAAK,GAAG;QAC5C,OAAO;YAAC;gBAACJ;aAAQ;SAAC;IACpB;IAEA,KAAK,MAAM,CAACK,kBAAkBC,cAAc,IAAIJ,OAAOK,OAAO,CAC5DN,gBACC;QACD,KAAK,MAAMO,gBAAgBX,0BAA0BS,eAAgB;YACnE,mEAAmE;YACnE,IAAIN,YAAY,IAAI;gBAClBD,SAASU,IAAI,CAAC;oBAACJ;uBAAqBG;iBAAa;YACnD,OAAO;gBACLT,SAASU,IAAI,CAAC;oBAACT;oBAASK;uBAAqBG;iBAAa;YAC5D;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASW,yBACPC,QAAmB,EACnBC,YAAuB,EACvBC,iBAAoC,EACpCC,SAA4B,EAC5BC,IAA8C;IAE9C,IAAIC,eAAe;IAEnBL,SAASM,MAAM,GAAG7C,YAAY8C,KAAK;IACnCP,SAASQ,WAAW,GAAGP,aAAaO,WAAW;IAC/CR,SAASV,cAAc,GAAG,IAAImB,IAAIR,aAAaX,cAAc;IAE7D,MAAMoB,qBAAqBxB,0BAA0BiB,WAAWQ,GAAG,CACjE,CAACtB,UAAY;eAAIa;eAAsBb;SAAQ;IAGjD,KAAK,MAAMuB,gBAAgBF,mBAAoB;QAC7C7C,0BAA0BmC,UAAUC,cAAcW,cAAcR;QAEhEC,eAAe;IACjB;IAEA,OAAOA;AACT;AACA,OAAO,SAASQ,gBACdnC,KAA2B,EAC3BoC,MAAsB;IAEtB,MAAM,EAAElC,GAAG,EAAEmC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAE,GAAGH;IAC3D,MAAMnC,UAAmB,CAAC;IAC1B,MAAM,EAAEuC,IAAI,EAAE,GAAGtC;IACjB,MAAMuC,OAAOxD,kBAAkBiB;IAC/B,MAAMC,cAAcmC,iBAAiB;IACrC,wFAAwF;IACxF1C,mBAAmBI,MAAM0C,aAAa;IAEtCzC,QAAQ0C,0BAA0B,GAAG;IAErC,IAAIN,eAAe;QACjB,OAAOtC,kBAAkBC,OAAOC,SAASC,IAAI0C,QAAQ,IAAIzC;IAC3D;IAEA,IAAI0C,iBAAiB7C,MAAM0C,aAAa,CAACI,GAAG,CAAC7D,kBAAkBiB,KAAK;IAEpE,2DAA2D;IAC3D,IAAI,CAAC2C,gBAAgB;QACnB,MAAMnB,OAAO1C,oBACXkB,KACAF,MAAM+C,IAAI,EACV/C,MAAMgD,OAAO,EACbhD,MAAMiD,OAAO,EACb,8EAA8E;QAC9E,0DAA0D;QAC1DC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB7D,aAAa8D,IAAI,GAAG9C;QAG/D,MAAM+C,mBAAmB;YACvB5B;YACA,iEAAiE;YACjE6B,MACEL,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrB7D,aAAa8D,IAAI,GACjB9D,aAAaiE,SAAS;YAC5BC,cAAcC,KAAKC,GAAG;YACtBC,sBAAsB5D,MAAM+C,IAAI;YAChCc,cAAc;QAChB;QAEA7D,MAAM0C,aAAa,CAACoB,GAAG,CAAC7E,kBAAkBiB,KAAK,QAAQoD;QACvDT,iBAAiBS;IACnB;IAEA,MAAMS,2BAA2BpE,4BAA4BkD;IAE7D,0DAA0D;IAC1D,MAAM,EAAEe,oBAAoB,EAAElC,IAAI,EAAE,GAAGmB;IAEvChD,cAAcmE,IAAI,CAACtC;IAEnB,OAAOA,KAAMuC,IAAI,CACf;YAAC,CAACC,YAAYC,sBAAsBC,UAAU;QAC5C,iCAAiC;QACjC,IAAIvB,kBAAkB,CAACA,eAAegB,YAAY,EAAE;YAClD,gGAAgG;YAChGhB,eAAegB,YAAY,GAAGH,KAAKC,GAAG;QACxC;QAEA,4DAA4D;QAC5D,IAAI,OAAOO,eAAe,UAAU;YAClC,OAAOnE,kBAAkBC,OAAOC,SAASiE,YAAY/D;QACvD;QAEA,IAAIkE,cAAcrE,MAAM+C,IAAI;QAC5B,IAAIxB,eAAevB,MAAMsE,KAAK;QAC9B,IAAIhE,qBAA0C,EAAE;QAChD,KAAK,MAAMiE,kBAAkBL,WAAY;YACvC,MAAM1C,oBAAoB+C,eAAeC,KAAK,CAC5C,GACA,CAAC;YAEH,0DAA0D;YAC1D,MAAM/C,YAAY8C,eAAeC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;YAE7C,sBAAsB;YACtB,MAAMC,oCAAoC;gBAAC;mBAAOjD;aAAkB;YAEpE,wEAAwE;YACxE,IAAIkD,UAAUtF,4BACZ,sBAAsB;YACtBqF,mCACAJ,aACA5C;YAGF,kGAAkG;YAClG,6IAA6I;YAC7I,IAAIiD,YAAY,MAAM;gBACpBA,UAAUtF,4BACR,sBAAsB;gBACtBqF,mCACAb,sBACAnC;YAEJ;YAEA,IAAIiD,YAAY,MAAM;gBACpB,IAAIpF,4BAA4B+E,aAAaK,UAAU;oBACrD,OAAO3E,kBAAkBC,OAAOC,SAASwC,MAAMtC;gBACjD;gBAEA,MAAMmE,QAAmBxE;gBACzB,IAAI6E,UAAUlF,gBACZ8B,cACA+C,OACAC,gBACA1B,CAAAA,kCAAAA,eAAgBU,IAAI,MAAK,UACvBQ,6BAA6BrE,yBAAyBkF,QAAQ;gBAGlE,IACE,AAAC,CAACD,WACAZ,6BAA6BrE,yBAAyBmF,KAAK,IAC7D,qEAAqE;gBACrE,6DAA6D;gBAC7DT,WACA;oBACAO,UAAUtD,yBACRiD,OACA/C,cACAC,mBACAC,WACA,wCAAwC;oBACxC,IACEzC,oBACEkB,KACAmE,aACArE,MAAMgD,OAAO,EACbhD,MAAMiD,OAAO;gBAGrB;gBAEA,MAAM6B,eAAezF,mBACnB,sBAAsB;gBACtBoF,mCACAJ;gBAGF,IAAIS,cAAc;oBAChBR,MAAM1C,MAAM,GAAG7C,YAAY8C,KAAK;oBAChC,mDAAmD;oBACnDyC,MAAMxC,WAAW,GAAGP,aAAaO,WAAW;oBAE5C5C,sCACEoF,OACA/C,cACAC;oBAEF,8EAA8E;oBAC9EvB,QAAQqE,KAAK,GAAGA;gBAClB,OAAO,IAAIK,SAAS;oBAClB1E,QAAQqE,KAAK,GAAGA;gBAClB;gBAEA/C,eAAe+C;gBACfD,cAAcK;gBAEd,KAAK,MAAMK,cAAcvE,0BAA0BiB,WAAY;oBAC7D,MAAMuD,wBAAwB;2BAAIxD;2BAAsBuD;qBAAW;oBACnE,kFAAkF;oBAClF,IACEC,qBAAqB,CAACA,sBAAsBjE,MAAM,GAAG,EAAE,KACvD,eACA;wBACAT,mBAAmBc,IAAI,CAAC4D;oBAC1B;gBACF;YACF;QACF;QAEA/E,QAAQgF,WAAW,GAAGZ;QACtBpE,QAAQI,YAAY,GAAG8D,uBACnBlF,kBAAkBkF,wBAClB1B;QACJxC,QAAQE,WAAW,GAAGA;QACtBF,QAAQK,kBAAkB,GAAGA;QAC7BL,QAAQiF,YAAY,GAAG1C;QACvBvC,QAAQsC,YAAY,GAAGA;QAEvB,OAAO/C,cAAcQ,OAAOC;IAC9B,GACA,IAAMD;AAEV"}