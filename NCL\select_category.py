from loguru import logger
import async<PERSON>
from NCL.cabin_categories import CabinCategories
from NCL.utils import NCLUtils
from Core.ui_logger import ui_log


class CategorySelector:

    @staticmethod
    def find_category_by_name(all_category_data, preferred_category):
        """
        Find a category by name using exact and fuzzy matching on description text.
        
        Args:
            all_category_data: List of category dictionaries
            preferred_category: The category name to search for (e.g., "large balcony")
            
        Returns:
            Tuple of (category_index, category_data) or (None, None) if not found
        """
        if not preferred_category or not all_category_data:
            return None, None
            
        # Normalize the search term
        search_term = preferred_category.lower().strip()
        
        # First, try exact matches by category code (case-insensitive)
        for i, category in enumerate(all_category_data):
            category_code = category.get('code', '').lower()
            if search_term == category_code:
                logger.info(f"Found exact category code match for '{preferred_category}': {category['full_text']}")
                return i, category
        
        # Second, try exact matches in description (highest priority for descriptions)
        for i, category in enumerate(all_category_data):
            description = category.get('description', '').lower()
            if search_term == description:
                logger.info(f"Found exact description match for '{preferred_category}': {category['full_text']}")
                return i, category
        
        # Third, try exact matches as complete words within description
        import re
        for i, category in enumerate(all_category_data):
            description = category.get('description', '').lower()
            # Use word boundaries to match complete words only
            pattern = r'\b' + re.escape(search_term) + r'\b'
            if re.search(pattern, description):
                logger.info(f"Found exact word match for '{preferred_category}': {category['full_text']}")
                return i, category
        
        # Fourth, try partial matches in category code (prefix matching)
        for i, category in enumerate(all_category_data):
            category_code = category.get('code', '').lower()
            if category_code.startswith(search_term):
                logger.info(f"Found category code prefix match for '{preferred_category}': {category['full_text']}")
                return i, category
        
        # Fifth, try substring matches (what was previously called "exact matches")
        for i, category in enumerate(all_category_data):
            description = category.get('description', '').lower()
            if search_term in description:
                logger.info(f"Found substring match for '{preferred_category}': {category['full_text']}")
                return i, category
        
        # Sixth, try partial matches on individual words
        search_words = search_term.split()
        best_match_score = 0
        best_match_index = None
        best_match_category = None
        
        for i, category in enumerate(all_category_data):
            description = category.get('description', '').lower()
            # Count how many search words are found in the description
            matches = sum(1 for word in search_words if word in description)
            match_score = matches / len(search_words)  # Percentage of words matched
            
            if match_score > best_match_score and match_score > 0.5:  # At least 50% match
                best_match_score = match_score
                best_match_index = i
                best_match_category = category
        
        if best_match_category:
            logger.info(f"Found partial match for '{preferred_category}': {best_match_category['full_text']} (score: {best_match_score:.2f})")
            return best_match_index, best_match_category
        
        # Finally, try to find by cabin type
        cabin_type_mapping = {
            'inside': 'inside',
            'interior': 'inside',
            'oceanview': 'outside',
            'ocean view': 'outside',
            'outside': 'outside',
            'balcony': 'balcony',
            'suite': 'suite',
            'mini-suite': 'mini-suite',
            'haven': 'haven'
        }
        
        for cabin_type, category_type in cabin_type_mapping.items():
            if cabin_type in search_term:
                matching_categories = [cat for cat in all_category_data if cat.get('cabin_type') == category_type]
                if matching_categories:
                    # Find the cheapest option in this category type
                    cheapest = min(matching_categories, key=lambda x: x.get('price_value', float('inf')))
                    category_index = all_category_data.index(cheapest)
                    logger.info(f"Found category by type '{cabin_type}': {cheapest['full_text']}")
                    return category_index, cheapest
        
        logger.warning(f"No matching category found for '{preferred_category}'")
        return None, None

    @staticmethod
    async def display_available_categories(
        page, preferred_category=None, session_id=None, cabin_id=None
    ):
        try:
            await asyncio.sleep(2)
            logger.info("Extracting category data from hidden input JSON")

            cabin_categories = CabinCategories()
            CABIN_RATE_CODES = cabin_categories.CABIN_RATE_CODES

            await page.wait_for_selector("#SWXMLForm_SelectCategory_category", timeout=10000)

            hidden_input = page.locator("input[type='hidden'][name='category']").first

            if await hidden_input.count() == 0:
                logger.error("Hidden input with category data not found")
                return None, None

            json_value = await hidden_input.get_attribute("value")

            if not json_value:
                logger.error("No JSON data found in hidden input")
                return None, None

            import json
            try:
                category_data = json.loads(json_value)

                logger.info(f"Successfully parsed category JSON data")
                logger.debug(
                    f"JSON structure keys: {list(category_data.keys()) if isinstance(category_data, dict) else 'Not a dictionary'}"
                )

                all_category_data = []
                all_category_descriptions = []

                logger.info("\n==== AVAILABLE STATEROOM CATEGORIES ====")

                if isinstance(category_data, dict):

                    non_category_keys = ['value', 'idColumn', 'data']

                    if any(key in category_data for key in non_category_keys):

                        if 'data' in category_data and isinstance(category_data['data'],
                                                                  (list, dict)):
                            logger.info("Found categories in 'data' field")
                            categories = category_data['data']
                        else:
                            logger.warning(
                                "Could not locate category data in expected structure"
                            )
                            return None, None
                    else:
                        categories = category_data

                    if isinstance(categories, dict):
                        category_count = 0
                        for idx, (category_code,
                                  details) in enumerate(categories.items()):
                            if category_code in non_category_keys:
                                continue

                            try:
                                if isinstance(details, dict):

                                    description = details.get("Description", "N/A")

                                    price = "N/A"
                                    res_total = details.get(
                                        "ResTotal",
                                        details.get("ResTotal_details", "N/A")
                                    )
                                    quote_res_total = details.get(
                                        "QuoteResTotal",
                                        details.get("QuoteResTotal_details", "N/A")
                                    )

                                    if res_total and res_total != "N/A" and res_total != "UNDEFINED":
                                        price = res_total
                                    elif quote_res_total and quote_res_total != "N/A" and quote_res_total != "UNDEFINED":
                                        price = quote_res_total

                                    if price == "UNDEFINED":
                                        price = "N/A"

                                    price_value = float('inf')
                                    try:
                                        if isinstance(price, (int, float)):
                                            price_value = float(price)
                                            price = f"${price_value:,.2f}"
                                        elif isinstance(price, str) and price.replace(
                                                '.', '', 1).isdigit():
                                            price_value = float(
                                                price.replace('.', '', 1)
                                            )
                                            price = f"${price_value:,.2f}"
                                    except:
                                        pass

                                    cabin_type = "unknown"
                                    for category_type, codes in CABIN_RATE_CODES.items(
                                    ):
                                        if any(category_code.startswith(code)
                                               for code in codes):
                                            cabin_type = category_type
                                            break

                                    display_idx = len(all_category_data) + 1

                                    category_info = f"{display_idx}. {category_code} - {description} - Price: {price} - Type: {cabin_type}"
                                    logger.info(category_info)
                                    all_category_data.append(
                                        {
                                            'index': len(all_category_data), 'code':
                                            category_code, 'description': description,
                                            'price': price, 'price_value': price_value,
                                            'cabin_type': cabin_type, 'full_text':
                                            category_info, 'raw_data': details
                                        }
                                    )
                                    all_category_descriptions.append(
                                        f"{category_code} - {description}"
                                    )
                            except Exception as e:
                                logger.error(
                                    f"Error processing category {category_code}: {str(e)}"
                                )

                    elif isinstance(categories, list):
                        for idx, category in enumerate(categories):
                            try:
                                if not isinstance(category, dict):
                                    continue

                                category_code = category.get(
                                    'category',
                                    category.get(
                                        'Category',
                                        category.get(
                                            'code',
                                            category.get('Code', f"Unknown-{idx}")
                                        )
                                    )
                                )

                                description = category.get(
                                    'Description', category.get('description', "N/A")
                                )

                                price = "N/A"
                                res_total = category.get(
                                    "ResTotal",
                                    category.get(
                                        "ResTotal_details",
                                        category.get(
                                            "resTotal", category.get("price", "N/A")
                                        )
                                    )
                                )
                                quote_res_total = category.get(
                                    "QuoteResTotal",
                                    category.get("QuoteResTotal_details", "N/A")
                                )

                                if res_total and res_total != "N/A" and res_total != "UNDEFINED":
                                    price = res_total
                                elif quote_res_total and quote_res_total != "N/A" and quote_res_total != "UNDEFINED":
                                    price = quote_res_total

                                if price == "UNDEFINED":
                                    price = "N/A"

                                price_value = float('inf')
                                try:
                                    if isinstance(price, (int, float)):
                                        price_value = float(price)
                                        price = f"${price_value:,.2f}"
                                    elif isinstance(price, str) and price.replace(
                                            '.', '', 1).isdigit():
                                        price_value = float(price.replace('.', '', 1))
                                        price = f"${price_value:,.2f}"
                                except:
                                    pass

                                cabin_type = "unknown"
                                for category_type, codes in CABIN_RATE_CODES.items():
                                    if any(category_code.startswith(code)
                                           for code in codes):
                                        cabin_type = category_type
                                        break

                                display_idx = len(all_category_data) + 1

                                category_info = f"{display_idx}. {category_code} - {description} - Price: {price} - Type: {cabin_type}"
                                logger.info(category_info)
                                all_category_data.append(
                                    {
                                        'index': len(all_category_data), 'code':
                                        category_code, 'description': description,
                                        'price': price, 'price_value': price_value,
                                        'cabin_type': cabin_type, 'full_text':
                                        category_info, 'raw_data': category
                                    }
                                )
                                all_category_descriptions.append(
                                    f"{category_code} - {description}"
                                )
                            except Exception as e:
                                logger.error(
                                    f"Error processing category at index {idx}: {str(e)}"
                                )

                else:
                    logger.error("Unexpected category data structure")
                    return None, None

                logger.info(f"Discovered {len(all_category_data)} total categories from JSON data ")

                # Check if preferred_category is provided and try to find it by name
                if preferred_category:
                    # First check if preferred_category is a string (category name)
                    if isinstance(preferred_category, str):
                        try:
                            # Check if it's a numeric string (index)
                            category_index = int(preferred_category)
                            if 0 <= category_index < len(all_category_data):
                                selected_category = all_category_data[category_index]
                                logger.info(f"Selected category by index {category_index}: {selected_category['full_text']}")
                                return category_index, all_category_data
                        except ValueError:
                            # Check if the category name exists in the predefined CATEGORY_MAPPING
                            # Only check for exact matches, not partial matches
                            clean_name = preferred_category.lower().strip()
                            exact_match_found = False
                            normalized_preference = None
                            
                            # Check for exact matches in CATEGORY_MAPPING
                            for key, value in cabin_categories.CATEGORY_MAPPING.items():
                                if clean_name == key.lower():
                                    exact_match_found = True
                                    normalized_preference = value
                                    break
                            
                            if exact_match_found:
                                logger.info(f"Found '{preferred_category}' in predefined category mapping, using existing logic for '{normalized_preference}'")
                                # Use existing predefined category logic
                                matching_categories = [
                                    cat for cat in all_category_data
                                    if cat['cabin_type'] == normalized_preference
                                ]
                                if matching_categories:
                                    cheapest_category = min(matching_categories, key=lambda x: x['price_value'])
                                    category_index = cheapest_category['index']
                                    logger.info(f"Selected cheapest {normalized_preference} cabin: {cheapest_category['code']} at ${cheapest_category['price_value']}")
                                    logger.info(f"\nSelected cheapest {normalized_preference} category: {cheapest_category['full_text']}")
                                    return category_index, all_category_data
                                else:
                                    logger.warning(f"No {normalized_preference} cabins found")
                            else:
                                # It's not in the predefined mapping, so use name-based search
                                logger.info(f"'{preferred_category}' not found in predefined mappings, searching by name")
                                category_index, selected_category = CategorySelector.find_category_by_name(
                                    all_category_data, preferred_category
                                )
                                if category_index is not None:
                                    logger.info(f"Found category by name: {selected_category['full_text']}")
                                    return category_index, all_category_data
                                else:
                                    logger.warning(f"Could not find category by name '{preferred_category}', using fallback logic")
                    else:
                        # preferred_category is not a string, use existing logic
                        logger.info(f"Using existing category selection logic for: {preferred_category}")

                # Fallback to existing logic for automatic selection
                if not preferred_category:
                    logger.info("No preferred category specified, using default selection logic")

                if preferred_category == "balcony":
                    logger.info("Finding cheapest balcony cabin")
                    balcony_cabins = [
                        cat for cat in all_category_data
                        if cat['cabin_type'] == 'balcony'
                    ]
                    if balcony_cabins:
                        cheapest_balcony = min(
                            balcony_cabins, key=lambda x: x['price_value']
                        )
                        category_index = cheapest_balcony['index']
                        logger.info(f"Selected cheapest balcony cabin: {cheapest_balcony['code']} at ${cheapest_balcony['price_value']}")
                        logger.info(f"\nSelected cheapest balcony category: {cheapest_balcony['full_text']}")
                        return category_index, all_category_data

                elif preferred_category == "inside":
                    logger.info("Finding cheapest inside cabin")
                    inside_cabins = [
                        cat for cat in all_category_data
                        if cat['cabin_type'] == 'inside'
                    ]
                    if inside_cabins:
                        cheapest_inside = min(
                            inside_cabins, key=lambda x: x['price_value']
                        )
                        category_index = cheapest_inside['index']
                        logger.info(f"Selected cheapest inside cabin: {cheapest_inside['code']} at ${cheapest_inside['price_value']} ")
                        logger.info(f"\nSelected cheapest inside category: {cheapest_inside['full_text']}")
                        return category_index, all_category_data

                elif preferred_category == "outside":
                    logger.info("Finding cheapest outside cabin")
                    outside_cabins = [
                        cat for cat in all_category_data
                        if cat['cabin_type'] == 'outside'
                    ]
                    if outside_cabins:
                        cheapest_outside = min(
                            outside_cabins, key=lambda x: x['price_value']
                        )
                        category_index = cheapest_outside['index']
                        logger.info(f"Selected cheapest outside cabin: {cheapest_outside['code']} at ${cheapest_outside['price_value']}")
                        logger.info(f"\nSelected cheapest outside category: {cheapest_outside['full_text']}")
                        return category_index, all_category_data

                elif preferred_category == "suite":
                    logger.info("Finding cheapest suite cabin")
                    suite_cabins = [
                        cat for cat in all_category_data
                        if cat['cabin_type'] == 'suite'
                    ]
                    if suite_cabins:
                        cheapest_suite = min(
                            suite_cabins, key=lambda x: x['price_value']
                        )
                        category_index = cheapest_suite['index']
                        logger.info(f"Selected cheapest suite cabin: {cheapest_suite['code']} at ${cheapest_suite['price_value']}")
                        logger.info(f"\nSelected cheapest suite category: {cheapest_suite['full_text']}")
                        return category_index, all_category_data

                else:
                    logger.info("No specific category preference, selecting cheapest overall")
                    cheapest_cabin = min(
                        all_category_data, key=lambda x: x['price_value']
                    )
                    category_index = cheapest_cabin['index']
                    logger.info(f"Selected cheapest cabin: {cheapest_cabin['code']} at ${cheapest_cabin['price_value']}")
                    logger.info(f"\nSelected cheapest category: {cheapest_cabin['full_text']}")
                    return category_index, all_category_data

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON data: {str(e)}")
                return None, None

        except Exception as e:
            logger.error(f"Error in display_available_categories: {str(e)}")
            return None, None

    @staticmethod
    def is_interior_cabin(code):
        interior_codes = [
            'T1', 'T2', 'TX', 'I1', 'I2', 'IA', 'IB', 'IC', 'IX', 'SS', 'L1'
        ]
        return code in interior_codes or code.startswith('I')

    @staticmethod
    async def select_category(page, category_index, session_id=None, cabin_id=None):
        try:
            logger.info(f"Selecting category at index {category_index}")
            ui_log(
                "Selecting cabin category — Kindly stand by",
                session_id=session_id,
                cabin_id=cabin_id,
                module="NCL",
                step="category_selection"
            )

            await asyncio.sleep(2)
            logger.info(f"Selecting category at index {category_index}")

            hidden_input = page.locator("input[type='hidden'][name='category']").first

            if await hidden_input.count() == 0:
                logger.error("Hidden input with category data not found")
                return False

            json_value = await hidden_input.get_attribute("value")

            if not json_value:
                logger.error("No JSON data found in hidden input")
                return False

            import json
            try:
                category_data = json.loads(json_value)
                logger.info(f"Successfully parsed category JSON data")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON data: {str(e)}")
                return False

            categories = []
            if isinstance(category_data, dict):
                if 'data' in category_data and isinstance(category_data['data'],
                                                          (list, dict)):
                    if isinstance(category_data['data'], dict):
                        categories = list(category_data['data'].items())
                    else:
                        categories = category_data['data']
                else:
                    categories = list(category_data.items())
            elif isinstance(category_data, list):
                categories = category_data

            if category_index >= len(categories):
                logger.warning(
                    f"Category index {category_index} is out of range (found {len(categories)} categories)"
                )
                return False

            selected_code = None
            if isinstance(categories, list):
                if isinstance(categories[category_index], dict):
                    category_item = categories[category_index]
                    selected_code = category_item.get(
                        'category',
                        category_item.get(
                            'Category',
                            category_item.get('code', category_item.get('Code'))
                        )
                    )
                else:
                    selected_code, _ = categories[category_index]
            else:
                logger.error("Unexpected category data structure")
                return False

            if not selected_code:
                logger.error("Could not determine selected category code")
                return False

            logger.info(f"Selected category code: {selected_code}")

            filter_input = page.locator(
                "//*[@id='SWXMLForm_SelectCategory_category_tp_filter']"
            ).first
            if await filter_input.count() == 0:
                logger.error("Category filter input not found")
                return False

            await filter_input.fill("")
            await filter_input.type(selected_code, delay=100)
            logger.info(f"Entered category code '{selected_code}' in filter box")

            await asyncio.sleep(2)

            all_rows = await page.locator(".slick-row").all()
            if not all_rows:
                logger.error("No rows visible after filtering")
                return False

            logger.info(f"Found {len(all_rows)} rows after filtering")

            exact_match_row = None

            for row in all_rows:
                try:
                    cell_selectors = [
                        ".slick-cell:first-child", "div.slick-cell:nth-child(1)",
                        "div[class*='l0']"
                    ]

                    for selector in cell_selectors:
                        cell = row.locator(selector).first
                        if await cell.count() > 0:
                            cell_text = await cell.text_content()
                            cell_text = cell_text.strip()
                            logger.info(f"Row contains category code: {cell_text}")

                            if cell_text == selected_code:
                                logger.info(
                                    f"Found exact match for category code {selected_code}"
                                )
                                exact_match_row = row
                                break

                except Exception as e:
                    logger.warning(f"Error extracting category code from row: {str(e)}")
                    continue

            if exact_match_row:
                filtered_row = exact_match_row
                logger.info(f"Using exact match row for category {selected_code}")
            else:
                logger.warning(f"Exact match not found, falling back to text search")
                filtered_row = page.locator(
                    f".slick-row:has-text('{selected_code}')"
                ).first
                if await filtered_row.count() == 0:
                    logger.warning(
                        f"Could not find filtered row for category {selected_code}"
                    )
                    filtered_row = page.locator(".slick-row").first
                    if await filtered_row.count() == 0:
                        logger.error("No rows visible after filtering")
                        return False

            select_link = filtered_row.locator(
                "a.navlink[href='#'][data-link-action='select']"
            ).first

            if await select_link.count() == 0:
                logger.info(
                    "Could not find the exact Select link format, trying alternatives"
                )

                fallback_selectors = [
                    ".l11.r11 a", "a[data-link-action='select']", "a.navlink",
                    "a[data-type='submitLink']", "a:has-text('Select')"
                ]

                for selector in fallback_selectors:
                    select_link = filtered_row.locator(selector).first
                    if await select_link.count() > 0:
                        logger.info(
                            f"Found Select link using fallback selector: {selector}"
                        )
                        break

                if await select_link.count() == 0:
                    logger.error(
                        "Select link not found in the filtered row after all attempts"
                    )
                    return False

            logger.info(f"Found select link for category {selected_code}")

            try:
                try:
                    href_attr = await select_link.get_attribute("href") or "#"
                    logger.info(f"Select link has href: {href_attr}")
                except:
                    pass

                await select_link.click()
                logger.info("Clicked select link")
                await asyncio.sleep(1)

                if await page.locator("#SWXMLForm_SelectCategory_category").count() == 0:
                    logger.info("Successfully navigated away from category page")
                    ui_log(
                        "Cabin category selected successfully — moving ahead",
                        session_id=session_id,
                        cabin_id=cabin_id,
                        module="NCL",
                        step="category_selection"
                    )
                    return True
                else:
                    logger.error("Failed to navigate away after clicking select")
                    return False
            except Exception as e:
                if "Execution context was destroyed" in str(e) or "navigation" in str(
                        e).lower():
                    logger.success(
                        "Navigation occurred after clicking select - this is expected and indicates success"
                    )
                    return True
                else:
                    logger.error(f"Error during select click: {str(e)}")
                    return False

        except Exception as e:
            if "Execution context was destroyed" in str(e) or "navigation" in str(
                    e).lower():
                logger.success(
                    "Navigation occurred during category selection - this is expected and indicates success"
                )
                return True
            else:
                logger.error(f"Error in category selection: {str(e)}")
                return False

    @staticmethod
    async def navigate_to_price_programs(page, session_id=None, cabin_id=None):
        """Navigate directly to the price programs page"""
        try:
            logger.info("Attempting to navigate to price programs page")

            try:
                price_programs_link = page.locator(
                    "a[href*='/agent-price-programs/']"
                ).first
                if await price_programs_link.count() > 0 and await price_programs_link.is_visible():
                    logger.info("Found price programs link by href")
                    try:
                        await price_programs_link.click()
                        return True
                    except Exception as click_err:
                        logger.debug(
                            f"Direct click failed, trying JS click: {str(click_err)}"
                        )
                        await page.evaluate(
                            "(element) => { element.click(); }", price_programs_link
                        )

                        return True
            except Exception as e:
                logger.warning(f"Could not find price programs link by href: {str(e)}")

            try:
                price_programs_link = page.locator("a:has-text('Price Programs')").first
                if await price_programs_link.count() > 0 and await price_programs_link.is_visible():
                    logger.info("Found price programs link by text")
                    try:
                        await price_programs_link.click()
                        return True
                    except Exception as click_err:
                        logger.debug(
                            f"Direct click failed, trying JS click: {str(click_err)}"
                        )
                        await page.evaluate(
                            "(element) => { element.click(); }", price_programs_link
                        )

                        return True
            except Exception as e:
                logger.warning(f"Could not find price programs link by text: {str(e)}")

            try:
                price_programs_tab = page.locator(
                    "xpath=//*[@id='Content']/div/div[2]/ul/li[6]/a"
                ).first
                if await price_programs_tab.count() > 0 and await price_programs_tab.is_visible():
                    logger.info("Found price programs tab using XPath li[6]")
                    try:
                        await price_programs_tab.click()

                        return True
                    except Exception as click_err:
                        logger.debug(
                            f"Direct click failed, trying JS click: {str(click_err)}"
                        )
                        await page.evaluate(
                            "(element) => { element.click(); }", price_programs_tab
                        )

                        return True
            except Exception as e:
                logger.warning(
                    f"Could not find price programs tab with XPath li[6]: {str(e)}"
                )

            try:
                price_programs_tab = page.locator(
                    "xpath=//*[@id='Content']/div/div[2]/ul/li[7]/a"
                ).first
                if await price_programs_tab.count() > 0 and await price_programs_tab.is_visible():
                    logger.info("Found price programs tab using XPath li[7]")
                    try:
                        await price_programs_tab.click()

                        return True
                    except Exception as click_err:
                        logger.debug(
                            f"Direct click failed, trying JS click: {str(click_err)}"
                        )
                        await page.evaluate(
                            "(element) => { element.click(); }", price_programs_tab
                        )
                        await asyncio.sleep(2)
                        return True
            except Exception as e:
                logger.warning(
                    f"Could not find price programs tab with XPath li[7]: {str(e)}"
                )

            try:
                price_programs_tab = page.locator(
                    "#Content > div > div.res-form > ul > li.current > a"
                ).first
                if await price_programs_tab.count() > 0 and await price_programs_tab.is_visible():
                    logger.info("Found price programs tab using CSS selector")
                    try:
                        await price_programs_tab.click()
                        await asyncio.sleep(2)
                        return True
                    except Exception as click_err:
                        logger.debug(
                            f"Direct click failed, trying JS click: {str(click_err)}"
                        )
                        await page.evaluate(
                            "(element) => { element.click(); }", price_programs_tab
                        )
                        await asyncio.sleep(2)
                        return True
            except Exception as e:
                logger.warning(
                    f"Could not find price programs tab with CSS selector: {str(e)}"
                )

            try:
                for text in ["Price Programs", "Price", "Programs", "Pricing"]:
                    try:
                        price_programs_tab = page.locator(f"text={text}").first
                        if await price_programs_tab.count(
                        ) > 0 and await price_programs_tab.is_visible():
                            logger.info(
                                f"Found price programs tab with text containing '{text}'"
                            )
                            try:
                                await price_programs_tab.click()
                                await asyncio.sleep(2)
                                return True
                            except Exception as click_err:
                                logger.debug(
                                    f"Direct click failed, trying JS click: {str(click_err)}"
                                )
                                await page.evaluate(
                                    "(element) => { element.click(); }",
                                    price_programs_tab
                                )
                                await asyncio.sleep(2)
                                return True
                    except:
                        continue
            except Exception as e:
                logger.warning(
                    f"Could not find price programs tab by link text: {str(e)}"
                )

            try:
                navigation_tabs = await page.locator(
                    "#Content > div > div.res-form > ul > li a"
                ).all()
                if len(navigation_tabs) >= 7:
                    logger.info("Trying 7th navigation tab as price programs")
                    try:
                        await navigation_tabs[6].click()
                        await asyncio.sleep(2)
                        return True
                    except Exception as click_err:
                        logger.debug(
                            f"Direct click failed, trying JS click: {str(click_err)}"
                        )
                        await page.evaluate(
                            "(element) => { element.click(); }", navigation_tabs[6]
                        )
                        await asyncio.sleep(2)
                        return True
            except Exception as e:
                logger.warning(f"Could not navigate using tab index: {str(e)}")

            try:
                await page.evaluate(
                    """
                    () => {
                        var links = document.querySelectorAll('li.link a');
                        for (var i = 0; i < links.length; i++) {
                            if (links[i].textContent.includes('Price Programs')) {
                                links[i].click();
                                return true;
                            }
                        }
                        return false;
                    }
                """
                )
                logger.info("Attempted to click price programs tab using JavaScript")
                await asyncio.sleep(2)
                return True
            except Exception as e:
                logger.warning(f"JavaScript approach failed: {str(e)}")

            logger.error("All attempts to navigate to price programs failed")
            return False

        except Exception as e:
            logger.error(f"Error navigating to price programs page: {str(e)}")
            return False