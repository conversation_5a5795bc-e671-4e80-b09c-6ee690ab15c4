{"version": 3, "sources": ["../../../src/server/base-http/index.ts"], "names": ["BaseNextRequest", "BaseNextResponse", "constructor", "method", "url", "body", "cookies", "_cookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "destination", "redirect", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "RedirectStatusCode", "PermanentRedirect"], "mappings": ";;;;;;;;;;;;;;;IAwBsBA,eAAe;eAAfA;;IAcAC,gBAAgB;eAAhBA;;;oCAnCa;iCAEH;AAmBzB,MAAeD;IAIpBE,YAAmBC,QAAuBC,KAAoBC,KAAY;sBAAvDF;mBAAuBC;oBAAoBC;IAAa;IAE3E,qDAAqD;IAErD,IAAWC,UAAU;QACnB,IAAI,IAAI,CAACC,QAAQ,EAAE,OAAO,IAAI,CAACA,QAAQ;QACvC,OAAQ,IAAI,CAACA,QAAQ,GAAGC,IAAAA,gCAAe,EAAC,IAAI,CAACC,OAAO;IACtD;AACF;AAEO,MAAeR;IAKpBC,YAAmBQ,YAA0B;2BAA1BA;IAA2B;IAmC9C,qDAAqD;IAErDC,SAASD,WAAmB,EAAEE,UAAkB,EAAE;QAChD,IAAI,CAACC,SAAS,CAAC,YAAYH;QAC3B,IAAI,CAACE,UAAU,GAAGA;QAElB,0DAA0D;QAC1D,qCAAqC;QACrC,IAAIA,eAAeE,sCAAkB,CAACC,iBAAiB,EAAE;YACvD,IAAI,CAACF,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEH,YAAY,CAAC;QAClD;QACA,OAAO,IAAI;IACb;AACF"}