'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';

const SessionTimer: React.FC = () => {
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const { isLoggedIn, getToken, logout } = useAuth();

  // Function to decode JWT and get expiration time
  const getTokenExpiration = (): number => {
    try {
      const token = getToken();
      if (!token) return 0;

      // Decode JWT token (simple base64 decode for the payload)
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp || 0;
    } catch (error) {
      console.error('Error decoding token:', error);
      return 0;
    }
  };

  // Calculate remaining time in seconds
  const calculateTimeRemaining = (): number => {
    const exp = getTokenExpiration();
    const now = Math.floor(Date.now() / 1000);
    return Math.max(0, exp - now);
  };

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Get color based on remaining time
  const getTimerColor = (seconds: number): string => {
    if (seconds > 600) return 'text-green-600 bg-green-50 border-green-200'; // > 10 minutes
    if (seconds > 300) return 'text-yellow-600 bg-yellow-50 border-yellow-200'; // > 5 minutes
    if (seconds > 120) return 'text-orange-600 bg-orange-50 border-orange-200'; // > 2 minutes
    return 'text-red-600 bg-red-50 border-red-200'; // < 2 minutes
  };

  // Update timer every second
  useEffect(() => {
    if (!isLoggedIn) {
      setIsVisible(false);
      return;
    }

    const updateTimer = () => {
      const remaining = calculateTimeRemaining();
      setTimeRemaining(remaining);
      
      // Show timer when less than 30 minutes remaining
      setIsVisible(remaining > 0 && remaining < 1800);

      // Auto logout when token expires
      if (remaining <= 0) {
        logout();
      }

      // Show warning notifications
      if (remaining === 300) { // 5 minutes
        alert('Your session will expire in 5 minutes.');
      } else if (remaining === 120) { // 2 minutes
        alert('Your session will expire in 2 minutes.');
      } else if (remaining === 60) { // 1 minute
        alert('Your session will expire in 1 minute.');
      }
    };

    // Update immediately
    updateTimer();

    // Set up interval to update every second
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [isLoggedIn, getToken, logout]);

  // Don't render if not logged in or timer not visible
  if (!isLoggedIn || !isVisible) {
    return null;
  }

  return (
    <div className="fixed top-28 xs:top-24 sm:top-20 md:top-4 right-0.5 xs:right-1 sm:right-2 md:right-4 z-30">
      <div className={`px-1 xs:px-1.5 sm:px-2 md:px-3 py-0.5 xs:py-1 sm:py-1.5 md:py-2 rounded-sm xs:rounded-md md:rounded-lg border shadow-lg transition-all duration-300 ${getTimerColor(timeRemaining)}`}>
        <div className="flex items-center space-x-0.5 xs:space-x-1 md:space-x-2">
          <svg 
            className="w-2.5 h-2.5 xs:w-3 xs:h-3 md:w-4 md:h-4 flex-shrink-0" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
            />
          </svg>
          <span className="text-xs md:text-sm font-medium whitespace-nowrap">
            {formatTime(timeRemaining)}
          </span>
        </div>
        {timeRemaining < 300 && (
          <div className="text-xs mt-1 opacity-75 hidden md:block">
            Session expires soon
          </div>
        )}
      </div>
    </div>
  );
};

export default SessionTimer; 