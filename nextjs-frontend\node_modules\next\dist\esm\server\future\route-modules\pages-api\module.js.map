{"version": 3, "sources": ["../../../../../src/server/future/route-modules/pages-api/module.ts"], "names": ["RouteModule", "apiResolver", "PagesAPIRouteModule", "constructor", "options", "userland", "default", "Error", "definition", "page", "render", "req", "res", "context", "query", "previewProps", "revalidate", "trustHostHeader", "allowedRevalidateHeaderKeys", "hostname", "minimalMode", "dev"], "mappings": "AAOA,SAASA,WAAW,QAAuC,kBAAiB;AAC5E,SAASC,WAAW,QAAQ,uCAAsC;AA8FlE,OAAO,MAAMC,4BAA4BF;IAIvCG,YAAYC,OAAmC,CAAE;QAC/C,KAAK,CAACA;QAEN,IAAI,OAAOA,QAAQC,QAAQ,CAACC,OAAO,KAAK,YAAY;YAClD,MAAM,IAAIC,MACR,CAAC,KAAK,EAAEH,QAAQI,UAAU,CAACC,IAAI,CAAC,oCAAoC,CAAC;QAEzE;IACF;IAEA;;;;;GAKC,GACD,MAAaC,OACXC,GAAoB,EACpBC,GAAmB,EACnBC,OAAoC,EACrB;QACf,MAAMZ,YACJU,KACAC,KACAC,QAAQC,KAAK,EACb,IAAI,CAACT,QAAQ,EACb;YACE,GAAGQ,QAAQE,YAAY;YACvBC,YAAYH,QAAQG,UAAU;YAC9BC,iBAAiBJ,QAAQI,eAAe;YACxCC,6BAA6BL,QAAQK,2BAA2B;YAChEC,UAAUN,QAAQM,QAAQ;QAC5B,GACAN,QAAQO,WAAW,EACnBP,QAAQQ,GAAG,EACXR,QAAQJ,IAAI;IAEhB;AACF;AAEA,eAAeP,oBAAmB"}