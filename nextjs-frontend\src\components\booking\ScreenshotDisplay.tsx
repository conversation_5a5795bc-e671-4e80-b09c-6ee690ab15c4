import React, { useState, useEffect, useRef, memo } from 'react';
import { ProviderType } from './ProviderSelector';
import { getScreenshots, Screenshot } from '../../services/api';


const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

interface ScreenshotDisplayProps {
  provider: ProviderType;
  sessionId: string;
  requestId: string;
}

const ScreenshotDisplay: React.FC<ScreenshotDisplayProps> = ({
  provider,
  sessionId,
  requestId
}) => {
  const [screenshots, setScreenshots] = useState<Screenshot[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedScreenshot, setSelectedScreenshot] = useState<Screenshot | null>(null);
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<{ x: number, y: number }>({ x: 0, y: 0 });
  const [position, setPosition] = useState<{ x: number, y: number }>({ x: 0, y: 0 });
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  // Prevent duplicate API calls
  const hasFetchedRef = useRef<string | null>(null);

  // Image cache using useRef - stores blob URLs for instant loading
  const imageCacheRef = useRef<Map<number, string>>(new Map());

  // Track ongoing requests to prevent duplicates
  const ongoingRequestsRef = useRef<Set<number>>(new Set());

  // Cleanup function to revoke blob URLs when component unmounts
  useEffect(() => {
    return () => {
      // Cleanup all blob URLs to prevent memory leaks
      imageCacheRef.current.forEach((blobUrl) => {
        URL.revokeObjectURL(blobUrl);
      });
      imageCacheRef.current.clear();
      ongoingRequestsRef.current.clear();
    };
  }, []);

  // Function to get cached image URL or fetch and cache it
  const getCachedImageUrl = async (screenshot: Screenshot): Promise<string> => {
    const screenshotId = screenshot.id;

    // Check if already cached
    if (imageCacheRef.current.has(screenshotId)) {
      return imageCacheRef.current.get(screenshotId)!;
    }

    // Check if request is already in progress
    if (ongoingRequestsRef.current.has(screenshotId)) {
      // Wait for the ongoing request to complete
      while (ongoingRequestsRef.current.has(screenshotId)) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      // Check cache again after waiting
      if (imageCacheRef.current.has(screenshotId)) {
        return imageCacheRef.current.get(screenshotId)!;
      }
    }

    // Mark request as in progress
    ongoingRequestsRef.current.add(screenshotId);

    try {
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/screenshot-image/${screenshotId}`, {
        headers
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch screenshot: ${response.statusText}`);
      }

      // Get the image blob
      const blob = await response.blob();

      // Create blob URL for instant future access
      const blobUrl = URL.createObjectURL(blob);

      // Cache the blob URL
      imageCacheRef.current.set(screenshotId, blobUrl);

      return blobUrl;
    } catch (error) {
      console.error(`Error fetching screenshot ${screenshotId}:`, error);
      throw error;
    } finally {
      // Mark request as complete
      ongoingRequestsRef.current.delete(screenshotId);
    }
  };

  // Memoized cached image component to prevent unnecessary re-renders
  const CachedImage = memo<{
    screenshot: Screenshot;
    className?: string;
    style?: React.CSSProperties;
    alt?: string;
  }>(({ screenshot, className, style, alt }) => {
    const [imageSrc, setImageSrc] = useState<string>('');
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string>('');
    const isMountedRef = useRef<boolean>(true);

    useEffect(() => {
      isMountedRef.current = true;

      const loadImage = async () => {
        try {
          setIsLoading(true);
          setError('');
          const url = await getCachedImageUrl(screenshot);
          if (isMountedRef.current) {
            setImageSrc(url);
            setIsLoading(false);
          }
        } catch (err) {
          if (isMountedRef.current) {
            setError(err instanceof Error ? err.message : 'Failed to load image');
            setIsLoading(false);
          }
        }
      };

      loadImage();

      return () => {
        isMountedRef.current = false;
      };
    }, [screenshot.id]); // Only re-run if screenshot ID changes

    if (isLoading) {
      return (
        <div className={`${className} flex items-center justify-center bg-gray-100`} style={style}>
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (error) {
      return (
        <div className={`${className} flex items-center justify-center bg-red-50 text-red-600 text-sm`} style={style}>
          Failed to load
        </div>
      );
    }

    return (
      <img
        src={imageSrc}
        alt={alt || screenshot.screenshot_type || 'Screenshot'}
        className={className}
        style={style}
      />
    );
  }, (prevProps, nextProps) => {
    // Custom comparison function - only re-render if screenshot ID changes
    return prevProps.screenshot.id === nextProps.screenshot.id &&
      prevProps.className === nextProps.className &&
      JSON.stringify(prevProps.style) === JSON.stringify(nextProps.style);
  });

  // Give the component a display name for debugging
  CachedImage.displayName = 'CachedImage';

  // Fetch screenshots on component mount
  useEffect(() => {
    // Prevent duplicate calls for the same session
    if (hasFetchedRef.current === sessionId) {
      return;
    }

    const fetchScreenshots = async () => {
      try {
        setLoading(true);
        const response = await getScreenshots(sessionId);
        if (response.success && response.screenshots) {
          setScreenshots(response.screenshots);
          hasFetchedRef.current = sessionId; // Mark as fetched
        } else {
          setError('Failed to fetch screenshots');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error fetching screenshots');
      } finally {
        setLoading(false);
      }
    };

    fetchScreenshots();
  }, [sessionId]);

  // Set initial selected category when screenshots are loaded
  useEffect(() => {
    if (screenshots.length > 0) {
      const grouped = groupScreenshotsByCabin();
      const categories = Object.keys(grouped);
      if (categories.length > 0 && selectedCategory === '') {
        setSelectedCategory(categories[0]);
      }
    }
  }, [screenshots.length]); // Only depend on length, not the array itself

  // Group screenshots by type for better organization
  // const groupScreenshotsByType = () => {
  //   const grouped: { [key: string]: Screenshot[] } = {};

  //   screenshots.forEach(screenshot => {
  //     // Normalize screenshot type name for grouping
  //     let type = screenshot.screenshot_type || 'Other';
  //     type = type.replace(/_/g, ' ').split(' ').map(word =>
  //       word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  //     ).join(' ');

  //     if (!grouped[type]) {
  //       grouped[type] = [];
  //     }
  //     grouped[type].push(screenshot);
  //   });

  //   return grouped;
  // };

  const groupScreenshotsByCabin = () => {
    const groupedByCabin: { [key: string]: Screenshot[] } = {};

    screenshots.forEach(screenshot => {
      const cabinId = screenshot.cabin_id || 'Unknown'; // Default to 'Unknown' if no cabin_id
      if (!groupedByCabin[cabinId]) {
        groupedByCabin[cabinId] = [];
      }
      groupedByCabin[cabinId].push(screenshot);
    });

    return groupedByCabin;
  };


  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (e) {
      return dateString;
    }
  };

  // Extract filename without path and timestamp
  const getCleanFileName = (fileName: string) => {
    // Remove timestamp part if it exists (assuming format with _YYYYMMDD_HHMMSS pattern)
    const parts = fileName.split('_');
    if (parts.length > 2) {
      // Try to remove timestamp parts
      const cleanParts = parts.filter(part => !/^\d{8}$/.test(part) && !/^\d{6}$/.test(part));
      return cleanParts.join('_');
    }
    return fileName;
  };

  // Handle screenshot click to show in modal
  const handleScreenshotClick = (screenshot: Screenshot) => {
    setSelectedScreenshot(screenshot);
    setZoomLevel(1);
    setPosition({ x: 0, y: 0 });
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedScreenshot) return;

      switch (e.key) {
        case 'ArrowLeft':
          navigateScreenshot('prev');
          break;
        case 'ArrowRight':
          navigateScreenshot('next');
          break;
        case 'Escape':
          setSelectedScreenshot(null);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedScreenshot]);

  // Handle zoom in/out
  const handleZoom = (increase: boolean) => {
    setZoomLevel(prev => {
      const newZoom = increase ? prev + 0.25 : prev - 0.25;
      return Math.max(0.5, Math.min(newZoom, 3)); // Limit zoom between 0.5x and 3x
    });
  };

  // Handle mouse down for dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
  };

  // Handle mouse move for dragging
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;

    const dx = e.clientX - dragStart.x;
    const dy = e.clientY - dragStart.y;

    setPosition(prev => ({
      x: prev.x + dx,
      y: prev.y + dy
    }));

    setDragStart({ x: e.clientX, y: e.clientY });
  };

  // Handle mouse up to stop dragging
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Reset zoom and position
  const handleReset = () => {
    setZoomLevel(1);
    setPosition({ x: 0, y: 0 });
  };

  // Helper: sort screenshots for navigation/modal
  const sortScreenshotsForNavigation = (shots: Screenshot[]) => {
    if (provider === 'NCL') {
      // 1. All 'Form Filled'
      const formFilled = shots.filter(s => (s.screenshot_type || '').toLowerCase() === 'form filled');
      // 2. All 'Available Cruises'
      const availableCruises = shots.filter(s => (s.screenshot_type || '').toLowerCase() === 'available cruises');
      // 3. The rest, by cabin_id then original order
      const rest = shots.filter(s => {
        const type = (s.screenshot_type || '').toLowerCase();
        return type !== 'form filled' && type !== 'available cruises';
      });
      // Sort the rest by cabin_id then original order
      rest.sort((a, b) => {
        if (a.cabin_id && b.cabin_id) {
          if (a.cabin_id !== b.cabin_id) {
            return Number(a.cabin_id) - Number(b.cabin_id);
          }
        } else if (a.cabin_id) {
          return -1;
        } else if (b.cabin_id) {
          return 1;
        }
        return 0;
      });
      return [...formFilled, ...availableCruises, ...rest];
    } else {
      // Default: by cabin_id then original order
      return [...shots].sort((a, b) => {
        if (a.cabin_id && b.cabin_id) {
          if (a.cabin_id !== b.cabin_id) {
            return Number(a.cabin_id) - Number(b.cabin_id);
          }
        } else if (a.cabin_id) {
          return -1;
        } else if (b.cabin_id) {
          return 1;
        }
        return 0;
      });
    }
  };

  // Navigate to the next or previous screenshot across all categories
  const navigateScreenshot = (direction: 'next' | 'prev') => {
    if (!selectedScreenshot) return;

    // Use the new sort helper
    const allScreenshots = sortScreenshotsForNavigation(screenshots);

    // Find the current index in the flat array
    const currentIndex = allScreenshots.findIndex(shot => shot.id === selectedScreenshot.id);
    if (currentIndex === -1) return;

    // Calculate the next index
    let nextIndex;
    if (direction === 'next') {
      nextIndex = (currentIndex + 1) % allScreenshots.length;
    } else {
      nextIndex = (currentIndex - 1 + allScreenshots.length) % allScreenshots.length;
    }

    // Set the new screenshot
    setSelectedScreenshot(allScreenshots[nextIndex]);

    // Update the selected category to match the new screenshot's category
    const newType = allScreenshots[nextIndex].screenshot_type || 'Other';
    const normalizedNewType = newType.replace(/_/g, ' ').split(' ').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');

    // Only update the category if it's different
    if (normalizedNewType !== selectedCategory) {
      setSelectedCategory(normalizedNewType);
    }

    // Reset zoom and position
    setZoomLevel(1);
    setPosition({ x: 0, y: 0 });
  };

  // Modal for viewing screenshots
  const renderScreenshotModal = () => {
    if (!selectedScreenshot) return null;

    // Use the new sort helper
    const allScreenshots = sortScreenshotsForNavigation(screenshots);

    const currentIndex = allScreenshots.findIndex(shot => shot.id === selectedScreenshot.id);
    const totalImages = allScreenshots.length;

    // Get the current type for display purposes
    const currentType = selectedScreenshot.screenshot_type || 'Other';
    const normalizedType = currentType.replace(/_/g, ' ').split(' ').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');

    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4"
        onClick={() => setSelectedScreenshot(null)}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* Left arrow navigation button */}
        <button
          className="absolute left-[20%] top-1/2 -translate-y-1/2 z-[100] bg-blue-600 bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-lg text-white hover:text-white transition-all transform hover:scale-110 focus:outline-none"
          onClick={(e) => {
            e.stopPropagation();
            navigateScreenshot('prev');
          }}
          style={{ filter: 'drop-shadow(0 0 8px rgba(0,0,0,0.5))' }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        {/* Right arrow navigation button */}
        <button
          className="absolute right-[20%] top-1/2 -translate-y-1/2 z-[100] bg-blue-600 bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-lg text-white hover:text-white transition-all transform hover:scale-110 focus:outline-none"
          onClick={(e) => {
            e.stopPropagation();
            navigateScreenshot('next');
          }}
          style={{ filter: 'drop-shadow(0 0 8px rgba(0,0,0,0.5))' }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M9 5l7 7-7 7" />
          </svg>
        </button>

        <div
          className="bg-white rounded-lg overflow-hidden max-w-4xl w-full max-h-[90vh] flex flex-col"
          onClick={e => e.stopPropagation()}
        >
          <div className="p-4 bg-gray-100 flex justify-between items-center border-b">
            <div>
              <h3 className="font-semibold text-lg">
                {normalizedType}
              </h3>
              {totalImages > 1 && (
                <p className="text-sm text-gray-500">
                  Image {currentIndex + 1} of {totalImages}
                  {allScreenshots[currentIndex]?.cabin_id ? ` (Cabin ${allScreenshots[currentIndex].cabin_id})` : ''}
                </p>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 bg-white rounded-md px-2 py-1 border">
                <button
                  onClick={() => handleZoom(false)}
                  className="text-gray-700 hover:text-gray-900 focus:outline-none"
                  disabled={zoomLevel <= 0.5}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </button>
                <span className="text-sm">{Math.round(zoomLevel * 100)}%</span>
                <button
                  onClick={() => handleZoom(true)}
                  className="text-gray-700 hover:text-gray-900 focus:outline-none"
                  disabled={zoomLevel >= 3}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              <button
                onClick={handleReset}
                className="text-sm bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded"
              >
                Reset
              </button>
              <button
                onClick={() => setSelectedScreenshot(null)}
                className="text-gray-600 hover:text-gray-900"
              >
                ✕
              </button>
            </div>
          </div>
          <div className="flex-1 overflow-auto p-4 relative">
            {/* Image container */}
            <div className="relative mx-auto">
              {/* Image */}
              <div
                className="relative mx-auto"
                style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
              >
                <CachedImage
                  screenshot={selectedScreenshot}
                  className="w-full h-auto"
                  style={{
                    transform: `scale(${zoomLevel}) translate(${position.x}px, ${position.y}px)`,
                    transformOrigin: 'center',
                    transition: isDragging ? 'none' : 'transform 0.1s ease-out'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading && screenshots.length === 0) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold mb-4"> Screenshots</h3>
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
          <span className="ml-2">Loading screenshots...</span>
        </div>
      </div>
    );
  }

  if (error && screenshots.length === 0) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold mb-4"> Screenshots</h3>
        <div className="p-4 bg-red-50 text-red-700 rounded-lg">
          Error loading screenshots: {error}
        </div>
      </div>
    );
  }

  if (screenshots.length === 0) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold mb-4"> Screenshots</h3>
        <div className="p-4 bg-yellow-50 text-yellow-700 rounded-lg">
          No screenshots available yet. They will appear here as the booking progresses.
        </div>
      </div>
    );
  }

  // const groupedScreenshots = groupScreenshotsByType();

  // return (
  //   <div className="mb-6">
  //     {/* Screenshots section */}
  //     <div className="">
  //       <div className="space-y-2 p-3 rounded-lg">
  //         {Object.entries(groupedScreenshots).map(([type, shots]) => (
  //           <div key={type} className=" rounded-md mb-1">
  //             <div
  //               className="bg-gradient-to-br from-blue-100 mb-1 to-gray-400 p-2 cursor-pointer transition-all duration-200 border-l-4 rounded-md"
  //               onClick={() => setSelectedCategory(selectedCategory === type ? '' : type)}

  //             >
  //               <div className="flex justify-between items-center">
  //                 <h3 className={`font-medium ${selectedCategory === type ? 'text-blue-900' : 'text-gray-700'}`}>{type} ({shots.length})</h3>
  //                 <svg
  //                   className={`w-5 h-5 transform transition-transform duration-200 ${selectedCategory === type ? 'text-green-600 rotate-180' : 'text-blue-900'}`}
  //                   fill="none"
  //                   stroke="currentColor"
  //                   viewBox="0 0 24 24"
  //                   xmlns="http://www.w3.org/2000/svg"
  //                 >
  //                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
  //                 </svg>
  //               </div>
  //             </div>

  //             {selectedCategory === type && (
  //               <div className="grid grid-cols-1 p-3 bg-white ">
  //                 {shots.map(screenshot => (
  //                   <div
  //                     key={screenshot.id}
  //                     className="rounded-lg overflow-hidden cursor-pointer my-2"
  //                     onClick={() => handleScreenshotClick(screenshot)}
  //                   >
  //                     <div className="aspect-w-5 aspect-h-9 relative" style={{ width: '50%', height: '70px', margin: '0 auto' }}>
  //                       {/* Display actual image */}
  //                       <CachedImage
  //                         screenshot={screenshot}
  //                         className="w-full h-full object-contain"
  //                       />
  //                       {screenshot.cabin_id && (
  //                         <span className="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded">
  //                           Cabin {screenshot.cabin_id}
  //                         </span>
  //                       )}
  //                       <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-sm">
  //                         <p className="font-medium">{getCleanFileName(screenshot.file_name)}</p>
  //                         <p className="text-xs opacity-80">{formatDate(screenshot.timestamp)}</p>
  //                       </div>
  //                     </div>
  //                   </div>
  //                 ))}
  //               </div>
  //             )}
  //           </div>
  //         ))}
  //       </div>
  //     </div>

  //     {/* Modal for viewing screenshots */}
  //     {renderScreenshotModal()}
  //   </div>
  // );

  const groupedScreenshots = groupScreenshotsByCabin();

return (
  <div className="mb-6">
    {/* Screenshots section */}
    <div className="">
      <div className="space-y-2 p-3 rounded-lg">
        {Object.entries(groupedScreenshots).map(([cabinId, shots]) => (
          <div key={cabinId} className="rounded-md mb-1">
            <div
              className="bg-gradient-to-br from-blue-100 mb-1 to-gray-400 p-2 cursor-pointer transition-all duration-200 border-l-4 rounded-md"
              onClick={() => setSelectedCategory(selectedCategory === cabinId ? '' : cabinId)}
            >
              <div className="flex justify-between items-center">
                <h3 className={`font-medium ${selectedCategory === cabinId ? 'text-blue-900' : 'text-gray-700'}`}>
                  Cabin {cabinId} ({shots.length})
                </h3>
                <svg
                  className={`w-5 h-5 transform transition-transform duration-200 ${selectedCategory === cabinId ? 'text-green-600 rotate-180' : 'text-blue-900'}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>

            {selectedCategory === cabinId && (
              <div className="grid grid-cols-1 p-3 bg-white ">
                {shots.map(screenshot => (
                  <div
                    key={screenshot.id}
                    className="rounded-lg overflow-hidden cursor-pointer my-2"
                    onClick={() => handleScreenshotClick(screenshot)}
                  >
                    <div className="aspect-w-5 aspect-h-9 relative" style={{ width: '50%', height: '70px', margin: '0 auto' }}>
                      {/* Display actual image */}
                      <CachedImage
                        screenshot={screenshot}
                        className="w-full h-full object-contain"
                      />
                      {screenshot.cabin_id && (
                        <span className="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded">
                          Cabin {screenshot.cabin_id}
                        </span>
                      )}
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-sm">
                        <p className="font-medium">{getCleanFileName(screenshot.file_name)}</p>
                        <p className="text-xs opacity-80">{formatDate(screenshot.timestamp)}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>

    {/* Modal for viewing screenshots */}
    {renderScreenshotModal()}
  </div>
);

};

export default ScreenshotDisplay;
