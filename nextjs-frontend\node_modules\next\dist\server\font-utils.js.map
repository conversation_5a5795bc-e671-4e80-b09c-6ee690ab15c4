{"version": 3, "sources": ["../../src/server/font-utils.ts"], "names": ["getFontDefinitionFromNetwork", "calculateOverrideValues", "calculateSizeAdjustValues", "getFontOverrideCss", "capsizeFontsMetrics", "require", "CHROME_UA", "IE_UA", "isGoogleFont", "url", "startsWith", "GOOGLE_FONT_PROVIDER", "getFontForUA", "UA", "res", "fetch", "headers", "text", "result", "e", "Log", "warn", "parseGoogleFontName", "css", "regex", "matches", "matchAll", "fontNames", "Set", "font", "fontFamily", "replace", "add", "formatName", "str", "word", "index", "toLowerCase", "toUpperCase", "formatOverrideValue", "val", "Math", "abs", "toFixed", "fontName", "font<PERSON>ey", "fontMetrics", "category", "ascent", "descent", "lineGap", "unitsPerEm", "fallbackFont", "DEFAULT_SERIF_FONT", "DEFAULT_SANS_SERIF_FONT", "name", "xWidthAvg", "mainFontAvgWidth", "fallback<PERSON>ontName", "fallbackFontMetrics", "fallbackFontAvgWidth", "sizeAdjust", "calculateOverrideCSS", "trim", "calculateSizeAdjustCSS", "useSizeAdjust", "calcFn", "fontCss", "reduce", "cssStr", "console", "log"], "mappings": ";;;;;;;;;;;;;;;;;IA4BsBA,4BAA4B;eAA5BA;;IAgDNC,uBAAuB;eAAvBA;;IAkBAC,yBAAyB;eAAzBA;;IA8DAC,kBAAkB;eAAlBA;;;6DA5JK;2BAKd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACP,MAAMC,sBAAsBC,QAAQ;AAEpC,MAAMC,YACJ;AACF,MAAMC,QAAQ;AASd,SAASC,aAAaC,GAAW;IAC/B,OAAOA,IAAIC,UAAU,CAACC,+BAAoB;AAC5C;AAEA,eAAeC,aAAaH,GAAW,EAAEI,EAAU;IACjD,MAAMC,MAAM,MAAMC,MAAMN,KAAK;QAAEO,SAAS;YAAE,cAAcH;QAAG;IAAE;IAC7D,OAAO,MAAMC,IAAIG,IAAI;AACvB;AAEO,eAAejB,6BACpBS,GAAW;IAEX,IAAIS,SAAS;IACb;;;GAGC,GACD,IAAI;QACF,IAAIV,aAAaC,MAAM;YACrBS,UAAU,MAAMN,aAAaH,KAAKF;QACpC;QACAW,UAAU,MAAMN,aAAaH,KAAKH;IACpC,EAAE,OAAOa,GAAG;QACVC,KAAIC,IAAI,CACN,CAAC,sCAAsC,EAAEZ,IAAI,+BAA+B,CAAC;QAE/E,OAAO;IACT;IAEA,OAAOS;AACT;AAEA,SAASI,oBAAoBC,GAAW;IACtC,MAAMC,QAAQ;IACd,MAAMC,UAAUF,IAAIG,QAAQ,CAACF;IAC7B,MAAMG,YAAY,IAAIC;IAEtB,KAAK,IAAIC,QAAQJ,QAAS;QACxB,MAAMK,aAAaD,IAAI,CAAC,EAAE,CAACE,OAAO,CAAC,gBAAgB;QACnDJ,UAAUK,GAAG,CAACF;IAChB;IAEA,OAAO;WAAIH;KAAU;AACvB;AAEA,SAASM,WAAWC,GAAW;IAC7B,OAAOA,IACJH,OAAO,CAAC,uBAAuB,SAAUI,IAAI,EAAEC,KAAK;QACnD,OAAOA,UAAU,IAAID,KAAKE,WAAW,KAAKF,KAAKG,WAAW;IAC5D,GACCP,OAAO,CAAC,QAAQ;AACrB;AAEA,SAASQ,oBAAoBC,GAAW;IACtC,OAAOC,KAAKC,GAAG,CAACF,MAAM,KAAKG,OAAO,CAAC;AACrC;AAEO,SAAS1C,wBAAwB2C,QAAgB;IACtD,MAAMC,UAAUZ,WAAWW;IAC3B,MAAME,cAAc1C,mBAAmB,CAACyC,QAAQ;IAChD,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAE,GAAGL;IACzD,MAAMM,eACJL,aAAa,UAAUM,6BAAkB,GAAGC,kCAAuB;IACrEN,SAAST,oBAAoBS,SAASG;IACtCF,UAAUV,oBAAoBU,UAAUE;IACxCD,UAAUX,oBAAoBW,UAAUC;IAExC,OAAO;QACLH;QACAC;QACAC;QACAE,cAAcA,aAAaG,IAAI;IACjC;AACF;AAEO,SAASrD,0BAA0B0C,QAAgB;IACxD,MAAMC,UAAUZ,WAAWW;IAC3B,MAAME,cAAc1C,mBAAmB,CAACyC,QAAQ;IAChD,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEK,SAAS,EAAE,GAC/DV;IACF,MAAMW,mBAAmBD,YAAYL;IACrC,MAAMC,eACJL,aAAa,UAAUM,6BAAkB,GAAGC,kCAAuB;IACrE,MAAMI,mBAAmBzB,WAAWmB,aAAaG,IAAI;IACrD,MAAMI,sBAAsBvD,mBAAmB,CAACsD,iBAAiB;IACjE,MAAME,uBACJD,oBAAoBH,SAAS,GAAGG,oBAAoBR,UAAU;IAChE,IAAIU,aAAaL,YAAYC,mBAAmBG,uBAAuB;IAEvEZ,SAAST,oBAAoBS,SAAUG,CAAAA,aAAaU,UAAS;IAC7DZ,UAAUV,oBAAoBU,UAAWE,CAAAA,aAAaU,UAAS;IAC/DX,UAAUX,oBAAoBW,UAAWC,CAAAA,aAAaU,UAAS;IAE/D,OAAO;QACLb;QACAC;QACAC;QACAE,cAAcA,aAAaG,IAAI;QAC/BM,YAAYtB,oBAAoBsB;IAClC;AACF;AAEA,SAASC,qBAAqBjC,IAAY;IACxC,MAAMe,WAAWf,KAAKkC,IAAI;IAE1B,MAAM,EAAEf,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEE,YAAY,EAAE,GAC9CnD,wBAAwB2C;IAE1B,OAAO,CAAC;;oBAEU,EAAEA,SAAS;uBACR,EAAEI,OAAO;wBACR,EAAEC,QAAQ;yBACT,EAAEC,QAAQ;kBACjB,EAAEE,aAAa;;EAE/B,CAAC;AACH;AAEA,SAASY,uBAAuBnC,IAAY;IAC1C,MAAMe,WAAWf,KAAKkC,IAAI;IAE1B,MAAM,EAAEf,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEE,YAAY,EAAES,UAAU,EAAE,GAC1D3D,0BAA0B0C;IAE5B,OAAO,CAAC;;oBAEU,EAAEA,SAAS;uBACR,EAAEI,OAAO;wBACR,EAAEC,QAAQ;yBACT,EAAEC,QAAQ;mBAChB,EAAEW,WAAW;kBACd,EAAET,aAAa;;EAE/B,CAAC;AACH;AAEO,SAASjD,mBACdM,GAAW,EACXc,GAAW,EACX0C,gBAAgB,KAAK;IAErB,IAAI,CAACzD,aAAaC,MAAM;QACtB,OAAO;IACT;IAEA,MAAMyD,SAASD,gBAAgBD,yBAAyBF;IAExD,IAAI;QACF,MAAMnC,YAAYL,oBAAoBC;QAEtC,MAAM4C,UAAUxC,UAAUyC,MAAM,CAAC,CAACC,QAAQzB;YACxCyB,UAAUH,OAAOtB;YACjB,OAAOyB;QACT,GAAG;QAEH,OAAOF;IACT,EAAE,OAAOhD,GAAG;QACVmD,QAAQC,GAAG,CAAC,yCAAyCpD;QACrD,OAAO;IACT;AACF"}