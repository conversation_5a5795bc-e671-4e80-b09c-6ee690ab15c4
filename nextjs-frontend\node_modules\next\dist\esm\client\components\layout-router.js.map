{"version": 3, "sources": ["../../../src/client/components/layout-router.tsx"], "names": ["React", "useContext", "use", "startTransition", "Suspense", "ReactDOM", "CacheStates", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "fetchServerResponse", "createInfinitePromise", "Error<PERSON>ou<PERSON><PERSON>", "matchSegment", "handleSmoothScroll", "RedirectBoundary", "NotFoundBoundary", "getSegmentValue", "createRouterCache<PERSON>ey", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "hasOwnProperty", "subTree", "undefined", "slice", "findDOMNode", "instance", "window", "process", "env", "NODE_ENV", "originalConsoleError", "console", "error", "messages", "includes", "rectProperties", "shouldSkipElement", "element", "getComputedStyle", "position", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "nextElement<PERSON><PERSON>ling", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "Error", "InnerLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "url", "childNodes", "tree", "cache<PERSON>ey", "buildId", "changeByServerResponse", "fullTree", "childNode", "get", "status", "LAZY_INITIALIZED", "refetchTree", "DATA_FETCH", "data", "URL", "location", "origin", "nextUrl", "subTreeData", "head", "parallelRoutes", "Map", "set", "flightData", "overrideCanonicalUrl", "setTimeout", "subtree", "Provider", "value", "LoadingBoundary", "loading", "loadingStyles", "loadingScripts", "hasLoading", "fallback", "OuterLayoutRouter", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "notFoundStyles", "styles", "childNodesForParallelRouter", "treeSegment", "currentChildSegmentValue", "preservedSegments", "map", "preservedSegment", "preservedSegmentValue", "key", "errorComponent", "isActive"], "mappings": "AAAA;AAWA,OAAOA,SAASC,UAAU,EAAEC,GAAG,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,QAAO;AACzE,OAAOC,cAAc,YAAW;AAChC,SACEC,WAAW,EACXC,mBAAmB,EACnBC,yBAAyB,EACzBC,eAAe,QACV,qDAAoD;AAC3D,SAASC,mBAAmB,QAAQ,yCAAwC;AAC5E,SAASC,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,kBAAkB,QAAQ,qDAAoD;AACvF,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,oBAAoB,QAAQ,2CAA0C;AAE/E;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIZ,aAAaQ,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACK,cAAc,CAACH,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMG,UAAUR,eACdS,WACAP,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBI,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLN,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBS,KAAK,CAAC,IACxBR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,4FAA4F;AAC5F;;CAEC,GACD,SAASS,YACPC,QAAoD;IAEpD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAC1C,wDAAwD;IACxD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,uBAAuBC,QAAQC,KAAK;QAC1C,IAAI;YACFD,QAAQC,KAAK,GAAG;iDAAIC;oBAAAA;;gBAClB,4DAA4D;gBAC5D,IAAI,CAACA,QAAQ,CAAC,EAAE,CAACC,QAAQ,CAAC,6CAA6C;oBACrEJ,wBAAwBG;gBAC1B;YACF;YACA,OAAOlC,SAASyB,WAAW,CAACC;QAC9B,SAAU;YACRM,QAAQC,KAAK,GAAGF;QAClB;IACF;IACA,OAAO/B,SAASyB,WAAW,CAACC;AAC9B;AAEA,MAAMU,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACH,QAAQ,CAACI,iBAAiBD,SAASE,QAAQ,GAAG;QACpE,IAAIZ,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1CE,QAAQS,IAAI,CACV,4FACAH;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMI,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOP,eAAeQ,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBR,OAAoB,EAAES,cAAsB;IAC1E,MAAML,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,yBAAxBC,2BACA,8FAA8F;IAC9FA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE;AAE/C;AAMA,MAAMK,mCAAmC5D,MAAM6D,SAAS;IAoGtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;;aAhHAN,wBAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAAC9C,MAAM,KAAK,KAC1C,CAACyC,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYrB,KAAK,CAAC,CAAC3B,SAASoD,QAC1B7D,aAAaS,SAASmD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMpB,eAAeW,kBAAkBX,YAAY;gBAEnD,IAAIA,cAAc;oBAChBoB,UAAUrB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACoB,SAAS;oBACZA,UAAU7C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE6C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMnC,kBAAkBiC,SAAU;oBACtE,uGAAuG;oBACvG,IAAIA,QAAQG,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAH,UAAUA,QAAQG,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7EZ,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBX,YAAY,GAAG;gBACjCW,kBAAkBK,YAAY,GAAG,EAAE;gBAEnCzD,mBACE;oBACE,uEAAuE;oBACvE,IAAIyC,cAAc;wBACdoB,QAAwBI,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAcxB,SAASyB,eAAe;oBAC5C,MAAM7B,iBAAiB4B,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAI/B,uBAAuBwB,SAAwBvB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7H4B,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAAChC,uBAAuBwB,SAAwBvB,iBAAiB;wBAEjEuB,QAAwBI,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBnB,kBAAkBmB,cAAc;gBAClD;gBAGF,wEAAwE;gBACxEnB,kBAAkBmB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3BV,QAAQW,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BjB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMmB,UAAUvF,WAAWO;IAC3B,IAAI,CAACgF,SAAS;QACZ,MAAM,IAAIC,MAAM;IAClB;IAEA,qBACE,oBAAC7B;QACCU,aAAaA;QACbJ,mBAAmBsB,QAAQtB,iBAAiB;OAE3CG;AAGP;AAEA;;CAEC,GACD,SAASqB,kBAAkB,KAiB1B;IAjB0B,IAAA,EACzBC,iBAAiB,EACjBC,GAAG,EACHC,UAAU,EACVvB,WAAW,EACXwB,IAAI,EACJ,oDAAoD;IACpD,YAAY;IACZC,QAAQ,EAST,GAjB0B;IAkBzB,MAAMP,UAAUvF,WAAWO;IAC3B,IAAI,CAACgF,SAAS;QACZ,MAAM,IAAIC,MAAM;IAClB;IAEA,MAAM,EAAEO,OAAO,EAAEC,sBAAsB,EAAEH,MAAMI,QAAQ,EAAE,GAAGV;IAE5D,yDAAyD;IACzD,IAAIW,YAAYN,WAAWO,GAAG,CAACL;IAE/B,oGAAoG;IACpG,IAAI,CAACI,aAAaA,UAAUE,MAAM,KAAK/F,YAAYgG,gBAAgB,EAAE;QACnE;;KAEC,GACD,sBAAsB;QACtB,MAAMC,cAAcpF,eAAe;YAAC;eAAOmD;SAAY,EAAE4B;QAEzDC,YAAY;YACVE,QAAQ/F,YAAYkG,UAAU;YAC9BC,MAAM/F,oBACJ,IAAIgG,IAAId,KAAKe,SAASC,MAAM,GAC5BL,aACAf,QAAQqB,OAAO,EACfb;YAEFc,aAAa;YACbC,MACEZ,aAAaA,UAAUE,MAAM,KAAK/F,YAAYgG,gBAAgB,GAC1DH,UAAUY,IAAI,GACdnF;YACNoF,gBACEb,aAAaA,UAAUE,MAAM,KAAK/F,YAAYgG,gBAAgB,GAC1DH,UAAUa,cAAc,GACxB,IAAIC;QACZ;QAEA;;KAEC,GACDpB,WAAWqB,GAAG,CAACnB,UAAUI;IAC3B;IAEA,kGAAkG;IAClG,IAAI,CAACA,WAAW;QACd,MAAM,IAAIV,MAAM;IAClB;IAEA,kGAAkG;IAClG,IAAIU,UAAUW,WAAW,IAAIX,UAAUM,IAAI,EAAE;QAC3C,MAAM,IAAIhB,MAAM;IAClB;IAEA,6FAA6F;IAC7F,IAAIU,UAAUM,IAAI,EAAE;QAClB;;KAEC,GACD,8DAA8D;QAC9D,MAAM,CAACU,YAAYC,qBAAqB,GAAGlH,IAAIiG,UAAUM,IAAI;QAE7D,sEAAsE;QACtEN,UAAUM,IAAI,GAAG;QAEjB,wGAAwG;QACxGY,WAAW;YACTlH,gBAAgB;gBACd8F,uBAAuBC,UAAUiB,YAAYC;YAC/C;QACF;QACA,yGAAyG;QACzGlH,IAAIS;IACN;IAEA,yIAAyI;IACzI,wFAAwF;IACxF,IAAI,CAACwF,UAAUW,WAAW,EAAE;QAC1B5G,IAAIS;IACN;IAEA,MAAM2G,UACJ,4EAA4E;kBAC5E,oBAAC/G,oBAAoBgH,QAAQ;QAC3BC,OAAO;YACL1B,MAAMA,IAAI,CAAC,EAAE,CAACH,kBAAkB;YAChCE,YAAYM,UAAUa,cAAc;YACpC,kDAAkD;YAClDpB,KAAKA;QACP;OAECO,UAAUW,WAAW;IAG1B,iFAAiF;IACjF,OAAOQ;AACT;AAEA;;;CAGC,GACD,SAASG,gBAAgB,KAYxB;IAZwB,IAAA,EACvBpD,QAAQ,EACRqD,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,UAAU,EAOX,GAZwB;IAavB,IAAIA,YAAY;QACd,qBACE,oBAACzH;YACC0H,wBACE,0CACGH,eACAC,gBACAF;WAIJrD;IAGP;IAEA,qBAAO,0CAAGA;AACZ;AAEA;;;CAGC,GACD,eAAe,SAAS0D,kBAAkB,KAgCzC;IAhCyC,IAAA,EACxCpC,iBAAiB,EACjBrB,WAAW,EACXhC,KAAK,EACL0F,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfT,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,UAAU,EACVO,QAAQ,EACRC,QAAQ,EACRC,cAAc,EACdC,MAAM,EAiBP,GAhCyC;IAiCxC,MAAM/C,UAAUvF,WAAWM;IAC3B,IAAI,CAACiF,SAAS;QACZ,MAAM,IAAIC,MAAM;IAClB;IAEA,MAAM,EAAEI,UAAU,EAAEC,IAAI,EAAEF,GAAG,EAAE,GAAGJ;IAElC,4CAA4C;IAC5C,IAAIgD,8BAA8B3C,WAAWO,GAAG,CAACT;IACjD,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAAC6C,6BAA6B;QAChCA,8BAA8B,IAAIvB;QAClCpB,WAAWqB,GAAG,CAACvB,mBAAmB6C;IACpC;IAEA,qCAAqC;IACrC,8IAA8I;IAC9I,MAAMC,cAAc3C,IAAI,CAAC,EAAE,CAACH,kBAAkB,CAAC,EAAE;IAEjD,gIAAgI;IAChI,MAAM+C,2BAA2BzH,gBAAgBwH;IAEjD;;GAEC,GACD,+DAA+D;IAC/D,MAAME,oBAA+B;QAACF;KAAY;IAElD,qBACE,0CACGF,QACAI,kBAAkBC,GAAG,CAAC,CAACC;QACtB,MAAMC,wBAAwB7H,gBAAgB4H;QAC9C,MAAM9C,WAAW7E,qBAAqB2H;QAEtC,OACE;;;;;;;;UAQA,iBACA,oBAACpI,gBAAgB8G,QAAQ;YACvBwB,KAAK7H,qBAAqB2H,kBAAkB;YAC5CrB,qBACE,oBAACjC;gBAAsBjB,aAAaA;6BAClC,oBAAC1D;gBACCoI,gBAAgB1G;gBAChB0F,aAAaA;gBACbC,cAAcA;6BAEd,oBAACR;gBACCI,YAAYA;gBACZH,SAASA;gBACTC,eAAeA;gBACfC,gBAAgBA;6BAEhB,oBAAC5G;gBACCqH,UAAUA;gBACVC,gBAAgBA;6BAEhB,oBAACvH,sCACC,oBAAC2E;gBACCC,mBAAmBA;gBACnBC,KAAKA;gBACLE,MAAMA;gBACND,YAAY2C;gBACZlE,aAAaA;gBACbyB,UAAUA;gBACVkD,UACEP,6BAA6BI;;WAU5CZ,gBACAC,iBACAC;IAGP;AAGN"}