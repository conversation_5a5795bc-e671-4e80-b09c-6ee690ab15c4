import React, { useState, useEffect } from 'react';
import { ProviderType } from './ProviderSelector';
import { getStudioResults, getNCLResults, getCruisingPowerResults, getOneSourceResults } from '../../services/api';
import MetricsDisplay from './MetricsDisplay';
import { ConfigState } from '../ui/Config';

interface BookingMetricsProps {
  provider: ProviderType;
  requestId: string;
  sessionId?: string;
  config?: ConfigState;
  cruiseDetails?: any;
}

// Define expected response types for each provider
interface StudioResults extends Array<any> { }

interface NCLResults {
  cabins: Array<{
    cabin_id?: string | number;
    category_code?: string;
    current_promos?: string[];
    passengers?: { total: number;[key: string]: any };
    onboard_credit?: number;
    final_price?: number;
    dining_package?: number;
    soda_package?: number;
    beverage_package?: number;
    [key: string]: any;
  }>;
  summary?: {
    total_passengers?: number;
    total_final_price?: number;
    [key: string]: any;
  };
  timestamp?: string;
  execution_time?: number | string;
  [key: string]: any;
}

interface CruisingPowerResults {
  cabins: Array<{
    cabin_number?: string | number;
    cabin_type?: string;
    normalized_type?: string;
    cabin_allocation?: string;
    total_price?: string | number;
    onboard_credit?: string | number;
    passengers?: { total: number;[key: string]: any };
    success?: boolean;
    [key: string]: any;
  }>;
  timestamp?: string;
  [key: string]: any;
}

interface OneSourceResults extends Array<{
  cabin_number?: number;
  category_type?: string;
  category_code?: string;
  selected_rate_code?: string;
  passenger_count?: number;
  commission?: number;
  gross_fare?: number;
  onboard_credit?: number;
  cabin_total?: number;
  status?: string;
  error?: string;
  [key: string]: any;
}> {}

interface OneSourceBookingInfo {
  total_cabins?: number;
  grand_total?: number;
  execution_time?: number;
  overall_status?: number;
  [key: string]: any;
}

const BookingMetrics: React.FC<BookingMetricsProps> = ({ provider, requestId, sessionId, config, cruiseDetails }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bookingInfo, setBookingInfo] = useState<any>(null);
  const [results, setResults] = useState<StudioResults | NCLResults | CruisingPowerResults | OneSourceResults | null>(null);

  // Helper function to extract category code and rate codes from cabin allocation
  const extractCategoryAndRateCodes = (cabinAllocation: string) => {
    const result = {
      categoryCode: '',
      rateCodes: 'N/A'
    };

    if (!cabinAllocation) return result;

    // Parse cabin allocation data - format looks like:
    // Allocated Category (Quantity) Fare
    // ,3B BOGO60 NRD
    // ,3B Savings NRD
    // ,3B Spring NRD

    const allocationLines = cabinAllocation.split('\n');
    const contentLines = allocationLines.filter((line: string) => line.trim() && line.includes(','));

    if (contentLines.length) {
      // Array to collect rate codes
      const rateCodesArray: string[] = [];

      // First, try to extract category code from the first line with a comma
      // The pattern is ",CATEGORY_CODE RATE_CODE"
      const firstLine = contentLines[0].trim();
      const firstParts = firstLine.split(',');

      if (firstParts.length > 1) {
        const firstCodePart = firstParts[1].trim();
        const spaceIndex = firstCodePart.indexOf(' ');

        if (spaceIndex > 0) {
          // If there's a space, the category code is everything before it (like "3B" in "3B BOGO60 NRD")
          result.categoryCode = firstCodePart.substring(0, spaceIndex);
        } else {
          // If no space, use the entire string as category code
          result.categoryCode = firstCodePart;
        }
      }

      // Extract all rate codes from all content lines
      contentLines.forEach((line: string) => {
        const parts = line.trim().split(',');
        if (parts.length > 1) {
          const codesPart = parts[1].trim();
          const components = codesPart.split(' ');

          // Rate codes are everything after the first component (after the category code)
          if (components.length > 1) {
            rateCodesArray.push(components.slice(1).join(' '));
          }
        }
      });

      if (rateCodesArray.length) {
        result.rateCodes = rateCodesArray.join(', ');
      }
    }

    return result;
  };

  // Helper function to determine NCL cabin category type from category code
  const getNCLCategoryType = (categoryCode: string): string => {
    if (!categoryCode) return 'Unknown';

    // Convert to uppercase for consistent matching
    const code = categoryCode.toUpperCase();

    // NCL cabin categories typically start with letters that indicate the type
    if (code.startsWith('I') || code.startsWith('IX')) {
      return 'Inside';
    } else if (code.startsWith('O') || code.startsWith('OX')) {
      return 'Oceanview';
    } else if (code.startsWith('B') || code.startsWith('BX')) {
      return 'Balcony';
    } else if (code.startsWith('M')) {
      return 'Mini-Suite';
    } else if (code.startsWith('S') || code.startsWith('H')) {
      return 'Suite';
    } else if (code.startsWith('T')) {
      return 'Studio';
    } else {
      // For any other codes, try to determine by the first character
      const firstChar = code.charAt(0);
      switch (firstChar) {
        case 'I': return 'Inside';
        case 'O': return 'Oceanview';
        case 'B': return 'Balcony';
        case 'M': return 'Mini-Suite';
        case 'S': return 'Suite';
        case 'T': return 'Studio';
        default: return 'Unknown';
      }
    }
  };

  // Helper function to get unique category types and their onboard credit percentages
  const getUniqueCategoryPercentages = (cabins: any[]) => {
    if (!config?.ncl_category_percentages) return [];

    const uniqueCategories = new Set<string>();
    const categoryPercentages: { categoryType: string; percentage: number }[] = [];

    cabins.forEach(cabin => {
      const categoryType = getNCLCategoryType(cabin.category_code || '');
      if (categoryType !== 'Unknown' && !uniqueCategories.has(categoryType)) {
        uniqueCategories.add(categoryType);
        
        // Map category type to config key
        let configKey = '';
        switch (categoryType) {
          case 'Inside':
            configKey = 'inside';
            break;
          case 'Oceanview':
            configKey = 'outside';
            break;
          case 'Balcony':
            configKey = 'balcony';
            break;
          case 'Mini-Suite':
            configKey = 'junior_suite';
            break;
          case 'Suite':
            configKey = 'suite';
            break;
          default:
            return; // Skip unknown categories
        }

        if (config.ncl_category_percentages[configKey as keyof typeof config.ncl_category_percentages]) {
          categoryPercentages.push({
            categoryType,
            percentage: config.ncl_category_percentages[configKey as keyof typeof config.ncl_category_percentages]
          });
        }
      }
    });

    return categoryPercentages;
  };

  useEffect(() => {
    let isMounted = true;
    let retryCount = 0;
    const maxRetries = 5;
    const retryDelay = 4000;
    
    const fetchBookingResults = async () => {
      try {
        if (!isMounted) return;
        
        setLoading(true);
        setError(null);

        let response;
        // Check provider formats to match the database/backend expectations
        if (provider.startsWith('Studio')) {
          // Handle all Studio provider variations
          response = await getStudioResults(requestId, sessionId);
        } else if (provider === 'NCL' || provider.startsWith('NCL.')) {
          // Handle all NCL provider variations
          response = await getNCLResults(requestId, sessionId);
        } else if (provider === 'Cruising Power' || provider.startsWith('Cruising Power.')) {
          // Handle all Cruising Power provider variations
          response = await getCruisingPowerResults(requestId, sessionId);
        } else if (provider === 'OneSource' || provider.startsWith('OneSource.')) {
          // Handle all OneSource provider variations
          response = await getOneSourceResults(requestId, sessionId);
        } else {
          throw new Error(`Unsupported provider: ${provider}`);
        }

        if (response.success) {
          if (isMounted) {
            setBookingInfo(response.bookingInfo);
            setResults(response.results || null);
          }
        } else {
          // If the session is still processing or data isn't available yet
          if ((response.status === 'processing' || response.error?.includes('No results found') || 
               response.error?.includes('still processing')) && retryCount < maxRetries) {
            retryCount++;
            if (isMounted) {
              // Show a more informative message
              const message = response.status === 'processing' || response.error?.includes('still processing')
                ? `Booking process still in progress (attempt ${retryCount}/${maxRetries})...`
                : `Waiting for booking data (attempt ${retryCount}/${maxRetries})...`;
              
              setError(message);
              // Schedule another attempt
              setTimeout(fetchBookingResults, retryDelay);
            }
          } else {
            if (isMounted) {
              setError(`Failed to fetch ${provider} results: ${response.error || 'Unknown error'}`);
            }
          }
        }
      } catch (err) {
        if (isMounted) {
          setError(`Error fetching booking results: ${err instanceof Error ? err.message : 'Unknown error'}`);
          console.error('Error fetching booking results:', err);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    if (requestId) {
      fetchBookingResults();
    }
    
    // Cleanup function to prevent state updates if component unmounts
    return () => {
      isMounted = false;
    };
  }, [provider, requestId, sessionId]);

  // Helper function to format prices with commas and two decimal places
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  // Render loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <svg className="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span className="ml-2 text-lg text-gray-700">Loading booking results...</span>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-300 text-red-700 p-4 rounded-lg mb-4">
        <p className="font-medium">Error Loading Results</p>
        <p className="text-sm">{error}</p>
        <p className="text-sm mt-2">
          If the booking just completed, the data might not be available yet.
          Please wait a few seconds and try refreshing the page.
        </p>
      </div>
    );
  }

  // Render empty state
  if (!bookingInfo && (!results || results.length === 0)) {
    return (
      <div className="bg-yellow-50 border border-yellow-300 text-yellow-800 p-4 rounded-lg mb-4">
        <p className="font-medium">No Results Found</p>
        <p className="text-sm">
          No booking results found for request ID: {requestId}.
          This could be because the booking is still in progress,
          or because the data hasn't been saved to the database yet.
        </p>
      </div>
    );
  }

  // Render Studio metrics
  const renderStudioMetrics = () => {
    const cabinResults = results as StudioResults;
    const grandTotalEntry = cabinResults.find(r => r.grand_total !== undefined);
    const grandTotal = grandTotalEntry ? grandTotalEntry.grand_total : 0;
    const executionTime = grandTotalEntry?.execution_time || bookingInfo?.execution_time;
    const actualCabinResults = cabinResults.filter(r => r.grand_total === undefined);
    const successfulCabins = actualCabinResults.filter(r => r.status === 'success').length;

    return (
      <div>
        <div className="grid grid-cols-4 gap-4 mb-6">
          <MetricsDisplay
            totalCabins={actualCabinResults.length}
            successfulCabins={successfulCabins}
            grandTotal={grandTotal}
            executionTime={executionTime}
            formatPrice={formatPrice}
          />
          <div className="col-span-3">
            {actualCabinResults.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-300 overflow-hidden mb-10">
                <div className="p-4 bg-gradient-to-r  from-teal-50 to-blue-50 border-b border-gray-200">
                  <div className="flex items-center">
                    
                    <h3 className="text-xl font-bold text-gray-800">Cost Summary</h3>
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gradient-to-r from-gray-100 to-gray-200">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Cabin</th>
                        <th className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Category</th>
                        <th className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Category Code</th>
                        <th className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Rate Code</th>
                        <th className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Passengers</th>
                        <th className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Total Cost</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {actualCabinResults.map((result, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white hover:bg-blue-50 transition-colors duration-150' : 'bg-gray-50 hover:bg-blue-50 transition-colors duration-150'}>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{result.selected_cabin || 'N/A'}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{result.category_type || 'N/A'}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{result.category_code || 'N/A'}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">
                            {result.selected_rate ? result.selected_rate.split("\n")[0] : 'N/A'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{result.passenger_count || 'N/A'}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-teal-700">
                            {formatPrice(result.cabin_total || 0)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render NCL metrics
  const renderNCLMetrics = () => {
    const nclResults = results as NCLResults;
    const { cabins = [], summary = {} } = nclResults;
    const totalCabins = cabins.length;
    const successfulCabins = cabins.filter(cabin =>
      (cabin.final_price || 0) > 0 && cabin.current_promos).length;

    return (
      <div>
        <div className="grid grid-cols-4 gap-4">
          <MetricsDisplay
            totalCabins={totalCabins}
            successfulCabins={successfulCabins}
            grandTotal={summary.total_final_price || 0}
            executionTime={nclResults.execution_time}
            formatPrice={formatPrice}
          />
          <div className="col-span-3">
            {cabins.length > 0 && (
              <div className="bg-white rounded-lg border overflow-hidden mb-10 border-gray-300">
                <div className="p-4 bg-gradient-to-r from-teal-50 to-blue-50 border-b ">
                  <div className="flex items-center">
                    
                    <h3 className="text-xl font-bold text-gray-800">Cost Summary</h3>
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gradient-to-r from-gray-100 to-gray-200">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category Type</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category Code</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate Codes</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dining Package</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Soda Package</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Beverage Package</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Passengers</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Onboard Credit</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Final Price</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {cabins.map((cabin, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            {getNCLCategoryType(cabin.category_code || '')}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            {(cabin.category_code || '').toUpperCase()}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            {cabin.current_promos?.join(', ') || 'None'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            {formatPrice(cabin.dining_package || 0)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            {formatPrice(cabin.soda_package || 0)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            {formatPrice(cabin.beverage_package || 0)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            {cabin.passengers?.total || 'N/A'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            {formatPrice(cabin.onboard_credit || 0)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 font-medium">
                            {formatPrice(cabin.final_price || 0)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {/* Onboard Credit Percentage Display */}
                {provider === 'NCL' && config?.ncl_category_percentages && (
                  <div className="p-4 bg-gray-50 border-t border-gray-200">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm font-medium text-gray-700">Onboard Credit Calculation Percentages:</span>
                    </div>
                    <div className="flex flex-wrap gap-3">
                      {getUniqueCategoryPercentages(cabins).map(({ categoryType, percentage }) => (
                        <div key={categoryType} className="bg-green-100 border border-green-300 rounded-md px-3 py-1">
                          <span className="text-sm font-medium text-green-800">
                            {categoryType} - {percentage}%
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render Cruising Power metrics
  const renderCruisingPowerMetrics = () => {
    const cpResults = results as CruisingPowerResults;
    const { cabins = [] } = cpResults;
    const totalCabins = cabins.length;
    const successfulCabins = cabins.filter(cabin => cabin.success).length;
    let grandTotal = 0;
    cabins.forEach(cabin => {
      let price = 0;
      if (typeof cabin.total_price === 'string') {
        const match = cabin.total_price.match(/([\d,]+\.?\d*)/);
        if (match) {
          price = parseFloat(match[0].replace(/,/g, ''));
        }
      } else if (typeof cabin.total_price === 'number') {
        price = cabin.total_price;
      }
      grandTotal += price;
    });

    return (
      <div>
        <div className='grid grid-cols-4 gap-4'>
          <MetricsDisplay
            totalCabins={totalCabins}
            successfulCabins={successfulCabins}
            grandTotal={grandTotal}
            executionTime={cpResults.execution_time}
            formatPrice={formatPrice}
          />
          <div className='col-span-3'>
            {cabins.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-300 overflow-hidden ">
                <div className="p-1 bg-gradient-to-r from-teal-50 to-blue-50 border-b border-gray-200">
                  <div className="flex items-center">
                    
                    <h3 className="text-xl font-bold text-gray-800">Cost Summary</h3>
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cabin</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category Code</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate Codes</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Passengers</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Onboard Credit</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Final Price</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {cabins.map((cabin, index) => {
                        // Process cabin allocation details to extract category code and rate codes
                        const { categoryCode, rateCodes } = extractCategoryAndRateCodes(cabin.cabin_allocation || '');

                        // Format total price for display
                        let displayPrice = cabin.total_price || '$0.00';
                        if (typeof displayPrice === 'string') {
                          // For strings, try to extract the numeric portion
                          const match = displayPrice.match(/([\d,]+\.?\d*)/);
                          if (match) {
                            // Successfully extracted - format it nicely
                            displayPrice = formatPrice(parseFloat(match[0].replace(/,/g, '')));
                          } else if (displayPrice.includes(':')) {
                            // Handle format like "Total for All Rooms(USD): 2,628.72"
                            const parts = displayPrice.split(':');
                            if (parts.length > 1) {
                              const numericPart = parts[1].trim().match(/([\d,]+\.?\d*)/);
                              if (numericPart) {
                                displayPrice = formatPrice(parseFloat(numericPart[0].replace(/,/g, '')));
                              }
                            }
                          } else if (!displayPrice.startsWith('$')) {
                            // Default formatting if no match and doesn't start with $
                            displayPrice = formatPrice(0);
                          }
                        } else if (typeof displayPrice === 'number') {
                          displayPrice = formatPrice(displayPrice);
                        }

                        // Format onboard credit
                        let onboardCredit = cabin.onboard_credit || '$0.00';
                        if (typeof onboardCredit !== 'string' || !onboardCredit.startsWith('$')) {
                          onboardCredit = formatPrice(typeof onboardCredit === 'number' ? onboardCredit : 0);
                        }

                        return (
                          <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                            <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-800">
                              {`Cabin ${cabin.cabin_number || index + 1}`}
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-800">
                              {cabin.cabin_type || 'Unknown'}
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-800">
                              {categoryCode || 'N/A'}
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-800">
                              {rateCodes}
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-800">
                              {cabin.passengers?.total || 3}
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-800">
                              {onboardCredit}
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-800 font-medium">
                              {displayPrice}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Function to determine commission percentage used for OneSource
  const getOneSourceCommissionInfo = () => {
    // Default commission percentages from config
    const commissionConfig = config?.onesource_commission_percentages || {
      holland: 12,
      princess: 11
    };

    // Try to determine cruise line from various sources
    let cruiseLine = '';
    let commissionPercentage = commissionConfig.princess; // Default to Princess
    let cruiseLineName = 'Unknown';

    // Check if we can get cruise line from various sources
    if (cruiseDetails?.cruise_line) {
      cruiseLine = cruiseDetails.cruise_line.toLowerCase();
    } else if (bookingInfo?.cruise_line) {
      cruiseLine = bookingInfo.cruise_line.toLowerCase();
    } else if (results && Array.isArray(results) && results.length > 0) {
      // Check if any cabin result has cruise line info
      const firstCabin = results[0];
      if (firstCabin?.cruise_line) {
        cruiseLine = firstCabin.cruise_line.toLowerCase();
      }
    }

    // Determine commission percentage based on cruise line
    if (cruiseLine.includes('holland') || cruiseLine.includes('hal') || cruiseLine.includes('holland america')) {
      commissionPercentage = commissionConfig.holland;
      cruiseLineName = 'Holland America';
    } else if (cruiseLine.includes('princess')) {
      commissionPercentage = commissionConfig.princess;
      cruiseLineName = 'Princess';
    } else {
      // Default to Princess percentage for unknown cruise lines
      commissionPercentage = commissionConfig.princess;
      cruiseLineName = cruiseLine || 'Unknown';
    }

    return {
      percentage: commissionPercentage,
      cruiseLineName,
      detectedCruiseLine: cruiseLine
    };
  };

  // Render OneSource metrics
  const renderOneSourceMetrics = () => {
    const cabinsRaw = results as any;
    // Ensure cabins is always an array, handling both possible response shapes
    const cabins: any[] = Array.isArray(cabinsRaw)
      ? (cabinsRaw as OneSourceResults)
      : (cabinsRaw?.cabins ?? []);
    const totalCabins = bookingInfo?.total_cabins || 0;
    const successfulCabins = cabins.filter((cabin: any) => cabin.status === 'success').length;
    const isOrCase = bookingInfo?.is_or_case || false;
    
    let grandTotal = 0;
    // Only calculate grand total if it's not an "or" case
    if (!isOrCase) {
      cabins.forEach((cabin: any) => {
        const price = cabin.cabin_total || 0;
        grandTotal += price;
      });
    }

    const commissionInfo = getOneSourceCommissionInfo();

    return (
      <div>
        <div className="grid grid-cols-4 gap-4">
          <MetricsDisplay
            totalCabins={totalCabins}
            successfulCabins={successfulCabins}
            grandTotal={grandTotal}
            executionTime={bookingInfo?.execution_time}
            formatPrice={formatPrice}
            showGrandTotalAsNA={isOrCase}
          />
          <div className="col-span-3">
            {cabins.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-300 overflow-hidden">
                <div className="p-4 bg-gradient-to-r from-teal-50 to-blue-50 border-b border-gray-200">
                  <div className="flex items-center">
                    
                    <h3 className="text-xl font-bold text-gray-800">Cost Summary</h3>
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cabin</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category Type</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category Code</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate Code</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Passengers</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commission</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Fare</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Onboard Credit</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cabin Total</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {cabins.map((cabin: any, index: number) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white hover:bg-blue-50 transition-colors duration-150' : 'bg-gray-50 hover:bg-blue-50 transition-colors duration-150'}>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{cabin.cabin_number || 'N/A'}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{cabin.category_type || 'N/A'}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{cabin.category_code || 'N/A'}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{cabin.selected_rate_code || 'N/A'}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{cabin.passenger_count || 'N/A'}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{formatPrice(cabin.commission || 0)}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{formatPrice(cabin.gross_fare || 0)}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800">{formatPrice(cabin.onboard_credit || 0)}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-teal-700">{formatPrice(cabin.cabin_total || 0)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
            
            {/* Commission Percentage Info - Simple green text with border */}
            {cabins.length > 0 && (
              <div className="mt-3 p-2 border-2 border-green-600 rounded-lg bg-green-10">
                <p className="text-sm font-medium text-green-600">
                  Commission Percentage: {commissionInfo.percentage}% ({commissionInfo.cruiseLineName !== 'Unknown' ? commissionInfo.cruiseLineName : 'Princess'})
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render the appropriate metrics based on provider
  return (
    <div className="">
        <h2 className="text-black text-3xl px-1.5 mb-1 font-semibold flex items-center">
          Overview
        </h2>

      <div className="p-2">
        {provider.startsWith('Studio') && renderStudioMetrics()}
        {(provider === 'NCL' || provider.startsWith('NCL.')) && renderNCLMetrics()}
        {(provider === 'Cruising Power' || provider.startsWith('Cruising Power.')) && renderCruisingPowerMetrics()}
        {(provider === 'OneSource' || provider.startsWith('OneSource.')) && renderOneSourceMetrics()}
      </div>
    </div>
  );
};

export default BookingMetrics; 