1. Update package lists and install PostgreSQL  
   ```bash
   sudo apt update
   sudo apt install -y postgresql postgresql-contrib
   ```
 
2. Ensure the service is enabled and running  
   ```bash
   sudo systemctl enable postgresql
   sudo systemctl start postgresql
   sudo systemctl status postgresql   # verify “active (running)”
   ```
 
3. Switch to the `postgres` superuser and create your DB and user  
   ```bash
   sudo -u postgres psql -c "CREATE ROLE <DB_USER> WITH LOGIN PASSWORD '<DB_PASSWORD>';"
   sudo -u postgres psql -c "CREATE DATABASE <DB_NAME> OWNER <DB_USER> ENCODING 'UTF8';"
   # (Optionally) grant additional privileges:
   sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE <DB_NAME> TO <DB_USER>;"
   ```
 
4. Allow remote TCP connections  
   a. Edit `postgresql.conf` to listen on all interfaces  
   ```bash
   sudo sed -i \"s/#listen_addresses = 'localhost'/listen_addresses = '*'/'\" \
     /etc/postgresql/*/main/postgresql.conf
   ```  
   b. Edit `pg_hba.conf` to trust/require MD5 from your app network  
   ```bash
   echo \"# allow app servers\nhost    all             all             **********/24          md5\" \
     | sudo tee -a /etc/postgresql/*/main/pg_hba.conf
   ```
 
5. Reload PostgreSQL to apply changes  
   ```bash
   sudo systemctl reload postgresql
   ```
 
6. (Optional) Open the port in Ubuntu’s firewall  
   ```bash
   sudo ufw allow 5432/tcp
   sudo ufw reload
   ```
 
7. Test connectivity from your local machine or app server  
   ```bash
   psql \"postgresql://<DB_USER>:<DB_PASSWORD>@***********:5432/<DB_NAME>\"
   ```
 
Once you can successfully connect, set your `DATABASE_URL` in `.env` or your environment to:  
```
DATABASE_URL=postgresql+psycopg2://<DB_USER>:<DB_PASSWORD>@***********:5432/<DB_NAME>
```