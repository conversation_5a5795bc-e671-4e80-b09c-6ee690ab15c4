{"version": 3, "sources": ["../../../../src/client/dev/error-overlay/hot-dev-client.ts"], "names": ["connect", "window", "__nextDevClientId", "Math", "round", "random", "Date", "now", "hadRuntimeError", "customHmrEventHandler", "MODE", "mode", "register", "addMessageListener", "payload", "processMessage", "err", "console", "warn", "stack", "subscribeToHmrEvent", "handler", "onUnrecoverableError", "isFirstCompilation", "mostRecentCompilationHash", "hasCompileErrors", "clearOutdatedErrors", "clear", "handleSuccess", "isHotUpdate", "__NEXT_DATA__", "page", "isUpdateAvailable", "tryApplyUpdates", "onBeforeFastRefresh", "onFastRefresh", "onBuildOk", "handleWarnings", "warnings", "printWarnings", "formatted", "formatWebpackMessages", "errors", "i", "length", "stripAnsi", "handleErrors", "onBuildError", "error", "process", "env", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "startLatency", "undefined", "updatedModules", "onBeforeRefresh", "onRefresh", "endLatency", "latency", "log", "sendMessage", "JSON", "stringify", "event", "id", "startTime", "endTime", "location", "pathname", "isPageHidden", "document", "visibilityState", "__NEXT_HMR_LATENCY_CB", "handleAvailableHash", "hash", "obj", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "BUILDING", "BUILT", "SYNC", "hasErrors", "Boolean", "errorCount", "clientId", "hasWarnings", "warningCount", "SERVER_COMPONENT_CHANGES", "reload", "SERVER_ERROR", "errorJSON", "message", "parse", "Error", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "onBeforeHotUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "performFullReload", "check", "then", "apply", "stackTrace", "split", "slice", "join"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,8EAA8E;AAC9E,qBAAqB;AACrB,2GAA2G;;;;;+BAoC3G;;;eAAwBA;;;;wBA5BjB;oEACe;2BAC0B;gFACd;kCACU;AAmB5CC,OAAOC,iBAAiB,GAAGC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AAEpE,IAAIC,kBAAkB;AACtB,IAAIC;AACJ,IAAIC,OAAgC;AACrB,SAASV,QAAQW,IAA6B;IAC3DD,OAAOC;IACPC,IAAAA,gBAAQ;IAERC,IAAAA,6BAAkB,EAAC,CAACC;QAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;YAC1B;QACF;QAEA,IAAI;YACFC,eAAeD;QACjB,EAAE,OAAOE,KAAU;gBAE+BA;YADhDC,QAAQC,IAAI,CACV,4BAA4BJ,UAAU,OAAQE,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKG,KAAK,YAAVH,aAAc,EAAC;QAEjE;IACF;IAEA,OAAO;QACLI,qBAAoBC,OAAY;YAC9BZ,wBAAwBY;QAC1B;QACAC;YACEd,kBAAkB;QACpB;IACF;AACF;AAEA,yDAAyD;AACzD,IAAIe,qBAAqB;AACzB,IAAIC,4BAA2C;AAC/C,IAAIC,mBAAmB;AAEvB,SAASC;IACP,4CAA4C;IAC5C,IAAI,OAAOT,YAAY,eAAe,OAAOA,QAAQU,KAAK,KAAK,YAAY;QACzE,IAAIF,kBAAkB;YACpBR,QAAQU,KAAK;QACf;IACF;AACF;AAEA,0BAA0B;AAC1B,SAASC;IACPF;IAEA,IAAIhB,SAAS,WAAW;QACtB,MAAMmB,cACJ,CAACN,sBACAtB,OAAO6B,aAAa,CAACC,IAAI,KAAK,aAAaC;QAC9CT,qBAAqB;QACrBE,mBAAmB;QAEnB,0CAA0C;QAC1C,IAAII,aAAa;YACfI,gBAAgBC,qBAAqBC;QACvC;IACF,OAAO;QACLC,IAAAA,iBAAS;IACX;AACF;AAEA,2CAA2C;AAC3C,SAASC,eAAeC,QAAa;IACnCZ;IAEA,MAAMG,cAAc,CAACN;IACrBA,qBAAqB;IACrBE,mBAAmB;IAEnB,SAASc;QACP,iCAAiC;QACjC,MAAMC,YAAYC,IAAAA,8BAAqB,EAAC;YACtCH,UAAUA;YACVI,QAAQ,EAAE;QACZ;QAEA,IAAI,OAAOzB,YAAY,eAAe,OAAOA,QAAQC,IAAI,KAAK,YAAY;gBACpDsB;YAApB,IAAK,IAAIG,IAAI,GAAGA,MAAIH,sBAAAA,UAAUF,QAAQ,qBAAlBE,oBAAoBI,MAAM,GAAED,IAAK;gBACnD,IAAIA,MAAM,GAAG;oBACX1B,QAAQC,IAAI,CACV,+CACE;oBAEJ;gBACF;gBACAD,QAAQC,IAAI,CAAC2B,IAAAA,kBAAS,EAACL,UAAUF,QAAQ,CAACK,EAAE;YAC9C;QACF;IACF;IAEAJ;IAEA,0CAA0C;IAC1C,IAAIV,aAAa;QACfI,gBAAgBC,qBAAqBC;IACvC;AACF;AAEA,kEAAkE;AAClE,SAASW,aAAaJ,MAAW;IAC/BhB;IAEAH,qBAAqB;IACrBE,mBAAmB;IAEnB,8BAA8B;IAC9B,IAAIe,YAAYC,IAAAA,8BAAqB,EAAC;QACpCC,QAAQA;QACRJ,UAAU,EAAE;IACd;IAEA,6BAA6B;IAC7BS,IAAAA,oBAAY,EAACP,UAAUE,MAAM,CAAC,EAAE;IAEhC,gCAAgC;IAChC,IAAI,OAAOzB,YAAY,eAAe,OAAOA,QAAQ+B,KAAK,KAAK,YAAY;QACzE,IAAK,IAAIL,IAAI,GAAGA,IAAIH,UAAUE,MAAM,CAACE,MAAM,EAAED,IAAK;YAChD1B,QAAQ+B,KAAK,CAACH,IAAAA,kBAAS,EAACL,UAAUE,MAAM,CAACC,EAAE;QAC7C;IACF;IAEA,gCAAgC;IAChC,0CAA0C;IAC1C,IAAIM,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QAChC,IAAIC,KAAKC,aAAa,EAAE;YACtBD,KAAKC,aAAa,CAACb,UAAUE,MAAM,CAAC,EAAE;YACtCU,KAAKC,aAAa,GAAG;QACvB;IACF;AACF;AAEA,IAAIC,eAAoBC;AAExB,SAASrB,oBAAoBsB,cAAwB;IACnD,IAAIA,eAAeZ,MAAM,GAAG,GAAG;QAC7B,2DAA2D;QAC3D,sBAAsB;QACtBa,IAAAA,uBAAe;IACjB;AACF;AAEA,SAAStB,cAAcqB,cAAwB;IAC7CpB,IAAAA,iBAAS;IACT,IAAIoB,eAAeZ,MAAM,GAAG,GAAG;QAC7B,sDAAsD;QACtD,4BAA4B;QAC5Bc,IAAAA,iBAAS;IACX;IAEA,IAAIJ,cAAc;QAChB,MAAMK,aAAarD,KAAKC,GAAG;QAC3B,MAAMqD,UAAUD,aAAaL;QAC7BrC,QAAQ4C,GAAG,CAAC,AAAC,4BAAyBD,UAAQ;QAC9CE,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;YACbC,OAAO;YACPC,IAAIjE,OAAOC,iBAAiB;YAC5BiE,WAAWb;YACXc,SAAST;YACT5B,MAAM9B,OAAOoE,QAAQ,CAACC,QAAQ;YAC9Bd;YACA,oEAAoE;YACpE,sDAAsD;YACtDe,cAAcC,SAASC,eAAe,KAAK;QAC7C;QAEF,IAAIrB,KAAKsB,qBAAqB,EAAE;YAC9BtB,KAAKsB,qBAAqB,CAACd;QAC7B;IACF;AACF;AAEA,kDAAkD;AAClD,SAASe,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCpD,4BAA4BoD;AAC9B;AAEA,mCAAmC;AACnC,SAAS7D,eAAe8D,GAAqB;IAC3C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;QACtB;IACF;IAEA,OAAQA,IAAIC,MAAM;QAChB,KAAKC,6CAA2B,CAACC,QAAQ;YAAE;gBACzC1B,eAAehD,KAAKC,GAAG;gBACvBU,QAAQ4C,GAAG,CAAC;gBACZ;YACF;QACA,KAAKkB,6CAA2B,CAACE,KAAK;QACtC,KAAKF,6CAA2B,CAACG,IAAI;YAAE;gBACrC,IAAIL,IAAID,IAAI,EAAE;oBACZD,oBAAoBE,IAAID,IAAI;gBAC9B;gBAEA,MAAM,EAAElC,MAAM,EAAEJ,QAAQ,EAAE,GAAGuC;gBAC7B,MAAMM,YAAYC,QAAQ1C,UAAUA,OAAOE,MAAM;gBACjD,IAAIuC,WAAW;oBACbrB,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPoB,YAAY3C,OAAOE,MAAM;wBACzB0C,UAAUrF,OAAOC,iBAAiB;oBACpC;oBAEF,OAAO4C,aAAaJ;gBACtB;gBAEA,MAAM6C,cAAcH,QAAQ9C,YAAYA,SAASM,MAAM;gBACvD,IAAI2C,aAAa;oBACfzB,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPuB,cAAclD,SAASM,MAAM;wBAC7B0C,UAAUrF,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOmC,eAAeC;gBACxB;gBAEAwB,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPqB,UAAUrF,OAAOC,iBAAiB;gBACpC;gBAEF,OAAO0B;YACT;QACA,KAAKmD,6CAA2B,CAACU,wBAAwB;YAAE;gBACzDxF,OAAOoE,QAAQ,CAACqB,MAAM;gBACtB;YACF;QACA,KAAKX,6CAA2B,CAACY,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGf;gBACtB,IAAIe,WAAW;oBACb,MAAM,EAAEC,OAAO,EAAE1E,KAAK,EAAE,GAAG4C,KAAK+B,KAAK,CAACF;oBACtC,MAAM5C,QAAQ,IAAI+C,MAAMF;oBACxB7C,MAAM7B,KAAK,GAAGA;oBACd2B,aAAa;wBAACE;qBAAM;gBACtB;gBACA;YACF;QACA;YAAS;gBACP,IAAIvC,uBAAuB;oBACzBA,sBAAsBoE;oBACtB;gBACF;gBACA;YACF;IACF;AACF;AAEA,mDAAmD;AACnD,SAAS7C;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOR,8BAA8BwE;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,yIAAyI;IACzI,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAc;IACvC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASjF,QAAQ+E,MAAc;YAC7B,IAAIA,WAAW,QAAQ;gBACrB,yIAAyI;gBACzIF,OAAOC,GAAG,CAACI,mBAAmB,CAAClF;gBAC/BiF;YACF;QACF;QACA,yIAAyI;QACzIJ,OAAOC,GAAG,CAACK,gBAAgB,CAACnF;IAC9B;AACF;AAEA,iEAAiE;AACjE,SAASY,gBACPwE,iBAAsE,EACtEC,kBAAyD;IAEzD,yIAAyI;IACzI,IAAI,CAACR,OAAOC,GAAG,EAAE;QACf,8DAA8D;QAC9DlF,QAAQ+B,KAAK,CAAC;QACd,4BAA4B;QAC5B;IACF;IAEA,IAAI,CAAChB,uBAAuB,CAACiE,mBAAmB;QAC9C7D,IAAAA,iBAAS;QACT;IACF;IAEA,SAASuE,mBAAmB3F,GAAQ,EAAEwC,cAA+B;QACnE,IAAIxC,OAAOR,mBAAmB,CAACgD,gBAAgB;YAC7C,IAAIxC,KAAK;gBACPC,QAAQC,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAIV,iBAAiB;gBAC1BS,QAAQC,IAAI,CACV;YAEJ;YACA0F,kBAAkB5F;YAClB;QACF;QAEA,IAAI,OAAO0F,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmBlD;QACrB;QAEA,IAAIxB,qBAAqB;YACvB,+DAA+D;YAC/D,6DAA6D;YAC7DC,gBACEuB,eAAeZ,MAAM,GAAG,IAAIW,YAAYkD,mBACxCjD,eAAeZ,MAAM,GAAG,IAAIR,iBAAS,GAAGsE;QAE5C,OAAO;YACLtE,IAAAA,iBAAS;YACT,IAAIa,QAAQC,GAAG,CAACC,gBAAgB,EAAE;gBAChCkD,kBAAkB;oBAChB,IAAIjD,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,yIAAyI;IACzI6C,OAAOC,GAAG,CACPU,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACtD;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAOiD,sBAAsB,YAAY;YAC3CA,kBAAkBjD;QACpB;QACA,yIAAyI;QACzI,OAAO0C,OAAOC,GAAG,CAACY,KAAK;IACzB,GACCD,IAAI,CACH,CAACtD;QACCmD,mBAAmB,MAAMnD;IAC3B,GACA,CAACxC;QACC2F,mBAAmB3F,KAAK;IAC1B;AAEN;AAEA,SAAS4F,kBAAkB5F,GAAQ;IACjC,MAAMgG,aACJhG,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAAC8F,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDnG,IAAI6E,OAAO,IACX7E,MAAM,EAAC;IAEX8C,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;QACbC,OAAO;QACP+C;QACAxG,iBAAiB,CAAC,CAACA;IACrB;IAGFP,OAAOoE,QAAQ,CAACqB,MAAM;AACxB"}