import React, { useState, useEffect } from 'react';
import { ProviderType } from './ProviderSelector';
import { ConfigState } from '../ui/Config';
import { startBooking, getBookingStatus, cancelBooking, submitValidation } from '../../services/api';

import ValidationModal from './ValidationModal';
import { useLogContext } from '../../contexts/LogContext';
import StatusContainer from '../ui/StatusContainer';

interface BookingProcessProps {
  provider: ProviderType;
  cruiseDetails: any;
  config: ConfigState;
  requestId: string;
  onReset: () => void;
}
 
const BookingProcess: React.FC<BookingProcessProps> = ({
  provider,
  cruiseDetails,
  config,
  requestId,
  onReset
}) => {
  const { clearLogs, setActiveSessionId } = useLogContext();
  const [showValidation, setShowValidation] = useState(false);
  const [isBookingStarted, setIsBookingStarted] = useState(false);
  const [isBookingComplete, setIsBookingComplete] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [status, setStatus] = useState<string>('Waiting to start booking...');
  const [error, setError] = useState<string | null>(null);
  const [executionTime, setExecutionTime] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [isReprocessing, setIsReprocessing] = useState<boolean>(false);
 
  // Automatically show validation modal when booking completes
  useEffect(() => {
    if (isBookingComplete && sessionId) {
      setShowValidation(true);
    }
  }, [isBookingComplete, sessionId]);
 
  // Reset booking state and start again
  const reprocessBooking = async () => {
    // Clear logs and reset all states
    clearLogs();
    setIsReprocessing(true);
    setIsBookingComplete(false);
    setIsBookingStarted(false);
    setStatus('Waiting to start booking...');
    setProgress(0);
    setExecutionTime(null);
    setError(null);
    
    // Mark the current booking as reprocessed and cancel without reset
    if (sessionId) {
      try {
        // First, mark the current booking as reprocessed
        await submitValidation({
          session_id: sessionId,
          provider: provider,
          request_id: requestId,
          val_status: 'reprocessed',
          issue_codes: [],
          val_mark: '',
          as_per_feature: '',
          val_comment: ''
        });
        console.log(`Marked booking as reprocessed for session ${sessionId}`);
        
        // Then cancel the booking without setting reset status
        await cancelBooking(sessionId, false);
        console.log(`Cancelled booking for session ${sessionId} (reprocess flow)`);
      } catch (error) {
        console.error('Error during reprocess flow:', error);
        // Continue with reprocessing even if validation/cancellation fails
      }
    }
    
    // Use setTimeout to ensure state updates have time to propagate
    // before starting the new booking process
    setTimeout(() => {
      handleStartBooking();
    }, 100);
  };
 
  // Start the booking process
  const handleStartBooking = async () => {
    try {
      setIsBookingStarted(true);
      setStatus('Starting booking process...');

      // Apply special config adjustments for certain providers
      let adjustedConfig = { ...config };
 
      // Call API to start booking process
      const response = await startBooking(
        provider,
        cruiseDetails,
        adjustedConfig,
        requestId
      );
 
      if (response.success && response.session_id) {
        const newSessionId = response.session_id;
        setSessionId(newSessionId);
        // Set the active session ID in LogContext for user-specific log filtering
        setActiveSessionId(newSessionId);
        setStatus(`Booking initiated. Session ID: ${newSessionId}`);
        
        // Progress will stream via logs; final status fetched once complete
        
        // Clear reprocessing state if it was set
        setIsReprocessing(false);
      } else {
        setError('Failed to start booking process.');
        setStatus('Failed to start booking process.');
        setIsReprocessing(false);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      setStatus(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Fetch final booking status once complete to get execution time
  useEffect(() => {
    if (isBookingComplete && sessionId) {
      const fetchFinalStatus = async () => {
        try {
          const statusResponse = await getBookingStatus(sessionId);
          const execTime = statusResponse.status.metadata.execution_time;
          if (execTime !== undefined) {
            const minutes = Math.floor(execTime / 60);
            const seconds = Math.floor(execTime % 60);
            setExecutionTime(`${minutes} min, ${seconds} sec`);
          }
        } catch (err) {
          console.error('Error fetching final booking status:', err);
        }
      };
      fetchFinalStatus();
    }
  }, [isBookingComplete, sessionId]);

  // Clean up session ID in LogContext when component unmounts
  useEffect(() => {
    return () => {
      setActiveSessionId(null);
    };
  }, [setActiveSessionId]);

  // Handle status changes from the StatusContainer
  const handleStatusChange = (newStatus: string) => {
    console.log('Received status change:', newStatus);
    console.log('Current provider:', provider);
    
    // Only mark as complete when we get specific completion messages
    const completionMessages: Record<string, string> = {
      'Studio.Sales.CabinCloseOut': 'Success! Your booking is all set',
      'Studio.Res.CabinCloseOut': 'Success! Your booking is all set',
      'NCL': 'Price retrieved successfully — thanks for waiting',
      'Cruising Power': 'Pricing information retrieved — thanks for your patience',
      'OneSource': 'OneSource booking completed successfully'
    };
    
    // Also handle failure completion messages that should trigger validation
    const failureCompletionMessages: Record<string, string[]> = {
      'Cruising Power': [
        'Search failed — no available cabins found (all are closed or on waitlist)',
        'Search failed — cabin selection error'
      ],
      'OneSource': [
        'OneSource booking failed: No cabins processed successfully',
        'Booking failed: No cabins processed successfully'
      ]
    };
    
    // For OneSource, also handle variations like "OneSource.Something"
    let expectedMessage = completionMessages[provider];
    if (!expectedMessage && provider.startsWith('OneSource')) {
      expectedMessage = 'OneSource booking completed successfully';
    }
    
    console.log('Expected completion message:', expectedMessage);
    console.log('Status message matches completion:', newStatus === expectedMessage);
    
    // Check if the new status exactly matches the completion message for the current provider
    let shouldMarkComplete = false;
    
    // Check for success completion
    if (expectedMessage && newStatus === expectedMessage) {
      console.log('Marking booking as complete due to success message!');
      shouldMarkComplete = true;
    }
    
    // Check for failure completion messages that should also trigger validation
    const providerFailureMessages = failureCompletionMessages[provider] || (provider.startsWith('OneSource') ? failureCompletionMessages['OneSource'] : undefined);
    if (providerFailureMessages && providerFailureMessages.includes(newStatus)) {
      console.log('Marking booking as complete due to failure message that requires validation!');
      shouldMarkComplete = true;
    }
    
    if (shouldMarkComplete) {
      // Add a slight delay before showing booking metrics to allow data to be stored
      setTimeout(() => {
        setIsBookingComplete(true);
      }, 3000); // 3 seconds delay
    }
  };

  // Clear UI after successful validation (no cancel)
  const handleValidationDone = () => {
    // Clear local and global states after a successful validation
    clearLogs();
    setActiveSessionId(null);
    setSessionId(null);
    setIsBookingStarted(false);
    setIsBookingComplete(false);
    setStatus('');
    setProgress(0);
    setExecutionTime(null);
    setError(null);

    // Notify parent so extraction panel resets (hides cruise details & booking panel)
    if (onReset) {
      onReset();
    }
  };

  // Reset session ID in LogContext when user resets booking
  const handleResetBooking = async () => {
    // Cancel active booking if there's a session
    if (sessionId) {
      try {
        await cancelBooking(sessionId);
        console.log(`Cancelled booking for session ${sessionId}`);
        setStatus('Booking cancelled successfully');
      } catch (error) {
        console.error('Error cancelling booking:', error);
        setStatus('Booking reset (cancellation may have failed)');
      }
    }
    
    // Clear logs and reset UI state
    clearLogs();
    setActiveSessionId(null);
    setSessionId(null);
    setIsBookingStarted(false);
    setIsBookingComplete(false);
    setStatus('Waiting to start booking...');
    setProgress(0);
    setExecutionTime(null);
    setError(null);
    
    // Call parent reset callback
    onReset();
  };
 
  return (
    <div className="mb-0">
      <div className="p-2 rounded-lg mb-1" >
        <h2 className="text-black text-xl font-semibold">Booking Process</h2>
        <div className="mt-1 relative pb-2">
            <div className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-500"></div>
            <div className="absolute bottom-0 left-0 w-16 h-0.5 bg-gray-900"></div>
        </div>
      </div>
 
      <div className="mb-2">
        <div className="flex flex-col md:flex-row gap-2 mb-2 justify-center items-center mx-auto max-w-3xl">
          <button
            onClick={handleStartBooking}
            className="text-black/80 font-bold py-1 px-3 rounded-lg disabled:opacity-70 disabled:cursor-not-allowed bg-gradient-to-r from-teal-300 to-blue-300 hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200 min-w-[90px]"
            disabled={isBookingStarted}
          >
            {isBookingStarted && !isBookingComplete ? (
              <div className="flex items-center justify-center gap-2">
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Processing</span>
              </div>
            ) : isBookingComplete ? (
              <>✅ Completed</>
            ) : (
              <>Start Booking</>
            )}
          </button>
 
          <button
            onClick={handleResetBooking}
            className="bg-blue-300 text-black/60 hover:bg-gray-700 hover:text-white/80  font-bold py-1 px-3 rounded-lg flex items-center justify-center gap-2"
          >
            Reset Booking
          </button>
          {isBookingComplete && sessionId && (
            <button
              onClick={() => setShowValidation(true)}
              className="text-gray-800 font-bold py-1 px-3 rounded-lg bg-gradient-to-r from-teal-300 to-green-400 hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200"
            >
              {isReprocessing ? (
                <div className="flex items-center justify-center gap-2">
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Reprocessing</span>
                </div>
              ) : (
                <>🛡 Validate</>
              )}
            </button>
          )}
        </div>
        
        {/* Status container using our new log-based StatusContainer */}
        <StatusContainer 
          provider={provider}
          onStatusChange={handleStatusChange}
          isReprocessing={isReprocessing}
        />
        
        {/* Show additional information */}
        {error && (
          <div className="mt-4 p-3 bg-red-100 text-red-700 border border-red-300 rounded">
            <h4 className="font-semibold">Error:</h4>
            <p>{error}</p>
          </div>
        )}
        
        {executionTime && (
          <div className="mt-4 p-3 bg-green-100 text-green-700 border border-green-300 rounded">
            <h4 className="font-semibold">Execution Time:</h4>
            <p>{executionTime}</p>
          </div>
        )}
      </div>
 
      {/* Display metrics and screenshots via ValidationModal when booking is complete */}
      {isBookingComplete && sessionId && (
                      <ValidationModal
                onValidationDone={handleValidationDone}
          open={showValidation}
          onClose={() => setShowValidation(false)}
          provider={provider}
          requestId={requestId}
          sessionId={sessionId}
          config={config}
          extractedDetails={cruiseDetails}
          onReprocess={reprocessBooking}
          onResetBooking={handleResetBooking}
          forceValidation={true}
        />
      )}
    </div>
  );
};
 
export default BookingProcess;