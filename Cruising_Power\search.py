from playwright.async_api import Page, expect, TimeoutError
from Cruising_Power.select_cabin import select_cheapest_cabin, select_cheapest_cabin_js, print_cabin_table, extract_all_categories
from Cruising_Power.price_quote import get_price_quote, get_detailed_pricing
import asyncio
import os
import re
from loguru import logger
from Core.ui_logger import ui_log

from Cruising_Power.llm_integration import extract_comments, analyze_comments_for_promotions
from Cruising_Power.promotion_handler import handle_promotions
from Cruising_Power.screenshot_utils import take_scrolling_screenshot, take_visible_screenshot


class CruiseSearch:
    """
    Class for handling cruise search functionality
    """
    def __init__(self, page, info_dict, onboard_percentage=12, log_dir=None, session_id=None):
        """
        Initialize the cruise search handler
        
        Args:
            page: Playwright Page instance
            info_dict: Dictionary containing structured cruise information
            onboard_percentage: Percentage to use when calculating onboard credit (default: 12)
            log_dir: Kept for backward compatibility but not used
            session_id: Optional session ID for tracking
        """
        self.page = page
        self.info_dict = info_dict
        self.onboard_percentage = onboard_percentage
        self.request_id = info_dict.get('request_id', 'unknown')
        self.cabin_id = info_dict.get('cabin_id', 0)
        self.session_id = session_id or info_dict.get('session_id')
        # Using loguru logger directly - no need to create a separate logger
        # self.logger = logging.getLogger('cruising_power')
        
    async def search(self):
        """
        Perform a cruise search with the provided information
        
        Returns:
            Boolean indicating success or failure
        """
        try:
            #logger.info("\nStarting cruise search...")
            ui_log("Starting cruise search — hang tight", session_id=self.session_id, cabin_id=self.cabin_id, step="initiating_cruise_search", module="Cruising_Power")
            # Brief wait for page to stabilize
            await asyncio.sleep(2)
            
            # Scroll to make the form visible using viewAllSaved element
            logger.info("Scrolling to make the form visible...")
            scroll_element = await self.page.wait_for_selector("a[data-qa='secure.hero.button.learn']", timeout=10000)
            await scroll_element.scroll_into_view_if_needed()
            
            # Select Brand through multiple approaches
            await self._select_brand()
            
            # Rest of the search process
            return await self._complete_search_process()
            
        except Exception as e:
            logger.error(f"An error occurred during search: {e}")
            ui_log("Oops! There was an error during the search — retrying now", session_id=self.session_id, cabin_id=self.cabin_id, step="search_error", module="Cruising_Power")
            return False
            
    async def _select_brand(self):
        """
        Select the cruise brand using Playwright best practices
        
        Returns:
            Boolean indicating if brand was successfully selected
        """
        logger.info(f"Selecting brand: {self.info_dict['ship_brand']}...")
        #ui_log(f"Selecting brand: {self.info_dict['ship_brand']}", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
        
        try:
            # Use fill() method which automatically clears the field first
            brand_field = await self.page.wait_for_selector("input[data-qa='secure.espresso.select.brand']", timeout=10000)
            await brand_field.fill(self.info_dict['ship_brand'])
            
            # Try to select first matching option
            try:
                await self.page.get_by_role("option", name=self.info_dict['ship_brand'], exact=False).first.click()
            except Exception:
                # Fallback to keyboard navigation if clicking fails
                await brand_field.press("ArrowDown")
                await brand_field.press("Enter")
            
            # Verify selection (optional but helpful for debugging)
            selected_value = await self.page.evaluate("el => el.value", brand_field)
            if selected_value and self.info_dict['ship_brand'].lower() in selected_value.lower():
                logger.info(f"Brand selection verified: '{selected_value}'")
                #ui_log(f"Brand selected: {selected_value}", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
                return True
            else:
                logger.warning(f"Brand selection verification failed: '{selected_value}'")
                #ui_log("Brand selection failed", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
                return False
                
        except Exception as e:
            logger.error(f"Brand selection failed: {e}")
            #ui_log(f"Brand selection error: {str(e)}", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            return False
            
    async def _complete_search_process(self):
        """
        Complete the rest of the search process.
        
        Returns:
            Boolean indicating success or failure
        """
        try:
            # 1. Select the ship using optimized approach
            logger.info(f"Selecting ship: {self.info_dict['ship_name']}...")
            ship_name = self.info_dict['ship_name'].strip().title()
            #ui_log(f"Selecting ship: {ship_name}", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            
            ship_field = await self.page.wait_for_selector("input[data-qa='secure.espresso.select.ship']", timeout=10000)
            await ship_field.fill(ship_name)
            
            try:
                await self.page.get_by_role("option", name=ship_name, exact=False).first.click()
            except Exception:
                await ship_field.press("ArrowDown")
                await ship_field.press("Enter")
            
            # 2. Enter the sail date using fill()
            logger.info(f"Setting departure date: {self.info_dict['departure_date']}...")
            # Format date for input if necessary
            date_parts = self.info_dict['departure_date'].split('-')
            formatted_date = self.info_dict['departure_date']
            
            if len(date_parts) == 3:
                day, month_abbr, year = date_parts
                # Format date for input (MM/DD/YYYY)
                months = {"Jan": "01", "Feb": "02", "Mar": "03", "Apr": "04", "May": "05", "Jun": "06", 
                         "Jul": "07", "Aug": "08", "Sep": "09", "Oct": "10", "Nov": "11", "Dec": "12"}
                formatted_date = f"{months.get(month_abbr, '01')}/{day}/{year}"
                logger.info(f"Formatted date: {formatted_date}")
                #ui_log(f"Setting departure date: {formatted_date}", session_id=self.session_id, cabin_id=self.cabin_id, module="Cruising_Power")
            
            date_field = await self.page.wait_for_selector("input[data-qa='secure.espresso.select.depart']", timeout=10000)
            await date_field.fill(formatted_date)
            await self.page.click("body")  # Click away to close any date picker
            
            # 3. Set passengers
            logger.info(f"Setting total passengers: {self.info_dict['total_passengers']}...")
            
            # Ensure valid values for passengers
            total_passengers = self.info_dict.get('total_passengers', 2)
            children = self.info_dict.get('children', 0)
            # Include seniors in adults count for the website form
            seniors = self.info_dict.get('seniors', 0)
            adults = self.info_dict.get('adults', 0) + seniors
            
            # Set adults using a more robust approach
            logger.info(f"Setting adults: {adults}...")
            try:
                adults_field = await self.page.wait_for_selector("input[data-qa='secure.espresso.select.adult']", timeout=10000)
                
                # Clear the field using JavaScript (most reliable)
                await self.page.evaluate("el => el.value = ''", adults_field)
                
                # Set the value directly with JavaScript
                await self.page.evaluate("(el) => { el.value = '" + str(adults) + "'; }", adults_field)
                
                # Trigger necessary events for React to recognize the change
                await self.page.evaluate("""
                    (el) => {
                        // Set the value
                        el.value = '""" + str(adults) + """';
                        
                        // Trigger input event for React
                        const event = new Event('input', { bubbles: true });
                        el.dispatchEvent(event);
                    }
                """, adults_field)
                
                # Click to show dropdown
                await adults_field.click()
                
                # Add a small delay before selecting option (as requested)
                await self.page.wait_for_timeout(300)
                
                # Try to select the option using JavaScript (successful method)
                await self.page.evaluate("""
                    (adultValue) => {
                        const options = Array.from(document.querySelectorAll('div[role="option"]'));
                        const option = options.find(opt => opt.textContent.trim() === adultValue);
                        if (option) {
                            option.click();
                            return true;
                        }
                        return false;
                    }
                """, str(adults))
                
                # Add a small delay after selection (as requested)
                await self.page.wait_for_timeout(200)
                
                # Verify the selection
                selected_value = await self.page.evaluate("el => el.value", adults_field)
                if selected_value and int(selected_value) == adults:
                    logger.info(f"Successfully verified adults selection: {selected_value}")
                else:
                    logger.warning(f"Warning: Adults selection verification failed. Expected: {adults}, Got: {selected_value}")
                    logger.info("Retrying adults selection using JavaScript method once more...")
                    
                    # Retry the same JavaScript method once more
                    await adults_field.click()
                    await self.page.wait_for_timeout(300)
                    
                    # Try to select the option using JavaScript again
                    await self.page.evaluate("""
                        (adultValue) => {
                            const options = Array.from(document.querySelectorAll('div[role="option"]'));
                            const option = options.find(opt => opt.textContent.trim() === adultValue);
                            if (option) {
                                option.click();
                                return true;
                            }
                            return false;
                        }
                    """, str(adults))
                    
                    await self.page.wait_for_timeout(200)
                    
                    # Verify the retry
                    selected_value = await self.page.evaluate("el => el.value", adults_field)
                    if selected_value and int(selected_value) == adults:
                        logger.info(f"Successfully verified adults selection after retry: {selected_value}")
                    else:
                        logger.warning(f"Adults selection retry also failed. Expected: {adults}, Got: {selected_value}")
                        logger.info("Using keyboard navigation as final fallback...")
                        
                        # Fallback to keyboard navigation
                        await adults_field.click()
                        await adults_field.press("ArrowDown")  # Focus first option
                        
                        # Navigate to the correct option
                        for _ in range(adults - 1):  # Assuming first option is 1
                            await adults_field.press("ArrowDown")
                        
                        await adults_field.press("Enter")
                        
                        # Final verification
                        selected_value = await self.page.evaluate("el => el.value", adults_field)
                        logger.info(f"Adults selection after fallback: {selected_value}")
            except Exception as e:
                logger.error(f"Error setting adults: {e}")
                # Last resort approach
                try:
                    adults_field = await self.page.wait_for_selector("input[data-qa='secure.espresso.select.adult']", timeout=10000)
                    await adults_field.fill(str(adults))
                    await adults_field.press("Tab")
                except Exception as fallback_error:
                    logger.error(f"Fallback adults selection also failed: {fallback_error}")
            
            # If there are children, set them too using similar robust approach
            if children > 0:
                try:
                    logger.info(f"Setting children: {children}...")
                    # Limit to maximum 3 if more than 3
                    children = min(children, 3)
                    
                    # Get the children field
                    children_field = await self.page.wait_for_selector("input[data-qa='secure.espresso.select.child']", timeout=10000)
                    
                    # First, clear the field using multiple methods
                    logger.info("Clearing the children field...")
                    await children_field.click()
                    await self.page.wait_for_timeout(500)
                    
                    # 1. Clear with standard fill("")
                    await children_field.fill("")
                    
                    # 2. Use JavaScript to clear
                    await self.page.evaluate("el => el.value = ''", children_field)
                    
                    # 3. Also use select all + delete approach
                    await children_field.press("Control+a")
                    await children_field.press("Delete")
                    
                    # Type the value with a delay between characters
                    logger.info(f"Typing children value: {children}")
                    await children_field.type(str(children), delay=100)
                    
                    # Wait for dropdown to appear
                    await self.page.wait_for_timeout(1000)
                    
                    # Add small delay before attempting selection (as requested)
                    await self.page.wait_for_timeout(300)
                    
                    # First try Enter key (the method that worked in Selenium)
                    logger.info("Trying Enter key to select first option in dropdown...")
                    await children_field.press("Enter")
                    await self.page.wait_for_timeout(500)
                    
                    # Check if it worked
                    selected_value = await self.page.evaluate("el => el.value", children_field)
                    logger.info(f"After Enter key: children = {selected_value}")
                    
                    # If not successful, try more approaches
                    if str(selected_value) != str(children):
                        logger.info("Enter key didn't set the correct value, trying dropdown selection...")
                        
                        # Click to ensure dropdown is open
                        await children_field.click()
                        await self.page.wait_for_timeout(500)
                        
                        # Add small delay before option selection (as requested)
                        await self.page.wait_for_timeout(200)
                        
                        # Try to find and click the matching option
                        try:
                            # Look for exact match first using role=option
                            option = await self.page.query_selector(f"div[role='option']:text('{children}')")
                            if option:
                                logger.info(f"Found exact match option with text '{children}'")
                                await option.click()
                                await self.page.wait_for_timeout(500)
                            else:
                                # Try to find options containing the children value
                                option = await self.page.query_selector(f"div[role='option']:has-text('{children}')")
                                if option:
                                    logger.info(f"Found option containing '{children}'")
                                    await option.click()
                                    await self.page.wait_for_timeout(500)
                                else:
                                    # If can't find specific option, use JavaScript approach
                                    logger.info("No matching option found with standard selectors, using JavaScript")
                                    await self.page.evaluate("""
                                        (childValue) => {
                                            const options = Array.from(document.querySelectorAll('div[role="option"], .rw-list-option, li.option'));
                                            
                                            // Look for exact match first
                                            let found = false;
                                            for (const option of options) {
                                                if (option.textContent.trim() === childValue) {
                                                    option.click();
                                                    found = true;
                                                    break;
                                                }
                                            }
                                            
                                            // If no exact match, try contains
                                            if (!found) {
                                                for (const option of options) {
                                                    if (option.textContent.includes(childValue)) {
                                                        option.click();
                                                        found = true;
                                                        break;
                                                    }
                                                }
                                            }
                                            
                                            return found;
                                        }
                                    """, str(children))
                        except Exception as option_error:
                            logger.error(f"Error selecting option: {option_error}")
                        
                        # Check if dropdown selection worked
                        selected_value = await self.page.evaluate("el => el.value", children_field)
                        logger.info(f"After dropdown selection: children = {selected_value}")
                    
                    # If still not successful, try direct value assignment + blur
                    if str(selected_value) != str(children):
                        logger.info("Dropdown selection unsuccessful, using direct value assignment")
                        await self.page.evaluate("""
                            (el, childValue) => {
                                el.value = childValue;
                                
                                // Create and dispatch events to notify React
                                const inputEvent = new Event('input', { bubbles: true });
                                el.dispatchEvent(inputEvent);
                                
                                const changeEvent = new Event('change', { bubbles: true });
                                el.dispatchEvent(changeEvent);
                                
                                // Move focus away
                                el.blur();
                            }
                        """, children_field, str(children))
                        
                        # Tab out to confirm
                        await children_field.press("Tab")
                        
                        # Check final value
                        selected_value = await self.page.evaluate("el => el.value", children_field)
                        logger.info(f"After direct assignment: children = {selected_value}")
                    
                    # Verify final result
                    if str(selected_value) == str(children):
                        logger.info(f"Successfully set children value to: {selected_value}")
                    else:
                        logger.warning(f"Warning: Unable to set children properly. Final value: {selected_value}")
                        logger.info("Retrying children selection using JavaScript method once more...")
                        
                        # Retry using the JavaScript method that usually works
                        await children_field.click()
                        await self.page.wait_for_timeout(300)
                        
                        # Try JavaScript selection method again
                        await self.page.evaluate("""
                            (childValue) => {
                                const options = Array.from(document.querySelectorAll('div[role="option"], .rw-list-option, li.option'));
                                
                                // Look for exact match first
                                let found = false;
                                for (const option of options) {
                                    if (option.textContent.trim() === childValue) {
                                        option.click();
                                        found = true;
                                        break;
                                    }
                                }
                                
                                // If no exact match, try contains
                                if (!found) {
                                    for (const option of options) {
                                        if (option.textContent.includes(childValue)) {
                                            option.click();
                                            found = true;
                                            break;
                                        }
                                    }
                                }
                                
                                return found;
                            }
                        """, str(children))
                        
                        await self.page.wait_for_timeout(200)
                        
                        # Verify the retry
                        selected_value = await self.page.evaluate("el => el.value", children_field)
                        if str(selected_value) == str(children):
                            logger.info(f"Successfully set children value after retry: {selected_value}")
                        else:
                            logger.warning(f"Children selection retry also failed. Expected: {children}, Got: {selected_value}")
                            # Try fallback JavaScript approach that worked in Selenium
                            logger.info("Trying final JavaScript fallback similar to Selenium approach...")
                        await self.page.evaluate("""
                            (childValue) => {
                                var field = document.querySelector('input[data-qa="secure.espresso.select.child"]');
                                if (field) {
                                    field.click();
                                    field.value = childValue;
                                    
                                    // Trigger events to notify React
                                    const inputEvent = new Event('input', { bubbles: true });
                                    field.dispatchEvent(inputEvent);
                                    
                                    const changeEvent = new Event('change', { bubbles: true });
                                    field.dispatchEvent(changeEvent);
                                    
                                    // Force option selection
                                    setTimeout(function() {
                                        var options = document.querySelectorAll('div[role="option"]');
                                        for (var i = 0; i < options.length; i++) {
                                            if (options[i].textContent.includes(childValue)) {
                                                options[i].click();
                                                return true;
                                            }
                                        }
                                        // If no option found, just press Enter
                                        field.dispatchEvent(new KeyboardEvent('keydown', {
                                            key: 'Enter',
                                            code: 'Enter',
                                            keyCode: 13,
                                            which: 13,
                                            bubbles: true
                                        }));
                                    }, 500);
                                }
                                return field ? field.value : 'field_not_found';
                            }
                        """, str(children))
                        
                        # Final verification
                        await self.page.wait_for_timeout(1000)  # Wait for any async operations
                        selected_value = await self.page.evaluate("el => el.value", children_field)
                        logger.info(f"After final fallback: children = {selected_value}")
                
                except Exception as e:
                    logger.error(f"Error setting children: {e}")
                    logger.info("Continuing with the search process despite children setting error...")
            
            # 4. Set residency using fill and selectOption
            logger.info(f"Setting residency: {self.info_dict['residency']}...")
            try:
                residency_field = await self.page.wait_for_selector("input[data-qa='secure.espresso.promos.select.resident']", timeout=10000)
                await residency_field.fill(self.info_dict['residency'])
                
                try:
                    await self.page.get_by_role("option", name=self.info_dict['residency'], exact=False).first.click()
                except Exception:
                    await residency_field.press("ArrowDown")
                    await residency_field.press("Enter")
                    
            except Exception as e:
                logger.error(f"Error setting residency: {e}")
            
            # 5. Set checkboxes using setChecked
            try:
                # Senior checkbox
                if self.info_dict.get('has_senior', "No") == "Yes":
                    logger.info("Checking Senior option...")
                    await self.page.locator("input[data-qa='secure.espresso.promos.check.senior']").set_checked(True)
                
                # Fire Department checkbox
                if self.info_dict.get('has_fire_department', "No") == "Yes":
                    logger.info("Checking Fire Department option...")
                    await self.page.locator("input[data-qa='secure.espresso.promos.check.fire']").set_checked(True)
                
                # Law Enforcement checkbox
                if self.info_dict.get('has_law_enforcement', "No") == "Yes":
                    logger.info("Checking Law Enforcement option...")
                    await self.page.locator("input[data-qa='secure.espresso.promos.check.law']").set_checked(True)
                
                # Military checkbox
                if self.info_dict.get('has_military', "No") == "Yes":
                    logger.info("Checking Military option...")
                    await self.page.locator("input[data-qa='secure.espresso.promos.check.military']").set_checked(True)
                    
            except Exception as e:
                logger.error(f"Error setting checkboxes: {e}")
            
            # 6. Take screenshot before search
            logger.info("Taking screenshot of filled form...")
            await take_visible_screenshot(self.page, "2_information_entry_page", self.request_id, self.cabin_id, self.session_id)
            
            # 7. Click the Search button using role-based selector
            logger.info("Clicking Search button...")
            try:
                search_button = await self.page.wait_for_selector("button[data-qa='secure.espresso.promos.button.search'], button[data-qa='secure.espresso.button.search']", timeout=10000)
                await search_button.click()
            except Exception as e:
                logger.warning(f"Button selector failed: {e}, trying alternate approach...")
                # Try clicking any button with 'Search' text
                try:
                    await self.page.get_by_role("button", name="Search", exact=False).click()
                except Exception:
                    logger.warning("Could not find search button by role, trying JavaScript click...")
                    await self.page.evaluate("""
                        (function() {
                            var buttons = document.querySelectorAll('button');
                            for (var i = 0; i < buttons.length; i++) {
                                if (buttons[i].textContent.toLowerCase().includes('search')) {
                                    buttons[i].click();
                                    return;
                                }
                            }
                        })();
                    """)
            
            # 8. Wait for search results or error using Promise.all
            try:
                logger.info("Waiting for search results to load...")
                
                # Wait for results with timeout
                async with self.page.expect_navigation(timeout=30000):
                    pass  # Navigation already triggered by search button click
                
                # Additional verification for results
                try:
                    await self.page.wait_for_selector("#catAvailCategoryList", timeout=15000)
                    logger.info("Search results loaded successfully.")
                except Exception:
                    # If specific element not found, check for any cabin elements
                    cabin_elements = await self.page.query_selector_all(".cabin-option, .cabin-selection, .availability-table")
                    if len(cabin_elements) > 0:
                        logger.info(f"Found {len(cabin_elements)} cabin elements - search successful")
                    else:
                        logger.warning("No cabin elements found - search might have failed")
                
                # Success - take screenshot
                logger.info("Search completed successfully!")
                await take_scrolling_screenshot(self.page, "3_search_results_page", self.request_id, self.cabin_id, self.session_id)
                
                # 9. Handle Celebrity cruise promotions if needed
                if self.info_dict.get('ship_brand', '').lower() == 'celebrity' and 'original_text' in self.info_dict:
                    comments = extract_comments(self.info_dict['original_text'])
                    
                    if comments:
                        logger.info(f"\nFound comments in the original text:\n{comments}\n")
                        logger.info("Analyzing comments with LLM to check for promotion requests...")
                        promotion_info = analyze_comments_for_promotions(comments, self.info_dict)
                        
                        if promotion_info.get('needs_promotion', False):
                            logger.info(f"\nHandling promotions based on LLM analysis...")
                            promotion_result = await handle_promotions(self.page, promotion_info, self.request_id, self.cabin_id)
                            
                            if isinstance(promotion_result, dict) and 'selected_rate_codes' in promotion_result:
                                self.info_dict['selected_rate_codes'] = promotion_result['selected_rate_codes']
                        else:
                            logger.info(f"\nLLM analysis indicates no promotions needed: {promotion_info.get('reason', 'No reason provided')}")
                
                # 10. Look for available cabins and select
                logger.info("\nLooking for available cabins table...")
                cabins_table = await self.page.wait_for_selector("#catAvailCategoryList", timeout=10000)
                logger.info("Cabin availability table found.")
                
                # 11. Print cabin table for analysis instead of selecting category
                cabin_type = self.info_dict['cabin_type']
                logger.info(f"\nExtracting all cabin data for all categories...")
                logger.info("(Extracting all category data instead of just selecting one category)")
                
                # Call with just the page parameter, no log_dir needed
                all_categories_data = await extract_all_categories(self.page, self.request_id, self.cabin_id, self.session_id)
                
                # Store the extracted data in the info_dict
                self.info_dict['all_categories_data'] = all_categories_data
                
                # Still print cabin table for the current view state
                logger.info("\nPrinting cabin table for current view state...")
                await print_cabin_table(self.page, self.request_id, self.cabin_id, self.session_id)
                
                # 12. Select the cheapest cabin - use JavaScript method first as it's more reliable
                logger.info("\nLooking for cheapest available cabin...")
                cabin_selected, cabin_message = await select_cheapest_cabin_js(self.page, cabin_type, self.request_id, self.cabin_id, self.session_id)
                
                # Fall back to standard method if JS method fails
                if not cabin_selected:
                    logger.warning(f"JavaScript cabin selection method failed: {cabin_message}")
                    logger.warning("Trying standard selection method...")
                    cabin_selected, cabin_message = await select_cheapest_cabin(self.page, cabin_type, self.request_id, self.cabin_id, self.session_id)
                
                # Last resort if both methods fail
                if not cabin_selected:
                    logger.warning(f"Could not select a cabin: {cabin_message}")
                    logger.warning("Trying last resort: clicking any available cabin...")
                    
                    try:
                        radio_buttons = await self.page.query_selector_all("#catAvailCategoryList tbody input[type='radio']")
                        if radio_buttons and len(radio_buttons) > 0:
                            await radio_buttons[0].click()
                            cabin_selected = True
                            cabin_message = "Selected a cabin using last resort method"
                        else:
                            logger.warning("No radio buttons found, search failed")
                            return False
                    except Exception as e:
                        logger.error(f"Last resort method also failed: {e}")
                        return False
                
                logger.info(f"Successfully selected cabin: {cabin_message}")
                
                # 13. Extract cabin allocation information
                try:
                    allocation_info = await self._extract_cabin_allocation_info()
                    if allocation_info:
                        logger.info(f"\nCabin Allocation Information:\n{allocation_info}")
                        self.info_dict['cabin_allocation'] = allocation_info
                except Exception as e:
                    logger.error(f"Error extracting cabin allocation information: {e}")
                
                # 14. Take a screenshot of the cabin selection
                logger.info("Taking cabin selection screenshot...")
                await self.page.evaluate("window.scrollTo(0, 0);")
                await take_scrolling_screenshot(self.page, "4_cabin_selection_page", self.request_id, self.cabin_id, self.session_id)
                
                # 15. Get price quote
                logger.info("\nGetting price quote...")
                ui_log("Retrieving price information — just a moment", session_id=self.session_id, cabin_id=self.cabin_id, step="retrieving_price_info", module="Cruising_Power")
                quote_success, total_price, onboard_credit = await get_price_quote(self.page, self.onboard_percentage, self.request_id, self.cabin_id, self.session_id)
                
                if quote_success:
                    logger.info(f"\nTotal price: {total_price}")
                    logger.info(f"Onboard credit: {onboard_credit}")
                    self.info_dict['total_price'] = total_price
                    self.info_dict['onboard_credit'] = onboard_credit
                    
                    # 16. Get detailed pricing information
                    logger.info("\nGetting detailed pricing information...")
                    pricing_details = await get_detailed_pricing(self.page, self.request_id, self.cabin_id, self.session_id)
                    
                    # Store pricing details
                    for key, value in pricing_details.items():
                        self.info_dict[f'pricing_{key}'] = value
                    
                    if pricing_details.get('cabin_allocation'):
                        self.info_dict['cabin_allocation'] = pricing_details['cabin_allocation']
                
                ui_log("Search results are in — verifying now", session_id=self.session_id, cabin_id=self.cabin_id, step="checking_search_results", module="Cruising_Power")
                return True
                
            except Exception as e:
                logger.error(f"Search results error: {e}")
                await take_visible_screenshot(self.page, "search_error", self.request_id, self.cabin_id, self.session_id)
                return False
                
        except Exception as e:
            logger.error(f"Error in search process: {e}")
            await take_visible_screenshot(self.page, "search_error", self.request_id, self.cabin_id, self.session_id)
            return False
            
    async def _extract_cabin_allocation_info(self):
        """
        Extracts cabin allocation information from the current page.
        
        Returns:
            str: Cabin allocation information or empty string if not found
        """
        try:
            # Wait for allocation info to be present
            await self.page.wait_for_selector("div.allocTable", timeout=5000)
            
            # Get allocation information
            alloc_table = await self.page.query_selector("div.allocTable")
            allocation_info = await alloc_table.text_content()
            return allocation_info
        except Exception as e:
            logger.error(f"Allocation information not found: {e}")
            return ""


# Function to maintain backward compatibility
async def search_cruise(page, info_dict, onboard_percentage=12, log_dir=None):
    """
    Wrapper function to search for a cruise and handle the booking process
    
    Args:
        page: Playwright Page instance
        info_dict: Dictionary containing structured cruise information
        onboard_percentage: Percentage to use when calculating onboard credit (default: 12)
        log_dir: Kept for backward compatibility but not used
        
    Returns:
        Tuple containing boolean success indicator and dictionary with results
    """
    # Get session_id from info_dict if available
    session_id = info_dict.get('session_id')
    
    search_handler = CruiseSearch(page, info_dict, onboard_percentage, None, session_id)
    return await search_handler.search()

def parse_cruise_info(cruise_info_text):
    """
    Parses cruise information text to extract key details.
    
    Args:
        cruise_info_text: Text containing cruise details
    
    Returns:
        Dictionary with parsed cruise information
    """
    logger.info("Parsing cruise information text")
    # This function would contain the original parse_cruise_info implementation
    # For simplicity, just return an empty dictionary for now
    return {}


def extract_cabin_allocation_info(page):
    """
    Extracts cabin allocation information from the current page.
    
    Args:
        page: Playwright Page instance
    
    Returns:
        Dictionary with cabin allocation information
    """
    logger.info("Extracting cabin allocation information")
    # This function would contain the original extract_cabin_allocation_info implementation
    # For simplicity, just return an empty dictionary for now
    return {}
