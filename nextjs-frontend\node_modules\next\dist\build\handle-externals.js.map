{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "names": ["isResourceInPackages", "resolveExternal", "makeExternalHandler", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "nodeModulesRegex", "resource", "packageNames", "packageDirMapping", "some", "p", "has", "startsWith", "get", "path", "sep", "includes", "join", "replace", "dir", "esmExternalsConfig", "context", "request", "isEsmRequested", "optOutBundlingPackages", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "NODE_ESM_RESOLVE_OPTIONS", "nodeResolveOptions", "NODE_RESOLVE_OPTIONS", "baseEsmResolveOptions", "NODE_BASE_ESM_RESOLVE_OPTIONS", "baseResolveOptions", "NODE_BASE_RESOLVE_OPTIONS", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "optOut", "preferEsm", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "config", "optOutBundlingPackageRegex", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "isWebpackAppLayer", "test", "notExternalModules", "BARREL_OPTIMIZATION_PREFIX", "resolveNextExternal", "isExternal", "isWebpackServerLayer", "WEBPACK_LAYERS", "serverSideRendering", "isRelative", "fullRequest", "resolveResult", "undefined", "defaultOverrides", "Error", "externalType", "transpilePackages", "Map", "pkg", "pkgRes", "set", "dirname", "shouldBeBundled", "bundlePagesExternals"], "mappings": ";;;;;;;;;;;;;;;;IA0BgBA,oBAAoB;eAApBA;;IAiBMC,eAAe;eAAfA;;IAgGNC,mBAAmB;eAAnBA;;;2BA3Ie;6BAEE;4BACU;6DAC1B;+BAMV;wBACiD;;;;;;AAExD,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,CAAC,EAAEF,SAAS,EAAEF,gBAAgB,EAAE,EAAEC,gBAAgB,CAAC;AAGrD,MAAMI,mBAAmB;AAElB,SAASV,qBACdW,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,IAAI,CAACD,cAAc,OAAO;IAC1B,OAAOA,aAAaE,IAAI,CAAC,CAACC,IACxBF,qBAAqBA,kBAAkBG,GAAG,CAACD,KACvCJ,SAASM,UAAU,CAACJ,kBAAkBK,GAAG,CAACH,KAAMI,aAAI,CAACC,GAAG,IACxDT,SAASU,QAAQ,CACfF,aAAI,CAACC,GAAG,GACND,aAAI,CAACG,IAAI,CAAC,gBAAgBP,EAAEQ,OAAO,CAAC,OAAOJ,aAAI,CAACC,GAAG,KACnDD,aAAI,CAACC,GAAG;AAGpB;AAEO,eAAenB,gBACpBuB,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACfC,OAAe,EACfC,cAAuB,EACvBC,sBAAgC,EAChCC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBC,uCAAwB,EACjDC,qBAA0BC,mCAAoB,EAC9CC,wBAA6BC,4CAA6B,EAC1DC,qBAA0BC,wCAAyB;IAEnD,MAAMC,eAAe,CAAC,CAAChB;IACvB,MAAMiB,oBAAoBjB,uBAAuB;IAEjD,IAAIkB,MAAqB;IACzB,IAAIC,QAAiB;IAErB,MAAMC,mBACJJ,gBACAb,kBACA,mEAAmE;IACnE,2EAA2E;IAC3E,2EAA2E;IAC3E,CAACC,uBAAuBf,IAAI,CAAC,CAACgC,SAAWnB,QAAQV,UAAU,CAAC6B,WACxD;QAAC;QAAM;KAAM,GACb;QAAC;KAAM;IAEb,KAAK,MAAMC,aAAaF,iBAAkB;QACxC,MAAMG,UAAUlB,WACdiB,YAAYd,oBAAoBE;QAGlC,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;YACD,CAACQ,KAAKC,MAAM,GAAG,MAAMI,QAAQtB,SAASC;QACzC,EAAE,OAAOsB,KAAK;YACZN,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACf,kBAAkBgB,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIX,iBAAiB;YACnB,OAAO;gBAAEmB,UAAUnB,gBAAgBY;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIX,kBAAkB;YACpB,IAAImB;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAAcvB,WAClBc,QAAQP,wBAAwBE;gBAEjC,CAACY,SAASC,UAAU,GAAG,MAAMC,YAAY7B,KAAKG;YACjD,EAAE,OAAOsB,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,2EAA2E;YAC3E,wDAAwD;YACxD,IAAID,YAAYR,OAAOC,UAAUQ,WAAW;gBAC1CT,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEO,SAAS1C,oBAAoB,EAClCoD,MAAM,EACNzB,sBAAsB,EACtB0B,0BAA0B,EAC1B/B,GAAG,EAMJ;QAE2B8B;IAD1B,IAAIE;IACJ,MAAMd,oBAAoBY,EAAAA,uBAAAA,OAAOG,YAAY,qBAAnBH,qBAAqBb,YAAY,MAAK;IAEhE,OAAO,eAAeiB,gBACpBhC,OAAe,EACfC,OAAe,EACfgC,cAAsB,EACtBC,KAA8B,EAC9B9B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAM+B,UACJlC,QAAQV,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBE,aAAI,CAAC2C,KAAK,CAACC,UAAU,CAACpC,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBqC,QAAQC,QAAQ,KAAK,WAAW9C,aAAI,CAAC+C,KAAK,CAACH,UAAU,CAACpC;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMwC,aAAaC,IAAAA,yBAAiB,EAACR;QAErC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,aAAaQ,IAAI,CAAC1C,UAAU;gBAC9B,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IAAIxB,mBAAmBkE,IAAI,CAAC1C,YAAY,CAACwC,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAExC,QAAQ,CAAC;YAC9B;YAEA,MAAM2C,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAAC1C,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQN,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIM,QAAQV,UAAU,CAACsD,sCAA0B,GAAG;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAM3C,iBAAiB+B,mBAAmB;QAE1C;;;;;;KAMC,GACD,MAAMa,sBAAsB,CAACtB;YAC3B,MAAMuB,aAAajE,gBAAgB6D,IAAI,CAACnB;YAExC,sFAAsF;YACtF,sGAAsG;YACtG,IAAIuB,YAAY;gBACd,oGAAoG;gBACpG,oCAAoC;gBACpC,OAAO,CAAC,SAAS,EAAEvB,SAAS3B,OAAO,CAAC,oBAAoB,aAAa,CAAC;YACxE;QACF;QAEA,4DAA4D;QAC5D,yFAAyF;QACzF,IACEmD,IAAAA,4BAAoB,EAACd,UACrBjC,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQV,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqDoD,IAAI,CAAC1C,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8C0C,IAAI,CAAC1C,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,8DAA8D0C,IAAI,CAChE1C,YAEF,4CAA4C0C,IAAI,CAAC1C,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,sEAAsE0C,IAAI,CACxE1C,YAEF,2CAA2C0C,IAAI,CAAC1C,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;YAC5B;YAEA,OAAO6C,oBAAoB7C;QAC7B;QAEA,gFAAgF;QAChF,qEAAqE;QACrE,oDAAoD;QACpD,IAAIiC,UAAUe,yBAAc,CAACC,mBAAmB,EAAE;YAChD,MAAMC,aAAalD,QAAQV,UAAU,CAAC;YACtC,MAAM6D,cAAcD,aAChB1D,aAAI,CAACG,IAAI,CAACI,SAASC,SAASJ,OAAO,CAAC,OAAO,OAC3CI;YACJ,OAAO6C,oBAAoBM;QAC7B;QAEA,6FAA6F;QAC7F,MAAMC,gBAAgB,MAAM9E,gBAC1BuB,KACA8B,OAAOG,YAAY,CAAChB,YAAY,EAChCf,SACAC,SACAC,gBACAC,wBACAC,YACA+B,UAAUW,sBAAsBQ;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAc7B,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAIvB,YAAY,oBAAoB;YAClCoD,cAAcpC,GAAG,GAAGsC,6BAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAEtC,GAAG,EAAEC,KAAK,EAAE,GAAGmC;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAACpC,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACf,kBAAkBgB,SAAS,CAACF,mBAAmB;YAClD,MAAM,IAAIwC,MACR,CAAC,cAAc,EAAEvD,QAAQ,2HAA2H,CAAC;QAEzJ;QAEA,MAAMwD,eAAevC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2CyB,IAAI,CAAC1B,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2B0B,IAAI,CAAC1B,QAChC,8BAA8B0B,IAAI,CAAC1B,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIW,OAAO8B,iBAAiB,IAAI,CAAC5B,6BAA6B;YAC5DA,8BAA8B,IAAI6B;YAClC,8DAA8D;YAC9D,KAAK,MAAMC,OAAOhC,OAAO8B,iBAAiB,CAAE;gBAC1C,MAAMG,SAAS,MAAMtF,gBACnBuB,KACA8B,OAAOG,YAAY,CAAChB,YAAY,EAChCf,SACA4D,MAAM,iBACN1D,gBACAC,wBACAC,YACA+B,UAAUW,sBAAsBQ;gBAElC,IAAIO,OAAO5C,GAAG,EAAE;oBACda,4BAA4BgC,GAAG,CAACF,KAAKnE,aAAI,CAACsE,OAAO,CAACF,OAAO5C,GAAG;gBAC9D;YACF;QACF;QAEA,MAAM+C,kBACJ1F,qBACE2C,KACAW,OAAO8B,iBAAiB,EACxB5B,gCAEDZ,SAASuB,cACT,CAACA,cAAcb,OAAOG,YAAY,CAACkC,oBAAoB;QAE1D,IAAIjF,iBAAiB2D,IAAI,CAAC1B,MAAM;YAC9B,IAAI+B,IAAAA,4BAAoB,EAACd,QAAQ;gBAC/B,IAAI,CAACL,2BAA2Bc,IAAI,CAAC1B,MAAM;oBACzC,QAAO,0BAA0B;gBACnC;gBACA,OAAO,CAAC,EAAEwC,aAAa,CAAC,EAAExD,QAAQ,CAAC,CAAC,2BAA2B;;YACjE;YAEA,IAAI,CAAC+D,mBAAmBnC,2BAA2Bc,IAAI,CAAC1B,MAAM;gBAC5D,OAAO,CAAC,EAAEwC,aAAa,CAAC,EAAExD,QAAQ,CAAC,CAAC,0CAA0C;;YAChF;QACF;IAEA,2CAA2C;IAC7C;AACF"}