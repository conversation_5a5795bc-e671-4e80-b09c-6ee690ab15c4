import React, { useState, useEffect } from 'react';
import { Agency, getAgencies, calculateTimeSavings } from '../../data/cruiseData';

interface CruiseAgencyChartProps {
  className?: string;
}

type ViewMode = 'timeSavings' | 'processingTimes' | 'quoteVolume';

const CruiseAgencyChart: React.FC<CruiseAgencyChartProps> = ({ className }) => {
  const [animateChart, setAnimateChart] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('timeSavings');
  const [selectedCabinType, setSelectedCabinType] = useState<'all' | 'single' | 'multiple'>('all');

  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setAnimateChart(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  // Get agencies from the data
  const agencies = getAgencies();

  // Calculate time savings for each agency
  const calculateAgencySavings = (agency: Agency, cabinType: 'all' | 'single' | 'multiple') => {
    const singleSavings = calculateTimeSavings(
      agency.processingTimes.singleCabin.manual,
      agency.processingTimes.singleCabin.oceanmind
    );

    const multipleSavings = calculateTimeSavings(
      agency.processingTimes.multipleCabin.manual,
      agency.processingTimes.multipleCabin.oceanmind
    );

    if (cabinType === 'single') return singleSavings;
    if (cabinType === 'multiple') return multipleSavings;

    // Average of single and multiple cabin savings for 'all'
    return Math.round((singleSavings + multipleSavings) / 2);
  };

  const getProcessingTime = (agency: Agency, cabinType: 'all' | 'single' | 'multiple', isOceanmind: boolean) => {
    if (cabinType === 'single') {
      return isOceanmind
        ? agency.processingTimes.singleCabin.oceanmind
        : agency.processingTimes.singleCabin.manual;
    }

    if (cabinType === 'multiple') {
      return isOceanmind
        ? agency.processingTimes.multipleCabin.oceanmind
        : agency.processingTimes.multipleCabin.manual;
    }

    // For 'all', calculate an average (this is simplified, could be weighted by quote volume)
    const singleTime = isOceanmind
      ? agency.processingTimes.singleCabin.oceanmind
      : agency.processingTimes.singleCabin.manual;

    const multipleTime = isOceanmind
      ? agency.processingTimes.multipleCabin.oceanmind
      : agency.processingTimes.multipleCabin.manual;

    // Simple average, could be made more sophisticated
    return singleTime; // Simplified for now
  };

  const getQuoteVolume = (agency: Agency, cabinType: 'all' | 'single' | 'multiple') => {
    if (cabinType === 'single') return agency.processingTimes.singleCabin.quotes;
    if (cabinType === 'multiple') return agency.processingTimes.multipleCabin.quotes;
    return agency.quotes; // Total for 'all'
  };

  const getMetricValue = (agency: Agency, cabinType: 'all' | 'single' | 'multiple') => {
    switch (viewMode) {
      case 'timeSavings':
        return calculateAgencySavings(agency, cabinType);
      case 'processingTimes':
        // Return Oceanmind processing time (could be enhanced to show both or ratio)
        return getProcessingTime(agency, cabinType, true);
      case 'quoteVolume':
        return getQuoteVolume(agency, cabinType);
      default:
        return calculateAgencySavings(agency, cabinType);
    }
  };

  const getMetricLabel = (metric: ViewMode) => {
    switch (metric) {
      case 'timeSavings': return 'faster';
      case 'processingTimes': return 'processing time';
      case 'quoteVolume': return 'quotes';
      default: return '';
    }
  };

  const getBarWidth = (agency: Agency, cabinType: 'all' | 'single' | 'multiple') => {
    const value = getMetricValue(agency, cabinType);

    switch (viewMode) {
      case 'timeSavings':
        // Percentage is already in the right format
        return `${value}%`;
      case 'processingTimes':
        // Show as percentage of a reasonable max time (e.g., 5 minutes)
        // This is just for visualization - could be enhanced
        const timeString = value as string;
        // Handle the time format correctly (00:00:00)
        const parts = timeString.split(':').map(Number);
        const minutes = parts.length === 3
          ? parts[0] * 60 + parts[1] + parts[2] / 60
          : parts[0] + parts[1] / 60;
        return `${Math.min((minutes / 5) * 100, 100)}%`;
      case 'quoteVolume':
        // Scale based on max quotes (simplified approach)
        const maxQuotes = Math.max(...agencies.map(a => a.quotes));
        const quoteValue = value as number;
        return `${Math.min((quoteValue / maxQuotes) * 100, 100)}%`;
      default:
        return '0%';
    }
  };

  return (
    <div className={`${className || ''} bg-transparent p-4 rounded-lg`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-3xl font-semibold text-sky-800">Agency Performance Metrics</h3>

        {/* View Mode Selector */}
        <div className="flex items-center bg-white/50 rounded-lg p-1">
          <button
            className={`px-2 py-1 text-lg font-medium rounded-md transition-all ${viewMode === 'timeSavings' ? 'text-white' : 'text-sky-800 hover:text-white'}`}
            style={{
              backgroundImage: viewMode === 'timeSavings' ? ' linear-gradient(135deg,rgb(154, 147, 192) 0%,rgb(76, 129, 228) 100%)' : 'none',
              backgroundColor: viewMode === 'timeSavings' ? '#B5A6FF' : 'transparent',
              transition: 'all 0.3s ease-in-out',
            }}
            onMouseOver={(e) => {
              if (viewMode !== 'timeSavings') {
                e.currentTarget.style.backgroundImage = 'linear-gradient(135deg,rgb(154, 147, 192) 0%,rgb(76, 129, 228) 100%)';
                e.currentTarget.style.opacity = '0.8';
              }
            }}
            onMouseOut={(e) => {
              if (viewMode !== 'timeSavings') {
                e.currentTarget.style.backgroundImage = 'none';
                e.currentTarget.style.opacity = '1';
              }
            }}
            onClick={() => setViewMode('timeSavings')}
          >
            Time Savings
          </button>
          <button
            className={`px-2 py-1 text-lg font-medium rounded-md transition-all ${viewMode === 'processingTimes' ? 'text-white' : 'text-sky-800 hover:text-white'}`}
            style={{
              backgroundImage: viewMode === 'processingTimes' ? 'linear-gradient(135deg,rgb(154, 147, 192) 0%,rgb(76, 129, 228) 100%)' : 'none',
              backgroundColor: viewMode === 'processingTimes' ? '#B5A6FF' : 'transparent',
              transition: 'all 0.3s ease-in-out',
            }}
            onMouseOver={(e) => {
              if (viewMode !== 'processingTimes') {
                e.currentTarget.style.backgroundImage = 'linear-gradient(135deg,rgb(154, 147, 192) 0%,rgb(76, 129, 228) 100%)';
                e.currentTarget.style.opacity = '0.8';
              }
            }}
            onMouseOut={(e) => {
              if (viewMode !== 'processingTimes') {
                e.currentTarget.style.backgroundImage = 'none';
                e.currentTarget.style.opacity = '1';
              }
            }}
            onClick={() => setViewMode('processingTimes')}
          >
            Processing Times
          </button>
          <button
            className={`px-2 py-1 text-lg font-medium rounded-md transition-all ${viewMode === 'quoteVolume' ? 'text-white' : 'text-sky-800 hover:text-white'}`}
            style={{
              backgroundImage: viewMode === 'quoteVolume' ? 'linear-gradient(135deg,rgb(154, 147, 192) 0%,rgb(76, 129, 228) 100%)' : 'none',
              backgroundColor: viewMode === 'quoteVolume' ? '#B5A6FF' : 'transparent',
              transition: 'all 0.3s ease-in-out',
            }}
            onMouseOver={(e) => {
              if (viewMode !== 'quoteVolume') {
                e.currentTarget.style.backgroundImage = 'linear-gradient(135deg,rgb(154, 147, 192) 0%,rgb(76, 129, 228) 100%)';
                e.currentTarget.style.opacity = '0.8';
              }
            }}
            onMouseOut={(e) => {
              if (viewMode !== 'quoteVolume') {
                e.currentTarget.style.backgroundImage = 'none';
                e.currentTarget.style.opacity = '1';
              }
            }}
            onClick={() => setViewMode('quoteVolume')}
          >
            Quote Volume
          </button>
        </div>
      </div>

      {/* Cabin Type Filter */}
      <div className="flex items-center mb-4">
        <div className="text-2xl text-sky-800 font-medium mr-2">Cabin type:</div>
        <div className="flex items-center bg-white/50 rounded-lg p-1">
          <button
            className={`px-2 py-1 text-xl font-medium rounded-md transition-colors ${selectedCabinType === 'all' ? 'bg-sky-900 text-white' : 'text-sky-800'}`}
            onClick={() => setSelectedCabinType('all')}
          >
            All Cabins
          </button>
          <button
            className={`px-2 py-1 text-xl font-medium rounded-md transition-colors ${selectedCabinType === 'single' ? 'bg-sky-900 text-white' : 'text-sky-800'}`}
            onClick={() => setSelectedCabinType('single')}
          >
            Single
          </button>
          <button
            className={`px-2 py-1 text-xl font-medium rounded-md transition-colors ${selectedCabinType === 'multiple' ? 'bg-sky-900 text-white' : 'text-sky-800'}`}
            onClick={() => setSelectedCabinType('multiple')}
          >
            Multiple
          </button>
        </div>
      </div>

      <div className="space-y-4">
        {agencies.map((agency: Agency, index: number) => {
          const metricValue = getMetricValue(agency, selectedCabinType);
          const metricLabel = getMetricLabel(viewMode);

          const singleCabinSavings = calculateTimeSavings(
            agency.processingTimes.singleCabin.manual,
            agency.processingTimes.singleCabin.oceanmind
          );
          const multipleCabinSavings = calculateTimeSavings(
            agency.processingTimes.multipleCabin.manual,
            agency.processingTimes.multipleCabin.oceanmind
          );

          return (
            <div
              key={agency.id}
              className="bg-white/20 backdrop-blur-sm rounded-lg p-4 hover:bg-white/30 transition-colors"
            >
              <div className="flex justify-between items-center mb-2">
                <span className="text-sky-800 font-medium">{agency.name}</span>
                <span className="text-black font-medium">
                  {viewMode === 'timeSavings' ? `${metricValue}% ${metricLabel}` : `${metricValue} ${metricLabel}`}
                </span>
              </div>

              <div className="h-2 w-full bg-white rounded-full overflow-hidden mb-3">
                <div
                  className="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all duration-1000 ease-out transform origin-left"
                  style={{
                    width: animateChart ? getBarWidth(agency, selectedCabinType) : '0%',
                    transitionDelay: `${index * 200}ms`
                  }}
                ></div>
              </div>

              {/* Stats in horizontal layout - simplified when not hovered */}
              <div className="flex text-xl">
                <div className="mr-6">
                  <span className="text-sky-800">Total quotes: </span>
                  <span className="font-medium">{agency.quotes}</span>
                </div>

                <div className="mr-6">
                  <span className="text-sky-800">Single cabin: </span>
                  <span className="font-medium text-black">{singleCabinSavings}%</span>
                </div>

                <div>
                  <span className="text-sky-800">Multiple cabin: </span>
                  <span className="font-medium text-black">{multipleCabinSavings}%</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CruiseAgencyChart;