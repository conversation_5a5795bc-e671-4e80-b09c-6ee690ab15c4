module.exports = "\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __accessCheck = (obj, member, msg) => {\n  if (!member.has(obj))\n    throw TypeError(\"Cannot \" + msg);\n};\nvar __privateGet = (obj, member, getter) => {\n  __accessCheck(obj, member, \"read from private field\");\n  return getter ? getter.call(obj) : member.get(obj);\n};\nvar __privateAdd = (obj, member, value) => {\n  if (member.has(obj))\n    throw TypeError(\"Cannot add the same private member more than once\");\n  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\n};\nvar __privateSet = (obj, member, value, setter) => {\n  __accessCheck(obj, member, \"write to private field\");\n  setter ? setter.call(obj, value) : member.set(obj, value);\n  return value;\n};\nvar __privateMethod = (obj, member, method) => {\n  __accessCheck(obj, member, \"access private method\");\n  return method;\n};\n\n// src/primitives/url.js\nvar url_exports = {};\n__export(url_exports, {\n  URLPattern: () => me\n});\nmodule.exports = __toCommonJS(url_exports);\n\n// ../../node_modules/.pnpm/urlpattern-polyfill@9.0.0/node_modules/urlpattern-polyfill/dist/urlpattern.js\nvar _a;\nvar k = (_a = class {\n  type = 3;\n  name = \"\";\n  prefix = \"\";\n  value = \"\";\n  suffix = \"\";\n  modifier = 3;\n  constructor(t, r, n, o, c, l) {\n    this.type = t, this.name = r, this.prefix = n, this.value = o, this.suffix = c, this.modifier = l;\n  }\n  hasCustomName() {\n    return this.name !== \"\" && typeof this.name != \"number\";\n  }\n}, __name(_a, \"k\"), _a);\nvar Pe = /[$_\\p{ID_Start}]/u;\nvar Se = /[$_\\u200C\\u200D\\p{ID_Continue}]/u;\nvar M = \".*\";\nfunction ke(e, t) {\n  return (t ? /^[\\x00-\\xFF]*$/ : /^[\\x00-\\x7F]*$/).test(e);\n}\n__name(ke, \"ke\");\nfunction v(e, t = false) {\n  let r = [], n = 0;\n  for (; n < e.length; ) {\n    let o = e[n], c = /* @__PURE__ */ __name(function(l) {\n      if (!t)\n        throw new TypeError(l);\n      r.push({ type: \"INVALID_CHAR\", index: n, value: e[n++] });\n    }, \"c\");\n    if (o === \"*\") {\n      r.push({ type: \"ASTERISK\", index: n, value: e[n++] });\n      continue;\n    }\n    if (o === \"+\" || o === \"?\") {\n      r.push({ type: \"OTHER_MODIFIER\", index: n, value: e[n++] });\n      continue;\n    }\n    if (o === \"\\\\\") {\n      r.push({ type: \"ESCAPED_CHAR\", index: n++, value: e[n++] });\n      continue;\n    }\n    if (o === \"{\") {\n      r.push({ type: \"OPEN\", index: n, value: e[n++] });\n      continue;\n    }\n    if (o === \"}\") {\n      r.push({ type: \"CLOSE\", index: n, value: e[n++] });\n      continue;\n    }\n    if (o === \":\") {\n      let l = \"\", s = n + 1;\n      for (; s < e.length; ) {\n        let i = e.substr(s, 1);\n        if (s === n + 1 && Pe.test(i) || s !== n + 1 && Se.test(i)) {\n          l += e[s++];\n          continue;\n        }\n        break;\n      }\n      if (!l) {\n        c(`Missing parameter name at ${n}`);\n        continue;\n      }\n      r.push({ type: \"NAME\", index: n, value: l }), n = s;\n      continue;\n    }\n    if (o === \"(\") {\n      let l = 1, s = \"\", i = n + 1, a = false;\n      if (e[i] === \"?\") {\n        c(`Pattern cannot start with \"?\" at ${i}`);\n        continue;\n      }\n      for (; i < e.length; ) {\n        if (!ke(e[i], false)) {\n          c(`Invalid character '${e[i]}' at ${i}.`), a = true;\n          break;\n        }\n        if (e[i] === \"\\\\\") {\n          s += e[i++] + e[i++];\n          continue;\n        }\n        if (e[i] === \")\") {\n          if (l--, l === 0) {\n            i++;\n            break;\n          }\n        } else if (e[i] === \"(\" && (l++, e[i + 1] !== \"?\")) {\n          c(`Capturing groups are not allowed at ${i}`), a = true;\n          break;\n        }\n        s += e[i++];\n      }\n      if (a)\n        continue;\n      if (l) {\n        c(`Unbalanced pattern at ${n}`);\n        continue;\n      }\n      if (!s) {\n        c(`Missing pattern at ${n}`);\n        continue;\n      }\n      r.push({ type: \"REGEX\", index: n, value: s }), n = i;\n      continue;\n    }\n    r.push({ type: \"CHAR\", index: n, value: e[n++] });\n  }\n  return r.push({ type: \"END\", index: n, value: \"\" }), r;\n}\n__name(v, \"v\");\nfunction D(e, t = {}) {\n  let r = v(e);\n  t.delimiter ??= \"/#?\", t.prefixes ??= \"./\";\n  let n = `[^${x(t.delimiter)}]+?`, o = [], c = 0, l = 0, s = \"\", i = /* @__PURE__ */ new Set(), a = /* @__PURE__ */ __name((f) => {\n    if (l < r.length && r[l].type === f)\n      return r[l++].value;\n  }, \"a\"), h = /* @__PURE__ */ __name(() => a(\"OTHER_MODIFIER\") ?? a(\"ASTERISK\"), \"h\"), p = /* @__PURE__ */ __name((f) => {\n    let u = a(f);\n    if (u !== void 0)\n      return u;\n    let { type: d, index: T } = r[l];\n    throw new TypeError(`Unexpected ${d} at ${T}, expected ${f}`);\n  }, \"p\"), O = /* @__PURE__ */ __name(() => {\n    let f = \"\", u;\n    for (; u = a(\"CHAR\") ?? a(\"ESCAPED_CHAR\"); )\n      f += u;\n    return f;\n  }, \"O\"), xe = /* @__PURE__ */ __name((f) => f, \"xe\"), L = t.encodePart || xe, I = \"\", H = /* @__PURE__ */ __name((f) => {\n    I += f;\n  }, \"H\"), $ = /* @__PURE__ */ __name(() => {\n    I.length && (o.push(new k(3, \"\", \"\", L(I), \"\", 3)), I = \"\");\n  }, \"$\"), G = /* @__PURE__ */ __name((f, u, d, T, Y) => {\n    let g = 3;\n    switch (Y) {\n      case \"?\":\n        g = 1;\n        break;\n      case \"*\":\n        g = 0;\n        break;\n      case \"+\":\n        g = 2;\n        break;\n    }\n    if (!u && !d && g === 3) {\n      H(f);\n      return;\n    }\n    if ($(), !u && !d) {\n      if (!f)\n        return;\n      o.push(new k(3, \"\", \"\", L(f), \"\", g));\n      return;\n    }\n    let m;\n    d ? d === \"*\" ? m = M : m = d : m = n;\n    let R = 2;\n    m === n ? (R = 1, m = \"\") : m === M && (R = 0, m = \"\");\n    let S;\n    if (u ? S = u : d && (S = c++), i.has(S))\n      throw new TypeError(`Duplicate name '${S}'.`);\n    i.add(S), o.push(new k(R, S, L(f), m, L(T), g));\n  }, \"G\");\n  for (; l < r.length; ) {\n    let f = a(\"CHAR\"), u = a(\"NAME\"), d = a(\"REGEX\");\n    if (!u && !d && (d = a(\"ASTERISK\")), u || d) {\n      let g = f ?? \"\";\n      t.prefixes.indexOf(g) === -1 && (H(g), g = \"\"), $();\n      let m = h();\n      G(g, u, d, \"\", m);\n      continue;\n    }\n    let T = f ?? a(\"ESCAPED_CHAR\");\n    if (T) {\n      H(T);\n      continue;\n    }\n    if (a(\"OPEN\")) {\n      let g = O(), m = a(\"NAME\"), R = a(\"REGEX\");\n      !m && !R && (R = a(\"ASTERISK\"));\n      let S = O();\n      p(\"CLOSE\");\n      let be = h();\n      G(g, m, R, S, be);\n      continue;\n    }\n    $(), p(\"END\");\n  }\n  return o;\n}\n__name(D, \"D\");\nfunction x(e) {\n  return e.replace(/([.+*?^${}()[\\]|/\\\\])/g, \"\\\\$1\");\n}\n__name(x, \"x\");\nfunction X(e) {\n  return e && e.ignoreCase ? \"ui\" : \"u\";\n}\n__name(X, \"X\");\nfunction Z(e, t, r) {\n  return F(D(e, r), t, r);\n}\n__name(Z, \"Z\");\nfunction y(e) {\n  switch (e) {\n    case 0:\n      return \"*\";\n    case 1:\n      return \"?\";\n    case 2:\n      return \"+\";\n    case 3:\n      return \"\";\n  }\n}\n__name(y, \"y\");\nfunction F(e, t, r = {}) {\n  r.delimiter ??= \"/#?\", r.prefixes ??= \"./\", r.sensitive ??= false, r.strict ??= false, r.end ??= true, r.start ??= true, r.endsWith = \"\";\n  let n = r.start ? \"^\" : \"\";\n  for (let s of e) {\n    if (s.type === 3) {\n      s.modifier === 3 ? n += x(s.value) : n += `(?:${x(s.value)})${y(s.modifier)}`;\n      continue;\n    }\n    t && t.push(s.name);\n    let i = `[^${x(r.delimiter)}]+?`, a = s.value;\n    if (s.type === 1 ? a = i : s.type === 0 && (a = M), !s.prefix.length && !s.suffix.length) {\n      s.modifier === 3 || s.modifier === 1 ? n += `(${a})${y(s.modifier)}` : n += `((?:${a})${y(s.modifier)})`;\n      continue;\n    }\n    if (s.modifier === 3 || s.modifier === 1) {\n      n += `(?:${x(s.prefix)}(${a})${x(s.suffix)})`, n += y(s.modifier);\n      continue;\n    }\n    n += `(?:${x(s.prefix)}`, n += `((?:${a})(?:`, n += x(s.suffix), n += x(s.prefix), n += `(?:${a}))*)${x(s.suffix)})`, s.modifier === 0 && (n += \"?\");\n  }\n  let o = `[${x(r.endsWith)}]|$`, c = `[${x(r.delimiter)}]`;\n  if (r.end)\n    return r.strict || (n += `${c}?`), r.endsWith.length ? n += `(?=${o})` : n += \"$\", new RegExp(n, X(r));\n  r.strict || (n += `(?:${c}(?=${o}))?`);\n  let l = false;\n  if (e.length) {\n    let s = e[e.length - 1];\n    s.type === 3 && s.modifier === 3 && (l = r.delimiter.indexOf(s) > -1);\n  }\n  return l || (n += `(?=${c}|${o})`), new RegExp(n, X(r));\n}\n__name(F, \"F\");\nvar b = { delimiter: \"\", prefixes: \"\", sensitive: true, strict: true };\nvar B = { delimiter: \".\", prefixes: \"\", sensitive: true, strict: true };\nvar q = { delimiter: \"/\", prefixes: \"/\", sensitive: true, strict: true };\nfunction J(e, t) {\n  return e.length ? e[0] === \"/\" ? true : !t || e.length < 2 ? false : (e[0] == \"\\\\\" || e[0] == \"{\") && e[1] == \"/\" : false;\n}\n__name(J, \"J\");\nfunction Q(e, t) {\n  return e.startsWith(t) ? e.substring(t.length, e.length) : e;\n}\n__name(Q, \"Q\");\nfunction Ee(e, t) {\n  return e.endsWith(t) ? e.substr(0, e.length - t.length) : e;\n}\n__name(Ee, \"Ee\");\nfunction W(e) {\n  return !e || e.length < 2 ? false : e[0] === \"[\" || (e[0] === \"\\\\\" || e[0] === \"{\") && e[1] === \"[\";\n}\n__name(W, \"W\");\nvar ee = [\"ftp\", \"file\", \"http\", \"https\", \"ws\", \"wss\"];\nfunction N(e) {\n  if (!e)\n    return true;\n  for (let t of ee)\n    if (e.test(t))\n      return true;\n  return false;\n}\n__name(N, \"N\");\nfunction te(e, t) {\n  if (e = Q(e, \"#\"), t || e === \"\")\n    return e;\n  let r = new URL(\"https://example.com\");\n  return r.hash = e, r.hash ? r.hash.substring(1, r.hash.length) : \"\";\n}\n__name(te, \"te\");\nfunction re(e, t) {\n  if (e = Q(e, \"?\"), t || e === \"\")\n    return e;\n  let r = new URL(\"https://example.com\");\n  return r.search = e, r.search ? r.search.substring(1, r.search.length) : \"\";\n}\n__name(re, \"re\");\nfunction ne(e, t) {\n  return t || e === \"\" ? e : W(e) ? j(e) : z(e);\n}\n__name(ne, \"ne\");\nfunction se(e, t) {\n  if (t || e === \"\")\n    return e;\n  let r = new URL(\"https://example.com\");\n  return r.password = e, r.password;\n}\n__name(se, \"se\");\nfunction ie(e, t) {\n  if (t || e === \"\")\n    return e;\n  let r = new URL(\"https://example.com\");\n  return r.username = e, r.username;\n}\n__name(ie, \"ie\");\nfunction ae(e, t, r) {\n  if (r || e === \"\")\n    return e;\n  if (t && !ee.includes(t))\n    return new URL(`${t}:${e}`).pathname;\n  let n = e[0] == \"/\";\n  return e = new URL(n ? e : \"/-\" + e, \"https://example.com\").pathname, n || (e = e.substring(2, e.length)), e;\n}\n__name(ae, \"ae\");\nfunction oe(e, t, r) {\n  return _(t) === e && (e = \"\"), r || e === \"\" ? e : K(e);\n}\n__name(oe, \"oe\");\nfunction ce(e, t) {\n  return e = Ee(e, \":\"), t || e === \"\" ? e : A(e);\n}\n__name(ce, \"ce\");\nfunction _(e) {\n  switch (e) {\n    case \"ws\":\n    case \"http\":\n      return \"80\";\n    case \"wws\":\n    case \"https\":\n      return \"443\";\n    case \"ftp\":\n      return \"21\";\n    default:\n      return \"\";\n  }\n}\n__name(_, \"_\");\nfunction A(e) {\n  if (e === \"\")\n    return e;\n  if (/^[-+.A-Za-z0-9]*$/.test(e))\n    return e.toLowerCase();\n  throw new TypeError(`Invalid protocol '${e}'.`);\n}\n__name(A, \"A\");\nfunction le(e) {\n  if (e === \"\")\n    return e;\n  let t = new URL(\"https://example.com\");\n  return t.username = e, t.username;\n}\n__name(le, \"le\");\nfunction he(e) {\n  if (e === \"\")\n    return e;\n  let t = new URL(\"https://example.com\");\n  return t.password = e, t.password;\n}\n__name(he, \"he\");\nfunction z(e) {\n  if (e === \"\")\n    return e;\n  if (/[\\t\\n\\r #%/:<>?@[\\]^\\\\|]/g.test(e))\n    throw new TypeError(`Invalid hostname '${e}'`);\n  let t = new URL(\"https://example.com\");\n  return t.hostname = e, t.hostname;\n}\n__name(z, \"z\");\nfunction j(e) {\n  if (e === \"\")\n    return e;\n  if (/[^0-9a-fA-F[\\]:]/g.test(e))\n    throw new TypeError(`Invalid IPv6 hostname '${e}'`);\n  return e.toLowerCase();\n}\n__name(j, \"j\");\nfunction K(e) {\n  if (e === \"\" || /^[0-9]*$/.test(e) && parseInt(e) <= 65535)\n    return e;\n  throw new TypeError(`Invalid port '${e}'.`);\n}\n__name(K, \"K\");\nfunction fe(e) {\n  if (e === \"\")\n    return e;\n  let t = new URL(\"https://example.com\");\n  return t.pathname = e[0] !== \"/\" ? \"/-\" + e : e, e[0] !== \"/\" ? t.pathname.substring(2, t.pathname.length) : t.pathname;\n}\n__name(fe, \"fe\");\nfunction ue(e) {\n  return e === \"\" ? e : new URL(`data:${e}`).pathname;\n}\n__name(ue, \"ue\");\nfunction pe(e) {\n  if (e === \"\")\n    return e;\n  let t = new URL(\"https://example.com\");\n  return t.search = e, t.search.substring(1, t.search.length);\n}\n__name(pe, \"pe\");\nfunction de(e) {\n  if (e === \"\")\n    return e;\n  let t = new URL(\"https://example.com\");\n  return t.hash = e, t.hash.substring(1, t.hash.length);\n}\n__name(de, \"de\");\nvar _i, _n, _t, _e, _s, _u, _c, _p, _d, _g, _r, r_fn, _k, k_fn, _P, P_fn, _f, f_fn, _m, m_fn, _a2, a_fn, _S, S_fn, _E, E_fn, _x, x_fn, _R, R_fn, _y, y_fn, _b, b_fn, _h, h_fn, _l, l_fn, _O, O_fn, _T, T_fn, _A, A_fn, _w, w_fn, _o, o_fn, _C, C_fn, _a3;\nvar U = (_a3 = class {\n  constructor(t) {\n    __privateAdd(this, _r);\n    __privateAdd(this, _k);\n    __privateAdd(this, _P);\n    __privateAdd(this, _f);\n    __privateAdd(this, _m);\n    __privateAdd(this, _a2);\n    __privateAdd(this, _S);\n    __privateAdd(this, _E);\n    __privateAdd(this, _x);\n    __privateAdd(this, _R);\n    __privateAdd(this, _y);\n    __privateAdd(this, _b);\n    __privateAdd(this, _h);\n    __privateAdd(this, _l);\n    __privateAdd(this, _O);\n    __privateAdd(this, _T);\n    __privateAdd(this, _A);\n    __privateAdd(this, _w);\n    __privateAdd(this, _o);\n    __privateAdd(this, _C);\n    __privateAdd(this, _i, void 0);\n    __privateAdd(this, _n, []);\n    __privateAdd(this, _t, {});\n    __privateAdd(this, _e, 0);\n    __privateAdd(this, _s, 1);\n    __privateAdd(this, _u, 0);\n    __privateAdd(this, _c, 0);\n    __privateAdd(this, _p, 0);\n    __privateAdd(this, _d, 0);\n    __privateAdd(this, _g, false);\n    __privateSet(this, _i, t);\n  }\n  get result() {\n    return __privateGet(this, _t);\n  }\n  parse() {\n    for (__privateSet(this, _n, v(__privateGet(this, _i), true)); __privateGet(this, _e) < __privateGet(this, _n).length; __privateSet(this, _e, __privateGet(this, _e) + __privateGet(this, _s))) {\n      if (__privateSet(this, _s, 1), __privateGet(this, _n)[__privateGet(this, _e)].type === \"END\") {\n        if (__privateGet(this, _c) === 0) {\n          __privateMethod(this, _P, P_fn).call(this), __privateMethod(this, _l, l_fn).call(this) ? __privateMethod(this, _r, r_fn).call(this, 9, 1) : __privateMethod(this, _h, h_fn).call(this) ? (__privateMethod(this, _r, r_fn).call(this, 8, 1), __privateGet(this, _t).hash = \"\") : (__privateMethod(this, _r, r_fn).call(this, 7, 0), __privateGet(this, _t).search = \"\", __privateGet(this, _t).hash = \"\");\n          continue;\n        } else if (__privateGet(this, _c) === 2) {\n          __privateMethod(this, _f, f_fn).call(this, 5);\n          continue;\n        }\n        __privateMethod(this, _r, r_fn).call(this, 10, 0);\n        break;\n      }\n      if (__privateGet(this, _p) > 0)\n        if (__privateMethod(this, _T, T_fn).call(this))\n          __privateSet(this, _p, __privateGet(this, _p) - 1);\n        else\n          continue;\n      if (__privateMethod(this, _O, O_fn).call(this)) {\n        __privateSet(this, _p, __privateGet(this, _p) + 1);\n        continue;\n      }\n      switch (__privateGet(this, _c)) {\n        case 0:\n          __privateMethod(this, _S, S_fn).call(this) && (__privateGet(this, _t).username = \"\", __privateGet(this, _t).password = \"\", __privateGet(this, _t).hostname = \"\", __privateGet(this, _t).port = \"\", __privateGet(this, _t).pathname = \"\", __privateGet(this, _t).search = \"\", __privateGet(this, _t).hash = \"\", __privateMethod(this, _f, f_fn).call(this, 1));\n          break;\n        case 1:\n          if (__privateMethod(this, _S, S_fn).call(this)) {\n            __privateMethod(this, _C, C_fn).call(this);\n            let t = 7, r = 1;\n            __privateGet(this, _g) && (__privateGet(this, _t).pathname = \"/\"), __privateMethod(this, _E, E_fn).call(this) ? (t = 2, r = 3) : __privateGet(this, _g) && (t = 2), __privateMethod(this, _r, r_fn).call(this, t, r);\n          }\n          break;\n        case 2:\n          __privateMethod(this, _x, x_fn).call(this) ? __privateMethod(this, _f, f_fn).call(this, 3) : (__privateMethod(this, _b, b_fn).call(this) || __privateMethod(this, _h, h_fn).call(this) || __privateMethod(this, _l, l_fn).call(this)) && __privateMethod(this, _f, f_fn).call(this, 5);\n          break;\n        case 3:\n          __privateMethod(this, _R, R_fn).call(this) ? __privateMethod(this, _r, r_fn).call(this, 4, 1) : __privateMethod(this, _x, x_fn).call(this) && __privateMethod(this, _r, r_fn).call(this, 5, 1);\n          break;\n        case 4:\n          __privateMethod(this, _x, x_fn).call(this) && __privateMethod(this, _r, r_fn).call(this, 5, 1);\n          break;\n        case 5:\n          __privateMethod(this, _A, A_fn).call(this) ? __privateSet(this, _d, __privateGet(this, _d) + 1) : __privateMethod(this, _w, w_fn).call(this) && __privateSet(this, _d, __privateGet(this, _d) - 1), __privateMethod(this, _y, y_fn).call(this) && !__privateGet(this, _d) ? __privateMethod(this, _r, r_fn).call(this, 6, 1) : __privateMethod(this, _b, b_fn).call(this) ? __privateMethod(this, _r, r_fn).call(this, 7, 0) : __privateMethod(this, _h, h_fn).call(this) ? __privateMethod(this, _r, r_fn).call(this, 8, 1) : __privateMethod(this, _l, l_fn).call(this) && __privateMethod(this, _r, r_fn).call(this, 9, 1);\n          break;\n        case 6:\n          __privateMethod(this, _b, b_fn).call(this) ? __privateMethod(this, _r, r_fn).call(this, 7, 0) : __privateMethod(this, _h, h_fn).call(this) ? __privateMethod(this, _r, r_fn).call(this, 8, 1) : __privateMethod(this, _l, l_fn).call(this) && __privateMethod(this, _r, r_fn).call(this, 9, 1);\n          break;\n        case 7:\n          __privateMethod(this, _h, h_fn).call(this) ? __privateMethod(this, _r, r_fn).call(this, 8, 1) : __privateMethod(this, _l, l_fn).call(this) && __privateMethod(this, _r, r_fn).call(this, 9, 1);\n          break;\n        case 8:\n          __privateMethod(this, _l, l_fn).call(this) && __privateMethod(this, _r, r_fn).call(this, 9, 1);\n          break;\n        case 9:\n          break;\n        case 10:\n          break;\n      }\n    }\n  }\n}, _i = new WeakMap(), _n = new WeakMap(), _t = new WeakMap(), _e = new WeakMap(), _s = new WeakMap(), _u = new WeakMap(), _c = new WeakMap(), _p = new WeakMap(), _d = new WeakMap(), _g = new WeakMap(), _r = new WeakSet(), r_fn = /* @__PURE__ */ __name(function(t, r) {\n  switch (__privateGet(this, _c)) {\n    case 0:\n      break;\n    case 1:\n      __privateGet(this, _t).protocol = __privateMethod(this, _o, o_fn).call(this);\n      break;\n    case 2:\n      break;\n    case 3:\n      __privateGet(this, _t).username = __privateMethod(this, _o, o_fn).call(this);\n      break;\n    case 4:\n      __privateGet(this, _t).password = __privateMethod(this, _o, o_fn).call(this);\n      break;\n    case 5:\n      __privateGet(this, _t).hostname = __privateMethod(this, _o, o_fn).call(this);\n      break;\n    case 6:\n      __privateGet(this, _t).port = __privateMethod(this, _o, o_fn).call(this);\n      break;\n    case 7:\n      __privateGet(this, _t).pathname = __privateMethod(this, _o, o_fn).call(this);\n      break;\n    case 8:\n      __privateGet(this, _t).search = __privateMethod(this, _o, o_fn).call(this);\n      break;\n    case 9:\n      __privateGet(this, _t).hash = __privateMethod(this, _o, o_fn).call(this);\n      break;\n    case 10:\n      break;\n  }\n  __privateMethod(this, _k, k_fn).call(this, t, r);\n}, \"#r\"), _k = new WeakSet(), k_fn = /* @__PURE__ */ __name(function(t, r) {\n  __privateSet(this, _c, t), __privateSet(this, _u, __privateGet(this, _e) + r), __privateSet(this, _e, __privateGet(this, _e) + r), __privateSet(this, _s, 0);\n}, \"#k\"), _P = new WeakSet(), P_fn = /* @__PURE__ */ __name(function() {\n  __privateSet(this, _e, __privateGet(this, _u)), __privateSet(this, _s, 0);\n}, \"#P\"), _f = new WeakSet(), f_fn = /* @__PURE__ */ __name(function(t) {\n  __privateMethod(this, _P, P_fn).call(this), __privateSet(this, _c, t);\n}, \"#f\"), _m = new WeakSet(), m_fn = /* @__PURE__ */ __name(function(t) {\n  return t < 0 && (t = __privateGet(this, _n).length - t), t < __privateGet(this, _n).length ? __privateGet(this, _n)[t] : __privateGet(this, _n)[__privateGet(this, _n).length - 1];\n}, \"#m\"), _a2 = new WeakSet(), a_fn = /* @__PURE__ */ __name(function(t, r) {\n  let n = __privateMethod(this, _m, m_fn).call(this, t);\n  return n.value === r && (n.type === \"CHAR\" || n.type === \"ESCAPED_CHAR\" || n.type === \"INVALID_CHAR\");\n}, \"#a\"), _S = new WeakSet(), S_fn = /* @__PURE__ */ __name(function() {\n  return __privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e), \":\");\n}, \"#S\"), _E = new WeakSet(), E_fn = /* @__PURE__ */ __name(function() {\n  return __privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e) + 1, \"/\") && __privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e) + 2, \"/\");\n}, \"#E\"), _x = new WeakSet(), x_fn = /* @__PURE__ */ __name(function() {\n  return __privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e), \"@\");\n}, \"#x\"), _R = new WeakSet(), R_fn = /* @__PURE__ */ __name(function() {\n  return __privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e), \":\");\n}, \"#R\"), _y = new WeakSet(), y_fn = /* @__PURE__ */ __name(function() {\n  return __privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e), \":\");\n}, \"#y\"), _b = new WeakSet(), b_fn = /* @__PURE__ */ __name(function() {\n  return __privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e), \"/\");\n}, \"#b\"), _h = new WeakSet(), h_fn = /* @__PURE__ */ __name(function() {\n  if (__privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e), \"?\"))\n    return true;\n  if (__privateGet(this, _n)[__privateGet(this, _e)].value !== \"?\")\n    return false;\n  let t = __privateMethod(this, _m, m_fn).call(this, __privateGet(this, _e) - 1);\n  return t.type !== \"NAME\" && t.type !== \"REGEX\" && t.type !== \"CLOSE\" && t.type !== \"ASTERISK\";\n}, \"#h\"), _l = new WeakSet(), l_fn = /* @__PURE__ */ __name(function() {\n  return __privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e), \"#\");\n}, \"#l\"), _O = new WeakSet(), O_fn = /* @__PURE__ */ __name(function() {\n  return __privateGet(this, _n)[__privateGet(this, _e)].type == \"OPEN\";\n}, \"#O\"), _T = new WeakSet(), T_fn = /* @__PURE__ */ __name(function() {\n  return __privateGet(this, _n)[__privateGet(this, _e)].type == \"CLOSE\";\n}, \"#T\"), _A = new WeakSet(), A_fn = /* @__PURE__ */ __name(function() {\n  return __privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e), \"[\");\n}, \"#A\"), _w = new WeakSet(), w_fn = /* @__PURE__ */ __name(function() {\n  return __privateMethod(this, _a2, a_fn).call(this, __privateGet(this, _e), \"]\");\n}, \"#w\"), _o = new WeakSet(), o_fn = /* @__PURE__ */ __name(function() {\n  let t = __privateGet(this, _n)[__privateGet(this, _e)], r = __privateMethod(this, _m, m_fn).call(this, __privateGet(this, _u)).index;\n  return __privateGet(this, _i).substring(r, t.index);\n}, \"#o\"), _C = new WeakSet(), C_fn = /* @__PURE__ */ __name(function() {\n  let t = {};\n  Object.assign(t, b), t.encodePart = A;\n  let r = Z(__privateMethod(this, _o, o_fn).call(this), void 0, t);\n  __privateSet(this, _g, N(r));\n}, \"#C\"), __name(_a3, \"U\"), _a3);\nvar V = [\"protocol\", \"username\", \"password\", \"hostname\", \"port\", \"pathname\", \"search\", \"hash\"];\nvar E = \"*\";\nfunction ge(e, t) {\n  if (typeof e != \"string\")\n    throw new TypeError(\"parameter 1 is not of type 'string'.\");\n  let r = new URL(e, t);\n  return { protocol: r.protocol.substring(0, r.protocol.length - 1), username: r.username, password: r.password, hostname: r.hostname, port: r.port, pathname: r.pathname, search: r.search !== \"\" ? r.search.substring(1, r.search.length) : void 0, hash: r.hash !== \"\" ? r.hash.substring(1, r.hash.length) : void 0 };\n}\n__name(ge, \"ge\");\nfunction P(e, t) {\n  return t ? C(e) : e;\n}\n__name(P, \"P\");\nfunction w(e, t, r) {\n  let n;\n  if (typeof t.baseURL == \"string\")\n    try {\n      n = new URL(t.baseURL), e.protocol = P(n.protocol.substring(0, n.protocol.length - 1), r), e.username = P(n.username, r), e.password = P(n.password, r), e.hostname = P(n.hostname, r), e.port = P(n.port, r), e.pathname = P(n.pathname, r), e.search = P(n.search.substring(1, n.search.length), r), e.hash = P(n.hash.substring(1, n.hash.length), r);\n    } catch {\n      throw new TypeError(`invalid baseURL '${t.baseURL}'.`);\n    }\n  if (typeof t.protocol == \"string\" && (e.protocol = ce(t.protocol, r)), typeof t.username == \"string\" && (e.username = ie(t.username, r)), typeof t.password == \"string\" && (e.password = se(t.password, r)), typeof t.hostname == \"string\" && (e.hostname = ne(t.hostname, r)), typeof t.port == \"string\" && (e.port = oe(t.port, e.protocol, r)), typeof t.pathname == \"string\") {\n    if (e.pathname = t.pathname, n && !J(e.pathname, r)) {\n      let o = n.pathname.lastIndexOf(\"/\");\n      o >= 0 && (e.pathname = P(n.pathname.substring(0, o + 1), r) + e.pathname);\n    }\n    e.pathname = ae(e.pathname, e.protocol, r);\n  }\n  return typeof t.search == \"string\" && (e.search = re(t.search, r)), typeof t.hash == \"string\" && (e.hash = te(t.hash, r)), e;\n}\n__name(w, \"w\");\nfunction C(e) {\n  return e.replace(/([+*?:{}()\\\\])/g, \"\\\\$1\");\n}\n__name(C, \"C\");\nfunction Re(e) {\n  return e.replace(/([.+*?^${}()[\\]|/\\\\])/g, \"\\\\$1\");\n}\n__name(Re, \"Re\");\nfunction ye(e, t) {\n  t.delimiter ??= \"/#?\", t.prefixes ??= \"./\", t.sensitive ??= false, t.strict ??= false, t.end ??= true, t.start ??= true, t.endsWith = \"\";\n  let r = \".*\", n = `[^${Re(t.delimiter)}]+?`, o = /[$_\\u200C\\u200D\\p{ID_Continue}]/u, c = \"\";\n  for (let l = 0; l < e.length; ++l) {\n    let s = e[l];\n    if (s.type === 3) {\n      if (s.modifier === 3) {\n        c += C(s.value);\n        continue;\n      }\n      c += `{${C(s.value)}}${y(s.modifier)}`;\n      continue;\n    }\n    let i = s.hasCustomName(), a = !!s.suffix.length || !!s.prefix.length && (s.prefix.length !== 1 || !t.prefixes.includes(s.prefix)), h = l > 0 ? e[l - 1] : null, p = l < e.length - 1 ? e[l + 1] : null;\n    if (!a && i && s.type === 1 && s.modifier === 3 && p && !p.prefix.length && !p.suffix.length)\n      if (p.type === 3) {\n        let O = p.value.length > 0 ? p.value[0] : \"\";\n        a = o.test(O);\n      } else\n        a = !p.hasCustomName();\n    if (!a && !s.prefix.length && h && h.type === 3) {\n      let O = h.value[h.value.length - 1];\n      a = t.prefixes.includes(O);\n    }\n    a && (c += \"{\"), c += C(s.prefix), i && (c += `:${s.name}`), s.type === 2 ? c += `(${s.value})` : s.type === 1 ? i || (c += `(${n})`) : s.type === 0 && (!i && (!h || h.type === 3 || h.modifier !== 3 || a || s.prefix !== \"\") ? c += \"*\" : c += `(${r})`), s.type === 1 && i && s.suffix.length && o.test(s.suffix[0]) && (c += \"\\\\\"), c += C(s.suffix), a && (c += \"}\"), s.modifier !== 3 && (c += y(s.modifier));\n  }\n  return c;\n}\n__name(ye, \"ye\");\nvar _i2, _n2, _t2, _e2, _s2, _a4;\nvar me = (_a4 = class {\n  constructor(t = {}, r, n) {\n    __privateAdd(this, _i2, void 0);\n    __privateAdd(this, _n2, {});\n    __privateAdd(this, _t2, {});\n    __privateAdd(this, _e2, {});\n    __privateAdd(this, _s2, {});\n    try {\n      let o;\n      if (typeof r == \"string\" ? o = r : n = r, typeof t == \"string\") {\n        let i = new U(t);\n        if (i.parse(), t = i.result, o === void 0 && typeof t.protocol != \"string\")\n          throw new TypeError(\"A base URL must be provided for a relative constructor string.\");\n        t.baseURL = o;\n      } else {\n        if (!t || typeof t != \"object\")\n          throw new TypeError(\"parameter 1 is not of type 'string' and cannot convert to dictionary.\");\n        if (o)\n          throw new TypeError(\"parameter 1 is not of type 'string'.\");\n      }\n      typeof n > \"u\" && (n = { ignoreCase: false });\n      let c = { ignoreCase: n.ignoreCase === true }, l = { pathname: E, protocol: E, username: E, password: E, hostname: E, port: E, search: E, hash: E };\n      __privateSet(this, _i2, w(l, t, true)), _(__privateGet(this, _i2).protocol) === __privateGet(this, _i2).port && (__privateGet(this, _i2).port = \"\");\n      let s;\n      for (s of V) {\n        if (!(s in __privateGet(this, _i2)))\n          continue;\n        let i = {}, a = __privateGet(this, _i2)[s];\n        switch (__privateGet(this, _t2)[s] = [], s) {\n          case \"protocol\":\n            Object.assign(i, b), i.encodePart = A;\n            break;\n          case \"username\":\n            Object.assign(i, b), i.encodePart = le;\n            break;\n          case \"password\":\n            Object.assign(i, b), i.encodePart = he;\n            break;\n          case \"hostname\":\n            Object.assign(i, B), W(a) ? i.encodePart = j : i.encodePart = z;\n            break;\n          case \"port\":\n            Object.assign(i, b), i.encodePart = K;\n            break;\n          case \"pathname\":\n            N(__privateGet(this, _n2).protocol) ? (Object.assign(i, q, c), i.encodePart = fe) : (Object.assign(i, b, c), i.encodePart = ue);\n            break;\n          case \"search\":\n            Object.assign(i, b, c), i.encodePart = pe;\n            break;\n          case \"hash\":\n            Object.assign(i, b, c), i.encodePart = de;\n            break;\n        }\n        try {\n          __privateGet(this, _s2)[s] = D(a, i), __privateGet(this, _n2)[s] = F(__privateGet(this, _s2)[s], __privateGet(this, _t2)[s], i), __privateGet(this, _e2)[s] = ye(__privateGet(this, _s2)[s], i);\n        } catch {\n          throw new TypeError(`invalid ${s} pattern '${__privateGet(this, _i2)[s]}'.`);\n        }\n      }\n    } catch (o) {\n      throw new TypeError(`Failed to construct 'URLPattern': ${o.message}`);\n    }\n  }\n  test(t = {}, r) {\n    let n = { pathname: \"\", protocol: \"\", username: \"\", password: \"\", hostname: \"\", port: \"\", search: \"\", hash: \"\" };\n    if (typeof t != \"string\" && r)\n      throw new TypeError(\"parameter 1 is not of type 'string'.\");\n    if (typeof t > \"u\")\n      return false;\n    try {\n      typeof t == \"object\" ? n = w(n, t, false) : n = w(n, ge(t, r), false);\n    } catch {\n      return false;\n    }\n    let o;\n    for (o of V)\n      if (!__privateGet(this, _n2)[o].exec(n[o]))\n        return false;\n    return true;\n  }\n  exec(t = {}, r) {\n    let n = { pathname: \"\", protocol: \"\", username: \"\", password: \"\", hostname: \"\", port: \"\", search: \"\", hash: \"\" };\n    if (typeof t != \"string\" && r)\n      throw new TypeError(\"parameter 1 is not of type 'string'.\");\n    if (typeof t > \"u\")\n      return;\n    try {\n      typeof t == \"object\" ? n = w(n, t, false) : n = w(n, ge(t, r), false);\n    } catch {\n      return null;\n    }\n    let o = {};\n    r ? o.inputs = [t, r] : o.inputs = [t];\n    let c;\n    for (c of V) {\n      let l = __privateGet(this, _n2)[c].exec(n[c]);\n      if (!l)\n        return null;\n      let s = {};\n      for (let [i, a] of __privateGet(this, _t2)[c].entries())\n        if (typeof a == \"string\" || typeof a == \"number\") {\n          let h = l[i + 1];\n          s[a] = h;\n        }\n      o[c] = { input: n[c] ?? \"\", groups: s };\n    }\n    return o;\n  }\n  static compareComponent(t, r, n) {\n    let o = /* @__PURE__ */ __name((i, a) => {\n      for (let h of [\"type\", \"modifier\", \"prefix\", \"value\", \"suffix\"]) {\n        if (i[h] < a[h])\n          return -1;\n        if (i[h] === a[h])\n          continue;\n        return 1;\n      }\n      return 0;\n    }, \"o\"), c = new k(3, \"\", \"\", \"\", \"\", 3), l = new k(0, \"\", \"\", \"\", \"\", 3), s = /* @__PURE__ */ __name((i, a) => {\n      let h = 0;\n      for (; h < Math.min(i.length, a.length); ++h) {\n        let p = o(i[h], a[h]);\n        if (p)\n          return p;\n      }\n      return i.length === a.length ? 0 : o(i[h] ?? c, a[h] ?? c);\n    }, \"s\");\n    return !__privateGet(r, _e2)[t] && !__privateGet(n, _e2)[t] ? 0 : __privateGet(r, _e2)[t] && !__privateGet(n, _e2)[t] ? s(__privateGet(r, _s2)[t], [l]) : !__privateGet(r, _e2)[t] && __privateGet(n, _e2)[t] ? s([l], __privateGet(n, _s2)[t]) : s(__privateGet(r, _s2)[t], __privateGet(n, _s2)[t]);\n  }\n  get protocol() {\n    return __privateGet(this, _e2).protocol;\n  }\n  get username() {\n    return __privateGet(this, _e2).username;\n  }\n  get password() {\n    return __privateGet(this, _e2).password;\n  }\n  get hostname() {\n    return __privateGet(this, _e2).hostname;\n  }\n  get port() {\n    return __privateGet(this, _e2).port;\n  }\n  get pathname() {\n    return __privateGet(this, _e2).pathname;\n  }\n  get search() {\n    return __privateGet(this, _e2).search;\n  }\n  get hash() {\n    return __privateGet(this, _e2).hash;\n  }\n}, _i2 = new WeakMap(), _n2 = new WeakMap(), _t2 = new WeakMap(), _e2 = new WeakMap(), _s2 = new WeakMap(), __name(_a4, \"me\"), _a4);\n\n// ../../node_modules/.pnpm/urlpattern-polyfill@9.0.0/node_modules/urlpattern-polyfill/index.js\nif (!globalThis.URLPattern) {\n  globalThis.URLPattern = me;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  URLPattern\n});\n"