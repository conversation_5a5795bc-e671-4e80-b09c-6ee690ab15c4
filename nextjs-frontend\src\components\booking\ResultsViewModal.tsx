import React, { useState, useEffect } from 'react';
import { ProviderType } from './ProviderSelector';
import { getResultsView, ResultsViewResponse, getScreenshots } from '../../services/api';
import { getVideoStreamUrl } from '../../services/video';

// Utility helpers to group items by cabin id and sort cabin keys
const groupByCabinId = (items: any[] = []) =>
  items.reduce<Record<string, any[]>>((acc, item) => {
    const key = item.cabin_id ?? 'general';
    acc[key] = acc[key] ? [...acc[key], item] : [item];
    return acc;
  }, {});

const sortByCabinKey = ([aKey]: [string, any[]], [bKey]: [string, any[]]) => {
  const numA = isNaN(Number(aKey)) ? Infinity : Number(aKey);
  const numB = isNaN(Number(bKey)) ? Infinity : Number(bKey);
  return numA - numB;
};

// Helper to prettify keys for display
declare const capitalizeWords: (s: string) => string;

const formatKey = (k: string): string => {
  if (k === 'id') return 'Record Id';
  if (k === 'total_price') return 'Total Price';
  return k.replace(/_/g, ' ');
};

const formatCurrency = (n: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(n);

// Convert raw timestamp strings to a more readable local format.
// Handles ISO strings (e.g. "2025-08-04T14:45:48.510607") and compact strings ("20250804132707" or "20250804_132707").
const formatTimestamp = (raw: string): string => {
  if (!raw) return raw;

  // 1) If it's a standard ISO string – parse directly.
  if (raw.includes('T')) {
    const parsed = Date.parse(raw);
    if (!Number.isNaN(parsed)) {
      return new Date(parsed).toLocaleString();
    }
  }

  // 2) Compact YYYYMMDDHHMMSS (with optional underscore between date/time)
  const match = raw.match(/^(\d{8})[_]?(\d{6})$/); // YYYYMMDD[_]HHMMSS
  if (match) {
    const [_, datePart, timePart] = match;
    const year   = datePart.substring(0, 4);
    const month  = datePart.substring(4, 6);
    const day    = datePart.substring(6, 8);
    const hour   = timePart.substring(0, 2);
    const minute = timePart.substring(2, 4);
    const second = timePart.substring(4, 6);
    const iso = `${year}-${month}-${day}T${hour}:${minute}:${second}`;
    const d = new Date(iso);
    return d.toLocaleString();
  }

  // Fallback – return raw if unknown format
  return raw;
};

// Format values for display in cabin detail grid
 type DisplayVal = string | number | JSX.Element;

const currencyKeys = ['reservation_total','non_comm_fare','savings','insurance','dining_package','soda_package','beverage_package','govt_tax','onboard_credit','commission','commission_amount','final_price','cabin_total','gross_fare','total_price','total_final_price','grand_total'];

const formatByKey = (k: string, v: any): DisplayVal => {
  if (currencyKeys.includes(k) && v !== null && v !== undefined) {
    const num = typeof v === 'number' ? v : parseFloat(String(v).replace(/[^0-9.-]/g, ''));
    return formatCurrency(num);
  }
  if (['selected_rate','selected_rate_code'].includes(k) && typeof v === 'string') {
    const trimmed = v.trim().replace(/\s+/g,' ');
    const short = trimmed.length > 120 ? trimmed.slice(0,117)+'…' : trimmed;
    return <span title={trimmed}>{short}</span>;
  }
  if (k === 'timestamp' && typeof v === 'string') {
    return formatTimestamp(v);
  }
  if (k === 'cruise_details' && (typeof v === 'string' || typeof v === 'object')) {
    try {
      const cd = typeof v === 'string' ? JSON.parse(v) : (v as any);
      return (
        <span className="whitespace-pre-wrap break-words">
          Ship: {cd.ship_name || cd.ship || '—'} | Date: {cd.travel_date || cd.date || '—'} | Nights: {cd.nights || '—'}
        </span>
      );
    } catch { /* fall through */ }
  }
  if (k === 'passengers' && typeof v === 'string') {
    try {
      const p = JSON.parse(v);
      return `${p.total||0} (A:${p.adults||0} C:${p.children||0} I:${p.infants||0})`;
    } catch {/* fall through */}
  }
  if (k === 'overall_status' && typeof v === 'number') {
    return formatValue(Boolean(v));
  }
  if (k === 'execution_time' && typeof v === 'number') {
    const minutes = Math.floor(v / 60);
    const seconds = Math.floor(v % 60).toString().padStart(2,'0');
    return `${minutes} min ${seconds} sec`;
  }
  return formatValue(v);
};

const formatPromoValue = (v: any): DisplayVal => {
  if (typeof v !== 'string') return formatValue(v);
    const items = v.split(',').map((s: string) => s.trim()).filter(Boolean);

  // If first segment looks like a heading (doesn't start with "X ") keep it above the list
  const [first, ...rest] = items;
  const hasHeading = first && !/^X\s/i.test(first);

    return (
    <div className="whitespace-pre-wrap break-words">
      {hasHeading && <div className="font-medium mb-1">{first}</div>}
      <ul className="list-disc ml-4 space-y-1">
        {(hasHeading ? rest : items).map((item, idx) => {
          const parts = item.split(/\s+/).filter(Boolean);
          const category = parts[0] || '';
          const promo = parts[1] || '';
          const flags = parts.slice(2).join(' ');
          return (
            <li key={idx}>
              <span className="font-medium">Category:</span> {category}
              {promo && (
                <>
                  {' '}<span className="font-medium">Promo:</span> {promo}
                </>
              )}
              {flags && (
                <>
                  {' '}<span className="font-medium">Flags:</span> {flags}
                </>
              )}
            </li>
          );
        })}
      </ul>
    </div>
  );
};

const formatValue = (v: any): DisplayVal => {
  if (v === null || v === undefined) return '—';

  // Boolean → Yes/No with colored icon
  if (typeof v === 'boolean') {
    return (
      <span className={v ? 'text-green-600' : 'text-red-600'}>
        {v ? '✓ Yes' : '✕ No'}
      </span>
    );
  }

  // Handle objects/arrays
  if (typeof v === 'object') {
    // 1. Arrays – show comma-separated list when primitive, else JSON
    if (Array.isArray(v)) {
      return v.every(item => typeof item !== 'object') ? v.join(', ') : JSON.stringify(v);
    }


    // 2. Passenger breakdown – pretty print counts
    const passengerKeys = ['adults', 'children', 'infants', 'seniors', 'total'];
    const objectKeys = Object.keys(v);
    const isPassengerObj = objectKeys.every(k => passengerKeys.includes(k));
    if (isPassengerObj) {
      const parts: string[] = [];
      if (v.adults) parts.push(`${v.adults} Adult${v.adults > 1 ? 's' : ''}`);
      if (v.children) parts.push(`${v.children} Child${v.children > 1 ? 'ren' : ''}`);
      if (v.infants) parts.push(`${v.infants} Infant${v.infants > 1 ? 's' : ''}`);
      if (v.seniors) parts.push(`${v.seniors} Senior${v.seniors > 1 ? 's' : ''}`);
      // Fallback to total if no specific breakdown captured
      if (parts.length === 0 && v.total) parts.push(`${v.total} Passenger${v.total > 1 ? 's' : ''}`);
      return parts.join(', ') || JSON.stringify(v);
    }

    // 3. Generic object – stringify
    return JSON.stringify(v);
  }

    // 4. ISO-timestamp strings → local date-time
  if (typeof v === 'string') {
    const parsed = Date.parse(v);
    if (!Number.isNaN(parsed) && v.includes('T')) {
      return new Date(parsed).toLocaleString();
    }
  }

  // Primitive fallback
  return String(v);
};


interface ResultsViewModalProps {
  open: boolean;
  onClose: () => void;
  provider: ProviderType;
  requestId: string;
  sessionId?: string;
}

const ResultsViewModal: React.FC<ResultsViewModalProps> = ({ open, onClose, provider, requestId, sessionId }) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [data, setData] = useState<ResultsViewResponse | null>(null);
  const [error, setError] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'results' | 'screenshots' | 'videos' | 'quote'>('results');
  const [preview, setPreview] = useState<any>(null);
  // Zoom & Pan states for screenshot preview modal
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [position, setPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // Handler to zoom in or out
  const handleZoom = (increase: boolean) => {
    setZoomLevel(prev => {
      const newZoom = increase ? prev + 0.25 : prev - 0.25;
      return Math.max(0.5, Math.min(newZoom, 3)); // constrain between 50% and 300%
    });
  };

  // Mouse events for panning
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;

    const dx = e.clientX - dragStart.x;
    const dy = e.clientY - dragStart.y;

    setPosition(prev => ({
      x: prev.x + dx,
      y: prev.y + dy
    }));

    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Reset zoom and position
  const handleReset = () => {
    setZoomLevel(1);
    setPosition({ x: 0, y: 0 });
  };

  const groupedScreens = React.useMemo(
    () => groupByCabinId(data?.screenshots),
    [data?.screenshots]
  );

  const groupedVideos = React.useMemo(
    () => groupByCabinId(data?.videos),
    [data?.videos]
  );

  useEffect(() => {
    if (!open) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        const res = await getResultsView(provider, requestId, sessionId);
        // If screenshots array is missing or empty, try fallback endpoint
        if ((!res.screenshots || res.screenshots.length === 0) && sessionId) {
          try {
            const fallback = await getScreenshots(sessionId);
            if (fallback.success) {
              res.screenshots = fallback.screenshots;
            }
          } catch (err) {
            console.warn('Fallback screenshot fetch failed', err);
          }
        }
        setData(res);
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data');
        setLoading(false);
      }
    };

    fetchData();
  }, [open, provider, requestId, sessionId]);

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4 overflow-y-auto">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-5xl max-h-full overflow-y-auto">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Results View – {provider}</h2>
          <button onClick={onClose} className="text-gray-600 hover:text-gray-900">✕</button>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-700" />
          </div>
        ) : error ? (
          <div className="p-4 text-red-600 text-center">{error}</div>
        ) : data && !data.success ? (
          <div className="p-4 text-red-600 text-center">{data.error || 'Failed to fetch data'}</div>
        ) : (
          <>
            {/* Tabs */}
            <div className="flex border-b px-4 space-x-6 text-sm font-medium">
              {['results', 'screenshots', 'videos', 'quote'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab as any)}
                  className={`py-2 ${activeTab === tab ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-600'}`}
                >
                  {tab.charAt(0).toUpperCase() + tab.slice(1)}
                  {tab === 'screenshots' && data?.screenshots ? ` (${data.screenshots.length})` : ''}
                  {tab === 'videos' && data?.videos ? ` (${data.videos.length})` : ''}
                </button>
              ))}
            </div>

            {/* Content */}
            <div className="p-4 max-h-[70vh] overflow-y-auto">
              {activeTab === 'results' && (
                <div className="space-y-4 text-sm">
                  {/* Booking Summary */}
                  {data?.bookingInfo && (
                    (() => {
                      // Prepare summary object we will display
                      const summary: Record<string, any> = { ...data.bookingInfo };

                      // Compute total passengers if missing
                      const hasPassengerKey = Object.keys(summary).some(k => ['total_passengers','passenger_total','passengers'].includes(k));
                      if (!hasPassengerKey) {
                        const calcPassengers = (() => {
                          const res: any = data.results;
                          // Handle array of cabins
                          if (Array.isArray(res)) {
                            return res.reduce((acc, cab: any) => acc + (cab.passenger_count ?? cab.passengers?.total ?? 0), 0);
                          }
                          // Handle object with summary
                          if (res && typeof res === 'object') {
                            if (res.summary?.total_passengers) return res.summary.total_passengers;
                            if (Array.isArray(res.cabins)) {
                              return res.cabins.reduce((acc: number, cab: any) => acc + (cab.passenger_count ?? cab.passengers?.total ?? 0), 0);
                            }
                          }
                          return null;
                        })();
                        if (calcPassengers && calcPassengers > 0) {
                          summary['total_passengers'] = calcPassengers;
                        }
                      }

                      return (
                        <div>
                          <h3 className="font-semibold text-blue-800 mb-2">Booking Summary</h3>
                          <div className="grid grid-cols-2 gap-x-4 gap-y-1 bg-gray-50 p-3 rounded border">
                            {Object.entries(summary).filter(([key]) => key !== 'id').map(([k, v]) => (
                              <React.Fragment key={k}>
                                <div className="font-medium capitalize truncate">{formatKey(k)}</div>
                                <div
                                  className={`${['cabin_allocation','current_promos','selected_rate','selected_rate_code'].includes(k) ? 'whitespace-pre-wrap break-words' : 'truncate'} text-gray-800`}
                                  title={typeof v === 'string' ? v : undefined}
                                >
                                  {['cabin_allocation','current_promos'].includes(k) ? formatPromoValue(v) : formatByKey(k, v)}
                                </div>
                              </React.Fragment>
                            ))}
                          </div>
                        </div>
                      );
                    })()
                  )}

                  {/* Cabin Details */}
                  {(() => {
                    // Support both shapes: results is array OR results.cabins is array
                    const rawCabins: any[] = Array.isArray(data?.results)
                      ? (data?.results as any)
                      : Array.isArray((data?.results as any)?.cabins)
                        ? (data?.results as any).cabins
                        : [];
                    // Filter out placeholder summary objects that only contain grand_total or lack cabin identifiers
                    const cabins = rawCabins.filter(c => {
                      const keys = Object.keys(c);
                      const hasId = 'cabin_number' in c || 'cabin_id' in c;
                      const isSummaryOnly = keys.length === 1 && keys[0] === 'grand_total';
                      return hasId && !isSummaryOnly;
                    });
                    return cabins.length > 0 ? (
                      <div>
                        <h3 className="font-semibold text-blue-800 mb-2">Cabins</h3>
                        <div className="space-y-3 pr-2">
                          {cabins.map((cab: any, idx: number) => (
                            <div key={idx} className="border rounded p-2 bg-white shadow-sm">
                              <h4 className="font-medium mb-1">Cabin {cab.cabin_number ?? cab.cabin_id ?? idx + 1}</h4>
                              <div className="grid grid-cols-2 gap-x-3 gap-y-1 text-xs">
                                {Object.entries(cab).filter(([key]) => !['current_promos','id','booking_id'].includes(key)).map(([k, v]) => (
                                  <React.Fragment key={k}>
                                    <div className="capitalize truncate text-gray-600">{k.replace(/_/g, ' ')}</div>
                                    <div
                                      className={`${['cabin_allocation','current_promos','selected_rate','selected_rate_code'].includes(k) ? 'whitespace-pre-wrap break-words' : 'truncate'} text-gray-800`}
                                      title={typeof v === 'string' ? v : undefined}
                                    >
                                      {['cabin_allocation','current_promos'].includes(k) ? formatPromoValue(v) : formatByKey(k, v)}
                                    </div>
                                  </React.Fragment>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : null;
                  })()}
                </div>
              )}

              {activeTab === 'quote' && (
                <div className="space-y-3 text-sm">
                  {data?.quote_input && (
                    <div className="grid grid-cols-2 gap-x-4 gap-y-1 bg-gray-50 p-3 rounded border">
                      <div className="font-medium">Request ID</div>
                      <div>{data.request_id}</div>
                      <div className="font-medium">Provider</div>
                      <div>{data.provider}</div>
                      <div className="font-medium">Submitted</div>
                      <div>{data.quote_input.created_at ? new Date(data.quote_input.created_at).toLocaleString() : '—'}</div>
                    </div>
                  )}
                  <pre className="whitespace-pre-wrap bg-gray-50 p-4 rounded border overflow-x-auto">
                    {data?.quote_input?.text_input || 'No quote input found.'}
                  </pre>
                </div>
              )}

              {activeTab === 'screenshots' && (
                <div className="space-y-6">
                  {Object.entries(groupedScreens).sort(sortByCabinKey).map(([cab, arr]: [string, any[]]) => (
                    <div key={cab}>
                      <h3 className="font-semibold mb-2 text-sm text-blue-800">Cabin {cab === 'general' ? 'General' : cab}</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {(arr as any[]).map((ss: any) => (
                          <button
                            type="button"
                            key={ss.id}
                            onClick={() => setPreview(ss)}
                            className="border rounded hover:shadow focus:outline-none"
                          >
                            <img
                              src={ss.minio_url || '/public/window.svg'}
                              alt={ss.file_name}
                              className="w-full h-40 object-cover rounded-t"
                            />
                            <div className="text-xs p-1 truncate">{ss.screenshot_type}</div>
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                  {data?.screenshots?.length === 0 && <p>No screenshots found.</p>}
                </div>
              )}

              {activeTab === 'videos' && (
                <div className="space-y-6">
                  {Object.entries(groupedVideos).sort(sortByCabinKey).map(([cab, vids]: [string, any[]]) => (
                    <div key={cab}>
                      <h3 className="font-semibold mb-2 text-sm text-blue-800">Cabin {cab === 'general' ? 'General' : cab}</h3>
                      <div className="grid grid-cols-2 gap-3">
                        {(vids as any[]).map((vid: any) => (
                          <div key={vid.id} className="border rounded p-2 flex flex-col">
                            <video controls className="w-full h-40 bg-black object-contain" preload="metadata">
                              <source src={getVideoStreamUrl(vid.id)} type="video/webm" />
                              Your browser does not support the video tag.
                            </video>
                            <div className="text-xs mt-1 font-medium">{vid.video_type?.replace(/_/g,' ') || 'Video'}</div>
                            <div className="text-[10px] text-gray-600">{vid.timestamp ? new Date(vid.timestamp).toLocaleString() : ''} • {vid.format?.toUpperCase() || 'WEBM'}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                  {data?.videos?.length === 0 && <p>No videos found.</p>}
                </div>
              )}
            </div>
          </>
        )}
      {/* Screenshot Preview */}
        {preview && (
          <div className="fixed inset-0 z-60 flex items-center justify-center bg-black bg-opacity-70 p-4" onClick={() => setPreview(null)}>
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] flex flex-col" onClick={e => e.stopPropagation()}>
              {/* Header */}
              <div className="flex justify-between items-center p-2 border-b shrink-0">
                <span className="text-sm font-medium truncate mr-2">{`Cabin ${preview.cabin_id ?? 'General'} – ${preview.screenshot_type?.replace(/_/g,' ') || 'Screenshot'}`}</span>
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2 bg-white rounded-md px-2 py-1 border">
                    <button
                      onClick={() => handleZoom(false)}
                      className="text-gray-700 hover:text-gray-900 focus:outline-none"
                      disabled={zoomLevel <= 0.5}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                    </button>
                    <span className="text-sm">{Math.round(zoomLevel * 100)}%</span>
                    <button
                      onClick={() => handleZoom(true)}
                      className="text-gray-700 hover:text-gray-900 focus:outline-none"
                      disabled={zoomLevel >= 3}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                  <button
                    onClick={handleReset}
                    className="text-sm bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded"
                  >
                    Reset
                  </button>
                  <button className="text-gray-600 hover:text-gray-900" onClick={() => setPreview(null)}>✕</button>
                </div>
              </div>
              {/* Image container with zoom & pan */}
              <div className="flex-1 overflow-auto p-2 bg-gray-50 relative" onMouseUp={handleMouseUp} onMouseLeave={handleMouseUp}>
                <div
                  className="relative mx-auto"
                  style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                >
                  <img
                    src={preview.minio_url}
                    alt={preview.file_name}
                    className="w-full h-auto select-none"
                    style={{
                      transform: `scale(${zoomLevel}) translate(${position.x}px, ${position.y}px)` ,
                      transformOrigin: 'center',
                      transition: isDragging ? 'none' : 'transform 0.1s ease-out'
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultsViewModal;
