"""
Quote Processor module for handling cruise booking quotes and extracting structured information.

This module provides functionality to parse cruise booking quote text and extract key
details such as sailing date, duration, ship ID, cabin types, and passenger distributions.
It uses regular expressions for text extraction and can leverage Google's Gemini AI
for advanced passenger distribution analysis.
"""

import os
import re
from datetime import datetime

from loguru import logger
from dotenv import load_dotenv
import google.generativeai as genai

from Studio.category_normalizer import CategoryNormalizer

# Load environment variables from .env file
load_dotenv()

# Initialize Gemini AI model for passenger distribution analysis
genai_model = None
try:
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
    if not GEMINI_API_KEY:
        logger.error("Missing GEMINI_API_KEY in environment")
    else:
        # Configure the Gemini API with the API key
        genai.configure(api_key=GEMINI_API_KEY)

        # Set up deterministic (temperature=0) configuration for consistent results
        generation_config = {
            "temperature": 0,
            "max_output_tokens": 8192,
            "response_mime_type": "text/plain",
        }

        # Initialize the Gemini model with the flash variant
        genai_model = genai.GenerativeModel(
            model_name="gemini-2.0-flash", generation_config=generation_config
        )
        logger.debug("Gemini API initialized for passenger distribution analysis")
except Exception as e:
    logger.error(f"Gemini API initialization failed: {e}")


class QuoteProcessor:
    """
    A class for processing cruise quotes and extracting structured information.
    
    This class provides static methods to parse cruise booking quotes, extract relevant
    details like sailing date, duration, cabin types, and analyze passenger distributions
    using both rule-based methods and AI-powered analysis.
    """

    @staticmethod
    def extract_cruise_details(quote):
        """
        Extract detailed cruise information from a quote text.
        
        This method uses regular expressions to parse the quote text and extract key
        booking details such as sailing date, duration, ship ID, airport code,
        number of cabins, and cabin types.
        
        Args:
            quote (str): The full text of the cruise quote to process
            
        Returns:
            list: A list containing the extracted details in the following order:
                 [sailing_date_formatted, duration, ship_id, airport_code, 
                  number_of_cabins, passengers_per_cabin..., cabin_types...]
                  
                 Where:
                 - sailing_date_formatted is in 'MM-DD-YYYY' format
                 - passengers_per_cabin is a list of passenger counts for each cabin
                 - cabin_types is a list of standardized cabin type strings
        """
        # Extract sailing date using regex, with handling for ordinal suffixes (1st, 2nd, etc.)
        sailing_date_match = re.search(
            r'Sails:\s*([A-Za-z]+ \d{1,2}(?:st|nd|rd|th)?, \d{4})', quote, re.IGNORECASE
        )
        sailing_date_formatted = "Sailing date not found"
        if sailing_date_match:
            try:
                # Remove ordinal suffixes from day number for proper datetime parsing
                date_str = re.sub(
                    r'(\d+)(st|nd|rd|th)', r'\1', sailing_date_match.group(1)
                )

                # Parse the date using datetime and format it as MM-DD-YYYY
                sailing_date = datetime.strptime(date_str, '%B %d, %Y')
                sailing_date_formatted = sailing_date.strftime('%m-%d-%Y')
            except Exception as e:
                logger.error(f"Date parsing error: {e}")

        # Extract cruise duration (number of nights)
        duration_match = re.search(r'(\d+)\s*Night', quote)
        duration = duration_match.group(1) if duration_match else "Duration not found"

        # Extract ship ID from URL-like pattern
        ship_id_match = re.search(r'ship_id[^=]*=(\d+)', quote)
        ship_id = ship_id_match.group(1) if ship_id_match else "Ship ID not found"

        # Extract airport code with two different patterns
        airport_code = "Airport code not found"
        # Try pattern like "Airport: Miami (MIA)"
        airport_text_match = re.search(r'Airport:\s*.*?\(([A-Z]{3})\)', quote)
        if airport_text_match:
            airport_code = airport_text_match.group(1)
        else:
            # Try URL-encoded pattern like "residency_airport%5D=MIA"
            airport_url_match = re.search(r'residency_airport%5D=([A-Z]{3})', quote)
            if airport_url_match:
                airport_code = airport_url_match.group(1)

        # Extract total passenger count
        passengers_match = re.search(r'Passengers:\s*(\d+)\s*total', quote)
        total_passengers = int(passengers_match.group(1)) if passengers_match else 1

        # Extract cabin information (number and types)
        cabins_match = re.search(
            r'Cabins:\s*(\d+)\s*total\s*\((.*?)\)', quote, re.IGNORECASE
        )
        number_of_cabins = 1  # Default to 1 cabin
        cabin_distribution = []  # Will hold cabin types
        is_or_case = False  # Flag for "or" case in cabin selection

        if cabins_match:
            try:
                number_of_cabins = int(cabins_match.group(1))
                category_details = cabins_match.group(2).strip().lower()

                # Handle "or" case (choice between cabin types)
                if ' or ' in category_details:
                    is_or_case = True
                    categories = category_details.split(' or ')
                    for cat in categories:
                        cabin_type = cat.strip()
                        # Normalize cabin category to standardized type
                        normalized_type = CategoryNormalizer.normalize_cabin_category(
                            cabin_type
                        )
                        cabin_distribution.append(normalized_type)

                    # In "or" case, number of cabins equals number of options
                    number_of_cabins = len(categories)
                else:
                    # Handle comma-separated list (multiple cabin types)
                    categories = category_details.split(',')
                    category_options = [cat.strip() for cat in categories]
                    for cat in category_options:
                        parts = cat.split()
                        # If first part is a number, it's the count of this cabin type
                        count = int(parts[0]) if parts[0].isdigit() else 1
                        # Rest is the cabin type (or all if first part isn't a number)
                        cabin_type = ' '.join(parts[1:]) if parts[0].isdigit() else cat
                        # Normalize cabin type
                        normalized_type = CategoryNormalizer.normalize_cabin_category(
                            cabin_type
                        )
                        # Add multiple entries for multiple cabins of same type
                        cabin_distribution.extend([normalized_type] * count)

                # Ensure cabin_distribution length matches number_of_cabins
                if len(cabin_distribution) < number_of_cabins:
                    # If too few, duplicate the last type
                    cabin_distribution.extend(
                        [cabin_distribution[-1]] *
                        (number_of_cabins - len(cabin_distribution))
                    )
                elif len(cabin_distribution) > number_of_cabins:
                    # If too many, truncate
                    cabin_distribution = cabin_distribution[:number_of_cabins]

                logger.debug(
                    f"Category details: {category_details}, OR case: {is_or_case}"
                )
                logger.info(
                    f"Detected {number_of_cabins} cabins with types: {cabin_distribution}"
                )

            except Exception as e:
                logger.error(f"Cabin extraction error: {e}")
                # Default to inside cabins if parsing fails
                cabin_distribution = ['INSIDE'] * number_of_cabins

        # Extract comments section for passenger distribution analysis
        comments_match = re.search(
            r'Comments:\s*(.*?)(?=\s+Booking URL:|$)', quote, re.IGNORECASE | re.DOTALL
        )
        comments_text = ""
        if comments_match:
            comments_text = comments_match.group(1).strip()
            # Normalize whitespace
            comments_text = re.sub(r'\s+', ' ', comments_text)
            logger.debug(f"Extracted comments: {comments_text[:100]}...")
        else:
            logger.debug("No comments section found")

        # Determine passenger distribution across cabins
        passengers_per_cabin = QuoteProcessor.extract_passenger_distribution(
            comments_text, number_of_cabins, total_passengers, is_or_case
        )

        # Compile all extracted details into a single list
        extracted_details = [
            sailing_date_formatted, duration, ship_id, airport_code,
            str(number_of_cabins)
        ]

        # Add passenger distribution (convert to strings)
        extracted_details.extend(map(str, passengers_per_cabin))

        # Add cabin types
        extracted_details.extend(cabin_distribution)

        return extracted_details

    @staticmethod
    def extract_passenger_distribution(
        comments_text, number_of_cabins, total_passengers, is_or_case
    ):
        """
        Determine the distribution of passengers across cabins.
        
        This method analyzes the comments section of a cruise quote to determine
        how passengers should be distributed across cabins. If AI analysis is available,
        it uses the Gemini model to interpret passenger arrangements mentioned in the text.
        Otherwise, it uses default distribution rules.
        
        Args:
            comments_text (str): The comments text to analyze
            number_of_cabins (int): The total number of cabins
            total_passengers (int): The total number of passengers
            is_or_case (bool): Whether this is an "or" case (cabin choice scenario)
            
        Returns:
            list: A list of integers representing the number of passengers per cabin,
                 with length equal to number_of_cabins and sum equal to total_passengers
        """
        # For "or" case, each cabin is a separate option, so all passengers go in each
        if is_or_case:
            default_distribution = [total_passengers] * number_of_cabins
        else:
            # For normal case, distribute passengers evenly across cabins
            default_distribution = [
                total_passengers // number_of_cabins
            ] * number_of_cabins

            # Distribute remaining passengers (from division remainder) one per cabin
            for i in range(total_passengers % number_of_cabins):
                default_distribution[i] += 1

        # If no comments to analyze or AI model not available, use default distribution
        if not comments_text or genai_model is None:
            logger.debug("Using default passenger distribution")
            return default_distribution

        # Use AI to analyze the comments for more precise passenger distribution
        try:
            # Construct prompt for AI model to analyze passenger distribution
            prompt = f"""Analyze this cruise booking comment and extract the passenger distribution per cabin.

                        Rules:
                        1. Count each person as 1 passenger (adult, senior, child, teen = 1 each)
                        2. Output exactly {number_of_cabins} numbers, one per line
                        3. Numbers must be in room/cabin order (Room 1 first, Room 2 second, etc.)
                        4. Sum of all numbers must equal {total_passengers}

                        Input text to analyze:
                        {comments_text}

                        Output format:
                        - ONLY numbers
                        - One number per line
                        - No text or explanations
                        - Example format:
                        4
                        2
                        1"""

            # Generate response from AI model
            response = genai_model.generate_content(prompt)
            response_text = response.text.strip()
            logger.debug(f"AI model response received")

            distribution = []

            # Parse response - either newline delimited or space delimited
            if '\n' in response_text:
                numbers = [line.strip() for line in response_text.split('\n')]
            else:
                # Fall back to space-separated if no newlines
                numbers = response_text.replace(',', ' ').split()

            # Convert text numbers to integers
            for num in numbers:
                if num.strip().isdigit():
                    distribution.append(int(num))

            # Validate distribution - must have correct length and sum to total passengers
            if len(distribution) == number_of_cabins and sum(distribution
                                                             ) == total_passengers:
                logger.info(f"AI-determined passenger distribution: {distribution}")
                return distribution
            else:
                # Fall back to default if AI result is invalid
                logger.warning(
                    f"Invalid passenger distribution from AI: {distribution}"
                )
                return default_distribution

        except Exception as e:
            logger.error(f"AI extraction error: {e}")
            return default_distribution
