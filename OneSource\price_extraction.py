 
import asyncio
import logging
import re
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError

logger = logging.getLogger(__name__)

class OneSourcePriceExtractor:
    """Handles extraction and calculation of prices from the pricing page."""

    def __init__(self, page: Page, commission_percentage: float = 11.0):
        self.page = page
        self.commission_percentage = commission_percentage

    async def extract_and_calculate(self) -> dict:
        """Extract commission, gross fare, onboard credit, calculate final price, and log them."""
        try:
            await self.page.wait_for_load_state('domcontentloaded', timeout=10000)
            await asyncio.sleep(2)  # Wait for dynamic content

            # Extract commission amount
            commission = await self._extract_commission()
            if commission is None:
                logger.error("Failed to extract commission amount")
                return None

            # Extract gross fare
            gross_fare = await self._extract_gross_fare()
            if gross_fare is None:
                logger.error("Failed to extract gross fare")
                return None

            # Extract onboard credit
            onboard_credit = await self._extract_onboard_credit()
            if onboard_credit is None:
                logger.warning("Failed to extract onboard credit, setting to 0")
                onboard_credit = 0

            # Calculate p = (commission * 10) * (commission_percentage / 100)
            p = (commission * 10) * (self.commission_percentage / 100)
            # Calculate final price = gross_fare - p
            final_price = round(gross_fare - p, 2)

            # Log the values
            logger.info(f"Extracted Commission: {commission}")
            logger.info(f"Extracted Gross Fare: {gross_fare}")
            logger.info(f"Extracted Onboard Credit: {onboard_credit}")
            logger.info(f"Commission Percentage Used: {self.commission_percentage}%")
            logger.info(f"Calculated Final Price: {final_price}")

            return {
                'commission': commission,
                'gross_fare': gross_fare,
                'onboard_credit': onboard_credit,
                'final_price': final_price
            }

        except Exception as e:
            logger.error(f"Error in price extraction and calculation: {e}")
            return None

    async def _extract_commission(self) -> float:
        """Extract the standard commission amount."""
        commission_selectors = [
            'tr.arial11_13h_333333_boxcontent:has-text("Standard") > td:nth-child(4)',
            'tr:has-text("Standard") > td[width="95"]',
            'xpath=//*[@id="commTab"]/table/tbody/tr/td/table/tbody/tr[1]/td[4]',
            '#commTab > table > tbody > tr > td > table > tbody > tr:nth-child(1) > td:nth-child(4)'
        ]

        for selector in commission_selectors:
            try:
                element = await self.page.wait_for_selector(selector, timeout=5000)
                if element:
                    text = await element.inner_text()
                    text = text.strip()
                    # Extract number, handling possible currency or spaces
                    match = re.search(r'\d+[.,]?\d*', text)
                    if match:
                        value = float(match.group().replace(',', '').replace('$', ''))
                        # If the value is around 10-15, it's likely the percentage; skip to next selector
                        if 5 <= value <= 20:
                            logger.debug(f"Extracted value {value} looks like percentage, trying next selector")
                            continue
                        return value
            except PlaywrightTimeoutError:
                continue
            except Exception as e:
                logger.debug(f"Error extracting commission with {selector}: {e}")
                continue

        # Fallback: if all selectors fail, try extracting from whole row
        fallback_selector = 'tr.arial11_13h_333333_boxcontent:has-text("Standard")'
        try:
            row = await self.page.wait_for_selector(fallback_selector, timeout=5000)
            if row:
                text = await row.inner_text()
                # Find all numbers, take the last one as amount
                numbers = re.findall(r'\d+[.,]?\d*', text)
                if numbers and len(numbers) >= 2:
                    # Second number is percentage, last is amount
                    return float(numbers[-1].replace(',', ''))
        except Exception as e:
            logger.debug(f"Fallback extraction failed: {e}")

        return None

    async def _extract_gross_fare(self) -> float:
        """Extract the total gross fare."""
        gross_selectors = [
            'tr.arial11_13h_333333_boxcontent:has-text("Gross Fare") > td:nth-child(8)',
            'xpath=//*[@id="pricingTab"]/table/tbody/tr[19]',
            '#pricingTab > table > tbody > tr:nth-child(24) > td:nth-child(8)'
        ]

        for selector in gross_selectors:
            try:
                element = await self.page.wait_for_selector(selector, timeout=5000)
                if element:
                    text = await element.inner_text()
                    # Extract number
                    match = re.search(r'\d+[.,]?\d*', text.strip())
                    if match:
                        return float(match.group().replace(',', ''))
            except PlaywrightTimeoutError:
                continue
            except Exception as e:
                logger.debug(f"Error extracting gross fare with {selector}: {e}")
                continue

        return None 

    async def _extract_onboard_credit(self) -> float:
        """Extract the total onboard credit."""
        onboard_selectors = [
            'xpath=//*[@id="pricingTab"]/table/tbody/tr[24]/td[8]',
        ]

        for selector in onboard_selectors:
            try:
                element = await self.page.wait_for_selector(selector, timeout=5000)
                if element:
                    text = await element.inner_text()
                    # Remove any script tags or extra whitespace
                    text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.DOTALL)
                    text = text.strip()
                    
                    # Extract number from text like "50.00 USD" or "    50.00"
                    match = re.search(r'(\d+[.,]?\d*)', text)
                    if match:
                        value = float(match.group().replace(',', ''))
                        logger.debug(f"Extracted onboard credit: {value} from text: {text}")
                        return value
            except PlaywrightTimeoutError:
                continue
            except Exception as e:
                logger.debug(f"Error extracting onboard credit with {selector}: {e}")
                continue

        return None 