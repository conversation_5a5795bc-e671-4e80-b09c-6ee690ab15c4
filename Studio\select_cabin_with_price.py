from datetime import datetime
import json

from loguru import logger
from playwright.async_api import TimeoutError
from Core.ui_logger import ui_log


class CabinSelector:
    """
    A class responsible for cabin selection operations in the cruise booking process.
    
    This class provides functionality to find available cabins on a cruise ship,
    select a specific cabin, handle selection errors, and extract pricing information.
    It relies on Playwright for web automation.
    """

    async def find_cabins(self, cabin_number, timestamp):
        """
        Find and extract available cabins from the cabin selection page.
        
        This method waits for the cabin table to load, takes a screenshot of the
        cabin selection screen, and extracts detailed information about each available cabin.
        
        Args:
            cabin_number (int): The target cabin number for reference
            timestamp (str): Timestamp for naming screenshot files
            
        Returns:
            list: A list of dictionaries containing cabin information including:
                  cabin number, deck, bed type, location, accessibility, and UI elements
                  
        Raises:
            Exception: If no cabin rows are found or no valid cabin data can be extracted
        """
        ui_log("Searching for available cabins", 
               session_id=self.session_id, 
               cabin_id=cabin_number, 
               step="cabins_find", module="Studio")
               
        # Wait for cabin table to be visible with sufficient timeout
        cabin_table = await self.page.wait_for_selector(
            "div.sticky-header-container.table-primary.table-results-standard",
            state="visible",
            timeout=30000
        )
        logger.info("Cabin table loaded")

        # Take screenshot of cabin selection page for record-keeping
        screenshot_cabin = self.page.locator(".main-area-container").first
        await self.optimize_screenshot(
            screenshot_cabin, f"cabin_selection_{timestamp}.png", cabin_number, quality=50
        )

        # Wait for actual cabin rows to be visible before proceeding
        await self.page.wait_for_selector(
            "div.sticky-header-container.table-primary.table-results-standard tbody tr[data-grid-row]",
            state="visible",
            timeout=30000
        )

        # Get all cabin rows using a precise locator
        cabin_rows = await self.page.locator(
            "div.sticky-header-container.table-primary.table-results-standard tbody tr[data-grid-row]"
        ).all()

        if not cabin_rows:
            raise Exception("No cabin rows found in table")

        cabin_data = []
        logger.info(f"Found {len(cabin_rows)} cabin rows to process")

        # Process each cabin row to extract relevant details
        for idx, row in enumerate(cabin_rows, 1):
            try:
                # Ensure row is visible in viewport before interacting
                await row.scroll_into_view_if_needed()

                # Extract row data from data attribute which contains JSON
                row_data_str = await row.get_attribute('data-grid-row-data')
                if not row_data_str:
                    continue

                try:
                    # Parse the JSON data for this cabin row
                    row_data = json.loads(row_data_str)
                except json.JSONDecodeError as je:
                    logger.error(f"JSON parsing failed for row {idx}: {str(je)}")
                    continue

                # Get cabin number from the parsed data
                cabin_number_row = row_data.get('cabin_number')
                if not cabin_number_row:
                    continue

                try:
                    # Locate selection elements for this cabin
                    radio_input = row.locator(
                        f"input[type='radio'][id='cabin_{cabin_number_row}-th1']"
                    ).first
                    select_button = row.locator(
                        f"label[for='cabin_{cabin_number_row}-th1']"
                    ).first

                    # Verify selection elements exist
                    if not await radio_input.count() or not await select_button.count():
                        logger.warning(
                            f"Selection elements missing for cabin {cabin_number_row}"
                        )
                        continue
                except Exception:
                    logger.warning(
                        f"Selection elements missing for cabin {cabin_number_row}"
                    )
                    continue

                # Build comprehensive cabin information object
                cabin_info = {
                    'cabin_number': cabin_number_row, 'deck': row_data.get('deck', ''),
                    'deck_number': row_data.get('deck_number', ''), 'bed_type':
                    row_data.get('bed_type',
                                 ''), 'location': row_data.get('location',
                                                               ''), 'accessible':
                    row_data.get('accessible',
                                 'N'), 'connecting': row_data.get('connecting', 'N'),
                    'radio_input': radio_input, 'select_button': select_button
                }
                cabin_data.append(cabin_info)

            except Exception as e:
                logger.error(f"Error processing cabin row {idx}: {str(e)}")
                continue

        # Ensure we found at least one valid cabin
        if not cabin_data:
            raise Exception("No valid cabin data could be extracted")

        logger.info(f"Successfully extracted {len(cabin_data)} cabins")

        #ui_log(f"Processed {len(cabin_data)} cabin options", 
        #       session_id=self.session_id, 
        #       cabin_id=cabin_number, 
        #       step="cabins_processed", module="Studio")

        return cabin_data

    async def select_cabin(self, cabin_data, cabin_number):
        """
        Select the first cabin from the provided cabin data.
        
        Args:
            cabin_data (list): List of dictionaries containing cabin information
            cabin_number (int): Target cabin number for logging purposes
            
        Returns:
            dict: The selected cabin information
            
        Note:
            This method always selects the first cabin in the list, regardless of the
            cabin_number parameter (which is used for logging only).
        """
        # Select the first cabin from the list
        selected_cabin = cabin_data[0]
        logger.info(f"Selected cabin {selected_cabin['cabin_number']}")

        # Add UI log for cabin selection
        ui_log(f"Cabin {selected_cabin['cabin_number']} Selected", 
               session_id=self.session_id, 
               cabin_id=cabin_number, 
               step="cabin_selected", module="Studio")

        # Get reference to the select button element
        select_button = selected_cabin['select_button']

        # Ensure button is in view before clicking
        await select_button.scroll_into_view_if_needed()

        # Click the button to select this cabin
        await select_button.click()
        logger.info(
            f"Clicked select button for cabin {selected_cabin['cabin_number']}"
        )

        # Wait for page to update after selection
        await self.wait_for_page_load(timeout=30)

        return selected_cabin

    async def handle_cabin_error(self, cabin_data, selected_cabin, timestamp, cabin_number):
        """
        Handle errors during cabin selection and attempt recovery if needed.
        
        This method first checks if the cabin selection was successful by looking for
        pricing elements. If unsuccessful, it checks for error messages and attempts
        to select an alternative cabin if the current one is unavailable.
        
        Args:
            cabin_data (list): List of dictionaries containing cabin information
            selected_cabin (dict): The initially selected cabin that may have errors
            timestamp (str): Timestamp for naming screenshot files
            cabin_number (int): The logical cabin number for tracking
            
        Returns:
            tuple: (selected_cabin, bool) where:
                - selected_cabin is the final cabin selection (may be different if recovery occurred)
                - bool indicates whether an alternative cabin was selected (True) or not (False)
        """
        ui_log("Verifying cabin selection", 
               session_id=self.session_id, 
               cabin_id=cabin_number, 
               step="cabin_check", module="Studio")

        try:
            # First check if cabin selection was successful by looking for pricing indicators
            pricing_indicators = [
                "table.table.table-pricing", ".pricing-details", "[class*='pricing']",
                ".fare-details"
            ]

            # Check various selectors that would indicate successful selection
            for indicator in pricing_indicators:
                try:
                    pricing_element = await self.page.wait_for_selector(
                        indicator, state="visible", timeout=10000
                    )

                    # If we found pricing, the selection was successful
                    if await pricing_element.is_visible():
                        logger.info("Cabin selection successful - pricing found")
                        ui_log("Cabin selected successfully", 
                               session_id=self.session_id, 
                               cabin_id=cabin_number, 
                               step="cabin_selected", module="Studio")
                        return selected_cabin, False
                except TimeoutError:
                    continue
        except Exception:
            pass

        # If we get here, we need to check for errors and possibly try another cabin
        try:
            # Wait for page to load after potential error
            await self.wait_for_page_load(timeout=30)

            # Common error message selectors
            error_selectors = [
                "div.message.message-system.message-error", ".error-message",
                "div.message-error"
            ]

            # Track if we found an error and its message
            error_found = False
            error_message_text = ""

            # Check all potential error selectors
            for selector in error_selectors:
                try:
                    error_elements = await self.page.locator(selector).all()
                    for error_element in error_elements:
                        if await error_element.is_visible():
                            error_message_text = await error_element.text_content()

                            # Identify specific error messages about unavailable cabins
                            if "cannot be held" in error_message_text.lower(
                            ) or "unavailable" in error_message_text.lower():
                                logger.warning(f"Error: {error_message_text}")
                                error_found = True
                                break
                    if error_found:
                        break
                except Exception:
                    continue

            # If cabin unavailable and we have alternatives, try the next one
            if error_found and len(cabin_data) > 1:
                next_cabin_index = cabin_data.index(selected_cabin) + 1
                if next_cabin_index < len(cabin_data):
                    next_cabin = cabin_data[next_cabin_index]
                    logger.info(
                        f"Trying alternative cabin {next_cabin['cabin_number']}"
                    )
                    ui_log("There was a problem selecting your cabin. We're selecting an alternative now", 
                               session_id=self.session_id, 
                               cabin_id=cabin_number, 
                               step="handle_cabin_error", module="Studio")

                    try:
                        # Wait for the cabin table to be visible again
                        cabin_table = await self.page.wait_for_selector(
                            "div.sticky-header-container.table-primary.table-results-standard",
                            state="visible",
                            timeout=30000
                        )

                        # Get all cabin rows to find the next cabin
                        cabin_rows = await self.page.locator(
                            "div.sticky-header-container.table-primary.table-results-standard tbody tr[data-grid-row]"
                        ).all()

                        if not cabin_rows:
                            raise Exception("No cabin rows found after error")

                        next_cabin_number = next_cabin['cabin_number']
                        next_select_button = None

                        # Find the row matching the next cabin
                        for row in cabin_rows:
                            try:
                                row_data_str = await row.get_attribute('data-grid-row-data')
                                if not row_data_str:
                                    continue

                                row_data = json.loads(row_data_str)
                                cabin_number_row = row_data.get('cabin_number')

                                # Found the target cabin in the table
                                if cabin_number_row == next_cabin_number:
                                    next_select_button = row.locator(
                                        f"label[for='cabin_{cabin_number_row}-th1']"
                                    ).first
                                    break
                            except Exception as e:
                                logger.warning(
                                    f"Error finding cabin {next_cabin_number}: {str(e)}"
                                )
                                continue

                        if not next_select_button:
                            raise Exception(
                                f"Select button not found for cabin {next_cabin_number}"
                            )

                        # Click the next cabin's select button
                        await next_select_button.scroll_into_view_if_needed()
                        await next_select_button.click()
                        logger.info(
                            f"Selected alternative cabin {next_cabin_number}"
                        )

                        # Wait for page to load after selection
                        await self.wait_for_page_load(timeout=30)

                        # Return the new cabin and flag that we changed cabins
                        selected_cabin = next_cabin
                        return selected_cabin, True

                    except Exception as e:
                        logger.error(
                            f"Failed to select alternative cabin: {str(e)}"
                        )
                        ui_log("The system couldn't find a suitable alternative cabin", 
                               session_id=self.session_id, 
                               cabin_id=cabin_number, 
                               step="handle_cabin_error", module="Studio")
                    # Wait for page to fully load after any operation
                    await self.wait_for_page_load(timeout=30)

        except Exception as e:
            logger.warning(f"Error checking cabin selection: {str(e)}")

        # Return original cabin if recovery failed or wasn't needed
        return selected_cabin, False

    async def extract_pricing_data(self, timestamp, cabin_number):
        """
        Extract pricing details from the pricing table for the selected cabin.
        
        This method locates the pricing table, extracts all pricing rows and their values,
        and expands additional pricing details if available.
        
        Args:
            timestamp (str): Timestamp for naming screenshot files
            cabin_number (int): The cabin number for reference
            
        Returns:
            list: A list of dictionaries containing pricing information including
                  item names and amounts
                  
        Raises:
            Exception: If the pricing table cannot be found after multiple attempts
        """
        # Ensure page is fully loaded
        await self.wait_for_page_load(timeout=30)

        pricing_table = None
        try:
            # First attempt to find the pricing table
            pricing_table = await self.page.wait_for_selector(
                "table.table.table-pricing.table-pricing-details",
                state="visible",
                timeout=30000
            )
            logger.info("Pricing table found")
        except TimeoutError:
            # If table not found initially, try again after ensuring page is fully loaded
            await self.wait_for_page_load(timeout=30)

            try:
                # Second attempt with extended wait
                pricing_table = await self.page.wait_for_selector(
                    "table.table.table-pricing.table-pricing-details",
                    state="visible",
                    timeout=30000
                )
                logger.info("Pricing table found after extended wait")
            except Exception as e:
                logger.warning(f"Extended pricing table wait failed: {str(e)}")

        # If we still don't have a pricing table, we can't proceed
        if not pricing_table:
            logger.error("Pricing table not found")
            raise Exception("Pricing table not found after multiple attempts")

        # Get pricing table locator and ensure it's in view
        pricing_table_locator = self.page.locator(
            "table.table.table-pricing.table-pricing-details"
        )
        await pricing_table_locator.scroll_into_view_if_needed()

        pricing_data = []

        # Get all pricing rows using selectors that match different row types
        pricing_rows = await pricing_table_locator.locator(
            "tr.table-row, tr.table-collapsable-row, tr.table-pricing-promotions, tr.table-pricing-total"
        ).all()

        # Process each row to extract item name and amount
        for row in pricing_rows:
            try:
                cells = await row.locator("td").all()
                if cells:
                    row_data = {}

                    # First cell contains the item name/description
                    row_data['Item'] = await cells[0].inner_text()
                    row_data['Item'] = row_data['Item'].strip()

                    # Second cell (if exists) contains the amount
                    if len(cells) > 1:
                        # Clean up dollar amounts by removing $ and commas
                        amount_text = await cells[1].inner_text()
                        row_data['Amount'] = amount_text.strip().replace(
                            '$', ''
                        ).replace(',', '')

                    pricing_data.append(row_data)
            except Exception as e:
                logger.error(f"Error extracting pricing row: {str(e)}")
                continue

        logger.info(f"Extracted {len(pricing_data)} pricing items")

        # Try to expand detailed pricing information for more complete data
        await self.expand_pricing_details(timestamp, cabin_number)

        return pricing_data

    async def expand_pricing_details(self, timestamp, cabin_number):
        """
        Expand the collapsible pricing details section if available.
        
        This method attempts to find and click the expansion button for detailed
        pricing information, then takes a screenshot of the expanded view.
        
        Args:
            timestamp (str): Timestamp for naming screenshot files
            
        Note:
            If the button is not found or already expanded, appropriate log messages
            are recorded but no exception is raised.
        """
        try:
            # Look for the pricing expansion button in non-expanded state
            pricing_button = self.page.locator(
                'button[data-collapser-trigger="pricing-12-th1"][aria-expanded="false"]'
            )

            # Check if the button exists and needs expansion
            if await pricing_button.count() > 0:
                logger.info("Found pricing-12-th1 button to expand")

                # Make sure the button is visible before clicking
                await pricing_button.scroll_into_view_if_needed()

                # Click the button to expand pricing details
                await pricing_button.click()

                # Wait for button to change to expanded state to confirm expansion worked
                try:
                    await self.page.wait_for_selector(
                        'button[data-collapser-trigger="pricing-12-th1"][aria-expanded="true"]',
                        timeout=10000,
                        state="visible"
                    )
                    logger.info("Successfully expanded pricing details")
                except Exception as wait_error:
                    logger.warning(
                        f"Could not verify if button expanded: {str(wait_error)}"
                    )
            else:
                # Check if button is already expanded
                expanded_button = self.page.locator(
                    'button[data-collapser-trigger="pricing-12-th1"][aria-expanded="true"]'
                )

                if await expanded_button.count() > 0:
                    logger.info("Pricing details are already expanded")
                else:
                    logger.warning("Pricing detail button not found on page")


        except Exception as e:
            # Non-critical error - we continue even if expansion fails
            logger.warning(
                f"Note: Pricing details may not have expanded: {str(e)}"
            )

        # Take screenshot of the final pricing details
        screenshot_pricing = self.page.locator(".main-area-container").first
        await self.optimize_screenshot(
            screenshot_pricing, f"pricing_details_{timestamp}.png", cabin_number, quality=50
        )
