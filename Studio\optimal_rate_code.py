"""
Optimal Rate Selector module for selecting the best cruise rate based on various strategies.

This module provides functionality to evaluate and select optimal cruise rates based on
different strategies such as "Cheapest", "Value Sails", and others. It analyzes rate
information like price, refundability, and perks to make an informed selection.
"""

from typing import List, Dict, Optional, Union, Any
from loguru import logger
import re


class OptimalRateSelector:
    """
    A class that implements various strategies for selecting optimal cruise rates.
    
    This class provides static methods to analyze cruise rate options and select
    the most suitable rate based on the specified strategy. Strategies include 
    selecting the cheapest rate, finding Value Sails rates, or identifying rates
    with specific perks or refundability features.
    """

    @staticmethod
    def select_rate_index(table_data: List[Dict], rate_selection_strategy: str) -> int:
        """
        Select the index of the optimal rate based on the specified strategy.
        
        This is the main entry point for rate selection. It routes to different 
        specialized selection methods based on the strategy name.
        
        Args:
            table_data (List[Dict]): List of dictionaries containing rate information
                                    Each dict should contain at least 'rate' and 'price' keys
            rate_selection_strategy (str): The strategy to use for selection
                                          (e.g., "Cheapest", "Value Sails", etc.)
            
        Returns:
            int: The index of the selected rate in the table_data list
            
        Note:
            If strategy is unknown or no rates are available, returns 0 (first rate)
        """
        # Check if we have any rate data to work with
        if not table_data:
            logger.warning("No rate data available")
            return 0

        logger.info(f"Using strategy: {rate_selection_strategy}")

        # Select appropriate strategy based on the strategy string
        if rate_selection_strategy == "Cheapest" or not rate_selection_strategy:
            # For "Cheapest" strategy or undefined strategy, simply select the first rate
            # (assuming rates are pre-sorted by price)
            logger.info("Selecting first available rate (cheapest)")
            return 0

        elif rate_selection_strategy == "Value Sails":
            # Find the best Value Sails rate (may consider refundability if price is the same)
            return OptimalRateSelector._select_value_sails_rate(table_data)

        elif rate_selection_strategy == "Value Sails (Refundable)":
            # Specifically find a refundable Value Sails rate
            return OptimalRateSelector._select_value_sails_refundable_rate(table_data)

        elif rate_selection_strategy == "Value Sails (Perk)":
            # Find a Value Sails rate that includes perks
            return OptimalRateSelector._select_value_sails_perk_rate(table_data)

        elif rate_selection_strategy == "USail":
            # Find the best USail rate (may consider refundability if price is the same)
            return OptimalRateSelector._select_usail_rate(table_data)

        elif rate_selection_strategy == "USail (Refundable)":
            # Specifically find a refundable USail rate
            return OptimalRateSelector._select_usail_refundable_rate(table_data)

        elif rate_selection_strategy == "USail (Perk)":
            # Find a USail rate that includes perks
            return OptimalRateSelector._select_usail_perk_rate(table_data)

        # If strategy is unknown, default to cheapest rate (index 0)
        logger.warning(
            f"Unknown strategy '{rate_selection_strategy}' - using cheapest rate"
        )
        return 0

    @staticmethod
    def is_value_sails_rate(rate_name: str) -> bool:
        """
        Determine if a rate is a Value Sails rate based on its name.
        
        Args:
            rate_name (str): The name of the rate to check
            
        Returns:
            bool: True if the rate is a Value Sails rate, False otherwise
        """
        # Convert to uppercase for case-insensitive comparison
        rate_name = rate_name.upper()

        # Check if rate name contains "VALUE SAIL"
        if "VALUE SAIL" in rate_name:
            return True

        return False

    @staticmethod
    def is_usail_rate(rate_name: str) -> bool:
        """
        Determine if a rate is a USail rate based on its name.
        
        Args:
            rate_name (str): The name of the rate to check
            
        Returns:
            bool: True if the rate is a USail rate, False otherwise
        """
        # Convert to uppercase for case-insensitive comparison
        rate_name = rate_name.upper()

        # Check if rate name contains "USAIL"
        if "USAIL" in rate_name:
            return True

        return False

    @staticmethod
    def _rate_has_perks(rate_name: str) -> bool:
        """
        Determine if a rate includes perks based on its name.
        
        This method checks for specific indicators in the rate name that
        suggest it includes extra perks like drink packages, dining, etc.
        
        Args:
            rate_name (str): The name of the rate to check
            
        Returns:
            bool: True if the rate includes perks, False otherwise
        """
        # Convert to uppercase for case-insensitive comparison
        rate_name = rate_name.upper()

        # List of keywords that indicate a rate includes perks
        perk_indicators = ["ALL IN", "PPLUS", "HAVE IT ALL", "PREMIER", "ENJOY IT ALL"]
        if any(perk in rate_name for perk in perk_indicators):
            return True

        return False

    @staticmethod
    def _parse_price(price_str: str) -> float:
        """
        Parse a price string to a float value for comparison.
        
        This method handles cleaning price strings by removing currency 
        symbols and commas.
        
        Args:
            price_str (str): The price string to parse (e.g., "$1,299.99")
            
        Returns:
            float: The parsed price as a float value
            
        Note:
            Returns 999999.0 as a fallback if parsing fails
        """
        try:
            # Remove currency symbol and thousand separators
            clean_price = price_str.replace('$', '').replace(',', '')
            return float(clean_price)
        except (ValueError, TypeError):
            logger.warning(f"Invalid price format: {price_str}")
            # Return a very high price as fallback (ensures invalid prices aren't selected)
            return 999999.0

    @classmethod
    def _select_value_sails_rate(cls, table_data: List[Dict]) -> int:
        """
        Select the optimal Value Sails rate from available options.
        
        This method identifies all Value Sails rates, categorizes them as refundable
        or non-refundable, and selects the best option based on price and refundability.
        
        Args:
            table_data (List[Dict]): List of dictionaries containing rate information
            
        Returns:
            int: The index of the selected Value Sails rate in the table_data list
            
        Note:
            If no Value Sails rates are available, returns 0 (first/cheapest rate)
        """
        # Track indices of Value Sails rates (all, refundable, and non-refundable)
        value_sails_indices = []
        refundable_indices = []
        nonrefundable_indices = []

        # Identify and categorize all Value Sails rates
        for idx, rate_info in enumerate(table_data):
            rate_name = rate_info.get('rate', '').upper()
            promotions = rate_info.get('promotions', '').upper()
            full_text = f"{rate_name} {promotions}".upper()

            # Check if this is a Value Sails rate
            if cls.is_value_sails_rate(rate_name):
                value_sails_indices.append(idx)

                # Determine if rate is refundable or non-refundable
                if "NON-REFUNDABLE" in full_text or "NON REFUNDABLE" in full_text or "NRD" in full_text:
                    nonrefundable_indices.append(idx)
                    logger.info(f"Found non-refundable Value Sails rate: {rate_name}")
                else:
                    refundable_indices.append(idx)
                    logger.info(f"Found refundable Value Sails rate: {rate_name}")

        # If no Value Sails rates found, default to first rate
        if not value_sails_indices:
            logger.info("No Value Sails rates found - using cheapest rate")
            return 0

        # Special case: If we have both refundable and non-refundable options at the same price
        if refundable_indices and nonrefundable_indices:
            # Check if all Value Sails rates have the same price
            same_price = True
            first_price = cls._parse_price(
                table_data[value_sails_indices[0]].get('price', '999999')
            )

            # Compare prices of all Value Sails rates
            for idx in value_sails_indices[1:]:
                price = cls._parse_price(table_data[idx].get('price', '999999'))
                if price != first_price:
                    same_price = False
                    break

            # If all rates have same price, prefer the refundable option
            if same_price:
                logger.info(
                    "All Value Sails rates have same price - selecting refundable option"
                )
                return refundable_indices[0]

        # Find the cheapest Value Sails rate
        return cls._select_cheapest_from_indices(table_data, value_sails_indices)

    @classmethod
    def _select_value_sails_refundable_rate(cls, table_data: List[Dict]) -> int:
        """
        Select a refundable Value Sails rate from available options.
        
        This method identifies all refundable Value Sails rates and selects
        the cheapest one, or defaults to the cheapest rate if none are found.
        
        Args:
            table_data (List[Dict]): List of dictionaries containing rate information
            
        Returns:
            int: The index of the selected refundable Value Sails rate
            
        Note:
            If no refundable Value Sails rates are available, returns 0 (first/cheapest rate)
        """
        # Track indices of refundable Value Sails rates
        refundable_indices = []

        # Identify all refundable Value Sails rates
        for idx, rate_info in enumerate(table_data):
            rate_name = rate_info.get('rate', '').upper()
            promotions = rate_info.get('promotions', '').upper()
            full_text = f"{rate_name} {promotions}".upper()

            # Must be a Value Sails rate without non-refundable indicators
            if cls.is_value_sails_rate(rate_name):
                # Check that none of the non-refundable indicators are present
                if "NON-REFUNDABLE" not in full_text and "NON REFUNDABLE" not in full_text and "NRD" not in full_text:
                    refundable_indices.append(idx)
                    logger.info(f"Found refundable Value Sails rate: {rate_name}")

        # If no refundable Value Sails rates found, default to first rate
        if not refundable_indices:
            logger.info("No refundable Value Sails rates found - using cheapest rate")
            return 0

        # If multiple refundable options, select the cheapest one
        if len(refundable_indices) > 1:
            return cls._select_cheapest_from_indices(table_data, refundable_indices)
        else:
            # If only one refundable option, return it directly
            return refundable_indices[0]

    @classmethod
    def _select_value_sails_perk_rate(cls, table_data: List[Dict]) -> int:
        """
        Select a Value Sails rate that includes perks from available options.
        
        This method looks for specific Value Sails rates that include additional
        perks like drink packages, dining, etc., with preferences for specific
        cruise lines' perk packages.
        
        Args:
            table_data (List[Dict]): List of dictionaries containing rate information
            
        Returns:
            int: The index of the selected Value Sails perk rate
            
        Note:
            If no Value Sails perk rates are available, returns 0 (first/cheapest rate)
        """
        # Look for specific perk rates from different cruise lines in order of preference
        for idx, rate_info in enumerate(table_data):
            rate_name = rate_info.get('rate', '').upper()

            # Check for Celebrity's "Value Sail All In" package
            if "VALUE SAIL ALL IN" in rate_name:
                logger.info("Found Celebrity 'Value Sail All In' perk rate")
                return idx

            # Check for Princess's "Value Sails PPLUS" package
            if "VALUE SAILS PPLUS" in rate_name:
                logger.info("Found Princess 'Value Sails PPLUS' perk rate")
                return idx

            # Check for Holland America's "HAVE IT ALL" package
            if "HAVE IT ALL" in rate_name:
                logger.info("Found Holland 'HAVE IT ALL' perk rate")
                return idx

        # If no specific perk rates found, default to first rate
        logger.info("No Value Sails perk rates found - using cheapest rate")
        return 0

    @classmethod
    def _select_cheapest_from_indices(
        cls, table_data: List[Dict], indices: List[int]
    ) -> int:
        """
        Select the cheapest rate from a subset of rates identified by indices.
        
        This method compares prices of rates at specified indices and selects
        the cheapest option. In case of tie (same price), it prefers refundable
        rates and rates with perks.
        
        Args:
            table_data (List[Dict]): List of dictionaries containing rate information
            indices (List[int]): List of indices to consider for selection
            
        Returns:
            int: The index of the cheapest rate among the specified indices
            
        Note:
            If indices list is empty, returns 0 (first rate)
        """
        # If no indices provided, return first rate
        if not indices:
            return 0

        # If only one option, return it directly
        if len(indices) == 1:
            return indices[0]

        # Start with first rate in indices as the baseline best rate
        best_index = indices[0]
        best_price = cls._parse_price(table_data[best_index].get('price', '999999'))
        best_rate_name = table_data[best_index].get('rate', '').upper()
        best_promotions = table_data[best_index].get('promotions', '').upper()

        # Combine rate name and promotions for full text search
        best_full_text = f"{best_rate_name} {best_promotions}".upper()

        # Determine if best rate is non-refundable
        best_is_nonrefundable = (
            "NON-REFUNDABLE" in best_full_text or "NON REFUNDABLE" in best_full_text
            or "NRD" in best_full_text
        )

        logger.info(f"Starting with rate: {best_rate_name} (${best_price})")

        # Compare with all other rates in the indices
        for idx in indices[1:]:
            rate_info = table_data[idx]
            rate_name = rate_info.get('rate', '').upper()
            promotions = rate_info.get('promotions', '').upper()
            price = cls._parse_price(rate_info.get('price', '999999'))

            full_text = f"{rate_name} {promotions}".upper()

            # Determine if this rate is non-refundable
            is_nonrefundable = (
                "NON-REFUNDABLE" in full_text or "NON REFUNDABLE" in full_text
                or "NRD" in full_text
            )

            logger.info(f"Comparing with: {rate_name} (${price})")

            # Case 1: New rate is cheaper - select it
            if price < best_price:
                logger.info(f"Selected cheaper rate: {rate_name}")
                best_index = idx
                best_price = price
                best_rate_name = rate_name
                best_promotions = promotions
                best_full_text = full_text
                best_is_nonrefundable = is_nonrefundable

            # Case 2: Same price - consider refundability and perks
            elif price == best_price:
                # If current best is non-refundable but new option is refundable, prefer refundable
                if best_is_nonrefundable and not is_nonrefundable:
                    logger.info(f"Selected refundable rate at same price: {rate_name}")
                    best_index = idx
                    best_rate_name = rate_name
                    best_promotions = promotions
                    best_full_text = full_text
                    best_is_nonrefundable = is_nonrefundable

                # If both have same refundability status, consider perks
                elif best_is_nonrefundable == is_nonrefundable:
                    # If new rate has perks but current best doesn't, prefer the one with perks
                    if cls._rate_has_perks(
                            rate_name) and not cls._rate_has_perks(best_rate_name):
                        logger.info(
                            f"Selected rate with perks at same price: {rate_name}"
                        )
                        best_index = idx
                        best_rate_name = rate_name
                        best_promotions = promotions
                        best_full_text = full_text
                        best_is_nonrefundable = is_nonrefundable

        logger.info(f"Final selection: {best_rate_name} (${best_price})")
        return best_index

    @classmethod
    def _select_usail_rate(cls, table_data: List[Dict]) -> int:
        """
        Select the optimal USail rate from available options.
        
        This method identifies all USail rates, categorizes them as refundable
        or non-refundable, and selects the best option based on price and refundability.
        
        Args:
            table_data (List[Dict]): List of dictionaries containing rate information
            
        Returns:
            int: The index of the selected USail rate in the table_data list
            
        Note:
            If no USail rates are available, returns 0 (first/cheapest rate)
        """
        # Track indices of USail rates (all, refundable, and non-refundable)
        usail_indices = []
        refundable_indices = []
        nonrefundable_indices = []

        # Identify and categorize all USail rates
        for idx, rate_info in enumerate(table_data):
            rate_name = rate_info.get('rate', '').upper()
            promotions = rate_info.get('promotions', '').upper()
            full_text = f"{rate_name} {promotions}".upper()

            # Check if this is a USail rate
            if cls.is_usail_rate(rate_name):
                usail_indices.append(idx)

                # Determine if rate is refundable or non-refundable
                if "NON-REFUNDABLE" in full_text or "NON REFUNDABLE" in full_text or "NRD" in full_text:
                    nonrefundable_indices.append(idx)
                    # Clean the rate name for logging
                    cleaned_name = cls._clean_usail_rate_name(rate_name)
                    logger.info(f"Found non-refundable USail rate: {cleaned_name}")
                else:
                    refundable_indices.append(idx)
                    # Clean the rate name for logging
                    cleaned_name = cls._clean_usail_rate_name(rate_name)
                    logger.info(f"Found refundable USail rate: {cleaned_name}")

        # If no USail rates found, default to first rate
        if not usail_indices:
            logger.info("No USail rates found - using cheapest rate")
            return 0

        # Special case: If we have both refundable and non-refundable options at the same price
        if refundable_indices and nonrefundable_indices:
            # Check if all USail rates have the same price
            same_price = True
            first_raw_price = table_data[usail_indices[0]].get('price', '999999')
            first_cleaned_price = cls._clean_usail_price(first_raw_price)
            first_price = cls._parse_price(first_cleaned_price)

            # Compare prices of all USail rates
            for idx in usail_indices[1:]:
                raw_price = table_data[idx].get('price', '999999')
                cleaned_price = cls._clean_usail_price(raw_price)
                price = cls._parse_price(cleaned_price)
                if price != first_price:
                    same_price = False
                    break

            # If all rates have same price, prefer the refundable option
            if same_price:
                logger.info(
                    "All USail rates have same price - selecting refundable option"
                )
                return refundable_indices[0]

        # Find the cheapest USail rate
        return cls._select_cheapest_usail_from_indices(table_data, usail_indices)

    @classmethod
    def _select_usail_refundable_rate(cls, table_data: List[Dict]) -> int:
        """
        Select a refundable USail rate from available options.
        
        This method identifies all refundable USail rates and selects
        the cheapest one, or defaults to the cheapest rate if none are found.
        
        Args:
            table_data (List[Dict]): List of dictionaries containing rate information
            
        Returns:
            int: The index of the selected refundable USail rate
            
        Note:
            If no refundable USail rates are available, returns 0 (first/cheapest rate)
        """
        # Track indices of refundable USail rates
        refundable_indices = []

        # Identify all refundable USail rates
        for idx, rate_info in enumerate(table_data):
            rate_name = rate_info.get('rate', '').upper()
            promotions = rate_info.get('promotions', '').upper()
            full_text = f"{rate_name} {promotions}".upper()

            # Must be a USail rate without non-refundable indicators
            if cls.is_usail_rate(rate_name):
                # Check that none of the non-refundable indicators are present
                if "NON-REFUNDABLE" not in full_text and "NON REFUNDABLE" not in full_text and "NRD" not in full_text:
                    refundable_indices.append(idx)
                    # Clean the rate name for logging
                    cleaned_name = cls._clean_usail_rate_name(rate_name)
                    logger.info(f"Found refundable USail rate: {cleaned_name}")

        # If no refundable USail rates found, default to first rate
        if not refundable_indices:
            logger.info("No refundable USail rates found - using cheapest rate")
            return 0

        # If multiple refundable options, select the cheapest one
        if len(refundable_indices) > 1:
            return cls._select_cheapest_usail_from_indices(table_data, refundable_indices)
        else:
            # If only one refundable option, return it directly
            return refundable_indices[0]

    @classmethod
    def _select_usail_perk_rate(cls, table_data: List[Dict]) -> int:
        """
        Select a USail rate that includes perks from available options.
        
        This method looks for specific USail rates that include additional
        perks like drink packages, dining, etc., with preferences for specific
        cruise lines' perk packages.
        
        Args:
            table_data (List[Dict]): List of dictionaries containing rate information
            
        Returns:
            int: The index of the selected USail perk rate
            
        Note:
            If no USail perk rates are available, returns 0 (first/cheapest rate)
        """
        # Look for specific perk rates from different cruise lines in order of preference
        for idx, rate_info in enumerate(table_data):
            rate_name = rate_info.get('rate', '').upper()

            # Check for Celebrity's "USail All In" package
            if "USAIL ALL IN" in rate_name:
                logger.info("Found Celebrity 'USail All In' perk rate")
                return idx

            # Check for Princess's "USail PPLUS" package
            if "USAIL PPLUS" in rate_name:
                logger.info("Found Princess 'USail PPLUS' perk rate")
                return idx

            if "ENJOY IT ALL" in rate_name:
                logger.info("Found Cunard 'ENJOY IT ALL' perk rate")
                return idx
            
        # If no specific perk rates found, default to first rate
        logger.info("No USail perk rates found - using cheapest rate")
        return 0

    @staticmethod
    def _clean_usail_rate_name(rate_name: str) -> str:
        """
        Clean USail rate names by removing extra content like button text and notices.
        
        Args:
            rate_name (str): The raw rate name that may contain extra content
            
        Returns:
            str: The cleaned rate name
        """
        if not rate_name:
            return rate_name
            
        # Convert to upper case for processing
        cleaned_name = rate_name.upper().strip()
        
        # Remove common unwanted content
        unwanted_phrases = [
            "GLOBAL.LEARN_MORE",
            "TAXES AND FEES",
            "PORT CHARGES",
            "INCLUDED",
            "USD"
        ]
        
        # Split by common delimiters and keep only the first meaningful part
        lines = cleaned_name.split('\n')
        
        # Find the first line that contains "USAIL" and use that
        for line in lines:
            line = line.strip()
            if "USAIL" in line:
                # Remove any unwanted phrases from this line
                for phrase in unwanted_phrases:
                    if phrase in line:
                        # Split at the phrase and take the part before it
                        line = line.split(phrase)[0].strip()
                        break
                return line
        
        # Fallback: return the first non-empty line
        for line in lines:
            line = line.strip()
            if line:
                return line
                
        return cleaned_name

    @staticmethod
    def _clean_usail_price(price_str: str) -> str:
        """
        Clean USail price strings by extracting only the main price value.
        
        Args:
            price_str (str): The raw price string that may contain extra content
            
        Returns:
            str: The cleaned price string
        """
        if not price_str:
            return price_str
            
        # Split by lines and look for the main price (not "per day" price)
        lines = price_str.split('\n')
        
        for line in lines:
            line = line.strip()
            # Look for a line that starts with $ and doesn't contain "per day"
            if line.startswith('$') and 'per day' not in line.lower():
                # Extract just the price part (remove currency indicators)
                # Match pattern like $1,234 or $1234.56
                price_match = re.search(r'\$[\d,]+(?:\.\d{2})?', line)
                if price_match:
                    return price_match.group(0)
        
        # Fallback: try to find any price pattern
        price_match = re.search(r'\$[\d,]+(?:\.\d{2})?', price_str)
        if price_match:
            return price_match.group(0)
            
        return price_str

    @classmethod
    def _select_cheapest_usail_from_indices(
        cls, table_data: List[Dict], indices: List[int]
    ) -> int:
        """
        Select the cheapest USail rate from a subset of rates identified by indices.
        
        This method compares prices of USail rates at specified indices and selects
        the cheapest option. Uses USail-specific cleaning for proper price parsing.
        
        Args:
            table_data (List[Dict]): List of dictionaries containing rate information
            indices (List[int]): List of indices to consider for selection
            
        Returns:
            int: The index of the cheapest USail rate among the specified indices
            
        Note:
            If indices list is empty, returns 0 (first rate)
        """
        # If no indices provided, return first rate
        if not indices:
            return 0

        # If only one option, return it directly
        if len(indices) == 1:
            return indices[0]

        # Start with first rate in indices as the baseline best rate
        best_index = indices[0]
        best_rate_info = table_data[best_index]
        best_raw_price = best_rate_info.get('price', '999999')
        best_cleaned_price = cls._clean_usail_price(best_raw_price)
        best_price = cls._parse_price(best_cleaned_price)
        best_rate_name = cls._clean_usail_rate_name(best_rate_info.get('rate', ''))
        best_promotions = best_rate_info.get('promotions', '').upper()

        # Combine rate name and promotions for full text search
        best_full_text = f"{best_rate_info.get('rate', '')} {best_promotions}".upper()

        # Determine if best rate is non-refundable
        best_is_nonrefundable = (
            "NON-REFUNDABLE" in best_full_text or "NON REFUNDABLE" in best_full_text
            or "NRD" in best_full_text
        )

        logger.info(f"Starting with rate: {best_rate_name} (${best_price})")

        # Compare with all other rates in the indices
        for idx in indices[1:]:
            rate_info = table_data[idx]
            raw_rate_name = rate_info.get('rate', '')
            rate_name = cls._clean_usail_rate_name(raw_rate_name)
            promotions = rate_info.get('promotions', '').upper()
            raw_price = rate_info.get('price', '999999')
            cleaned_price = cls._clean_usail_price(raw_price)
            price = cls._parse_price(cleaned_price)

            full_text = f"{raw_rate_name} {promotions}".upper()

            # Determine if this rate is non-refundable
            is_nonrefundable = (
                "NON-REFUNDABLE" in full_text or "NON REFUNDABLE" in full_text
                or "NRD" in full_text
            )

            logger.info(f"Comparing with: {rate_name} (${price})")

            # Case 1: New rate is cheaper - select it
            if price < best_price:
                logger.info(f"Selected cheaper rate: {rate_name}")
                best_index = idx
                best_price = price
                best_rate_name = rate_name
                best_promotions = promotions
                best_full_text = full_text
                best_is_nonrefundable = is_nonrefundable

            # Case 2: Same price - consider refundability and perks
            elif price == best_price:
                # If current best is non-refundable but new option is refundable, prefer refundable
                if best_is_nonrefundable and not is_nonrefundable:
                    logger.info(f"Selected refundable rate at same price: {rate_name}")
                    best_index = idx
                    best_rate_name = rate_name
                    best_promotions = promotions
                    best_full_text = full_text
                    best_is_nonrefundable = is_nonrefundable

                # If both have same refundability status, consider perks
                elif best_is_nonrefundable == is_nonrefundable:
                    # If new rate has perks but current best doesn't, prefer the one with perks
                    if cls._rate_has_perks(raw_rate_name) and not cls._rate_has_perks(
                        table_data[best_index].get('rate', '')
                    ):
                        logger.info(f"Selected rate with perks at same price: {rate_name}")
                        best_index = idx
                        best_rate_name = rate_name
                        best_promotions = promotions
                        best_full_text = full_text
                        best_is_nonrefundable = is_nonrefundable

        logger.info(f"Final selection: {best_rate_name} (${best_price})")
        return best_index
