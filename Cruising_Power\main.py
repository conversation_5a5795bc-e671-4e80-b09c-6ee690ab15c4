import sys
import os
import datetime
import asyncio
from loguru import logger
from Cruising_Power.config import setup_logger, get_cabin_logger
from Cruising_Power.extraction import extract_cruise_info, normalize_cabin_category
from Cruising_Power.search import search_cruise
from Cruising_Power.screenshot_utils import take_scrolling_screenshot
from playwright.async_api import expect
from Cruising_Power.login import my_username, my_password, login_to_cruising_power
from Cruising_Power.select_cabin import select_cheapest_cabin, select_cheapest_cabin_js
from Cruising_Power.price_quote import get_price_quote, get_detailed_pricing
from Core.browser_setup import AsyncBrowserSetup
from Core.database_helper import save_cruising_power_results


class CabinProcessor:
    """
    Class to process individual cabin searches
    """
    def __init__(self, page, playwright, browser, context, info_dict, cabin, log_dir=None, cabin_index=0, onboard_percentage=12, session_id=None):
        """
        Initialize the cabin processor
        
        Args:
            page: Playwright Page instance
            playwright: Playwright instance
            browser: Browser instance
            context: BrowserContext instance
            info_dict: Dictionary containing structured cruise information
            cabin: Dictionary containing cabin and passenger information
            log_dir: Directory to save logs (optional)
            cabin_index: Index of the cabin for logging
            onboard_percentage: Percentage to use when calculating onboard credit (default: 12)
            session_id: Session ID for consistent database tracking
        """
        self.page = page
        self.playwright = playwright
        self.browser = browser
        self.context = context
        self.info_dict = info_dict
        self.cabin = cabin
        self.log_dir = log_dir
        self.cabin_index = cabin_index
        self.onboard_percentage = onboard_percentage
        self.session_id = session_id
        self.cabin_log_dir = log_dir
        
    async def process(self):
        """
        Process the cabin search
        
        Returns:
            Boolean indicating success or failure
        """
        try:
            # Create a cabin-specific info_dict with just this cabin's passenger info
            cabin_info = self.info_dict.copy()
            cabin_info.update({
                "cabin_type": self.cabin["normalized"],
                "adults": self.cabin["assigned_adults"],
                "children": self.cabin["assigned_children"],
                "seniors": self.cabin["assigned_seniors"],
                "total_passengers": self.cabin["assigned_adults"] + self.cabin["assigned_children"] + self.cabin["assigned_seniors"],
                "session_id": self.session_id,  # Pass session_id to functions called from here
                "cabin_id": self.cabin_index + 1  # Set cabin_id to the cabin_number
            })
            
            logger.info(f"{'=' * 50}")
            logger.info(f"PROCESSING CABIN {self.cabin_index+1}: {self.cabin['type']} with {cabin_info['total_passengers']} passengers")
            logger.info(f"{'=' * 50}")
            
            # Prepare cabin result dict for JSON output
            cabin_result = {
                "cabin_number": self.cabin_index + 1,
                "cabin_type": self.cabin['type'],
                "normalized_type": self.cabin['normalized'],
                "passengers": {
                    "adults": self.cabin["assigned_adults"],
                    "children": self.cabin["assigned_children"],
                    "seniors": self.cabin["assigned_seniors"],
                    "total": cabin_info['total_passengers']
                },
                "timestamp": datetime.datetime.now().isoformat(),
                "success": False,
                "request_id": self.info_dict.get('request_id'),  # Ensure request_id is set in cabin_result
                "provider": "Cruising Power",  # Explicitly set the provider
                "session_id": self.session_id  # Pass session_id to functions called from here
            }
            
            # Perform search using the cabin-specific information dictionary
            search_result = await search_cruise(self.page, cabin_info, self.onboard_percentage, self.cabin_log_dir)
            
            if search_result:
                logger.info(f"\nCabin {self.cabin_index+1} search completed successfully!")
                cabin_result["success"] = True
                
                # Capture cabin allocation information if available
                if 'cabin_allocation' in cabin_info and cabin_info['cabin_allocation']:
                    logger.info(f"\nCabin {self.cabin_index+1} Allocation Details:")
                    logger.info(cabin_info['cabin_allocation'])
                    cabin_result["cabin_allocation"] = cabin_info['cabin_allocation']
                
                # Capture total price if available
                if 'total_price' in cabin_info and cabin_info['total_price']:
                    logger.info(f"\nCabin {self.cabin_index+1} Total Price: {cabin_info['total_price']}")
                    cabin_result["total_price"] = cabin_info['total_price']
                    
                    # Also display onboard credit if available
                    if 'onboard_credit' in cabin_info and cabin_info['onboard_credit']:
                        logger.info(f"Cabin {self.cabin_index+1} Onboard Credit: {cabin_info['onboard_credit']}")
                        cabin_result["onboard_credit"] = cabin_info['onboard_credit']
                
                # Capture additional pricing details if available
                pricing_keys = [key for key in cabin_info.keys() if key.startswith('pricing_')]
                if pricing_keys:
                    logger.info(f"\nCabin {self.cabin_index+1} Detailed Pricing Information:")
                    pricing_details = {}
                    
                    for key in pricing_keys:
                        display_key = key.replace('pricing_', '').replace('_', ' ').title()
                        value = cabin_info[key]
                        if isinstance(value, list):
                            logger.info(f"  {display_key}:")
                            for item in value:
                                logger.info(f"    - {item}")
                        else:
                            logger.info(f"  {display_key}: {value}")
                        
                        # Store in result dictionary
                        pricing_details[display_key] = value
                    
                    cabin_result["pricing_details"] = pricing_details
                
                # Capture promotions/rate codes if available
                if 'selected_rate_codes' in cabin_info:
                    cabin_result["rate_codes"] = cabin_info['selected_rate_codes']
                
                # Append cabin result to the instance-scoped results container
                # Parent `CruisingPower` instance will collect these
                # (caller should attach cabin_result to its own results dict)
                return cabin_result
            else:
                logger.info(f"\nCabin {self.cabin_index+1} search failed. Please check the error messages.")
                cabin_result["success"] = False
                cabin_result["error"] = "Search process failed"
                
                # Append cabin result to the instance-scoped results container
                # Parent `CruisingPower` instance will collect these
                # (caller should attach cabin_result to its own results dict)
                return cabin_result
                
        except Exception as e:
            logger.error(f"An error occurred during cabin {self.cabin_index+1} processing: {e}")
            
            # Add error to cabin result
            cabin_result = {
                "cabin_number": self.cabin_index + 1,
                "cabin_type": self.cabin['type'],
                "normalized_type": self.cabin['normalized'],
                "success": False,
                "error": str(e),
                "request_id": self.info_dict.get('request_id'),  # Ensure request_id is set in error case too
                "provider": "Cruising Power",  # Explicitly set the provider
                "session_id": self.session_id  # Pass session_id to functions called from here
            }
            return cabin_result


class CruisingPower:
    """
    Main class to coordinate the extraction, login, and search processes
    """
    def __init__(self, config=None, cp_details=None):
        """
        Initialize the CruisingPower processor
        
        Args:
            config: Configuration options
            cp_details: Pre-extracted cruise details
        """
        self.config = config or {}
        self.cp_details = cp_details
        
        # Reset tracking mechanisms to ensure clean state for this booking process
        try:
            from db.cruising_power_provider import CPResultsCache
            CPResultsCache.reset()
            logger.info("Reset CPResultsCache tracking to ensure clean state")
        except ImportError:
            logger.debug("CPResultsCache not available - skipping reset")
            
        # Reset processing results
        from Cruising_Power.config import reset_processing_results
        reset_processing_results()
        
        # Extract request_id from config
        self.request_id = self.config.get('request_id', f"cp_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}")
        # Extract session_id from config
        self.session_id = self.config.get('session_id')
        
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # Set up logger
        self.logger, self.log_dir = setup_logger()
        # Get onboard credit percentage from config
        self.onboard_percentage = 12  # Default value
        if self.config and 'cruising_power_onboard_percentage' in self.config:
            self.onboard_percentage = self.config['cruising_power_onboard_percentage']
        # Other instance variables
        self.start_time = None
        self.end_time = None
        self.cabin_results = {}
        self.info_dict = None
        self.total_cabins = 0
        self.cabins_with_passengers = []
        
        # Initialize instance-scoped results container
        # Will collect all cabin results and metadata per booking run
        self.results = {
            "cabins": [],
            "timestamp": None,
            "overall_status": False,
            "provider": "Cruising Power",
            "request_id": self.config.get('request_id'),
            "session_id": self.config.get('session_id')
        }
        
    def _get_cruise_information(self):
        """
        Get cruise information either from cp_details or by extraction
        
        Returns:
            Boolean indicating success or failure
        """
        try:
            # If cp_details is provided, use it directly
            if self.cp_details:
                self.info_dict = self.cp_details
                if not self.info_dict:
                    self.logger.error("Invalid CP details provided")
                    return False
                
                self.logger.info(f"Using provided CP details: {self.info_dict}")
            else:
                # Extract cruise information from file
                self.logger.info("Extracting cruise information from file...")
                self.info_dict = extract_cruise_info()
                if not self.info_dict:
                    self.logger.error("Failed to extract cruise information")
                    return False
                
                self.logger.info(f"Extracted cruise information: {self.info_dict}")
            
            # Add request_id to info_dict - this is critical for file naming and tracking
            self.info_dict['request_id'] = self.request_id
            
            # Process cabins and allocate passengers
            self._allocate_passengers_to_cabins()
            
            return True
        except Exception as e:
            self.logger.error(f"Error getting cruise information: {e}")
            return False
        
    def _allocate_passengers_to_cabins(self):
        """
        Allocate passengers to cabins based on the extracted information
        """
        try:
            # Get the total number of cabins
            # Fix: Get total cabins from different sources with proper fallbacks
            if 'total_cabins' in self.info_dict:
                self.total_cabins = int(self.info_dict.get('total_cabins'))
            elif 'cabin_count' in self.info_dict:
                self.total_cabins = int(self.info_dict.get('cabin_count'))
            elif 'cabin_categories' in self.info_dict and isinstance(self.info_dict['cabin_categories'], list):
                self.total_cabins = len(self.info_dict['cabin_categories'])
            elif 'cabins_with_passengers' in self.info_dict and isinstance(self.info_dict['cabins_with_passengers'], list):
                self.total_cabins = len(self.info_dict['cabins_with_passengers'])
            else:
                self.total_cabins = 1  # Default fallback
                
            self.logger.info(f"Allocating passengers to {self.total_cabins} cabins...")
            
            # Get passenger counts
            adults = int(self.info_dict.get('adults', 0))
            children = int(self.info_dict.get('children', 0))
            seniors = int(self.info_dict.get('seniors', 0))
            
            # See if cabins are already pre-allocated with passengers
            if 'cabins_with_passengers' in self.info_dict and isinstance(self.info_dict['cabins_with_passengers'], list):
                self.cabins_with_passengers = self.info_dict['cabins_with_passengers']
                self.logger.info(f"Using pre-allocated cabins: {len(self.cabins_with_passengers)} cabins")
                
                # Identify similar cabins for optimization
                self._identify_similar_cabins()
                return
            
            # Normalize the cabin types
            raw_cabin_types = []
            
            # Check different possible keys for cabin types
            if 'cabin_categories' in self.info_dict and isinstance(self.info_dict['cabin_categories'], list):
                for cabin in self.info_dict['cabin_categories']:
                    if isinstance(cabin, dict) and 'type' in cabin:
                        raw_cabin_types.append(cabin['type'])
            elif 'cabin_types' in self.info_dict:
                raw_cabin_types = self.info_dict.get('cabin_types', [])
            
            if isinstance(raw_cabin_types, str):
                raw_cabin_types = [raw_cabin_types]
                
            # If no cabin types specified, use cabin_type if available
            if not raw_cabin_types and 'cabin_type' in self.info_dict:
                raw_cabin_types = [self.info_dict['cabin_type']]
                
            # If still no cabin types, default to Balcony
            if not raw_cabin_types:
                raw_cabin_types = ["Balcony"]
            
            # Create the cabins list with normalized categories
            self.cabins_with_passengers = []
            for i in range(len(raw_cabin_types[:self.total_cabins])):
                cabin_type = raw_cabin_types[i] if i < len(raw_cabin_types) else raw_cabin_types[-1]
                normalized_type = normalize_cabin_category(cabin_type)
                
                self.cabins_with_passengers.append({
                    "type": cabin_type,
                    "normalized": normalized_type,
                    "assigned_adults": 0,
                    "assigned_children": 0,
                    "assigned_seniors": 0
                })
            
            # If we have more cabins than specified types, duplicate the last type
            while len(self.cabins_with_passengers) < self.total_cabins:
                cabin_type = raw_cabin_types[-1] if raw_cabin_types else "Balcony"
                normalized_type = normalize_cabin_category(cabin_type)
                
                self.cabins_with_passengers.append({
                    "type": cabin_type,
                    "normalized": normalized_type,
                    "assigned_adults": 0,
                    "assigned_children": 0,
                    "assigned_seniors": 0
                })
            
            # Allocate passengers to cabins - simple round-robin approach
            current_cabin = 0
            
            # First ensure at least one adult per cabin
            for _ in range(min(adults, self.total_cabins)):
                self.cabins_with_passengers[current_cabin]["assigned_adults"] += 1
                current_cabin = (current_cabin + 1) % self.total_cabins
                adults -= 1
            
            # Then allocate remaining adults
            while adults > 0:
                self.cabins_with_passengers[current_cabin]["assigned_adults"] += 1
                current_cabin = (current_cabin + 1) % self.total_cabins
                adults -= 1
            
            # Then allocate children
            current_cabin = 0
            while children > 0:
                self.cabins_with_passengers[current_cabin]["assigned_children"] += 1
                current_cabin = (current_cabin + 1) % self.total_cabins
                children -= 1
            
            # Then allocate seniors
            current_cabin = 0
            while seniors > 0:
                self.cabins_with_passengers[current_cabin]["assigned_seniors"] += 1
                current_cabin = (current_cabin + 1) % self.total_cabins
                seniors -= 1
            
            # Log the cabin allocation
            for i, cabin in enumerate(self.cabins_with_passengers):
                total_in_cabin = cabin["assigned_adults"] + cabin["assigned_children"] + cabin["assigned_seniors"]
                self.logger.info(f"Cabin {i+1} ({cabin['type']}): {cabin['assigned_adults']} adults, {cabin['assigned_children']} children, {cabin['assigned_seniors']} seniors (Total: {total_in_cabin})")
            
            # Identify similar cabins for optimization
            self._identify_similar_cabins()
            
        except Exception as e:
            self.logger.error(f"Error allocating passengers to cabins: {e}")
            
    def _identify_similar_cabins(self):
        """
        Identify groups of similar cabins (same category type and passenger allocation)
        """
        try:
            self.similar_cabin_groups = {}
            self.cabin_to_group_mapping = {}
            
            # Generate a unique cabin signature for each cabin
            for i, cabin in enumerate(self.cabins_with_passengers):
                # Create a signature based on normalized type and passenger allocation
                signature = (
                    cabin["normalized"],
                    cabin["assigned_adults"],
                    cabin["assigned_children"],
                    cabin["assigned_seniors"]
                )
                
                # Add to group
                if signature not in self.similar_cabin_groups:
                    self.similar_cabin_groups[signature] = []
                
                self.similar_cabin_groups[signature].append(i)
                self.cabin_to_group_mapping[i] = signature
            
            # Log information about similar cabins
            for signature, cabin_indices in self.similar_cabin_groups.items():
                if len(cabin_indices) > 1:
                    cabin_type, adults, children, seniors = signature
                    self.logger.info(f"Found {len(cabin_indices)} similar cabins of type {cabin_type} with {adults} adults, {children} children, {seniors} seniors")
                    self.logger.info(f"  Cabin numbers: {[idx+1 for idx in cabin_indices]}")
            
        except Exception as e:
            self.logger.error(f"Error identifying similar cabins: {e}")
            # Ensure we have an empty mapping in case of error
            self.similar_cabin_groups = {}
            self.cabin_to_group_mapping = {}
    
    async def _process_cabin_async(self, cabin_index):
        """
        Process a single cabin with its own driver instance
        
        Args:
            cabin_index: Index of the cabin to process
            
        Returns:
            Boolean indicating success or failure
        """
        cabin = self.cabins_with_passengers[cabin_index]
        cabin_logger, _ = get_cabin_logger(self.log_dir, cabin_index, cabin["normalized"])
        
        try:
            # Get shared browser for Cruising Power
            cabin_logger.info(f"Using shared browser for cabin {cabin_index+1}...")
            
            from Core.browser_setup import browser_manager, AsyncBrowserSetup
            
            # Get the shared browser instance for Cruising Power
            browser = await browser_manager.get_or_launch_browser('Cruising_Power')
            cabin_logger.info("Retrieved shared Cruising Power browser")
            
            # Extract video auditing configuration
            video_auditing = self.config.get('video_auditing', False)
            cabin_logger.info(f"Video auditing configuration from config: {video_auditing}")
            
            if video_auditing:
                cabin_logger.info("Video auditing enabled for Cruising Power")
                # Verify import is working
                try:
                    from db.video_manager import save_video_to_db
                    cabin_logger.info("Successfully imported save_video_to_db function")
                except ImportError as ie:
                    cabin_logger.error(f"Failed to import video_manager functions: {str(ie)}")
            
            # Create browser setup instance for context creation
            browser_setup = AsyncBrowserSetup()
            
            # Create an optimized browser context using the shared browser
            context = await browser_setup.create_optimized_context(
                browser,
                logger=cabin_logger,
                video_auditing=video_auditing,
                cabin_id=cabin_index+1,
                session_id=self.session_id
            )
            
            cabin_logger.info(f"Video path after context creation: {browser_setup.video_path if hasattr(browser_setup, 'video_path') else 'None'}")
            
            # Create a new page and configure it
            page = await context.new_page()
            
            # Set default timeout for page operations to 30 seconds
            page.set_default_timeout(30000)  
            playwright = browser_manager.get_playwright('Cruising_Power')
            
            # Apply stealth mode JavaScript
            await self._apply_stealth_script(page)
            
            # Configure resource blocking to improve performance
            await browser_setup.setup_resource_blocking(page, cabin_logger, block=False)
            
            cabin_logger.info("Browser setup complete with enhanced anti-detection")            
            
            # Determine headless setting (Cruising Power prefers non-headless)
            headless_setting = self.config.get('headless', False)
            # Force to False (non-headless) if required by anti-bot strategy
            headless_setting = False
            
            # Attempt login
            cabin_logger.info(f"Logging in for cabin {cabin_index+1}...")
            page, login_success = await login_to_cruising_power(
                username=my_username,
                password=my_password,
                log_dir=self.log_dir,
                headless=headless_setting,
                existing_page=page,
                video_auditing=video_auditing,
                session_id=self.session_id
            )
            
            if not login_success:
                cabin_logger.error(f"Login failed for cabin {cabin_index+1}")
                if page:
                    await page.close()
                if context:
                    await context.close()
                if browser:
                    await browser.close()
                if playwright:
                    await playwright.stop()
                return False
            
            # Process cabin search
            cabin_logger.info(f"Starting search process for cabin {cabin_index+1}...")
            processor = CabinProcessor(
                page=page,
                playwright=playwright,
                browser=browser,
                context=context,
                info_dict=self.info_dict,
                cabin=cabin,
                log_dir=self.log_dir,
                cabin_index=cabin_index,
                onboard_percentage=self.onboard_percentage,
                session_id=self.session_id  # Pass session_id to CabinProcessor
            )
            
            cabin_result = await processor.process()

            # First, add a dedicated video saving section before browser closure
            cabin_logger.info(f"Processing completed, saving video recording for cabin {cabin_index+1}...")

            # Check if video recording was enabled and the browser_setup has a video_path
            if video_auditing:
                try:
                    cabin_logger.info(f"Video auditing was enabled. Checking for video_path attribute: {hasattr(browser_setup, 'video_path')}")
                    if hasattr(browser_setup, 'video_path') and browser_setup.video_path:
                        cabin_logger.info(f"Found video recording at path: {browser_setup.video_path}")
                        
                        # Get the actual video file from the recording directory
                        video_dir = os.path.dirname(browser_setup.video_path)
                        cabin_logger.info(f"Video directory: {video_dir}")
                        
                        # List files in the directory to debug
                        if os.path.exists(video_dir):
                            video_files = os.listdir(video_dir)
                            cabin_logger.info(f"Files in video directory: {video_files}")
                        else:
                            cabin_logger.error(f"Video directory does not exist: {video_dir}")
                        
                        # Call stop_video_recording with all required parameters
                        cabin_logger.info("Calling stop_video_recording with request_id, provider, and session_id")
                        cabin_logger.info(f"Request ID: {self.info_dict.get('request_id')}")
                        cabin_logger.info(f"Provider: cruising_power")
                        cabin_logger.info(f"Session ID: {self.session_id}")
                        
                        # Pass the correct cabin_id so the video is stored against the right cabin
                        video_result = await browser_setup.stop_video_recording(
                            page,
                            cabin_logger,
                            self.info_dict.get('request_id'),
                            "cruising_power",
                            self.session_id,
                            cabin_index+1  # Pass cabin index
                        )
                        
                        if video_result:
                            cabin_logger.info(f"Video successfully saved to database from path: {video_result}")
                        else:
                            cabin_logger.error("Failed to save video to database: stop_video_recording returned None")
                    else:
                        cabin_logger.warning(f"Video auditing was enabled but no video_path was found on browser_setup")
                        cabin_logger.warning(f"Browser setup object attributes: {dir(browser_setup)}")
                        
                        # Try to find video files in the default directory as a fallback
                        from Core.browser_setup import ensure_video_directory
                        video_dir = ensure_video_directory(cabin_index)
                        cabin_logger.info(f"Fallback: checking video directory: {video_dir}")
                        
                        if os.path.exists(video_dir):
                            video_files = [f for f in os.listdir(video_dir) if f.endswith('.webm')]
                            cabin_logger.info(f"Found {len(video_files)} video files in directory")
                            
                            if video_files:
                                # Find the most recent video file
                                latest_video = None
                                latest_time = 0
                                for file in video_files:
                                    file_path = os.path.join(video_dir, file)
                                    file_time = os.path.getmtime(file_path)
                                    if file_time > latest_time:
                                        latest_time = file_time
                                        latest_video = file_path
                                
                                if latest_video:
                                    cabin_logger.info(f"Found latest video file: {latest_video}")
                                    # Manually save the video to database
                                    try:
                                        from db.video_manager import save_video_to_db
                                        success = await save_video_to_db(
                                            latest_video,
                                            self.info_dict.get('request_id'),
                                            "cruising_power",
                                            "booking_process",
                                            None,
                                            cabin_index + 1,  # Associate video with the current cabin
                                            self.session_id,
                                            "webm",
                                            True,
                                            True
                                        )
                                        if success:
                                            cabin_logger.info(f"Video successfully saved to database via fallback method")
                                        else:
                                            cabin_logger.error(f"Failed to save video to database via fallback method")
                                    except Exception as save_err:
                                        cabin_logger.error(f"Error saving video via fallback: {str(save_err)}")
                except Exception as e:
                    cabin_logger.error(f"Error saving video to database: {str(e)}")
                    import traceback
                    cabin_logger.error(f"Traceback: {traceback.format_exc()}")

            # Close context and page (but keep shared browser alive)
            cabin_logger.info(f"Cleaning up cabin {cabin_index+1} resources...")
            
            # Proper cleanup order: page -> context (shared browser stays alive)
            try:
                if page:
                    await page.close()
                    cabin_logger.info(f"Page closed for cabin {cabin_index+1}")
            except Exception as e:
                cabin_logger.error(f"Error closing page: {str(e)}")
            
            try:
                if context:
                    await context.close()
                    cabin_logger.info(f"Context closed for cabin {cabin_index+1}")
            except Exception as e:
                cabin_logger.error(f"Error closing context: {str(e)}")
            
            return cabin_result
        
        except Exception as e:
            cabin_logger.error(f"Error processing cabin {cabin_index+1}: {e}")
            
            # Proper cleanup in case of exception - page -> context (shared browser stays alive)
            try:
                if 'page' in locals() and page:
                    await page.close()
                    cabin_logger.info(f"Page closed for cabin {cabin_index+1} (exception cleanup)")
            except Exception as cleanup_e:
                cabin_logger.error(f"Error closing page during exception cleanup: {str(cleanup_e)}")
            
            try:
                if 'context' in locals() and context:
                    await context.close()
                    cabin_logger.info(f"Context closed for cabin {cabin_index+1} (exception cleanup)")
            except Exception as cleanup_e:
                cabin_logger.error(f"Error closing context during exception cleanup: {str(cleanup_e)}")
            
            # Add error to cabin result
            cabin_result = {
                "cabin_number": cabin_index + 1,
                "cabin_type": cabin["type"],
                "normalized_type": cabin["normalized"],
                "success": False,
                "error": str(e),
                "request_id": self.info_dict.get('request_id'),
                "provider": "Cruising Power",
                "session_id": self.session_id  # Pass session_id to functions called from here
            }
            return cabin_result
    
    async def _apply_stealth_script(self, page):
        """Apply stealth mode JavaScript to evade bot detection"""
        try:
            # Override JavaScript properties that can reveal automation
            await page.add_init_script("""
            // Override WebDriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
                configurable: true
            });
            
            // Override Chrome property
            Object.defineProperty(window, 'chrome', {
                get: () => ({
                    runtime: {},
                    app: {},
                    // Add other chrome properties as needed
                }),
                configurable: true
            });
            
            // Override permissions
            Object.defineProperty(navigator, 'permissions', {
                get: () => ({
                    query: () => Promise.resolve({ state: 'granted' })
                }),
                configurable: true
            });
            
            // Override plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => {
                    return [1, 2, 3, 4, 5];
                },
                configurable: true
            });
            
            // Override languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
                configurable: true
            });
            """)
            
            logger.info("Applied stealth mode JavaScript")
        except Exception as e:
            logger.error(f"Error applying stealth script: {e}")
    
    async def run(self):
        """
        Run the CruisingPower process
        
        Returns:
            Dictionary with processing results
        """
        # Step 1: Get cruise information
        self._get_cruise_information()
        
        # Step 2: Set up browser
        self.logger.info("\n" + "=" * 50)
        self.logger.info("STEP 2: SETTING UP BROWSER")
        self.logger.info("=" * 50)
        
        # Record start time
        self.start_time = datetime.datetime.now()
        self.logger.info(f"Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        
        try:
            # Determine which cabins need to be processed (one per group of similar cabins)
            cabins_to_process = []
            for signature, cabin_indices in self.similar_cabin_groups.items():
                # Only process the first cabin in each group
                cabins_to_process.append(cabin_indices[0])
            
            self.logger.info(f"Processing {len(cabins_to_process)} unique cabins out of {self.total_cabins} total cabins")
            
            # Process only unique cabins using asyncio.gather
            tasks = [asyncio.create_task(self._process_cabin_async(i)) for i in cabins_to_process]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle results and replicate to similar cabins
            for i, (cabin_index, result) in enumerate(zip(cabins_to_process, results)):
                try:
                    if isinstance(result, Exception):
                        self.cabin_results[cabin_index] = None
                    else:
                        # Each processor returns cabin_result dict
                        self.cabin_results[cabin_index] = result
                        # Append to instance results
                        self.results['cabins'].append(result)
                        
                        # Replicate the result to similar cabins
                        signature = self.cabin_to_group_mapping.get(cabin_index)
                        if signature and result:
                            for similar_index in self.similar_cabin_groups.get(signature, []):
                                if similar_index != cabin_index:
                                    self.logger.info(f"Replicating results from cabin {cabin_index+1} to similar cabin {similar_index+1}")
                                    # Deep copy and update cabin_number
                                    replica = result.copy()
                                    replica['cabin_number'] = similar_index + 1
                                    self.results['cabins'].append(replica)
                                    self.cabin_results[similar_index] = replica
                        
                        self.logger.info(f"Cabin {cabin_index+1} processing completed with result: {result}")
                except Exception as e:
                    self.logger.error(f"Error handling cabin {cabin_index+1} result: {str(e)}")
                    self.cabin_results[cabin_index] = None
            
            # Report overall results
            self.end_time = datetime.datetime.now()
            self.logger.info(f"End Time: {self.end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
            
            duration = self.end_time - self.start_time
            minutes, seconds = divmod(duration.seconds, 60)
            self.logger.info(f"Total Execution Time: {minutes} minutes, {seconds} seconds")
            
            self.logger.info("\nOverall Results:")
            successful_cabins = sum(1 for result in self.cabin_results.values() if result)
            self.logger.info(f"Successfully processed {successful_cabins} out of {self.total_cabins} cabins")
            
            # Build final results dict with timing and status
            self.results['start_time'] = self.start_time.isoformat()
            self.results['end_time'] = self.end_time.isoformat()
            self.results['execution_time'] = duration.total_seconds()
            self.results['total_cabins'] = self.total_cabins
            self.results['successful_cabins'] = successful_cabins
            self.results['overall_status'] = (successful_cabins == self.total_cabins)
            self.results['timestamp'] = datetime.datetime.now().isoformat()
            # Save instance results to database
            from db.cruising_power_provider import save_cruising_power_results
            db_save_success, _ = await save_cruising_power_results(self.results, self.request_id, self.session_id)
            self.results['database_save_success'] = db_save_success

            # Send final UI log to trigger validation window
            from Core.ui_logger import ui_log
            if successful_cabins == 0:
                # All cabins failed - send failure completion message
                ui_log("Search failed — no available cabins found (all are closed or on waitlist)", 
                       session_id=self.session_id, cabin_id=None, step="process_completed_with_failures", module="Cruising_Power")
            else:
                # At least one cabin succeeded - send success completion message  
                ui_log("Pricing information retrieved — thanks for your patience", 
                       session_id=self.session_id, cabin_id=None, step="process_completed_successfully", module="Cruising_Power")

            return self.results
        
        except Exception as e:
            self.logger.error(f"An error occurred during the overall process: {e}")
            return False
            
    def _replicate_cabin_result(self, source_cabin_index, target_cabin_index):
        """
        Replicates results from a processed cabin to a similar cabin
        
        Args:
            source_cabin_index: Index of the source cabin (already processed)
            target_cabin_index: Index of the target cabin (to replicate results to)
        """
        try:
            # Get cabin info
            source_cabin = self.cabins_with_passengers[source_cabin_index]
            target_cabin = self.cabins_with_passengers[target_cabin_index]
            
            # Initialize cabin logger for target
            target_logger, _ = get_cabin_logger(self.log_dir, target_cabin_index, target_cabin["normalized"])
            
            target_logger.info(f"{'=' * 50}")
            target_logger.info(f"REPLICATING RESULTS FROM CABIN {source_cabin_index+1} TO CABIN {target_cabin_index+1}")
            target_logger.info(f"{'=' * 50}")
            
            # Create a deep copy of the source cabin result to modify for the target
            from Cruising_Power.config import processing_results
            
            source_result = None
            target_result = None
            
            # Find the source cabin result in the processing_results
            for cabin_result in processing_results["cabins"]:
                if cabin_result.get("cabin_number") == source_cabin_index + 1:
                    source_result = cabin_result
                    break
                    
            if not source_result:
                target_logger.error(f"Could not find source cabin result for cabin {source_cabin_index+1}")
                return False
                
            # Create a copy of the source result for the target cabin
            import copy
            target_result = copy.deepcopy(source_result)
            
            # Update the target result with target cabin information
            target_result["cabin_number"] = target_cabin_index + 1
            target_result["cabin_type"] = target_cabin["type"]
            target_result["normalized_type"] = target_cabin["normalized"]
            target_result["timestamp"] = datetime.datetime.now().isoformat()
            target_result["replicated_from"] = source_cabin_index + 1
            
            # Add the target result to the processing results
            return target_result
            
        except Exception as e:
            self.logger.error(f"Error replicating results from cabin {source_cabin_index+1} to cabin {target_cabin_index+1}: {e}")
            return False


async def main(config=None, cp_details=None):
    """
    Main entry point for the Cruising Power processing
    
    Args:
        config: Dictionary of configuration options. Supports:
            - request_id: Unique identifier for this booking request
            - session_id: Database session ID for tracking
            - cruise_info_file: Path to a text file containing cruise information
            - headless: Boolean to run in headless mode (defaults to True)
            - parallel_cabins: Boolean to process cabins in parallel
            - cruising_power_onboard_percentage: Percentage for onboard credit calculation
        cp_details: Optional pre-extracted cruise details
        
    Returns:
        Dictionary containing processing results
    """
    # Ensure config exists
    if config is None:
        config = {}
    
    # Set headless to True by default if not specified
    if 'headless' not in config:
        config['headless'] = False
        
    processor = CruisingPower(config, cp_details)
    try:
        return await processor.run()
    except asyncio.CancelledError:
        logger.warning(f"Cruising Power booking cancelled for session {config.get('session_id')}")
        # Perform any necessary cleanup
        try:
            await processor.cleanup()
        except:
            pass  # Don't let cleanup errors mask the cancellation
        raise  # Re-raise to ensure proper cancellation propagation


if __name__ == "__main__":
    # Set up logging configuration first
    logger_instance, log_dir = setup_logger()
    
    # When run directly, print usage if no cruise info provided
    if len(sys.argv) < 2:
        logger.info("Usage: python main.py <cruise_info_file.txt>")
        sys.exit(1)
    
    # Use file path from command line if provided
    cruise_info_file = sys.argv[1]
    if not os.path.exists(cruise_info_file):
        logger.error(f"Error: Cruise info file not found: {cruise_info_file}")
        sys.exit(1)
    
    # Create config with the file path
    config = {
        "cruise_info_file": cruise_info_file,
        "headless": False,  # Default to headless mode for direct execution
        "parallel_cabins": True,  # Use parallel processing for multiple cabins
        "log_dir": log_dir,  # Pass log directory to ensure consistent logging
        "video_auditing": True,  # Explicitly enable video recording
    }
    
    # Run the main process
    logger.info(f"Starting Cruising Power booking process with file: {cruise_info_file}")
    results = asyncio.run(main(config))
    
    # Print summary
    logger.info("\nProcessing completed")
    logger.info(f"Success: {results.get('overall_status', False)}")
    if results.get('overall_status', False):
        logger.info(f"Cabins processed: {len(results.get('cabins', []))}")
        logger.info(f"Database save: {results.get('database_save_success', False)}")
    else:
        logger.error(f"Error: {results.get('error', 'Unknown error')}")