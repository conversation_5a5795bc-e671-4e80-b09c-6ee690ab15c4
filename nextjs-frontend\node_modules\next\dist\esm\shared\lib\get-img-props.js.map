{"version": 3, "sources": ["../../../src/shared/lib/get-img-props.ts"], "names": ["warnOnce", "getImageBlurSvg", "imageConfigDefault", "VALID_LOADING_VALUES", "undefined", "isStaticRequire", "src", "default", "isStaticImageData", "isStaticImport", "allImgs", "Map", "perfObserver", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "getWidths", "width", "sizes", "deviceSizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "push", "length", "smallestRatio", "Math", "min", "widths", "filter", "s", "kind", "Set", "map", "w", "find", "p", "generateImgAttrs", "config", "unoptimized", "quality", "loader", "srcSet", "last", "i", "join", "getImgProps", "_state", "priority", "loading", "className", "height", "fill", "style", "onLoad", "onLoadingComplete", "placeholder", "blurDataURL", "fetchPriority", "layout", "objectFit", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "defaultLoader", "c", "imageSizes", "sort", "a", "b", "isDefaultLoader", "Error", "customImageLoader", "obj", "_", "opts", "layoutToStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutToSizes", "layoutStyle", "layoutSizes", "staticSrc", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "startsWith", "endsWith", "dangerouslyAllowSVG", "qualityInt", "process", "env", "NODE_ENV", "output", "position", "isNaN", "includes", "String", "VALID_BLUR_EXT", "urlStr", "url", "URL", "err", "pathname", "search", "<PERSON><PERSON><PERSON>", "legacyValue", "Object", "entries", "window", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "get", "observe", "type", "buffered", "console", "error", "imgStyle", "assign", "left", "top", "right", "bottom", "color", "backgroundImage", "placeholder<PERSON><PERSON><PERSON>", "backgroundSize", "backgroundPosition", "backgroundRepeat", "imgAttributes", "fullUrl", "e", "location", "href", "set", "props", "decoding", "meta"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,oBAAmB;AAC5C,SAASC,eAAe,QAAQ,mBAAkB;AAClD,SAASC,kBAAkB,QAAQ,iBAAgB;AA4EnD,MAAMC,uBAAuB;IAAC;IAAQ;IAASC;CAAU;AAkBzD,SAASC,gBACPC,GAAoC;IAEpC,OAAO,AAACA,IAAsBC,OAAO,KAAKH;AAC5C;AAEA,SAASI,kBACPF,GAAoC;IAEpC,OAAO,AAACA,IAAwBA,GAAG,KAAKF;AAC1C;AAEA,SAASK,eAAeH,GAA0B;IAChD,OACE,OAAOA,QAAQ,YACdD,CAAAA,gBAAgBC,QACfE,kBAAkBF,IAAmB;AAE3C;AAEA,MAAMI,UAAU,IAAIC;AAIpB,IAAIC;AAEJ,SAASC,OAAOC,CAAU;IACxB,IAAI,OAAOA,MAAM,aAAa;QAC5B,OAAOA;IACT;IACA,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOC,OAAOC,QAAQ,CAACF,KAAKA,IAAIG;IAClC;IACA,IAAI,OAAOH,MAAM,YAAY,WAAWI,IAAI,CAACJ,IAAI;QAC/C,OAAOK,SAASL,GAAG;IACrB;IACA,OAAOG;AACT;AAEA,SAASG,UACP,KAAsC,EACtCC,KAAyB,EACzBC,KAAyB;IAFzB,IAAA,EAAEC,WAAW,EAAEC,QAAQ,EAAe,GAAtC;IAIA,IAAIF,OAAO;QACT,yDAAyD;QACzD,MAAMG,kBAAkB;QACxB,MAAMC,eAAe,EAAE;QACvB,IAAK,IAAIC,OAAQA,QAAQF,gBAAgBG,IAAI,CAACN,QAASK,MAAO;YAC5DD,aAAaG,IAAI,CAACV,SAASQ,KAAK,CAAC,EAAE;QACrC;QACA,IAAID,aAAaI,MAAM,EAAE;YACvB,MAAMC,gBAAgBC,KAAKC,GAAG,IAAIP,gBAAgB;YAClD,OAAO;gBACLQ,QAAQV,SAASW,MAAM,CAAC,CAACC,IAAMA,KAAKb,WAAW,CAAC,EAAE,GAAGQ;gBACrDM,MAAM;YACR;QACF;QACA,OAAO;YAAEH,QAAQV;YAAUa,MAAM;QAAI;IACvC;IACA,IAAI,OAAOhB,UAAU,UAAU;QAC7B,OAAO;YAAEa,QAAQX;YAAac,MAAM;QAAI;IAC1C;IAEA,MAAMH,SAAS;WACV,IAAII,IACL,uEAAuE;QACvE,qEAAqE;QACrE,kEAAkE;QAClE,oEAAoE;QACpE,uEAAuE;QACvE,sEAAsE;QACtE,uCAAuC;QACvC,qIAAqI;QACrI;YAACjB;YAAOA,QAAQ,EAAE,aAAa;SAAG,CAACkB,GAAG,CACpC,CAACC,IAAMhB,SAASiB,IAAI,CAAC,CAACC,IAAMA,KAAKF,MAAMhB,QAAQ,CAACA,SAASM,MAAM,GAAG,EAAE;KAGzE;IACD,OAAO;QAAEI;QAAQG,MAAM;IAAI;AAC7B;AAkBA,SAASM,iBAAiB,KAQR;IARQ,IAAA,EACxBC,MAAM,EACNtC,GAAG,EACHuC,WAAW,EACXxB,KAAK,EACLyB,OAAO,EACPxB,KAAK,EACLyB,MAAM,EACU,GARQ;IASxB,IAAIF,aAAa;QACf,OAAO;YAAEvC;YAAK0C,QAAQ5C;YAAWkB,OAAOlB;QAAU;IACpD;IAEA,MAAM,EAAE8B,MAAM,EAAEG,IAAI,EAAE,GAAGjB,UAAUwB,QAAQvB,OAAOC;IAClD,MAAM2B,OAAOf,OAAOJ,MAAM,GAAG;IAE7B,OAAO;QACLR,OAAO,CAACA,SAASe,SAAS,MAAM,UAAUf;QAC1C0B,QAAQd,OACLK,GAAG,CACF,CAACC,GAAGU,IACF,AAAGH,OAAO;gBAAEH;gBAAQtC;gBAAKwC;gBAASzB,OAAOmB;YAAE,KAAG,MAC5CH,CAAAA,SAAS,MAAMG,IAAIU,IAAI,CAAA,IACtBb,MAENc,IAAI,CAAC;QAER,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtD7C,KAAKyC,OAAO;YAAEH;YAAQtC;YAAKwC;YAASzB,OAAOa,MAAM,CAACe,KAAK;QAAC;IAC1D;AACF;AAEA;;CAEC,GACD,OAAO,SAASG,YACd,KAuBa,EACbC,MAKC;IA7BD,IAAA,EACE/C,GAAG,EACHgB,KAAK,EACLuB,cAAc,KAAK,EACnBS,WAAW,KAAK,EAChBC,OAAO,EACPC,SAAS,EACTV,OAAO,EACPzB,KAAK,EACLoC,MAAM,EACNC,OAAO,KAAK,EACZC,KAAK,EACLC,MAAM,EACNC,iBAAiB,EACjBC,cAAc,OAAO,EACrBC,WAAW,EACXC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,YAAY,EACZC,QAAQ,EACR,GAAGC,MACQ,GAvBb;IAuCA,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,aAAa,EAAE,GAAGrB;IAC9D,IAAIT;IACJ,IAAI+B,IAAIJ,WAAWrE;IACnB,IAAI,cAAcyE,GAAG;QACnB/B,SAAS+B;IACX,OAAO;QACL,MAAMnD,WAAW;eAAImD,EAAEpD,WAAW;eAAKoD,EAAEC,UAAU;SAAC,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMxD,cAAcoD,EAAEpD,WAAW,CAACsD,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrDnC,SAAS;YAAE,GAAG+B,CAAC;YAAEnD;YAAUD;QAAY;IACzC;IAEA,IAAIwB,SAAgCuB,KAAKvB,MAAM,IAAI2B;IAEnD,sDAAsD;IACtD,OAAOJ,KAAKvB,MAAM;IAClB,OAAO,AAACuB,KAAatB,MAAM;IAE3B,6CAA6C;IAC7C,oDAAoD;IACpD,MAAMgC,kBAAkB,wBAAwBjC;IAEhD,IAAIiC,iBAAiB;QACnB,IAAIpC,OAAOG,MAAM,KAAK,UAAU;YAC9B,MAAM,IAAIkC,MACR,AAAC,qBAAkB3E,MAAI,gCACpB;QAEP;IACF,OAAO;QACL,8CAA8C;QAC9C,+CAA+C;QAC/C,iDAAiD;QACjD,MAAM4E,oBAAoBnC;QAC1BA,SAAS,CAACoC;YACR,MAAM,EAAEvC,QAAQwC,CAAC,EAAE,GAAGC,MAAM,GAAGF;YAC/B,OAAOD,kBAAkBG;QAC3B;IACF;IAEA,IAAIpB,QAAQ;QACV,IAAIA,WAAW,QAAQ;YACrBP,OAAO;QACT;QACA,MAAM4B,gBAAoE;YACxEC,WAAW;gBAAEC,UAAU;gBAAQ/B,QAAQ;YAAO;YAC9CgC,YAAY;gBAAEpE,OAAO;gBAAQoC,QAAQ;YAAO;QAC9C;QACA,MAAMiC,gBAAoD;YACxDD,YAAY;YACZ/B,MAAM;QACR;QACA,MAAMiC,cAAcL,aAAa,CAACrB,OAAO;QACzC,IAAI0B,aAAa;YACfhC,QAAQ;gBAAE,GAAGA,KAAK;gBAAE,GAAGgC,WAAW;YAAC;QACrC;QACA,MAAMC,cAAcF,aAAa,CAACzB,OAAO;QACzC,IAAI2B,eAAe,CAACtE,OAAO;YACzBA,QAAQsE;QACV;IACF;IAEA,IAAIC,YAAY;IAChB,IAAIC,WAAWjF,OAAOQ;IACtB,IAAI0E,YAAYlF,OAAO4C;IACvB,IAAIuC;IACJ,IAAIC;IACJ,IAAIxF,eAAeH,MAAM;QACvB,MAAM4F,kBAAkB7F,gBAAgBC,OAAOA,IAAIC,OAAO,GAAGD;QAE7D,IAAI,CAAC4F,gBAAgB5F,GAAG,EAAE;YACxB,MAAM,IAAI2E,MACR,AAAC,gJAA6IkB,KAAKC,SAAS,CAC1JF;QAGN;QACA,IAAI,CAACA,gBAAgBzC,MAAM,IAAI,CAACyC,gBAAgB7E,KAAK,EAAE;YACrD,MAAM,IAAI4D,MACR,AAAC,6JAA0JkB,KAAKC,SAAS,CACvKF;QAGN;QAEAF,YAAYE,gBAAgBF,SAAS;QACrCC,aAAaC,gBAAgBD,UAAU;QACvClC,cAAcA,eAAemC,gBAAgBnC,WAAW;QACxD8B,YAAYK,gBAAgB5F,GAAG;QAE/B,IAAI,CAACoD,MAAM;YACT,IAAI,CAACoC,YAAY,CAACC,WAAW;gBAC3BD,WAAWI,gBAAgB7E,KAAK;gBAChC0E,YAAYG,gBAAgBzC,MAAM;YACpC,OAAO,IAAIqC,YAAY,CAACC,WAAW;gBACjC,MAAMM,QAAQP,WAAWI,gBAAgB7E,KAAK;gBAC9C0E,YAAY/D,KAAKsE,KAAK,CAACJ,gBAAgBzC,MAAM,GAAG4C;YAClD,OAAO,IAAI,CAACP,YAAYC,WAAW;gBACjC,MAAMM,QAAQN,YAAYG,gBAAgBzC,MAAM;gBAChDqC,WAAW9D,KAAKsE,KAAK,CAACJ,gBAAgB7E,KAAK,GAAGgF;YAChD;QACF;IACF;IACA/F,MAAM,OAAOA,QAAQ,WAAWA,MAAMuF;IAEtC,IAAIU,SACF,CAACjD,YAAaC,CAAAA,YAAY,UAAU,OAAOA,YAAY,WAAU;IACnE,IAAI,CAACjD,OAAOA,IAAIkG,UAAU,CAAC,YAAYlG,IAAIkG,UAAU,CAAC,UAAU;QAC9D,uEAAuE;QACvE3D,cAAc;QACd0D,SAAS;IACX;IACA,IAAI3D,OAAOC,WAAW,EAAE;QACtBA,cAAc;IAChB;IACA,IAAImC,mBAAmB1E,IAAImG,QAAQ,CAAC,WAAW,CAAC7D,OAAO8D,mBAAmB,EAAE;QAC1E,yDAAyD;QACzD,+CAA+C;QAC/C7D,cAAc;IAChB;IACA,IAAIS,UAAU;QACZU,gBAAgB;IAClB;IAEA,MAAM2C,aAAa9F,OAAOiC;IAE1B,IAAI8D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAIlE,OAAOmE,MAAM,KAAK,YAAY/B,mBAAmB,CAACnC,aAAa;YACjE,MAAM,IAAIoC,MACP;QAML;QACA,IAAI,CAAC3E,KAAK;YACR,iDAAiD;YACjD,+CAA+C;YAC/C,2CAA2C;YAC3CuC,cAAc;QAChB,OAAO;YACL,IAAIa,MAAM;gBACR,IAAIrC,OAAO;oBACT,MAAM,IAAI4D,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAImD,QAAQ;oBACV,MAAM,IAAIwB,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIqD,CAAAA,yBAAAA,MAAOqD,QAAQ,KAAIrD,MAAMqD,QAAQ,KAAK,YAAY;oBACpD,MAAM,IAAI/B,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIqD,CAAAA,yBAAAA,MAAOtC,KAAK,KAAIsC,MAAMtC,KAAK,KAAK,QAAQ;oBAC1C,MAAM,IAAI4D,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIqD,CAAAA,yBAAAA,MAAOF,MAAM,KAAIE,MAAMF,MAAM,KAAK,QAAQ;oBAC5C,MAAM,IAAIwB,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;YACF,OAAO;gBACL,IAAI,OAAOwF,aAAa,aAAa;oBACnC,MAAM,IAAIb,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B,OAAO,IAAI2G,MAAMnB,WAAW;oBAC1B,MAAM,IAAIb,MACR,AAAC,qBAAkB3E,MAAI,sFAAmFe,QAAM;gBAEpH;gBACA,IAAI,OAAO0E,cAAc,aAAa;oBACpC,MAAM,IAAId,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B,OAAO,IAAI2G,MAAMlB,YAAY;oBAC3B,MAAM,IAAId,MACR,AAAC,qBAAkB3E,MAAI,uFAAoFmD,SAAO;gBAEtH;YACF;QACF;QACA,IAAI,CAACtD,qBAAqB+G,QAAQ,CAAC3D,UAAU;YAC3C,MAAM,IAAI0B,MACR,AAAC,qBAAkB3E,MAAI,iDAA8CiD,UAAQ,wBAAqBpD,qBAAqBoC,GAAG,CACxH4E,QACAhE,IAAI,CAAC,OAAK;QAEhB;QACA,IAAIG,YAAYC,YAAY,QAAQ;YAClC,MAAM,IAAI0B,MACR,AAAC,qBAAkB3E,MAAI;QAE3B;QACA,IACEwD,gBAAgB,WAChBA,gBAAgB,UAChB,CAACA,YAAY0C,UAAU,CAAC,gBACxB;YACA,MAAM,IAAIvB,MACR,AAAC,qBAAkB3E,MAAI,2CAAwCwD,cAAY;QAE/E;QACA,IAAIA,gBAAgB,SAAS;YAC3B,IAAIgC,YAAYC,aAAaD,WAAWC,YAAY,MAAM;gBACxD/F,SACE,AAAC,qBAAkBM,MAAI;YAE3B;QACF;QACA,IAAIwD,gBAAgB,UAAU,CAACC,aAAa;YAC1C,MAAMqD,iBAAiB;gBAAC;gBAAQ;gBAAO;gBAAQ;aAAO,CAAC,iCAAiC;;YAExF,MAAM,IAAInC,MACR,AAAC,qBAAkB3E,MAAI,6TAGkE8G,eAAejE,IAAI,CACxG,OACA;QAIR;QACA,IAAI,SAASmB,MAAM;YACjBtE,SACE,AAAC,qBAAkBM,MAAI;QAE3B;QAEA,IAAI,CAACuC,eAAe,CAACmC,iBAAiB;YACpC,MAAMqC,SAAStE,OAAO;gBACpBH;gBACAtC;gBACAe,OAAOyE,YAAY;gBACnBhD,SAAS6D,cAAc;YACzB;YACA,IAAIW;YACJ,IAAI;gBACFA,MAAM,IAAIC,IAAIF;YAChB,EAAE,OAAOG,KAAK,CAAC;YACf,IAAIH,WAAW/G,OAAQgH,OAAOA,IAAIG,QAAQ,KAAKnH,OAAO,CAACgH,IAAII,MAAM,EAAG;gBAClE1H,SACE,AAAC,qBAAkBM,MAAI,4HACpB;YAEP;QACF;QAEA,IAAIuD,mBAAmB;YACrB7D,SACE,AAAC,qBAAkBM,MAAI;QAE3B;QAEA,KAAK,MAAM,CAACqH,WAAWC,YAAY,IAAIC,OAAOC,OAAO,CAAC;YACpD7D;YACAC;YACAC;YACAC;YACAC;QACF,GAAI;YACF,IAAIuD,aAAa;gBACf5H,SACE,AAAC,qBAAkBM,MAAI,wBAAqBqH,YAAU,0CACnD;YAEP;QACF;QAEA,IACE,OAAOI,WAAW,eAClB,CAACnH,gBACDmH,OAAOC,mBAAmB,EAC1B;YACApH,eAAe,IAAIoH,oBAAoB,CAACC;gBACtC,KAAK,MAAMC,SAASD,UAAUE,UAAU,GAAI;wBAE3BD;oBADf,0EAA0E;oBAC1E,MAAME,SAASF,CAAAA,0BAAAA,iBAAAA,MAAOG,OAAO,qBAAdH,eAAgB5H,GAAG,KAAI;oBACtC,MAAMgI,WAAW5H,QAAQ6H,GAAG,CAACH;oBAC7B,IACEE,YACA,CAACA,SAAShF,QAAQ,IAClBgF,SAASxE,WAAW,KAAK,WACzB,CAACwE,SAAShI,GAAG,CAACkG,UAAU,CAAC,YACzB,CAAC8B,SAAShI,GAAG,CAACkG,UAAU,CAAC,UACzB;wBACA,iDAAiD;wBACjDxG,SACE,AAAC,qBAAkBsI,SAAShI,GAAG,GAAC,8HAC7B;oBAEP;gBACF;YACF;YACA,IAAI;gBACFM,aAAa4H,OAAO,CAAC;oBACnBC,MAAM;oBACNC,UAAU;gBACZ;YACF,EAAE,OAAOlB,KAAK;gBACZ,oCAAoC;gBACpCmB,QAAQC,KAAK,CAACpB;YAChB;QACF;IACF;IACA,MAAMqB,WAAWhB,OAAOiB,MAAM,CAC5BpF,OACI;QACEsD,UAAU;QACVvD,QAAQ;QACRpC,OAAO;QACP0H,MAAM;QACNC,KAAK;QACLC,OAAO;QACPC,QAAQ;QACRhF;QACAC;IACF,IACA,CAAC,GACLK,cAAc,CAAC,IAAI;QAAE2E,OAAO;IAAc,GAC1CxF;IAGF,MAAMyF,kBACJ,CAAC3E,gBAAgBX,gBAAgB,UAC7BA,gBAAgB,SACd,AAAC,2CAAwC7D,gBAAgB;QACvD6F;QACAC;QACAC;QACAC;QACAlC,aAAaA,eAAe;QAC5BG,WAAW2E,SAAS3E,SAAS;IAC/B,KAAG,OACH,AAAC,UAAOJ,cAAY,KAAI,uBAAuB;OACjD;IAEN,IAAIuF,mBAAmBD,kBACnB;QACEE,gBAAgBT,SAAS3E,SAAS,IAAI;QACtCqF,oBAAoBV,SAAS1E,cAAc,IAAI;QAC/CqF,kBAAkB;QAClBJ;IACF,IACA,CAAC;IAEL,IAAIxC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IACEuC,iBAAiBD,eAAe,IAChCtF,gBAAgB,WAChBC,+BAAAA,YAAayC,UAAU,CAAC,OACxB;YACA,8EAA8E;YAC9E,gFAAgF;YAChF,qFAAqF;YACrF6C,iBAAiBD,eAAe,GAAG,AAAC,UAAOrF,cAAY;QACzD;IACF;IAEA,MAAM0F,gBAAgB9G,iBAAiB;QACrCC;QACAtC;QACAuC;QACAxB,OAAOyE;QACPhD,SAAS6D;QACTrF;QACAyB;IACF;IAEA,IAAI6D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAOiB,WAAW,aAAa;YACjC,IAAI2B;YACJ,IAAI;gBACFA,UAAU,IAAInC,IAAIkC,cAAcnJ,GAAG;YACrC,EAAE,OAAOqJ,GAAG;gBACVD,UAAU,IAAInC,IAAIkC,cAAcnJ,GAAG,EAAEyH,OAAO6B,QAAQ,CAACC,IAAI;YAC3D;YACAnJ,QAAQoJ,GAAG,CAACJ,QAAQG,IAAI,EAAE;gBAAEvJ;gBAAKgD;gBAAUQ;YAAY;QACzD;IACF;IAEA,MAAMiG,QAAkB;QACtB,GAAGzF,IAAI;QACPf,SAASgD,SAAS,SAAShD;QAC3BS;QACA3C,OAAOyE;QACPrC,QAAQsC;QACRiE,UAAU;QACVxG;QACAG,OAAO;YAAE,GAAGkF,QAAQ;YAAE,GAAGQ,gBAAgB;QAAC;QAC1C/H,OAAOmI,cAAcnI,KAAK;QAC1B0B,QAAQyG,cAAczG,MAAM;QAC5B1C,KAAKmJ,cAAcnJ,GAAG;IACxB;IACA,MAAM2J,OAAO;QAAEpH;QAAaS;QAAUQ;QAAaJ;IAAK;IACxD,OAAO;QAAEqG;QAAOE;IAAK;AACvB"}