{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/hydration-error-info.ts"], "names": ["hydrationErrorWarning", "hydrationErrorComponentStack", "knownHydrationWarnings", "Set", "patchConsoleError", "prev", "console", "error", "msg", "serverContent", "clientContent", "componentStack", "has", "replace", "apply", "arguments"], "mappings": "AAAA,OAAO,IAAIA,sBAAyC;AACpD,OAAO,IAAIC,6BAAgD;AAE3D,iIAAiI;AACjI,MAAMC,yBAAyB,IAAIC,IAAI;IACrC;IACA;IACA;IACA;IACA;CACD;AAED;;;;;CAKC,GACD,OAAO,SAASC;IACd,MAAMC,OAAOC,QAAQC,KAAK;IAC1BD,QAAQC,KAAK,GAAG,SAAUC,GAAG,EAAEC,aAAa,EAAEC,aAAa,EAAEC,cAAc;QACzE,IAAIT,uBAAuBU,GAAG,CAACJ,MAAM;YACnCR,wBAAwBQ,IACrBK,OAAO,CAAC,MAAMJ,eACdI,OAAO,CAAC,MAAMH,eACdG,OAAO,CAAC,MAAM;YACjBZ,+BAA+BU;QACjC;QAEA,uCAAuC;QACvCN,KAAKS,KAAK,CAACR,SAASS;IACtB;AACF"}