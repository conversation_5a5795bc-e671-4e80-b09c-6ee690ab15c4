import React, { useState, useEffect } from 'react';
import { CruiseLine, getAllCruiseLines, getTopCruiseLines } from '../../data/cruiseData';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Toolt<PERSON>, Legend } from 'recharts';

interface CruiseLineDonutProps {
  className?: string;
}

// Array of colors for the pie chart segments
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#8DD1E1', '#A4DE6C', '#D0ED57'];

const CruiseLinesDonut: React.FC<CruiseLineDonutProps> = ({ className }) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [animateChart, setAnimateChart] = useState(false);
  
  // Get top cruise lines for the donut chart
  const topCruiseLines = getTopCruiseLines(5);
  
  // Get total for percentage calculations
  const allCruiseLines = getAllCruiseLines();
  const totalQuotes = allCruiseLines.reduce((sum, line) => sum + line.count, 0);
  
  // Format data for the chart with percentages
  const chartData = topCruiseLines.map(line => ({
    ...line,
    percentage: Math.round((line.count / totalQuotes) * 100)
  }));
  
  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setAnimateChart(true);
    }, 300);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Custom tooltip component
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white/90 backdrop-blur-sm p-3 rounded-md shadow-md border border-sky-100">
          <p className="font-semibold text-sky-900">{data.name}</p>
          <p className="text-sky-800">{data.totalQuotes.toLocaleString()} Total Quotes</p>
          <p className="text-green-600">{data.percentage}% of All Quotes</p>
          <div className="text-xs mt-1 text-sky-700">
            <p>Single Cabin: {data.singleCabinQuotes.toLocaleString()} quotes</p>
            <p>Multiple Cabins: {data.multipleCabinQuotes.toLocaleString()} quotes</p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className={`${className || ''} p-5 bg-white/80 backdrop-blur-sm rounded-xl shadow-md border border-white/40`}>
      <h3 className="text-xl font-bold text-sky-900 mb-4 text-center">Supported Cruise Lines</h3>
      
      <div className="flex flex-col md:flex-row items-center justify-between">
        {/* Donut chart */}
        <div className="w-full md:w-1/2 h-[300px]">
          {chartData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  innerRadius={animateChart ? 60 : 0}
                  outerRadius={80}
                  fill="#8884d8"
                  paddingAngle={2}
                  dataKey="totalQuotes"
                  nameKey="name"
                  animationDuration={1000}
                  animationBegin={300}
                  onMouseEnter={(_, index) => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  {chartData.map((entry, index) => (
                    <Cell 
                      key={`cell-${entry.id}`} 
                      fill={COLORS[index % COLORS.length]}
                      stroke="#fff"
                      strokeWidth={hoveredIndex === index ? 2 : 1}
                      style={{
                        filter: hoveredIndex === index ? 'drop-shadow(0px 0px 4px rgba(0,0,0,0.3))' : 'none',
                        opacity: hoveredIndex === null || hoveredIndex === index ? 1 : 0.7,
                        transition: 'all 0.3s ease'
                      }}
                    />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend 
                  layout="vertical" 
                  verticalAlign="middle" 
                  align="right"
                  iconType="circle"
                  formatter={(value, entry, index) => (
                    <span className={`text-sm ${hoveredIndex === index ? 'font-semibold' : ''}`}>
                      {value}
                    </span>
                  )}
                />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">No data available</p>
            </div>
          )}
        </div>
        
        {/* Stats section */}
        <div className="w-full md:w-1/2 p-4">
          <div className="bg-sky-50/70 backdrop-blur-sm rounded-lg p-4 shadow-inner">
            <h4 className="font-semibold text-sky-800 mb-3">Processing Time Savings</h4>
            
            {chartData.map((line, index) => (
              <div key={line.id} className="mb-3">
                <div className="flex justify-between text-sm">
                  <span className="font-medium text-sky-900">{line.name}</span>
                  <span className="text-green-600">{45 + index * 5}% faster</span>
                </div>
                <div className="h-2 w-full bg-gray-200 rounded-full mt-1">
                  <div 
                    className="h-full rounded-full bg-gradient-to-r from-green-400 to-green-600"
                    style={{ 
                      width: animateChart ? `${45 + index * 5}%` : '0%',
                      transition: 'width 1s ease-out',
                      transitionDelay: `${index * 150}ms`
                    }}
                  ></div>
                </div>
              </div>
            ))}
            
            <div className="mt-6">
              <div className="text-center">
                <span className="text-2xl font-bold text-sky-900">10+</span>
                <p className="text-sm text-sky-700">Cruise Lines Supported</p>
              </div>
              <div className="flex justify-between mt-4 text-center">
                <div>
                  <span className="text-xl font-bold text-sky-900">2,753</span>
                  <p className="text-xs text-sky-700">Total Quotes</p>
                </div>
                <div>
                  <span className="text-xl font-bold text-green-600">89%</span>
                  <p className="text-xs text-sky-700">Success Rate</p>
                </div>
                <div>
                  <span className="text-xl font-bold text-amber-600">47%</span>
                  <p className="text-xs text-sky-700">Time Saved</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CruiseLinesDonut;