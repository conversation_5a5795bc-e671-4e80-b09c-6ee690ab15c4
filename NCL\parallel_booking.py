import os
import asyncio
import sys
from datetime import datetime
import copy
from pathlib import Path
import logging

sys.modules['parallel_booking'] = sys.modules[__name__]

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from NCL.login import NCLLogin
from NCL.select_cruise import CruiseSelector
from NCL.select_category import CategorySelector
from NCL.select_stateroom import StateRoomSelector
from NCL.utils import NCLUtils
from NCL.reservation import Reservation

import NCL.cruise_selection as cruise_selection
from NCL.cruise_selection import CruiseSelection
from db.screenshot_manager import save_screenshot_to_db

NCL_Utils = NCLUtils()

cabin_logs = {}
cruise_index = 0

MAX_RETRIES = 3

CABIN_TIMEOUT = 300

logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("NCL-Booking")


class CabinLogger:

    def __init__(self, cabin_id):

        self.logger = logging.getLogger(f"Cabin-{cabin_id}")
        self.cabin_id = cabin_id

    def info(self, message):
        self.logger.info(f"[Cabin {self.cabin_id}] {message}")

    def error(self, message):
        self.logger.error(f"[Cabin {self.cabin_id}] {message}")

    def warning(self, message):
        self.logger.warning(f"[Cabin {self.cabin_id}] {message}")

    def debug(self, message):
        self.logger.debug(f"[Cabin {self.cabin_id}] {message}")

    def critical(self, message):
        self.logger.critical(f"[Cabin {self.cabin_id}] {message}")

    def exception(self, message):
        self.logger.exception(f"[Cabin {self.cabin_id}] {message}")


class ParallelBooking:

    @staticmethod
    def cabin_specific_logger(cabin_id):
        return CabinLogger(cabin_id)

    @staticmethod
    async def process_single_cabin(
        cabin_id, cruise_details, shared_results, first_cabin_event=None, config=None
    ):
        cabin_start_time = asyncio.get_event_loop().time()

        global cruise_index

        request_id = cruise_details.get('request_id')
        session_id = cruise_details.get('session_id')

        cabin_log = ParallelBooking.cabin_specific_logger(cabin_id)
        cabin_log.info(f"Starting booking process for cabin {cabin_id}")

        preferred_category = None
        if cabin_id <= len(cruise_details.get('cabin_categories', [])):
            preferred_category = cruise_details['cabin_categories'][cabin_id - 1]
            cabin_log.info(f"Preferred category for this cabin: {preferred_category}")

        ncl_onboard_percentage = cruise_details.get('ncl_onboard_percentage', 10)
        cabin_log.info(
            f"Using NCL onboard credit percentage: {ncl_onboard_percentage}%"
        )

        reservation_data = {
            "cabin_id": cabin_id, "cruise_details": {
                "ship_name": cruise_details.get('ship_name', ''),
                "travel_date": cruise_details.get('travel_date', ''),
                "nights": cruise_details.get('nights', 0),
            }, "passengers": {"total": 0, "adults": 0, "children": 0, "infants": 0},
            "selected_category": "", "selected_promo_code": "", "total_cost": "0",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        if request_id:
            reservation_data["request_id"] = request_id
        if session_id:
            reservation_data["session_id"] = session_id

        total_adults = cruise_details['adults']
        total_children = cruise_details.get('children', 0)
        total_infants = cruise_details.get('infants', 0)
        num_cabins = cruise_details.get('cabins', 1)

        cabin_passengers_dict = cruise_details.get('cabin_passengers_dict', {})
        is_or_cabin_scenario = False

        if cruise_details.get('is_edited',
                              False) and str(cabin_id) in cabin_passengers_dict:
            cabin_data = cabin_passengers_dict[str(cabin_id)]
            cabin_adults = cabin_data['adults']
            cabin_children = cabin_data['children']
            cabin_infants = cabin_data.get('infants', 0)

            cabin_log.info(
                f"Using manually edited values: {cabin_adults} adults, {cabin_children} children, {cabin_infants} infants"
            )

            reservation_data['passengers']['adults'] = cabin_adults
            reservation_data['passengers']['children'] = cabin_children
            reservation_data['passengers']['infants'] = cabin_infants
            reservation_data['passengers'][
                'total'] = cabin_adults + cabin_children + cabin_infants

        else:
            if num_cabins > 1:
                is_or_cabin_scenario = (
                    cruise_details['cabins'] > 1
                    and len(cruise_details.get('cabin_categories', [])
                            ) == cruise_details['cabins'] and cruise_details['cabins']
                    != int(cruise_details.get('original_cabin_count', '1'))
                )

            if num_cabins == 1:
                cabin_adults = total_adults
                cabin_children = total_children
                cabin_infants = total_infants
                cabin_log.info(
                    f"Single cabin booking - using all {cabin_adults} adults, {cabin_children} children, {cabin_infants} infants"
                )
            elif is_or_cabin_scenario:
                cabin_adults = total_adults
                cabin_children = total_children
                cabin_infants = total_infants
                cabin_log.info(
                    f"'OR' cabin scenario - each cabin gets all passengers: {cabin_adults} adults, {cabin_children} children, {cabin_infants} infants"
                )
            else:
                cabin_adults = total_adults // num_cabins
                extras = total_adults % num_cabins

                if cabin_id <= extras:
                    cabin_adults += 1

                cabin_children = 0
                if total_children > 0:
                    cabin_children = total_children // num_cabins
                    child_extras = total_children % num_cabins

                    if cabin_id <= child_extras:
                        cabin_children += 1

                cabin_infants = 0
                if total_infants > 0:
                    cabin_infants = total_infants // num_cabins
                    infant_extras = total_infants % num_cabins

                    if cabin_id <= infant_extras:
                        cabin_infants += 1

                cabin_log.info(
                    f"Distributing passengers - Cabin {cabin_id} gets {cabin_adults} adults, {cabin_children} children, {cabin_infants} infants"
                )

            reservation_data['passengers']['adults'] = cabin_adults
            reservation_data['passengers']['children'] = cabin_children
            reservation_data['passengers']['infants'] = cabin_infants
            reservation_data['passengers'][
                'total'] = cabin_adults + cabin_children + cabin_infants

        if reservation_data['passengers']['adults'] < 1:
            cabin_log.error(
                f"Cabin {cabin_id} has no adults assigned. Minimum 1 adult required."
            )

            shared_results[cabin_id] = {
                "success": False, "error": "No adults assigned to cabin",
                "reservation_data": reservation_data
            }

            return None

        cruise_details_copy = copy.deepcopy(cruise_details)

        page = None
        try:
            NCL_Login = NCLLogin()

            video_auditing = config.get('video_auditing', False) if config else False

            page = await NCL_Login.setup_driver(
                headless=True,
                session_id=session_id,
                cabin_id=cabin_id,
                video_auditing=video_auditing
            )

            credentials = NCL_Login.get_credentials()

            if not credentials["username"] or not credentials[
                    "password"] or not credentials["url"]:
                cabin_log.error("Missing credentials in .env file")

                shared_results[cabin_id] = {
                    "success": False, "error": "Missing credentials",
                    "reservation_data": reservation_data
                }

                return None

            cabin_log.info("Logging in to NCL website")
            login_success = await NCL_Login.login(
                page,
                credentials["url"],
                credentials["username"],
                credentials["password"],
                session_id=session_id,
                cabin_id=cabin_id
            )

            if not login_success:
                cabin_log.error("Failed to log in to NCL website")

                shared_results[cabin_id] = {
                    "success": False, "error": "Login failed", "reservation_data":
                    reservation_data
                }

                return None

            reservation = Reservation()
            cabin_log.info("Navigating to new reservation page")
            if not await reservation.click_new_reservation_and_continue(page):
                cabin_log.error("Failed to navigate to new reservation page")
                shared_results[cabin_id] = {
                    "success": False, "error": "Navigation to new reservation failed",
                    "reservation_data": reservation_data
                }
                return None

            if cruise_details.get('is_edited',
                                  False) and str(cabin_id) in cabin_passengers_dict:
                cabin_log.info(f"Using exact passenger distribution from edited values")
                cabin_data = cabin_passengers_dict[str(cabin_id)]
                cruise_details_copy['adults'] = cabin_data['adults']
                cruise_details_copy['children'] = cabin_data['children']
                cruise_details_copy['infants'] = cabin_data.get('infants', 0)
            else:
                cruise_details_copy['adults'] = cabin_adults

                if 'children' in reservation_data['passengers']:
                    cruise_details_copy['children'] = reservation_data['passengers'][
                        'children']
                    cabin_log.info(
                        f"Setting children for this cabin: {cruise_details_copy['children']}"
                    )

                cruise_details_copy['infants'] = reservation_data['passengers'][
                    'infants']
                cabin_log.info(
                    f"Setting infants for this cabin: {cruise_details_copy.get('infants', 0)}"
                )

            if cabin_id > 1 or is_or_cabin_scenario:
                cruise_details_copy['cabins'] = 1

            cabin_categories = cruise_details_copy.get('cabin_categories', [])
            if cabin_categories:
                if cabin_id <= len(cabin_categories):
                    cruise_details_copy['cabin_categories'] = [
                        cabin_categories[cabin_id - 1]
                    ]
                    cabin_log.info(
                        f"Using cabin category: {cabin_categories[cabin_id-1]}"
                    )
                else:
                    cabin_log.info(
                        f"No specific category found for cabin {cabin_id}, will auto-select"
                    )
            else:
                cabin_log.info("No cabin categories specified, will auto-select")

            cabin_log.info(f"Cabin {cabin_id} filling search form")
            if not await cruise_selection.CruiseSelection.fill_search_form_improved(
                    page, cruise_details_copy):
                cabin_log.error("Form filling failed. Aborting cabin processing.")
                shared_results[cabin_id] = {
                    "success": False, "error": "Search form filling failed",
                    "reservation_data": reservation_data
                }
                return None

            await NCL_Utils.save_screenshot(
                page, "01_search_form_filled", cabin_id, request_id, session_id
            )

            cabin_log.info(f"Cabin {cabin_id} clicking search button")
            if not await cruise_selection.CruiseSelection.start_search_improved(page):
                cabin_log.error("Search failed. Aborting cabin processing.")
                shared_results[cabin_id] = {
                    "success": False, "error": "Search button click failed",
                    "reservation_data": reservation_data
                }
                return None

            Cruise_Selector = CruiseSelector()

            cabin_log.info(f"Cabin {cabin_id} waiting for search results")
            cruise_index = await Cruise_Selector.display_available_cruises(page)
            if cruise_index is None:
                cabin_log.error("Cruise selection failed. Aborting cabin processing.")
                shared_results[cabin_id] = {
                    "success": False, "error": "Cruise display failed",
                    "reservation_data": reservation_data
                }
                return None

            await NCL_Utils.save_screenshot(
                page, "02_available_cruises", cabin_id, request_id, session_id
            )

            is_index_provided = cruise_details.get('cruise_index') is not None
            if is_index_provided:
                cruise_index = int(cruise_details.get('cruise_index', 0))
                cabin_log.info(f"Using provided cruise index: {cruise_index}")

            cabin_log.info(f"Selecting cruise at index {cruise_index}")
            if not await Cruise_Selector.select_cruise(
                    page, cruise_index, session_id=session_id, cabin_id=cabin_id):
                cabin_log.error(
                    f"Failed to select cruise at index {cruise_index}. Aborting cabin {cabin_id}."
                )
                shared_results[cabin_id] = {
                    "success": False, "error": "Cruise selection failed",
                    "reservation_data": reservation_data
                }
                return None

            selected_cruise_details = {
                "ship_name": cruise_details.get("ship_name", "Unknown Ship"),
                "sailing_date": cruise_details.get("travel_date", "Unknown Date"),
                "cruise_line": "Norwegian Cruise Line"
            }

            cabin_log.info(
                f"Selected cruise: {selected_cruise_details['ship_name']} sailing on {selected_cruise_details['sailing_date']}"
            )
            reservation_data["cruise_details"].update(selected_cruise_details)

            await NCL_Utils.save_screenshot(
                page,
                "03_available_categories",
                cabin_id,
                request_id,
                session_id  #available category
            )

            Category_Selector = CategorySelector()
            cabin_log.info("Displaying available categories")
            category_index, category_data = await Category_Selector.display_available_categories(
                page,
                preferred_category,
                session_id=session_id,
                cabin_id=cabin_id
            )

            await capture_full_categories_screenshot(page, cabin_id, request_id, session_id)

            if category_index is None:
                cabin_log.error("Category selection failed. Aborting cabin processing.")
                shared_results[cabin_id] = {
                    "success": False, "error": "Category display failed",
                    "reservation_data": reservation_data
                }

                return None

            try:
                if category_data and len(category_data) > category_index:
                    selected_category = category_data[category_index]['full_text']
                    reservation_data['selected_category'] = selected_category
                    cabin_log.info(f"Selected category: {selected_category}")
            except Exception as e:
                cabin_log.warning(f"Could not capture selected category name: {str(e)}")
                reservation_data['selected_category'
                                 ] = f"Category index {category_index}"

            cabin_log.info(f"Selecting category at index {category_index}")
            if not await Category_Selector.select_category(
                    page,
                    category_index,
                    session_id=session_id,
                    cabin_id=cabin_id):
                cabin_log.error("Failed to select category. Aborting cabin processing.")
                shared_results[cabin_id] = {
                    "success": False, "error": "Category selection failed",
                    "reservation_data": reservation_data
                }

                return None

            await NCL_Utils.save_screenshot(
                page, "04_available_staterooms", cabin_id, request_id, session_id
            )

            State_Room_Selector = StateRoomSelector()

            cabin_log.info("Selecting stateroom")
            stateroom_result = await State_Room_Selector.select_stateroom(
                page,
                reservation_data,
                session_id=session_id,
                cabin_id=cabin_id,
                config=config
            )

            if not stateroom_result:
                cabin_log.error(
                    "Failed to select stateroom. Aborting cabin processing."
                )
                shared_results[cabin_id] = {
                    "success": False, "error": "Stateroom selection failed",
                    "reservation_data": reservation_data
                }
                return None

            cabin_log.info("Navigating to pricing page")
            if not await cruise_selection.CruiseSelection.navigate_to_pricing(page):
                cabin_log.error(
                    "Failed to navigate to pricing page. Aborting cabin processing."
                )
                shared_results[cabin_id] = {
                    "success": False, "error": "Navigation to pricing failed",
                    "reservation_data": reservation_data
                }

                return None

            await NCL_Utils.save_screenshot(
                page, "06_pricing_page", cabin_id, request_id, session_id
            )

            Cruise_Selection = CruiseSelection()
            cabin_log.info("Extracting pricing information")
            pricing_data = await Cruise_Selection.extract_detailed_pricing(
                page,
                reservation_data,
                ncl_onboard_percentage,
                session_id=session_id,
                cabin_id=cabin_id,
                config=config
            )

            if pricing_data:
                cabin_log.info(f"Extracted pricing data: {pricing_data}")
                for key, value in pricing_data.items():
                    reservation_data[key] = value

                if 'reservation_total' in pricing_data and pricing_data[
                        'reservation_total'] > 0:
                    reservation_total = pricing_data['reservation_total']

                    reservation_data['reservation_total'] = reservation_total
                    reservation_data['formatted_reservation_total'
                                     ] = f"${reservation_total:.2f}"

                    reservation_data['total_cost'] = reservation_total
                    reservation_data['formatted_total_cost'
                                     ] = f"${reservation_total:.2f}"

                    cabin_log.info(f"Set reservation total: ${reservation_total:.2f}")

                    if 'onboard_credit' in pricing_data:
                        cabin_log.info(
                            f"Using extracted onboard credit: ${pricing_data['onboard_credit']:.2f}"
                        )
                    else:
                        cabin_log.warning(
                            "No onboard credit found in extracted pricing data"
                        )
                else:

                    if 'total_cost' in pricing_data and pricing_data['total_cost'] > 0:
                        total_cost = pricing_data['total_cost']

                        reservation_data['reservation_total'] = total_cost
                        reservation_data['formatted_reservation_total'
                                         ] = f"${total_cost:.2f}"

                        reservation_data['total_cost'] = total_cost
                        reservation_data['formatted_total_cost'] = f"${total_cost:.2f}"

                        cabin_log.info(
                            f"Using total_cost as reservation total: ${total_cost:.2f}"
                        )

                        if 'onboard_credit' in pricing_data:
                            cabin_log.info(
                                f"Using extracted onboard credit: ${pricing_data['onboard_credit']:.2f}"
                            )
                        else:
                            cabin_log.warning(
                                "No onboard credit found in extracted pricing data"
                            )
                    else:

                        cabin_log.warning(
                            "No valid reservation total found. Trying page extraction."
                        )
                        try:

                            import re
                            page_source = await page.content()
                            price_matches = re.findall(r'\$([0-9,\.]+)', page_source)

                            if price_matches:
                                prices = [
                                    float(p.replace(',', '')) for p in price_matches
                                ]
                                largest_price = max(prices)

                                reservation_data['reservation_total'] = largest_price
                                reservation_data['formatted_reservation_total'
                                                 ] = f"${largest_price:.2f}"

                                reservation_data['total_cost'] = largest_price
                                reservation_data['formatted_total_cost'
                                                 ] = f"${largest_price:.2f}"

                                cabin_log.info(
                                    f"Extracted largest price from page: ${largest_price:.2f}"
                                )

                                if 'onboard_credit' in pricing_data:
                                    cabin_log.info(
                                        f"Using extracted onboard credit: ${pricing_data['onboard_credit']:.2f}"
                                    )
                                else:
                                    cabin_log.warning(
                                        "No onboard credit found in extracted pricing data"
                                    )
                        except Exception as extract_err:
                            cabin_log.error(
                                f"Failed to extract price from page: {str(extract_err)}"
                            )
            else:
                cabin_log.warning("Failed to extract pricing data")

            cabin_log.info(f"Final reservation data before save: {reservation_data}")

            if request_id and "request_id" not in reservation_data:
                reservation_data["request_id"] = request_id

            await NCL_Utils.save_reservation_data(
                reservation_data,
                cabin_id,
                is_parallel=True,
                request_id=request_id,
                session_id=session_id
            )

            cabin_execution_time = asyncio.get_event_loop().time() - cabin_start_time

            shared_results[cabin_id] = {
                "success": True, "reservation_data": reservation_data, "execution_time":
                cabin_execution_time
            }

            cabin_log.info(f"Cabin {cabin_id} booking process completed successfully")

            return reservation_data

        except Exception as e:
            cabin_log.error(f"Error in process_single_cabin: {str(e)}")

            shared_results[cabin_id] = {
                "success": False, "error": str(e), "reservation_data": reservation_data
            }

            return None
        finally:
            if page:
                try:
                    if hasattr(page, '_browser_setup'):
                        try:
                            await page._browser_setup.stop_video_recording(
                                page,
                                logger=cabin_log,
                                request_id=request_id,
                                provider="NCL",
                                session_id=session_id,
                                cabin_id=cabin_id
                            )
                        except Exception as e:
                            cabin_log.error(f"Error stopping video recording: {e}")

                    # Close context and page (but keep shared browser alive)
                    if hasattr(page, '_context'):
                        await page._context.close()
                        cabin_log.info(f"Context closed for cabin {cabin_id}")
                    
                    # Note: Don't close browser or stop playwright as they are shared
                    
                except Exception as e:
                    cabin_log.error(f"Error during cleanup: {e}")
                    pass

    @staticmethod
    async def parallel_cruise_reservation(cruise_details, config=None, session_id=None):
        overall_start_time = asyncio.get_event_loop().time()

        if not cruise_details:
            logger.error("No cruise details provided")
            return False
        
        request_id = cruise_details.get('request_id')

        if request_id:
            request_id = request_id.strip()
            cruise_details['request_id'] = request_id
            logger.info(f"Using cleaned request_id: {request_id}")

        if session_id:
            cruise_details['session_id'] = session_id
            logger.info(f"Using session ID: {session_id}")
        elif request_id:
            try:
                from Core.database_helper import register_session
                session_id = await register_session(request_id, "NCL")
                cruise_details['session_id'] = session_id
                logger.info(
                    f"Created new session ID for parallel booking: {session_id}"
                )
            except Exception as e:
                logger.error(f"Error registering session: {e}")
                session_id = f"{request_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                cruise_details['session_id'] = session_id

        total_cabins = cruise_details.get('cabins', 1)

        if total_cabins == 1:
            logger.info(
                "Single cabin booking - processing directly without optimization"
            )

            NCLUtils.reset_processed_bookings()

            shared_results = {}
            await ParallelBooking.process_single_cabin(
                1, cruise_details, shared_results, None, config
            )

            execution_time = asyncio.get_event_loop().time() - overall_start_time
            logger.info(f"Single cabin execution time: {execution_time:.2f} seconds")

            if 1 in shared_results:
                result = shared_results[1]
                if "reservation_data" in result:
                    result["reservation_data"]["execution_time"] = execution_time

                    cabin_data = result["reservation_data"]
                    if request_id and "request_id" not in cabin_data:
                        cabin_data["request_id"] = request_id

                    combined_data = await NCLUtils.combine_reservation_data(
                        [cabin_data],
                        is_parallel=False,
                        execution_time=execution_time,
                        request_id=request_id,
                        session_id=session_id
                    )

                    return combined_data
                return result
            else:
                return {
                    "success": False, "error": "Single cabin processing failed",
                    "reservation_data":
                    {"cabin_id": 1, "execution_time": execution_time}
                }

        logger.info("\n" + "=" * 60)
        logger.info("STEP 1: ANALYZING CABINS FOR OPTIMIZATION")
        logger.info("=" * 60)

        enable_cabin_optimization = True
        if config and 'enable_cabin_optimization' in config:
            enable_cabin_optimization = config['enable_cabin_optimization']

        logger.info(
            f"Cabin optimization: {'ENABLED' if enable_cabin_optimization else 'DISABLED'}"
        )

        if enable_cabin_optimization:
            similar_cabin_groups, cabin_to_group_mapping = NCLUtils.identify_similar_cabins(
                cruise_details
            )
        else:
            similar_cabin_groups = {}
            cabin_to_group_mapping = {}
            logger.info(
                "Cabin optimization disabled - processing all cabins individually"
            )

        cabins_to_process = []

        if similar_cabin_groups and enable_cabin_optimization:
            for signature, cabin_indices in similar_cabin_groups.items():
                cabins_to_process.append(cabin_indices[0])

            logger.info(
                f"Optimization enabled: Processing {len(cabins_to_process)} unique cabins out of {total_cabins} total cabins"
            )

            for signature, cabin_indices in similar_cabin_groups.items():
                if len(cabin_indices) > 1:
                    cabin_type, adults, children, infants = signature
                    logger.info(
                        f"Cabin group '{cabin_type}' ({adults}A, {children}C, {infants}I): Processing cabin {cabin_indices[0]}, replicating to cabins {cabin_indices[1:]}"
                    )
        else:
            cabins_to_process = list(range(1, total_cabins + 1))
            if enable_cabin_optimization:
                logger.info(
                    f"No optimization possible: Processing all {len(cabins_to_process)} cabins individually"
                )
            else:
                logger.info(
                    f"Optimization disabled: Processing all {len(cabins_to_process)} cabins individually"
                )

        logger.info("\n" + "=" * 60)
        logger.info("STEP 2: PARALLEL CABIN PROCESSING")
        logger.info("=" * 60)

        cabin_plural = "cabin" if len(cabins_to_process) == 1 else "cabins"
        logger.info(
            f"Starting parallel processing for {len(cabins_to_process)} unique {cabin_plural}..."
        )

        NCLUtils.reset_processed_bookings()

        shared_results = {}
        tasks = []

        for i, cabin_id in enumerate(cabins_to_process):
            task = asyncio.create_task(
                ParallelBooking.process_single_cabin(
                    cabin_id, cruise_details, shared_results, None, config
                )
            )
            tasks.append((task, cabin_id))
            await asyncio.sleep(0.5)

        # Use asyncio.gather to wait for all tasks to complete
        try:
            await asyncio.wait_for(
                asyncio.gather(*[task for task, _ in tasks], return_exceptions=True),
                timeout=CABIN_TIMEOUT + 30
            )
        except asyncio.TimeoutError:
            logger.error("Some cabin tasks did not complete within timeout")
            for task, cabin_id in tasks:
                if not task.done():
                    logger.error(f"Task for cabin {cabin_id} did not complete within timeout")
                    task.cancel()
        except Exception as e:
            logger.error(f"Error waiting for cabin tasks: {str(e)}")

        logger.info("\n" + "=" * 60)
        logger.info("STEP 3: RESULT REPLICATION")
        logger.info("=" * 60)

        replicated_count = 0
        if similar_cabin_groups and cabin_to_group_mapping and enable_cabin_optimization:
            for signature, cabin_indices in similar_cabin_groups.items():
                if len(cabin_indices) > 1:
                    source_cabin_id = cabin_indices[0]

                    if source_cabin_id in shared_results and shared_results[
                            source_cabin_id].get('success', False):
                        source_result = shared_results[source_cabin_id]

                        for target_cabin_id in cabin_indices[1:]:
                            logger.info(
                                f"Replicating results from cabin {source_cabin_id} to cabin {target_cabin_id}"
                            )

                            replicated_result = NCLUtils.replicate_cabin_result(
                                source_cabin_id, target_cabin_id, source_result,
                                cruise_details
                            )
                            shared_results[target_cabin_id] = replicated_result
                            replicated_count += 1
                    else:
                        logger.warning(
                            f"Source cabin {source_cabin_id} failed, marking similar cabins as failed"
                        )
                        for target_cabin_id in cabin_indices[1:]:
                            shared_results[target_cabin_id] = {
                                'success': False, 'cabin_id': target_cabin_id, 'error':
                                f'Source cabin {source_cabin_id} failed',
                                'reservation_data': {
                                    'cabin_id':
                                    target_cabin_id, 'timestamp':
                                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                    'source_failure':
                                    True
                                }
                            }
                            replicated_count += 1

            if replicated_count > 0:
                logger.info(f"Replicated results to {replicated_count} cabins")
            else:
                logger.info("No results to replicate")
        else:
            if enable_cabin_optimization:
                logger.info("No similar cabins found - no replication needed")
            else:
                logger.info("Optimization disabled - no replication performed")

        logger.info("\n" + "=" * 60)
        logger.info("STEP 4: RESULTS COMPILATION")
        logger.info("=" * 60)

        all_cabin_data = []
        successful_cabins = 0
        failed_cabins = 0

        for cabin_id in range(1, total_cabins + 1):
            if cabin_id in shared_results:
                result = shared_results[cabin_id]
                if result.get('success', False):
                    successful_cabins += 1
                    if 'reservation_data' in result:
                        all_cabin_data.append(result['reservation_data'])
                else:
                    failed_cabins += 1
                    failed_data = result.get('reservation_data', {})
                    failed_data.update(
                        {
                            'cabin_id': cabin_id, 'success': False, 'error':
                            result.get('error', 'Unknown error')
                        }
                    )
                    all_cabin_data.append(failed_data)
            else:
                failed_cabins += 1
                logger.warning(f"No result found for cabin {cabin_id}")
                all_cabin_data.append(
                    {
                        'cabin_id': cabin_id, 'success': False, 'error':
                        'No processing result available', 'timestamp':
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                )

        overall_end_time = asyncio.get_event_loop().time()
        total_execution_time = overall_end_time - overall_start_time

        logger.info(f"Processing Summary:")
        logger.info(f"  Total cabins: {total_cabins}")
        logger.info(
            f"  Optimization: {'ENABLED' if enable_cabin_optimization else 'DISABLED'}"
        )
        logger.info(f"  Unique cabins processed: {len(cabins_to_process)}")
        if enable_cabin_optimization:
            logger.info(f"  Results replicated: {replicated_count}")
            logger.info(
                f"  Time saved: ~{((total_cabins - len(cabins_to_process)) / total_cabins * 100):.1f}% reduction in processing"
            )
        logger.info(f"  Successful cabins: {successful_cabins}")
        logger.info(f"  Failed cabins: {failed_cabins}")
        logger.info(f"  Total execution time: {total_execution_time:.2f} seconds")

        try:
            if all_cabin_data:
                logger.info("Saving combined reservation data to database...")
                combined_result = await NCLUtils.combine_reservation_data(
                    all_cabin_data,
                    is_parallel=True,
                    execution_time=total_execution_time,
                    request_id=request_id,
                    session_id=session_id
                )

                if combined_result:
                    logger.success("All cabin data successfully saved to database")
                    return combined_result
                else:
                    logger.error("Failed to save combined data to database")
                    return False
            else:
                logger.error("No cabin data to save")
                return False

        except Exception as e:
            logger.error(f"Error saving combined reservation data: {str(e)}")
            return False
        
        except asyncio.CancelledError:
            logger.warning(f"NCL booking cancelled for session {session_id}")
            # Perform any necessary cleanup
            # The contexts will be closed by the cancellation service
            raise  # Re-raise to ensure proper cancellation propagation

    async def init_booking(self, request_id=None, session_id=None, log_file=None):
        """Initialize booking"""
        if self.is_initialized:
            logger.info("Booking already initialized")
            return True

        self.request_id = request_id or f"{datetime.now().strftime('%Y%m%d%H%M%S')}"
        if session_id:
            self.session_id = session_id
        else:
            try:
                from Core.database_helper import register_session
                self.session_id = await register_session(self.request_id, "NCL")
                logger.info(f"Registered new session ID for booking: {self.session_id}")
            except Exception as e:
                logger.error(f"Error registering session: {e}")
                self.session_id = f"{self.request_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

        NCLUtils._processed_bookings = set()
        logger.info("Reset processed bookings tracking")

        self.log_file = log_file or os.path.join(
            f"ncl_booking_{self.request_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.log"
        )
        self.init_logger()

        logger.info(f"Begin NCL booking with request_id: {self.request_id}")

        self.is_initialized = True
        return True

# ✨ NEW HELPER ───────────────────────────────────────────────────────────
async def capture_full_categories_screenshot(page, cabin_id, request_id, session_id):
    """Render a temporary static table built from the hidden input JSON and screenshot THAT.
    This bypasses SlickGrid's virtual-row rendering entirely, guaranteeing the PNG contains
    every category even when the grid only keeps ~20 DOM rows in memory at once."""

    OVERLAY_ID = "__full_cat_table_overlay__"

    # 1️⃣  Inject overlay table built from the hidden JSON
    injected = await page.evaluate(
        """
        (overlayId) => {
            // Clean previous attempt
            const prev = document.getElementById(overlayId);
            if (prev) prev.remove();

            const hidden = document.querySelector("input[name='category']");
            if (!hidden) return "missing-hidden";

            let payload;
            try { payload = JSON.parse(hidden.value); }
            catch(e) { return "json-parse-error"; }

            const rows = (payload && payload.data) ? payload.data : [];
            if (!rows.length) return "no-data";

            // Build overlay container
            const overlay = document.createElement('div');
            overlay.id = overlayId;
            overlay.style.position = 'fixed';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.zIndex = '2147483647'; // on very top
            overlay.style.background = '#ffffff';
            overlay.style.border = '1px solid #ccc';
            overlay.style.maxWidth = 'none';
            overlay.style.padding = '6px';

            const table = document.createElement('table');
            table.style.borderCollapse = 'collapse';
            table.style.font = '12px/16px Arial, sans-serif';

            const columns = [
                'Category',
                'Description',
                'MaxCapacity',
                'Status',
                'CabinAvailableDisplay',
                'CurrentPromo',
                'QuoteTotal1',
                'QuoteTotal2'
            ];

            // Header
            const thead = document.createElement('tr');
            columns.forEach(col => {
                const th = document.createElement('th');
                th.textContent = col;
                th.style.border = '1px solid #ccc';
                th.style.padding = '2px 4px';
                th.style.background = '#e8e8e8';
                thead.appendChild(th);
            });
            table.appendChild(thead);

            // Rows
            rows.forEach(r => {
                const tr = document.createElement('tr');
                columns.forEach(col => {
                    const td = document.createElement('td');
                    td.textContent = r[col] !== undefined ? r[col] : '';
                    td.style.border = '1px solid #ddd';
                    td.style.padding = '2px 4px';
                    tr.appendChild(td);
                });
                table.appendChild(tr);
            });

            overlay.appendChild(table);
            document.body.appendChild(overlay);

            // Return OK
            return "ok";
        }
        """,
        OVERLAY_ID,
    )

    if injected != "ok":
        # Fallback: bail out quietly
        return

    # 2️⃣  Grab screenshot of the overlay
    overlay_element = await page.wait_for_selector(f"#{OVERLAY_ID}")
    png_data = await overlay_element.screenshot()

    # 3️⃣  Remove overlay to leave page untouched
    await page.evaluate(
        """
        (overlayId) => {
            const el = document.getElementById(overlayId);
            if (el) el.remove();
        }
        """,
        OVERLAY_ID,
    )

    # 4️⃣  Persist via pipeline
    file_name = f"03_available_categories_full_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    await save_screenshot_to_db(
        png_data,
        request_id,
        "NCL",
        "Available Categories Full",
        file_name,
        cabin_id,
        session_id,
    )
# ─────────────────────────────────────────────────────────────────────────
